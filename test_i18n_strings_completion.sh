#!/bin/bash

# AutoLaunch 多語系字串完成測試腳本
# 測試所有新增的字串資源是否正確翻譯

echo "=== AutoLaunch 多語系字串完成測試 ==="
echo "測試日期: $(date)"
echo

# 檢查編譯狀態
echo "1. 檢查編譯狀態..."
cd "$(dirname "$0")"
./gradlew compileDebugKotlin > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ 編譯成功"
else
    echo "❌ 編譯失敗"
    exit 1
fi
echo

# 檢查新增的字串資源
echo "2. 檢查新增的字串資源..."

# 要檢查的新字串鍵
new_strings=(
    "day_name_monday"
    "day_name_tuesday" 
    "day_name_wednesday"
    "day_name_thursday"
    "day_name_friday"
    "day_name_saturday"
    "day_name_sunday"
    "day_short_monday"
    "day_short_tuesday"
    "day_short_wednesday"
    "day_short_thursday"
    "day_short_friday"
    "day_short_saturday"
    "day_short_sunday"
    "days_none"
    "days_daily"
    "days_weekdays"
    "days_weekend"
    "time_just_now"
    "time_minutes_ago"
    "time_hour_ago"
    "time_hours_ago"
    "time_yesterday"
    "time_days_ago"
    "time_expired"
    "time_minute_later"
    "time_minutes_later"
    "time_hour_later"
    "time_hours_later"
    "time_day_later"
    "time_days_later"
    "time_week_later"
    "time_weeks_later"
    "schedule_type_web"
    "schedule_unknown_app"
    "repeat_mode_once_short"
    "repeat_mode_daily_short"
    "repeat_mode_weekly_short"
    "repeat_mode_monthly_short"
    "welcome_page_1_title"
    "welcome_page_1_description"
    "welcome_page_2_title"
    "welcome_page_2_description"
    "welcome_page_3_title"
    "welcome_page_3_description"
    "welcome_page_4_title"
    "welcome_page_4_description"
    "welcome_page_5_title"
    "welcome_page_5_description"
    "menu_group_settings"
    "menu_group_tools"
    "menu_group_help"
    "menu_theme_settings_title"
    "menu_language_settings_title"
    "menu_notification_settings"
    "menu_notification_settings_description"
    "action_search"
    "action_filter"
    "action_export_logs"
    "action_clear_logs"
    "tab_app"
    "tab_web"
    "tab_local_backup"
    "tab_cloud_backup"
    "tab_tutorial"
    "tab_qa"
    "tab_light_mode"
    "tab_dark_mode"
)

# 檢查的語言文件
languages=("values" "values-en" "values-ja" "values-ko")
language_names=("中文" "English" "日本語" "한국어")

missing_strings=0

for i in "${!languages[@]}"; do
    lang="${languages[$i]}"
    lang_name="${language_names[$i]}"
    file="app/src/main/res/$lang/strings.xml"
    
    echo "檢查 $lang_name ($lang):"
    
    if [ ! -f "$file" ]; then
        echo "  ❌ 文件不存在: $file"
        missing_strings=$((missing_strings + 1))
        continue
    fi
    
    lang_missing=0
    for string_key in "${new_strings[@]}"; do
        if ! grep -q "name=\"$string_key\"" "$file"; then
            echo "  ❌ 缺少字串: $string_key"
            lang_missing=$((lang_missing + 1))
            missing_strings=$((missing_strings + 1))
        fi
    done
    
    if [ $lang_missing -eq 0 ]; then
        echo "  ✅ 所有字串完整 (${#new_strings[@]} 個)"
    else
        echo "  ⚠️  缺少 $lang_missing 個字串"
    fi
    echo
done

echo "3. 檢查硬編碼字串移除狀況..."

# 檢查關鍵文件中是否還有硬編碼中文字串
files_to_check=(
    "app/src/main/java/com/example/autolaunch/model/DaysOfWeek.kt"
    "app/src/main/java/com/example/autolaunch/utils/TimeFormatUtils.kt"
    "app/src/main/java/com/example/autolaunch/adapter/UnifiedScheduleAdapter.kt"
    "app/src/main/java/com/example/autolaunch/fragments/WelcomePageFragment.kt"
)

hardcoded_found=0

for file in "${files_to_check[@]}"; do
    if [ -f "$file" ]; then
        # 檢查是否有硬編碼的中文字串（排除註釋）
        hardcoded=$(grep -v "^\s*//" "$file" | grep -v "^\s*\*" | grep -E "[\u4e00-\u9fff]" | grep -v "getString\|R\.string" | wc -l)
        if [ $hardcoded -gt 0 ]; then
            echo "⚠️  $file 可能還有 $hardcoded 行硬編碼中文"
            hardcoded_found=$((hardcoded_found + hardcoded))
        else
            echo "✅ $file 無硬編碼中文字串"
        fi
    else
        echo "❌ 文件不存在: $file"
    fi
done

echo

# 總結
echo "=== 測試總結 ==="
if [ $missing_strings -eq 0 ] && [ $hardcoded_found -eq 0 ]; then
    echo "🎉 所有測試通過！多語系字串翻譯完成。"
    echo
    echo "✅ 完成項目："
    echo "   - 新增 ${#new_strings[@]} 個字串資源"
    echo "   - 支援 ${#languages[@]} 種語言"
    echo "   - 移除硬編碼中文字串"
    echo "   - 代碼編譯成功"
    echo
    echo "📱 用戶現在可以："
    echo "   - 切換到任何支援的語言"
    echo "   - 看到完全本地化的時間顯示（如 '5小時後'、'工作日'、'週末'）"
    echo "   - 享受完全翻譯的歡迎導覽頁面"
    echo "   - 使用本地化的星期幾顯示"
else
    echo "❌ 測試未完全通過"
    if [ $missing_strings -gt 0 ]; then
        echo "   - 缺少 $missing_strings 個字串翻譯"
    fi
    if [ $hardcoded_found -gt 0 ]; then
        echo "   - 發現 $hardcoded_found 行可能的硬編碼字串"
    fi
    echo
    echo "請檢查上述問題並修復。"
fi

echo
echo "=== 手動測試建議 ==="
echo "1. 啟動應用程式"
echo "2. 進入語言設定頁面"
echo "3. 切換到不同語言（English、日本語、한국어）"
echo "4. 檢查以下項目的翻譯："
echo "   - 首頁時間顯示（如 '5小時後'）"
echo "   - 排程重複模式（如 '工作日'、'週末'）"
echo "   - 歡迎導覽頁面內容"
echo "   - 星期幾顯示"
echo "5. 確認所有文字都已正確翻譯"

exit 0
