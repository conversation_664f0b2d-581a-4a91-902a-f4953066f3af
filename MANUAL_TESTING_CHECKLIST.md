# URL 排程功能手動測試檢查清單

## 測試前準備
- [ ] 確保應用程式已安裝最新版本
- [ ] 確保設備有網路連接
- [ ] 確保設備有瀏覽器應用程式
- [ ] 清除應用程式數據（如需要全新測試）

## 1. 基本功能測試

### 1.1 排程類型選擇
- [ ] 開啟新增排程頁面
- [ ] 確認預設選擇為 "啟動應用程式"
- [ ] 點擊 "開啟網址" 按鈕
- [ ] 確認 UI 切換到 URL 輸入界面
- [ ] 確認應用程式選擇界面隱藏
- [ ] 切換回 "啟動應用程式"
- [ ] 確認 UI 切換回應用程式選擇界面

### 1.2 URL 輸入驗證
- [ ] 在 URL 輸入框輸入無效 URL："not-a-url"
- [ ] 確認顯示錯誤狀態
- [ ] 確認預覽按鈕被禁用
- [ ] 清除並輸入有效 URL："https://www.google.com"
- [ ] 確認顯示成功狀態
- [ ] 確認預覽按鈕被啟用

### 1.3 URL 自動補全
- [ ] 輸入不含協議的 URL："www.google.com"
- [ ] 點擊其他區域失去焦點
- [ ] 確認自動添加 "https://" 前綴

### 1.4 URL 預覽功能
- [ ] 輸入有效 URL："https://www.google.com"
- [ ] 點擊預覽按鈕
- [ ] 確認瀏覽器開啟並顯示正確網頁

## 2. 排程創建測試

### 2.1 創建基本 URL 排程
- [ ] 選擇 "開啟網址" 類型
- [ ] 輸入 URL："https://www.google.com"
- [ ] 輸入標題："Google 搜尋"
- [ ] 設定時間為當前時間後 2 分鐘
- [ ] 選擇 "單次" 重複模式
- [ ] 點擊儲存
- [ ] 確認返回主頁面
- [ ] 確認排程出現在列表中

### 2.2 創建重複 URL 排程
- [ ] 創建新的 URL 排程
- [ ] 設定為每日重複
- [ ] 確認保存成功
- [ ] 檢查排程列表顯示

### 2.3 創建週期性 URL 排程
- [ ] 創建新的 URL 排程
- [ ] 選擇每週重複
- [ ] 選擇特定星期幾
- [ ] 確認保存成功

## 3. 排程顯示測試

### 3.1 列表顯示
- [ ] 確認 URL 排程顯示網址圖標
- [ ] 確認顯示正確的標題
- [ ] 確認副標題顯示 URL 地址
- [ ] 確認時間顯示正確
- [ ] 確認重複模式顯示正確

### 3.2 混合排程顯示
- [ ] 創建一個應用程式排程
- [ ] 創建一個 URL 排程
- [ ] 確認兩種類型在列表中正確顯示
- [ ] 確認圖標區別明顯

## 4. 排程編輯測試

### 4.1 編輯 URL 排程
- [ ] 點擊現有 URL 排程
- [ ] 確認正確載入排程資料
- [ ] 確認 URL 類型被選中
- [ ] 確認 URL 和標題正確顯示
- [ ] 修改 URL 為："https://www.example.com"
- [ ] 修改標題為："Example 網站"
- [ ] 保存變更
- [ ] 確認變更生效

### 4.2 類型轉換測試
- [ ] 編輯現有 URL 排程
- [ ] 嘗試切換到應用程式類型
- [ ] 確認 UI 正確切換
- [ ] 選擇應用程式
- [ ] 保存變更
- [ ] 確認排程類型已變更

## 5. 排程執行測試

### 5.1 URL 排程執行
- [ ] 創建一個即將觸發的 URL 排程（1-2分鐘後）
- [ ] 等待排程觸發時間
- [ ] 確認瀏覽器自動開啟
- [ ] 確認開啟正確的 URL
- [ ] 檢查排程狀態更新

### 5.2 錯誤處理測試
- [ ] 創建包含無效 URL 的排程
- [ ] 等待執行時間
- [ ] 確認顯示錯誤通知
- [ ] 確認排程被自動禁用

## 6. 數據持久性測試

### 6.1 應用重啟測試
- [ ] 創建多個 URL 排程
- [ ] 強制關閉應用程式
- [ ] 重新開啟應用程式
- [ ] 確認所有排程仍然存在
- [ ] 確認排程資料完整

### 6.2 系統重啟測試
- [ ] 創建 URL 排程
- [ ] 重啟設備
- [ ] 開啟應用程式
- [ ] 確認排程仍然存在
- [ ] 確認鬧鐘仍然有效

## 7. 錯誤情況測試

### 7.1 網路錯誤
- [ ] 關閉網路連接
- [ ] 觸發 URL 排程
- [ ] 確認適當的錯誤處理

### 7.2 無瀏覽器應用
- [ ] 禁用所有瀏覽器應用（如可能）
- [ ] 觸發 URL 排程
- [ ] 確認適當的錯誤處理

### 7.3 權限問題
- [ ] 撤銷相關權限
- [ ] 嘗試執行 URL 排程
- [ ] 確認適當的錯誤處理

## 8. 性能測試

### 8.1 大量排程測試
- [ ] 創建 20+ 個 URL 排程
- [ ] 確認列表滾動流暢
- [ ] 確認應用程式響應正常

### 8.2 長 URL 測試
- [ ] 輸入非常長的 URL（200+ 字符）
- [ ] 確認 UI 正確處理
- [ ] 確認保存和顯示正常

## 9. 用戶體驗測試

### 9.1 易用性測試
- [ ] 新用戶能否直觀理解如何創建 URL 排程
- [ ] UI 元素是否清晰易懂
- [ ] 錯誤訊息是否有幫助

### 9.2 一致性測試
- [ ] URL 排程與應用排程的操作是否一致
- [ ] UI 風格是否統一
- [ ] 行為模式是否一致

## 10. 兼容性測試

### 10.1 不同瀏覽器測試
- [ ] 使用 Chrome 瀏覽器
- [ ] 使用 Firefox 瀏覽器
- [ ] 使用系統預設瀏覽器
- [ ] 確認都能正確開啟 URL

### 10.2 不同 URL 類型測試
- [ ] HTTP URL
- [ ] HTTPS URL
- [ ] 帶查詢參數的 URL
- [ ] 帶錨點的 URL
- [ ] 帶端口號的 URL

## 測試結果記錄

### 通過的測試項目
- [ ] 記錄所有通過的測試項目

### 失敗的測試項目
- [ ] 記錄失敗的測試項目
- [ ] 描述失敗原因
- [ ] 記錄重現步驟

### 發現的問題
- [ ] 列出所有發現的 bug
- [ ] 評估問題嚴重程度
- [ ] 提供修復建議

## 測試完成標準
- [ ] 所有核心功能測試通過
- [ ] 無嚴重或阻塞性問題
- [ ] 用戶體驗良好
- [ ] 性能符合預期
- [ ] 與現有功能無衝突
