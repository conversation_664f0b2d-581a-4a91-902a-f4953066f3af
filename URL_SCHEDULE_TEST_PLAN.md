# URL 排程功能測試計劃

## 概述
本文檔描述了 AutoLaunch 應用程式中新增的 URL 排程功能的全面測試計劃。

## 測試範圍

### 1. 數據模型測試 (Schedule.kt)
- [x] ScheduleType 枚舉功能
- [x] URL 排程創建和驗證
- [x] APP 排程向後兼容性
- [x] 顯示名稱邏輯
- [x] 數據驗證邏輯
- [x] 複製功能

### 2. URL 輸入驗證測試
- [x] 有效 URL 格式驗證
- [x] 無效 URL 格式處理
- [x] 協議支持 (HTTP, HTTPS, FTP)
- [x] 邊界情況處理
- [x] 大小寫不敏感驗證

### 3. UI 組件測試

#### 3.1 排程創建界面 (AddEditScheduleActivity)
**手動測試項目：**
- [ ] 排程類型選擇器顯示正確
- [ ] 選擇 "啟動應用程式" 顯示應用選擇界面
- [ ] 選擇 "開啟網址" 顯示 URL 輸入界面
- [ ] URL 輸入框驗證功能正常
- [ ] URL 預覽按鈕功能正常
- [ ] 保存 URL 排程成功
- [ ] 編輯現有 URL 排程正常載入

#### 3.2 排程列表顯示 (ScheduleAdapter)
**手動測試項目：**
- [ ] URL 排程顯示正確圖標
- [ ] URL 排程顯示正確標題和副標題
- [ ] APP 排程顯示不受影響
- [ ] 混合排程列表顯示正常

### 4. 執行邏輯測試

#### 4.1 LaunchReceiver 測試
**手動測試項目：**
- [ ] URL 排程觸發時正確開啟瀏覽器
- [ ] 無效 URL 處理正常
- [ ] 錯誤通知顯示正確
- [ ] APP 排程執行不受影響

#### 4.2 AlarmManagerService 測試
**手動測試項目：**
- [ ] URL 排程鬧鐘設定正確
- [ ] URL 排程鬧鐘取消正確
- [ ] 混合類型排程管理正常

### 5. 數據庫測試

#### 5.1 數據庫遷移測試
**手動測試項目：**
- [ ] 從版本 2 升級到版本 3 成功
- [ ] 現有 APP 排程數據保持完整
- [ ] 新欄位正確添加
- [ ] 數據類型約束正確

#### 5.2 CRUD 操作測試
**手動測試項目：**
- [ ] 創建 URL 排程
- [ ] 讀取 URL 排程
- [ ] 更新 URL 排程
- [ ] 刪除 URL 排程
- [ ] 查詢不同類型排程

### 6. 整合測試

#### 6.1 端到端測試場景
**測試場景 1：創建 URL 排程**
1. 開啟應用程式
2. 點擊新增排程
3. 選擇 "開啟網址"
4. 輸入有效 URL
5. 設定時間和重複模式
6. 保存排程
7. 驗證排程出現在列表中

**測試場景 2：執行 URL 排程**
1. 創建一個即將觸發的 URL 排程
2. 等待排程觸發時間
3. 驗證瀏覽器開啟正確 URL
4. 檢查排程狀態更新

**測試場景 3：編輯 URL 排程**
1. 點擊現有 URL 排程
2. 修改 URL 或其他設定
3. 保存變更
4. 驗證變更生效

**測試場景 4：混合排程管理**
1. 創建多個 APP 和 URL 排程
2. 驗證列表顯示正確
3. 測試啟用/禁用功能
4. 測試刪除功能

### 7. 錯誤處理測試

#### 7.1 網路錯誤
- [ ] 無網路連接時 URL 開啟處理
- [ ] DNS 解析失敗處理
- [ ] 伺服器無回應處理

#### 7.2 輸入錯誤
- [ ] 空 URL 輸入處理
- [ ] 無效 URL 格式處理
- [ ] 超長 URL 處理

#### 7.3 系統錯誤
- [ ] 無瀏覽器應用時的處理
- [ ] 權限不足時的處理
- [ ] 記憶體不足時的處理

### 8. 性能測試
- [ ] 大量 URL 排程的性能
- [ ] 數據庫查詢性能
- [ ] UI 響應性能

### 9. 兼容性測試
- [ ] 不同 Android 版本兼容性
- [ ] 不同瀏覽器兼容性
- [ ] 不同螢幕尺寸適配

### 10. 安全性測試
- [ ] 惡意 URL 處理
- [ ] JavaScript URL 阻止
- [ ] 本地文件 URL 阻止

## 測試執行指南

### 自動化測試
```bash
# 執行單元測試
./gradlew test

# 執行特定測試類
./gradlew test --tests "ScheduleTest"
./gradlew test --tests "UrlValidationTest"
```

### 手動測試
1. 安裝應用程式到測試設備
2. 按照測試場景逐一執行
3. 記錄測試結果
4. 報告發現的問題

## 測試數據

### 測試 URL 列表
```
有效 URL：
- https://www.google.com
- http://example.org
- https://github.com/user/repo
- https://api.example.com/v1/data

無效 URL：
- not-a-url
- www.google.com (缺少協議)
- javascript:alert('test') (不安全協議)
- file:///etc/passwd (本地文件)
```

## 驗收標準
- [ ] 所有自動化測試通過
- [ ] 所有手動測試場景通過
- [ ] 無嚴重或阻塞性缺陷
- [ ] 性能符合要求
- [ ] 用戶體驗良好

## 風險評估
- **高風險**：數據庫遷移失敗
- **中風險**：URL 執行失敗
- **低風險**：UI 顯示問題

## 測試環境
- Android 版本：6.0 - 14.0
- 測試設備：實體設備和模擬器
- 瀏覽器：Chrome, Firefox, Samsung Internet
