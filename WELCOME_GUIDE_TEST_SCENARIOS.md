# Welcome Guide Test Scenarios

## Overview
This document outlines the test scenarios for the App Welcome Guide (Onboarding) feature.

## Test Scenarios

### 1. First Launch Detection
**Scenario**: User launches the app for the first time
**Expected Behavior**: 
- App should detect it's the first launch
- WelcomeActivity should be displayed instead of MainActivity
- User should see the multi-page carousel/walkthrough

**Test Steps**:
1. Clear app data or uninstall/reinstall the app
2. Launch the app
3. Verify WelcomeActivity is shown
4. Verify the welcome guide pages are displayed correctly

### 2. Welcome Guide Navigation
**Scenario**: User navigates through the welcome guide
**Expected Behavior**:
- User can swipe left/right to navigate between pages
- Page indicators show current position
- Navigation buttons (Previous/Next) work correctly
- "Skip" button is available on all pages
- "Get Started" button appears only on the last page

**Test Steps**:
1. Launch the welcome guide
2. Test swiping between pages
3. Test Previous/Next buttons
4. Verify page indicators update correctly
5. Test Skip button functionality
6. Test Get Started button on final page

### 3. Welcome Guide Completion
**Scenario**: User completes the welcome guide
**Expected Behavior**:
- After clicking "Get Started" or "Skip", MainActivity should be launched
- First launch flag should be set to false
- Subsequent app launches should go directly to MainActivity

**Test Steps**:
1. Complete the welcome guide by clicking "Get Started"
2. Verify MainActivity is launched
3. Close and reopen the app
4. Verify MainActivity is shown directly (no welcome guide)

### 4. Re-enable Welcome Guide
**Scenario**: User wants to see the welcome guide again
**Expected Behavior**:
- User can access Settings page
- "Show Welcome Guide" button is available
- Clicking the button re-enables the welcome guide
- Next app launch or immediate launch shows the welcome guide

**Test Steps**:
1. Open Settings page (通知設定)
2. Find and click "顯示歡迎導覽" button
3. Verify WelcomeActivity is launched immediately
4. Complete the guide and verify normal functionality

### 5. Welcome Guide Content
**Scenario**: Verify welcome guide content is correct
**Expected Behavior**:
- All 5 pages display correct content
- Icons, titles, and descriptions are appropriate
- Colors and styling match app theme
- Text is properly localized

**Test Pages**:
1. Page 1: Welcome to AutoLaunch (rocket icon)
2. Page 2: Schedule Apps (apps icon)
3. Page 3: Open Web Links (web icon)
4. Page 4: Permission Settings (security icon)
5. Page 5: Get Started (play arrow icon)

## Implementation Status
✅ First launch detection implemented
✅ Multi-page carousel with ViewPager2
✅ Navigation buttons and page indicators
✅ Settings integration for re-enabling
✅ Proper theme integration
✅ All required pages and content

## Notes
- The welcome guide uses a portrait-only orientation
- Back button is disabled during the welcome guide
- The guide integrates with the existing theme system
- All strings are properly localized in Chinese
