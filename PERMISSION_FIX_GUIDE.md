# 本地備份權限問題修復指南

## 🔧 問題解決

已修復「本地備份按鈕無法點擊，顯示本地備份需要儲存權限」的問題。

## ✅ 修復內容

### 1. 權限檢查邏輯優化
- **Android 13+ (API 33+)**：不再要求特殊權限，可直接使用應用專用目錄
- **Android 6-12 (API 23-32)**：只檢查 `WRITE_EXTERNAL_STORAGE` 權限
- **Android 6 以下**：自動擁有權限

### 2. 文件存儲路徑調整
- **Android 10+ (API 29+)**：使用應用專用外部目錄 `context.getExternalFilesDir()`
- **Android 9 及以下**：使用公共 Downloads 目錄

### 3. 用戶體驗改善
- 按鈕狀態智能更新
- 權限請求後自動執行備份
- 清晰的狀態提示信息

## 📱 現在的使用體驗

### Android 13+ 用戶
- ✅ **立即可用**：本地備份按鈕直接可點擊
- ✅ **無需權限**：不會彈出權限請求對話框
- ✅ **自動保存**：備份文件保存到應用專用目錄

### Android 6-12 用戶
- ✅ **一次授權**：首次使用時請求存儲權限
- ✅ **自動執行**：授權後立即執行備份操作
- ✅ **記住選擇**：授權後後續使用無需再次請求

### Android 6 以下用戶
- ✅ **直接使用**：無需任何權限設定

## 🔍 技術細節

### 權限檢查邏輯
```kotlin
fun hasStoragePermission(context: Context): Boolean {
    return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
        // Android 13+ 對於應用專用目錄通常不需要特殊權限
        true
    } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
        // Android 6-12 檢查傳統存儲權限
        ContextCompat.checkSelfPermission(context, Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED
    } else {
        // Android 6 以下自動擁有權限
        true
    }
}
```

### 文件存儲路徑
```kotlin
private fun getBackupDirectory(): File {
    return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
        // Android 10+ 使用應用專用目錄
        File(context.getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS), BACKUP_DIRECTORY)
    } else {
        // Android 9 及以下使用公共 Downloads 目錄
        File(
            Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS),
            BACKUP_DIRECTORY
        )
    }
}
```

## 📂 備份文件位置

### Android 10+ (API 29+)
```
/Android/data/com.example.autolaunch/files/Download/AutoLaunch/
```

### Android 9 及以下
```
/storage/emulated/0/Download/AutoLaunch/
```

## 🎯 使用步驟（修復後）

1. **打開應用**：啟動 AutoLaunch
2. **進入備份頁面**：側邊欄 → 「備份與匯入」
3. **點擊本地備份**：
   - **Android 13+**：直接創建備份
   - **Android 6-12**：首次使用會請求權限，授權後自動執行
   - **Android 6 以下**：直接創建備份
4. **查看結果**：顯示備份成功訊息和文件信息

## ⚠️ 注意事項

### 對於 Android 6-12 用戶
- 首次使用時會彈出存儲權限請求
- 建議選擇「允許」以正常使用備份功能
- 如果拒絕權限，可以在系統設定中手動開啟

### 對於所有用戶
- 備份文件會自動管理，保留最新 10 個
- 可以通過「本地備份」管理按鈕查看所有備份
- 支援從任何備份文件恢復數據

## 🔧 故障排除

### 如果仍然無法使用
1. **重新啟動應用**：完全關閉後重新開啟
2. **檢查存儲空間**：確保設備有足夠空間
3. **手動授權權限**：
   - 進入系統設定 → 應用程式 → AutoLaunch → 權限
   - 開啟「存儲」權限（僅 Android 6-12）

### 如果找不到備份文件
- **Android 10+**：使用文件管理器查看應用專用目錄
- **Android 9 及以下**：在 Downloads/AutoLaunch 目錄查看

## ✨ 總結

修復後的本地備份功能：
- 🚀 **更智能**：根據 Android 版本自動調整權限策略
- 🎯 **更簡單**：大多數用戶可以直接使用，無需權限設定
- 🔒 **更安全**：使用現代 Android 存儲最佳實踐
- 💡 **更友好**：清晰的狀態提示和自動化操作

**現在您可以輕鬆使用本地備份功能來保護您的排程設定！**
