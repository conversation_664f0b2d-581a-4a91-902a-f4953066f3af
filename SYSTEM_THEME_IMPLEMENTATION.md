# AutoLaunch 系統主題實現

## 實現概述

根據用戶需求，重新設計了主題系統，移除了獨立的「跟隨系統主題」選項，改為在對應的經典主題上顯示「系統主題」標籤。

## 設計邏輯

### 系統主題映射
- **系統深色模式** → 自動選中「經典深色」並顯示「系統主題」標籤
- **系統淺色模式** → 自動選中「經典淺色」並顯示「系統主題」標籤

### 用戶體驗
- 用戶看到的是具體的主題（經典淺色/經典深色）
- 當啟用跟隨系統時，對應主題會顯示「系統主題」標籤
- 界面簡潔，無需額外的系統主題選項

## 技術實現

### 1. ThemeType 枚舉簡化
```kotlin
enum class ThemeType(
    val id: String,
    val displayName: String,
    val description: String,
    val isDarkMode: Boolean
) {
    // 移除了 FOLLOW_SYSTEM 枚舉值
    // 移除了 isSystemTheme 屬性
    LIGHT_CLASSIC("light_classic", "經典淺色", "溫和舒適的淺色主題", false),
    DARK_CLASSIC("dark_classic", "經典深色", "經典優雅的深色主題", true),
    // ... 其他主題
}
```

### 2. ThemeManager 邏輯更新

#### getCurrentTheme() 方法
```kotlin
fun getCurrentTheme(): ThemeType {
    // 如果設定為跟隨系統，返回對應的經典主題
    if (isFollowingSystem()) {
        val isSystemDarkMode = isSystemInDarkMode()
        return if (isSystemDarkMode) ThemeType.DARK_CLASSIC else ThemeType.LIGHT_CLASSIC
    }
    
    // 其他邏輯保持不變
}
```

#### 新增 isSystemTheme() 方法
```kotlin
fun isSystemTheme(themeType: ThemeType): Boolean {
    if (!isFollowingSystem()) return false
    
    val isSystemDarkMode = isSystemInDarkMode()
    return if (isSystemDarkMode) {
        themeType == ThemeType.DARK_CLASSIC
    } else {
        themeType == ThemeType.LIGHT_CLASSIC
    }
}
```

### 3. UI 布局更新

#### item_theme.xml
新增系統主題標籤：
```xml
<!-- 系統主題標籤 -->
<com.google.android.material.textview.MaterialTextView
    android:id="@+id/tvSystemThemeLabel"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginTop="2dp"
    android:text="系統主題"
    android:textAppearance="@style/TextAppearance.Material3.LabelSmall"
    android:textColor="?attr/colorPrimary"
    android:textStyle="bold"
    android:visibility="gone" />
```

### 4. ThemeAdapter 更新

#### 系統主題標籤顯示邏輯
```kotlin
fun bind(theme: ThemeType, currentTheme: ThemeType, onThemeClick: (ThemeType) -> Unit) {
    // 檢查是否為系統主題並顯示標籤
    val context = binding.root.context
    val themeManager = ThemeManager.getInstance(context)
    val isSystemTheme = themeManager.isSystemTheme(theme)
    binding.tvSystemThemeLabel.visibility = if (isSystemTheme) View.VISIBLE else View.GONE
    
    // 其他邏輯保持不變
}
```

## 用戶界面效果

### 跟隨系統關閉時
```
淺色主題
├── 經典淺色
├── 溫暖淺色
└── 清涼淺色

深色主題
├── 經典深色
├── 深藍主題
├── 深綠主題
├── 深紫主題
└── 深橙主題
```

### 跟隨系統開啟（系統為深色模式）時
```
淺色主題
├── 經典淺色
├── 溫暖淺色
└── 清涼淺色

深色主題
├── 經典深色 ✓ 系統主題  ← 自動選中並顯示標籤
├── 深藍主題
├── 深綠主題
├── 深紫主題
└── 深橙主題
```

### 跟隨系統開啟（系統為淺色模式）時
```
淺色主題
├── 經典淺色 ✓ 系統主題  ← 自動選中並顯示標籤
├── 溫暖淺色
└── 清涼淺色

深色主題
├── 經典深色
├── 深藍主題
├── 深綠主題
├── 深紫主題
└── 深橙主題
```

## 優勢

### 1. 界面簡潔
- 移除了複雜的系統主題選項
- 用戶直接看到具體的主題效果
- 減少了認知負擔

### 2. 邏輯清晰
- 系統主題就是經典主題的別名
- 標籤清楚指示當前狀態
- 無需額外的主題類型

### 3. 實現簡單
- 代碼邏輯更清晰
- 減少了特殊情況處理
- 易於維護和擴展

## 兼容性

### 1. 功能兼容
- 跟隨系統功能完全保留
- 主題切換邏輯不變
- 用戶設定自動遷移

### 2. 數據兼容
- 現有用戶設定完全兼容
- 無需數據遷移
- 平滑升級體驗

## 測試結果

- ✅ 編譯成功
- ✅ 單元測試通過（7個測試案例）
- ✅ 系統主題標籤正確顯示
- ✅ 主題切換功能正常
- ✅ 跟隨系統功能正常

## 文件變更清單

### 主分支和 w-feature-adjust-schedule-naming 分支
- `ThemeType.kt` - 移除 FOLLOW_SYSTEM 枚舉
- `ThemeManager.kt` - 更新主題獲取邏輯，新增 isSystemTheme 方法
- `ThemeAdapter.kt` - 添加系統主題標籤顯示邏輯
- `item_theme.xml` - 新增系統主題標籤 UI 元素
- `ThemeTest.kt` - 更新測試案例

## 總結

此次實現成功簡化了主題系統，移除了獨立的系統主題選項，改為在對應的經典主題上顯示標籤。新設計更加直觀，用戶可以清楚看到系統主題對應的具體效果，同時保持了完整的跟隨系統功能。

實現邏輯簡潔明了，代碼質量得到提升，為未來的功能擴展奠定了良好的基礎。
