# Bug 修復報告

## 問題描述
在 `AddEditScheduleActivity.kt` 文件中發現編譯錯誤：
```
e: file:///Users/<USER>/Desktop/github/Android%20AutoLaunch/app/src/main/java/com/example/autolaunch/AddEditScheduleActivity.kt:595:40 Unresolved reference 'visibility'.
```

## 錯誤原因
問題出現在 `updateScheduleTypeUI()` 方法中，嘗試直接訪問 `include` 標籤的 `visibility` 屬性：

```kotlin
// 錯誤的代碼
binding.layoutUrlInput.visibility = View.GONE
```

在 Android 中，當使用 `<include>` 標籤包含佈局時，需要通過 `.root` 屬性來訪問包含佈局的根視圖。

## 修復方案
將直接訪問 `visibility` 改為通過 `root` 屬性訪問：

### 修復前：
```kotlin
private fun updateScheduleTypeUI() {
    when (selectedScheduleType) {
        ScheduleType.APP -> {
            binding.cardAppSelectionContainer.visibility = View.VISIBLE
            binding.layoutUrlInput.visibility = View.GONE  // ❌ 錯誤
        }
        ScheduleType.URL -> {
            binding.cardAppSelectionContainer.visibility = View.GONE
            binding.layoutUrlInput.visibility = View.VISIBLE  // ❌ 錯誤
        }
    }
}
```

### 修復後：
```kotlin
private fun updateScheduleTypeUI() {
    when (selectedScheduleType) {
        ScheduleType.APP -> {
            binding.cardAppSelectionContainer.visibility = View.VISIBLE
            binding.layoutUrlInput.root.visibility = View.GONE  // ✅ 正確
        }
        ScheduleType.URL -> {
            binding.cardAppSelectionContainer.visibility = View.GONE
            binding.layoutUrlInput.root.visibility = View.VISIBLE  // ✅ 正確
        }
    }
}
```

## 技術說明
在 Android 的 Data Binding 中：
- 對於普通的 View，可以直接訪問其屬性
- 對於 `<include>` 標籤，binding 對象代表的是包含的佈局的 binding，需要通過 `.root` 來訪問根視圖
- 這是因為 `<include>` 標籤本身不是一個 View，而是一個佈局包含指令

## 驗證結果
✅ 編譯錯誤已解決
✅ 所有相關文件編譯通過
✅ 功能邏輯保持不變
✅ 無其他副作用

## 相關文件
- `app/src/main/java/com/example/autolaunch/AddEditScheduleActivity.kt` (已修復)
- `app/src/main/res/layout/activity_add_edit_schedule.xml` (包含 include 標籤)
- `app/src/main/res/layout/layout_url_input.xml` (被包含的佈局)

## 預防措施
在未來開發中，使用 `<include>` 標籤時需要注意：
1. 訪問包含佈局的根視圖時使用 `.root`
2. 訪問包含佈局內部的 View 時直接使用 binding 對象
3. 在 IDE 中注意自動完成提示，避免類似錯誤

## 狀態
🟢 **已修復** - 所有編譯錯誤已解決，功能正常運作
