# PRD - 雲端備份時間顯示相對時間功能

**建立時間:** 2025-07-02 12:12:19

## 需求討論

本次實作為 Android AutoLaunch 專案的雲端備份功能添加了相對時間顯示，讓用戶能夠更直觀地了解備份文件的創建時間。

### 需求描述
用戶希望在雲端備份文件列表中，除了顯示絕對時間（如 2025-07-02 12:03:49）外，還能在時間後方用括號顯示相對時間（如 "3小時前"、"2天前"），讓用戶更直觀地感受上次備份距離現在多久。

### 功能目標
- 在雲端備份文件時間顯示中添加相對時間
- 格式：`yyyy-MM-dd HH:mm:ss (N小時前)`
- 支援多種時間單位：剛剛、N分鐘前、N小時前、N天前、N週前
- 支援多語言國際化（中文、英文、日文、韓文）

## 技術實作細節

### 1. 修改 DriveFileInfo 類
在 `app/src/main/java/com/example/autolaunch/utils/GoogleDriveManager.kt` 中：

```kotlin
/**
 * 獲取格式化的創建時間，包含相對時間
 * 格式：yyyy-MM-dd HH:mm:ss (N小時前)
 */
fun getFormattedCreatedTimeWithRelative(context: android.content.Context): String {
    val date = Date(createdTime)
    val formattedTime = android.text.format.DateFormat.format("yyyy-MM-dd HH:mm:ss", date).toString()
    val relativeTime = getRelativeTimeString(context, createdTime)
    return "$formattedTime ($relativeTime)"
}

/**
 * 獲取相對時間字符串
 */
private fun getRelativeTimeString(context: android.content.Context, timestamp: Long): String {
    val now = System.currentTimeMillis()
    val diff = now - timestamp
    
    return when {
        diff < DateUtils.MINUTE_IN_MILLIS -> context.getString(R.string.time_just_now)
        diff < DateUtils.HOUR_IN_MILLIS -> {
            val minutes = diff / DateUtils.MINUTE_IN_MILLIS
            context.getString(R.string.time_minutes_ago, minutes)
        }
        diff < DateUtils.DAY_IN_MILLIS -> {
            val hours = diff / DateUtils.HOUR_IN_MILLIS
            if (hours == 1L) context.getString(R.string.time_hour_ago)
            else context.getString(R.string.time_hours_ago, hours)
        }
        diff < 7 * DateUtils.DAY_IN_MILLIS -> {
            val days = diff / DateUtils.DAY_IN_MILLIS
            context.getString(R.string.time_days_ago, days)
        }
        else -> {
            val weeks = diff / (7 * DateUtils.DAY_IN_MILLIS)
            if (weeks == 1L) context.getString(R.string.time_week_ago)
            else context.getString(R.string.time_weeks_ago, weeks)
        }
    }
}
```

### 2. 更新 CloudBackupFileAdapter
在 `app/src/main/java/com/example/autolaunch/dialog/BackupFileListDialog.kt` 中：

```kotlin
override fun onBindViewHolder(holder: ViewHolder, position: Int) {
    val file = files[position]
    
    holder.tvFileName.text = file.name
    holder.tvFileInfo.text = "雲端備份文件"
    holder.tvFileSize.text = file.getFormattedSize()
    // 使用新的方法顯示帶有相對時間的格式化時間
    holder.tvCreatedTime.text = file.getFormattedCreatedTimeWithRelative(holder.itemView.context)
    
    // ... 其他代碼
}
```

### 3. 添加字串資源
為所有語言版本添加缺失的字串資源：

**中文 (values/strings.xml):**
```xml
<string name="time_week_ago">1週前</string>
<string name="time_weeks_ago">%d週前</string>
```

**英文 (values-en/strings.xml):**
```xml
<string name="time_week_ago">1 week ago</string>
<string name="time_weeks_ago">%d weeks ago</string>
```

**日文 (values-ja/strings.xml):**
```xml
<string name="time_week_ago">1週間前</string>
<string name="time_weeks_ago">%d週間前</string>
```

**韓文 (values-ko/strings.xml):**
```xml
<string name="time_week_ago">1주 전</string>
<string name="time_weeks_ago">%d주 전</string>
```

## 功能特色

### 1. 智能時間顯示
- **剛剛**: 1分鐘內
- **N分鐘前**: 1小時內
- **N小時前**: 24小時內
- **N天前**: 7天內
- **N週前**: 7天以上

### 2. 多語言支援
完整支援中文、英文、日文、韓文的相對時間顯示，確保國際化用戶體驗。

### 3. 用戶體驗優化
- 保留原有的絕對時間顯示，確保精確性
- 添加括號內的相對時間，提升直觀性
- 格式統一：`2025-07-02 12:03:49 (3小時前)`

## 測試驗證

用戶可以通過以下步驟驗證功能：

1. 進入備份與恢復頁面
2. 切換到雲端備份分頁
3. 點擊「從雲端匯入」按鈕
4. 查看備份文件列表中的時間顯示
5. 確認時間格式為：`yyyy-MM-dd HH:mm:ss (相對時間)`

## 實作影響

### 正面影響
- 提升用戶體驗，讓用戶更直觀地了解備份時間
- 保持向後兼容性，不影響現有功能
- 利用現有的時間格式化基礎設施，代碼複用性高

### 技術債務
- 無新增技術債務
- 利用專案現有的 `TimeFormatUtils` 設計模式
- 遵循現有的國際化實現規範

## 後續優化建議

1. **本地備份時間顯示**: 考慮為本地備份文件也添加相對時間顯示
2. **時間刷新**: 考慮實現時間的動態刷新，讓相對時間保持最新
3. **用戶設置**: 未來可考慮添加用戶設置，讓用戶選擇顯示格式偏好

## 總結

本次實作成功為雲端備份功能添加了相對時間顯示，提升了用戶體驗。實作過程中充分利用了專案現有的時間格式化基礎設施和國際化框架，確保了代碼的一致性和可維護性。功能實現後，用戶能夠更直觀地了解備份文件的創建時間，改善了整體的用戶體驗。 