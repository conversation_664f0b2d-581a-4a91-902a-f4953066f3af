# PRD-2025-06-02 23:31:32-主介面排程列表UI實作

## 需求概要
實作 TASK_004: 主介面排程列表 UI，建立應用程式的主介面使用者介面，以列表形式顯示所有已建立的排程，並提供新增排程的入口。

## 需求討論過程

### 1. 任務分析
- **目標**: 設計並實現應用程式主介面 UI
- **核心功能**:
  - 顯示排程列表 (RecyclerView)
  - 新增排程按鈕 (FloatingActionButton)
  - 排程項目詳細資訊顯示
  - 啟用/禁用排程開關

### 2. 技術實作決策

#### 2.1 UI架構選擇
- **佈局方式**: 使用傳統 XML 佈局 (而非 Jetpack Compose)
- **主要元件**:
  - `ConstraintLayout` 作為根佈局
  - `RecyclerView` 顯示排程列表
  - `ExtendedFloatingActionButton` 新增排程
  - `MaterialCardView` 顯示歡迎訊息

#### 2.2 排程列表項目設計
- **佈局檔案**: `item_schedule.xml`
- **包含元素**:
  - App 圖標 (48dp × 48dp)
  - App 名稱 (粗體顯示)
  - 排程時間 (HH:mm 格式)
  - 重複模式說明
  - 啟用/禁用 MaterialSwitch

#### 2.3 資料處理
- **Adapter**: `ScheduleAdapter` 繼承 `ListAdapter`
- **DiffUtil**: 實作 `ScheduleDiffCallback` 優化列表更新
- **資料模型**: 使用現有的 `Schedule` 資料類別

### 3. 實作詳細

#### 3.1 檔案結構
```
app/src/main/res/layout/
├── activity_main.xml          # 主介面佈局
└── item_schedule.xml          # 排程項目佈局

app/src/main/java/com/example/autolaunch/
├── MainActivity.kt            # 主Activity (已修改)
└── ScheduleAdapter.kt         # 新建的RecyclerView Adapter
```

#### 3.2 主介面佈局特點
- **響應式設計**: 根據是否有排程切換顯示狀態
  - 有排程: 顯示 `RecyclerView` 和列表標題
  - 無排程: 顯示歡迎卡片
- **Material Design**: 使用 Material 3 元件
- **無障礙友好**: 適當的文字大小和對比度

#### 3.3 排程項目顯示邏輯
- **App圖標載入**: 優先顯示實際應用圖標，失敗時使用預設圖標
- **重複模式顯示**:
  - 單次: "單次執行"
  - 每日: "每日重複"
  - 每週: 顯示具體星期幾，特殊情況自動識別 ("工作日", "週末", "每日")
  - 每月: "每月重複"

#### 3.4 互動功能
- **項目點擊**: 顯示Toast提示 (後續可擴展為編輯功能)
- **開關切換**: 即時回饋開關狀態變更
- **新增按鈕**: 準備接入新增排程流程

### 4. 測試資料
為了驗證UI效果，建立了三個樣本排程：
1. **Chrome** - 每日 08:00
2. **WhatsApp** - 週一到週五 09:30
3. **YouTube** - 週末 20:00 (預設禁用)

### 5. 技術細節

#### 5.1 關鍵程式碼片段
```kotlin
// RecyclerView 設置
private fun setupRecyclerView() {
    scheduleAdapter = ScheduleAdapter(
        onItemClick = { schedule -> /* 處理點擊 */ },
        onToggleEnabled = { schedule, isEnabled -> /* 處理開關 */ }
    )
    binding.recyclerViewSchedules.apply {
        layoutManager = LinearLayoutManager(this@MainActivity)
        adapter = scheduleAdapter
    }
}
```

#### 5.2 狀態管理
```kotlin
private fun showScheduleList(schedules: List<Schedule>) {
    binding.apply {
        welcomeCard.visibility = View.GONE
        scheduleListTitle.visibility = View.VISIBLE
        recyclerViewSchedules.visibility = View.VISIBLE
    }
    scheduleAdapter.submitList(schedules)
}
```

### 6. 完成狀態

#### 6.1 已實現功能 ✅
- [x] 創建 `activity_main.xml` 主介面佈局
- [x] 實作 `RecyclerView` 顯示排程列表
- [x] 新增 `FloatingActionButton` 
- [x] 設計排程列表項目佈局 (`item_schedule.xml`)
- [x] 顯示App圖標、名稱、時間、重複模式、啟用開關
- [x] 建立 `ScheduleAdapter` 和相關邏輯
- [x] 實作空狀態和有資料狀態的切換

#### 6.2 技術債務 🔄
- [ ] 整合真實資料庫資料 (目前使用假資料)
- [ ] 實作開關狀態變更的資料庫更新
- [ ] 連結新增排程按鈕到實際新增流程
- [ ] 實作排程項目點擊編輯功能

### 7. 下一步規劃
1. **TASK_005**: 實作新增排程功能
2. **資料庫整合**: 將假資料替換為真實資料庫查詢
3. **編輯功能**: 點擊排程項目進入編輯模式
4. **狀態同步**: 開關狀態變更時更新資料庫

## 驗收標準檢查

### 原始接受標準對照:
1. ✅ 創建 `activity_main.xml` 實現主介面佈局
2. ✅ 介面包含 `RecyclerView` 用於顯示排程列表
3. ✅ 介面包含明顯的"新增排程"按鈕 (`ExtendedFloatingActionButton`)
4. ✅ 排程列表項目佈局完成，顯示所有要求的基本資訊:
   - App 圖標 ✅
   - App 名稱 ✅  
   - 設定的時間 ✅
   - 重複模式 ✅
   - 啟用/禁用開關 ✅

### 額外實現:
- ✅ Material Design 規範遵循
- ✅ 響應式空狀態處理
- ✅ 完整的互動回饋機制
- ✅ 程式碼架構良好，易於擴展

**結論**: TASK_004 已成功完成，所有接受標準均已達成，並且為後續功能開發建立了良好的基礎。 