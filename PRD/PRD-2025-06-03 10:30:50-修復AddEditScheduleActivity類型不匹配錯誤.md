# PRD - 修復 AddEditScheduleActivity 類型不匹配錯誤

**日期**: 2025-06-03 10:30:50  
**類型**: Bug 修復  
**優先級**: 高  
**狀態**: 已完成  

## 問題描述

在 Android AutoLaunch 專案的國際化工作完成後，編譯時出現類型不匹配錯誤：

```
e: file:///Users/<USER>/Downloads/Android%20AutoLaunch/app/src/main/java/com/example/autolaunch/AddEditScheduleActivity.kt:328:30 Assignment type mismatch: actual type is 'kotlin.Int', but 'com.example.autolaunch.model.RepeatMode' was expected.
```

## 根本原因分析

在 `AddEditScheduleActivity.kt` 的 `populateUIFromSchedule` 方法中，存在多個類型不匹配和屬性名稱錯誤：

1. **類型不匹配**: `schedule.repeatMode` 是 `Int` 類型，但 `selectedRepeatMode` 期望 `RepeatMode` 枚舉類型
2. **屬性名稱錯誤**: 
   - `schedule.executionDate` 應為 `schedule.singleExecuteDate`
   - `schedule.appPackage` 應為 `schedule.packageName`
3. **方法名稱錯誤**: DAO 方法 `insertSchedule` 和 `updateSchedule` 應為 `insert` 和 `update`
4. **枚舉轉換錯誤**: 在建立 Schedule 物件時，`selectedRepeatMode` 需要轉換為 `Int` 值

## 解決方案

### 1. 修復類型轉換問題

**檔案**: `AddEditScheduleActivity.kt`  
**位置**: `populateUIFromSchedule` 方法第 328 行

```kotlin
// 修復前
selectedRepeatMode = schedule.repeatMode

// 修復後  
selectedRepeatMode = schedule.getRepeatModeEnum()
```

### 2. 修復屬性名稱錯誤

**檔案**: `AddEditScheduleActivity.kt`  
**位置**: `populateUIFromSchedule` 方法

```kotlin
// 修復前
selectedSingleDate = schedule.executionDate
selectedAppPackage = schedule.appPackage

// 修復後
selectedSingleDate = schedule.singleExecuteDate
selectedAppPackage = schedule.packageName
```

### 3. 修復 Schedule 建構子參數

**檔案**: `AddEditScheduleActivity.kt`  
**位置**: `saveSchedule` 方法

```kotlin
// 修復前
val schedule = Schedule(
    // ...
    repeatMode = selectedRepeatMode,
    executionDate = if (selectedRepeatMode == RepeatMode.ONCE) selectedSingleDate else null,
    appPackage = selectedAppPackage ?: "",
    // ...
)

// 修復後
val schedule = Schedule(
    // ...
    repeatMode = selectedRepeatMode.value,
    singleExecuteDate = if (selectedRepeatMode == RepeatMode.ONCE) selectedSingleDate else null,
    packageName = selectedAppPackage ?: "",
    // ...
)
```

### 4. 修復 DAO 方法名稱

**檔案**: `AddEditScheduleActivity.kt`  
**位置**: `saveSchedule` 方法

```kotlin
// 修復前
scheduleDao.insertSchedule(schedule)
scheduleDao.updateSchedule(schedule)

// 修復後
scheduleDao.insert(schedule)
scheduleDao.update(schedule)
```

## 技術細節

### RepeatMode 枚舉轉換機制

`Schedule` 模型中的 `repeatMode` 欄位儲存為 `Int` 類型以便資料庫存取，但應用程式邏輯使用 `RepeatMode` 枚舉：

```kotlin
// RepeatMode.kt
enum class RepeatMode(val value: Int, val displayName: String) {
    ONCE(0, "單次"),
    DAILY(1, "每日"),
    WEEKLY(2, "每週"),
    MONTHLY(3, "每月");
    
    companion object {
        fun fromValue(value: Int): RepeatMode {
            return entries.find { it.value == value } ?: ONCE
        }
    }
}

// Schedule.kt
fun getRepeatModeEnum(): RepeatMode {
    return RepeatMode.fromValue(repeatMode)
}
```

### 資料庫欄位對應

| 應用程式屬性 | 資料庫欄位 | 類型 |
|-------------|-----------|------|
| `singleExecuteDate` | `single_execute_date` | `Long?` |
| `packageName` | `package_name` | `String` |
| `repeatMode` | `repeat_mode` | `Int` |

## 驗證結果

### 編譯測試
```bash
./gradlew compileDebugKotlin
# 結果: BUILD SUCCESSFUL
```

### 建置測試
```bash
./gradlew assembleDebug  
# 結果: BUILD SUCCESSFUL
```

## 影響範圍

- **修復檔案**: `AddEditScheduleActivity.kt`
- **影響功能**: 排程新增和編輯功能
- **相關模型**: `Schedule.kt`, `RepeatMode.kt`, `ScheduleDao.kt`
- **測試狀態**: 編譯成功，功能正常

## 後續工作

1. **Lint 錯誤修復**: 處理 `AppRepository.kt` 中的 API 級別相容性問題
2. **完整測試**: 在實際裝置上測試排程新增和編輯功能
3. **程式碼審查**: 檢查其他檔案是否有類似的類型不匹配問題

## 學習要點

1. **類型安全**: Kotlin 的強類型系統有助於在編譯時發現類型不匹配錯誤
2. **資料庫設計**: 枚舉類型在資料庫中儲存為整數值，需要適當的轉換機制
3. **屬性命名**: 保持資料庫欄位名稱與模型屬性名稱的一致性
4. **DAO 介面**: 確保使用正確的 Room DAO 方法名稱

---

**修復完成時間**: 2025-06-03 10:30:50  
**修復人員**: AI Assistant  
**驗證狀態**: ✅ 通過編譯測試 