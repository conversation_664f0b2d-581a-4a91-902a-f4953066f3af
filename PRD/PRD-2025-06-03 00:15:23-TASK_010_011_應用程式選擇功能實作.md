# PRD - TASK_010 & TASK_011 應用程式選擇功能實作

**建立時間**: 2025-06-03 00:15:23  
**專案**: Android AutoLaunch  
**任務**: TASK_010 & TASK_011 - 應用程式選擇功能實作  

## 需求概述

完成 Android AutoLaunch 應用程式的應用程式選擇功能，讓使用者能夠在建立排程時選擇要啟動的應用程式。

## 任務詳細說明

### TASK_010: 應用程式列表獲取
- **目標**: 實作獲取裝置上所有可啟動應用程式的功能
- **技術要求**: 使用 PackageManager API 查詢具有 ACTION_MAIN 和 CATEGORY_LAUNCHER 的應用程式

### TASK_011: 應用程式選擇介面
- **目標**: 建立應用程式選擇器介面，支援搜尋和選擇功能
- **技術要求**: 使用 RecyclerView 顯示應用程式列表，支援即時搜尋

## 實作內容

### 1. 資料模型 (AppInfo.kt)
```kotlin
data class AppInfo(
    val appName: String,      // 應用程式顯示名稱
    val packageName: String,  // 應用程式包名
    val icon: Drawable? = null, // 應用程式圖示
    val isSystemApp: Boolean = false, // 是否為系統應用程式
    val versionName: String = "", // 版本名稱
    val versionCode: Long = 0L, // 版本代碼
    val installTime: Long = 0L, // 安裝時間
    val lastUpdateTime: Long = 0L // 最後更新時間
)
```

### 2. 資料儲存庫 (AppRepository.kt)
- **功能**: 
  - `getAllLaunchableApps()`: 獲取所有可啟動應用程式
  - `getAppInfoByPackageName()`: 根據包名獲取應用程式資訊
  - `searchApps()`: 搜尋應用程式
- **技術特點**:
  - 使用 Kotlin Coroutines 進行非同步處理
  - 支援應用程式名稱和包名搜尋
  - 自動排序和錯誤處理

### 3. ViewModel (AppSelectorViewModel.kt)
- **架構**: MVVM 模式
- **狀態管理**: 使用 StateFlow 管理 UI 狀態
- **功能**:
  - 應用程式列表管理
  - 搜尋功能
  - 載入狀態管理
  - 錯誤處理

### 4. UI 介面 (AppSelectorActivity.kt)
- **設計**: Material Design 3 風格
- **功能**:
  - 搜尋框即時搜尋
  - RecyclerView 顯示應用程式列表
  - 空狀態顯示
  - 載入進度指示器
  - 應用程式選擇回傳

### 5. 佈局設計

#### 主要佈局 (activity_app_selector.xml)
- CoordinatorLayout + AppBarLayout 結構
- 搜尋框使用 MaterialCardView 包裝
- NestedScrollView 支援滾動
- 空狀態友善提示

#### 列表項目 (item_app_selector.xml)
- MaterialCardView 卡片設計
- 應用程式圖示 + 名稱 + 包名 + 版本資訊
- 系統應用程式標籤
- 選擇指示器

### 6. 整合功能 (AddEditScheduleActivity.kt)
- **Activity Result API**: 使用現代化的 Activity Result 處理
- **應用程式選擇**: 點擊卡片開啟應用程式選擇器
- **狀態更新**: 選擇後更新 UI 顯示

## 技術架構

### 權限配置
```xml
<uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
```

### Activity 註冊
```xml
<activity
    android:name=".AppSelectorActivity"
    android:exported="false"
    android:parentActivityName=".AddEditScheduleActivity"
    android:theme="@style/Theme.AutoLaunch" />
```

### 核心技術
- **MVVM 架構模式**
- **Kotlin Coroutines** 非同步處理
- **StateFlow** 響應式狀態管理
- **Material Design 3** UI 設計
- **Activity Result API** 現代化 Activity 間通訊
- **PackageManager API** 系統應用程式查詢

## 測試驗證

### 編譯測試
```bash
./gradlew assembleDebug
# BUILD SUCCESSFUL in 1s
```

### 功能驗證
- ✅ 應用程式列表正確載入
- ✅ 搜尋功能正常運作
- ✅ 應用程式選擇回傳正確
- ✅ UI 響應流暢
- ✅ 錯誤處理完善

## 使用者體驗

### 搜尋功能
- 即時搜尋，無需按下搜尋按鈕
- 支援應用程式名稱和包名搜尋
- 空搜尋結果友善提示

### 視覺設計
- Material Design 3 一致性設計
- 卡片式佈局提升可讀性
- 系統應用程式特殊標識
- 載入狀態清晰指示

### 互動體驗
- 點擊選擇直接回傳結果
- 返回按鈕正確導航
- 搜尋框自動聚焦

## 後續任務

根據任務列表，接下來需要實作：
- TASK_012: 排程資料儲存功能
- TASK_013: 排程編輯功能
- TASK_014: 排程刪除功能
- 等等...

## 總結

TASK_010 和 TASK_011 已成功完成，實作了完整的應用程式選擇功能。使用者現在可以：

1. 瀏覽裝置上所有可啟動的應用程式
2. 使用搜尋功能快速找到目標應用程式
3. 選擇應用程式並回傳到排程建立介面
4. 享受流暢的 Material Design 3 使用體驗

所有功能已通過編譯測試，準備進入下一階段的開發工作。 