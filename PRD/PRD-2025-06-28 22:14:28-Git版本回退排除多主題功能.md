# PRD - Git版本回退排除多主題功能

## 需求概述
用戶遇到嚴重的 Git 衝突問題，需要回退到 `f0dcaa7` commit，完全排除 `feature/add-multiple-themes` 分支的所有記錄和功能。

## 問題描述
- 用戶在 main branch 上遇到 Git 衝突
- 不希望保留 `feature/add-multiple-themes` 分支的任何記錄
- 需要回退到 `f0dcaa7` (feat: enhance schedule editing with unsaved changes tracking and UI improvements)
- 要排除的 commits:
  - `374900e` (Merge branch 'origin/main')
  - `90601b8` (Merge pull request #3 from bobo52310/feature/add-multiple-themes)
  - `c2031be` (feat: Add multiple theme support with 6 theme options)

## 解決方案

### 執行步驟
1. **檢查當前狀態**
   - 使用 `git log --oneline -10` 查看 commit 歷史
   - 使用 `git log --oneline --all | grep f0dcaa7` 找到目標 commit

2. **確認目標 commit**
   - 找到 `f0dcaa7` (feat: enhance schedule editing with unsaved changes tracking and UI improvements)
   - 確認這是最後一個不包含多主題功能的 commit

3. **備份當前修改**
   - 使用 `git stash push -m "backup before reset to before 374900e"` 保存當前修改

4. **執行版本回退**
   - 使用 `git reset --hard f0dcaa7` 重置到目標 commit

5. **驗證結果**
   - 確認 HEAD 指向正確的 commit
   - 檢查工作目錄狀態

### 執行結果
- ✅ 成功回退到 commit `f0dcaa7`
- ✅ 完全排除了 `feature/add-multiple-themes` 分支的所有記錄
- ✅ 排除了有問題的 commits `374900e`, `90601b8`, `c2031be`
- ✅ 當前修改已安全保存在 stash 中
- ✅ 工作目錄乾淨，只有未追蹤的 `w-feature-adjust-schedule-naming/` 目錄

## 功能影響分析
排除 `feature/add-multiple-themes` 分支後，以下功能將被移除：
- 多主題支援功能
- 6 種主題選項
- 主題設定相關的 UI 組件
- 主題管理器和相關工具類

保留的功能：
- 排程編輯時的未儲存變更追蹤
- UI 改進功能
- 拖放排序功能
- 通知欄常駐功能
- 應用程式圖示載入功能
- 排程名稱編輯功能

## 後續建議
1. 如需恢復之前的修改：使用 `git stash pop`
2. 如需推送到遠端：使用 `git push --force-with-lease origin main`
3. 如果將來需要多主題功能，建議重新開發而非合併舊分支
4. 建議在進行重要操作前先建立備份分支

## 技術細節
- 目標 commit: `f0dcaa7` (feat: enhance schedule editing with unsaved changes tracking and UI improvements)
- 排除 commits: `374900e`, `90601b8`, `c2031be`
- 備份位置: Git stash
- 當前狀態: main branch 落後 origin/main 4 個 commits
- 包含的功能: 排程編輯改進、拖放排序、通知欄常駐等

## 風險評估
- 低風險：已備份當前修改到 stash
- 可回復：可透過 stash 或 reflog 恢復之前的狀態
- 需要強制推送：如要同步到遠端需使用 --force-with-lease
- 功能損失：多主題功能將完全移除，需要重新開發

---
*建立時間: 2025-06-28 22:14:28*
*執行狀態: 完成* 