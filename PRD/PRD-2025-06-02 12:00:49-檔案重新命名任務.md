# PRD - 檔案重新命名任務

## 需求描述
用戶要求修改所有的 txt 檔案名稱，檔名來自於檔案內第一行，格式參考 `TASK_001: 專案基礎設定.md`。

## 執行時間
2025-06-02 12:00:49

## 需求分析
1. 檢查專案中所有的 txt 檔案
2. 讀取每個檔案的第一行內容
3. 根據第一行的任務標題重新命名檔案
4. 將副檔名從 .txt 改為 .md
5. 保持與現有檔案 `TASK_001: 專案基礎設定.md` 相同的命名格式

## 執行過程
1. 發現專案中有 22 個 txt 檔案需要重新命名：
   - code.txt
   - code (1).txt 到 code (21).txt

2. 逐一讀取每個檔案的第一行，發現都是以 `### TASK_XXX:` 開頭的任務標題

3. 根據任務標題重新命名所有檔案：
   - code.txt → TASK_002: 排程數據模型定義.md
   - code (1).txt → TASK_003: Room 資料庫整合與 DAO.md
   - code (2).txt → TASK_004: 主介面排程列表 UI.md
   - ... (依此類推到 TASK_023)

## 執行結果
✅ 成功重新命名所有 22 個 txt 檔案
✅ 所有檔案都按照第一行的任務標題命名
✅ 檔名格式與 `TASK_001: 專案基礎設定.md` 保持一致
✅ 副檔名統一改為 .md

## 最終檔案列表
- TASK_001: 專案基礎設定.md (原本就存在)
- TASK_002: 排程數據模型定義.md
- TASK_003: Room 資料庫整合與 DAO.md
- TASK_004: 主介面排程列表 UI.md
- TASK_005: 排程列表數據展示與更新.md
- TASK_006: 新增排程入口與基本導航.md
- TASK_007: 新增_編輯排程介面 UI.md
- TASK_008: 時間選擇器功能實現.md
- TASK_009: 日期_週期選擇器 UI 與邏輯.md
- TASK_010: APP 選擇器權限與應用列表獲取.md
- TASK_011: APP 選擇器介面 UI 與交互.md
- TASK_012: 排程數據保存_更新邏輯.md
- TASK_013: AlarmManager 核心調度服務.md
- TASK_014: APP 啟動廣播接收器與邏輯.md
- TASK_015: 設備重啟後重新註冊排程.md
- TASK_016: 排程啟用_禁用功能.md
- TASK_017: 排程刪除功能.md
- TASK_018: 上次執行時間更新與顯示.md
- TASK_019: 下次執行時間計算與顯示.md
- TASK_020: 目標 APP 卸載或啟動失敗處理.md
- TASK_021: 電池優化提示與引導.md
- TASK_022: UX_UI 細節優化與空狀態處理.md
- TASK_023: 測試與調試.md

## 備註
這些檔案看起來是 Android AutoLaunch 專案的任務規劃文件，涵蓋了從基礎設定到測試調試的完整開發流程。 