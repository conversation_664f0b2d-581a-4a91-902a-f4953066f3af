# PRD - 雲端備份介面優化

## 需求概述
優化雲端備份介面，簡化使用者操作流程，移除不必要的按鈕並整合相關功能。

## 具體需求
1. 移除「同步備份」按鈕
2. 移除「管理文件」按鈕  
3. 將「創建備份」按鈕文字修改為「建立備份」
4. 將「建立備份」和「匯入備份」按鈕放在同一個區塊中

## 技術實現

### UI 層修改
**檔案：** `app/src/main/res/layout/fragment_cloud_backup.xml`

1. **卡片整合**
   - 移除「雲端備份文件管理」MaterialCardView
   - 保留並優化「雲端備份操作」MaterialCardView
   - 更新標題為「雲端備份」
   - 更新描述為「將您的排程數據備份到 Google Drive 或從中還原」

2. **按鈕重新配置**
   - 保留 `btnCreateCloudBackup`，文字改為「建立備份」
   - 移除 `btnSyncBackup` 按鈕
   - 將 `btnImportFromCloud` 移至主要操作區塊
   - 移除 `btnManageCloudBackups` 按鈕

### 邏輯層修改
**檔案：** `app/src/main/java/com/example/autolaunch/fragment/CloudBackupFragment.kt`

1. **移除無用按鈕監聽器**
   ```kotlin
   // 移除以下監聽器
   binding.btnSyncBackup.setOnClickListener { syncBackup() }
   binding.btnManageCloudBackups.setOnClickListener { manageCloudBackups() }
   ```

2. **移除相關函式**
   - 刪除 `syncBackup()` 函式
   - 刪除 `manageCloudBackups()` 函式

3. **更新狀態管理**
   - 在 `updateGoogleAccountStatus()` 中移除對已刪除按鈕的啟用/停用邏輯
   - 只保留 `btnCreateCloudBackup` 和 `btnImportFromCloud` 的狀態管理

## 修改結果

### 修改前
- 雲端備份操作區塊：「創建備份」、「同步備份」
- 雲端備份文件管理區塊：「匯入備份」、「管理文件」

### 修改後  
- 雲端備份區塊：「建立備份」、「匯入備份」

## 使用者體驗改善
1. **介面簡化**：從 4 個按鈕減少到 2 個按鈕
2. **功能集中**：主要的備份和還原功能集中在一個區塊
3. **操作直觀**：移除了容易混淆的「同步備份」功能
4. **管理簡化**：移除了獨立的文件管理功能，統一在匯入流程中處理

## 測試要點
1. 確認「建立備份」功能正常運作
2. 確認「匯入備份」功能正常運作  
3. 確認移除的按鈕不再出現在介面上
4. 確認 Google 帳戶登入/登出狀態正確控制按鈕啟用狀態
5. 確認不存在對已移除按鈕的引用導致的崩潰

## 完成狀態
✅ 已完成 - 2025-07-02 12:03:04

所有要求的 UI 和邏輯修改都已成功實現，雲端備份介面已優化完成。 