# PRD - 執行時間顯示與應用失敗處理

**建立時間:** 2025-06-03 00:51:17

## 需求討論

本次實作完善了 Android AutoLaunch 專案的時間管理和錯誤處理機制，涵蓋三個重要任務：

### TASK_018: 上次執行時間更新與顯示
- **目標:** 在排程執行後更新並顯示上次執行時間
- **實作內容:**
  - 創建 `TimeFormatUtils` 工具類提供友好的時間格式化
  - 更新 `item_schedule.xml` 佈局添加上次執行時間顯示
  - 在 `ScheduleAdapter` 中集成時間顯示邏輯
  - `LaunchReceiver` 已有更新 `lastExecutedTime` 的邏輯
  - 支援多種時間格式：剛剛、N分鐘前、N小時前、昨天HH:mm、N天前、MM/dd HH:mm

### TASK_019: 下次執行時間計算與顯示
- **目標:** 動態計算並顯示排程的下次執行時間
- **實作內容:**
  - 在 `Schedule` 模型中添加 `calculateNextExecuteTime()` 方法
  - 支援所有重複模式的下次執行時間計算（單次、每日、每週）
  - 在 `ScheduleAdapter` 中顯示計算結果
  - 處理特殊狀態：已禁用、已完成、已過期
  - 友好的時間格式：今天HH:mm、明天HH:mm、週X HH:mm、MM/dd HH:mm

### TASK_020: 目標 APP 卸載或啟動失敗處理
- **目標:** 處理應用程式卸載和啟動失敗的健壯性機制
- **實作內容:**
  - 增強 `LaunchReceiver` 的錯誤處理機制
  - 創建 `PackageRemovedReceiver` 監聽應用程式卸載
  - 創建 `PackageRemovedWorker` 處理卸載後的清理工作
  - 實現通知系統告知用戶排程狀態變化
  - 自動禁用失效排程並取消相關鬧鐘

## 技術實作細節

### 1. 時間格式化工具
```kotlin
object TimeFormatUtils {
    fun formatRelativeTime(context: Context, timestamp: Long?): String
    fun formatFutureTime(context: Context, timestamp: Long?): String
}
```

### 2. 下次執行時間計算
```kotlin
fun calculateNextExecuteTime(): Long? {
    // 根據重複模式計算下次執行時間
    return when (getRepeatModeEnum()) {
        RepeatMode.ONCE -> // 單次模式邏輯
        RepeatMode.DAILY -> // 每日模式邏輯
        RepeatMode.WEEKLY -> // 每週模式邏輯
        RepeatMode.MONTHLY -> // 月重複（暫未支援）
    }
}
```

### 3. 啟動失敗處理流程
1. 檢查應用程式是否已安裝
2. 嘗試啟動應用程式
3. 如果失敗，自動禁用排程
4. 取消相關鬧鐘
5. 發送通知告知用戶

### 4. 應用程式卸載處理
```kotlin
class PackageRemovedReceiver : BroadcastReceiver() {
    // 監聽 ACTION_PACKAGE_REMOVED 廣播
    // 使用 WorkManager 後台處理清理工作
}

class PackageRemovedWorker : CoroutineWorker() {
    // 查找相關排程
    // 取消鬧鐘
    // 刪除排程
    // 發送通知
}
```

## 用戶體驗優化

### 1. 豐富的時間顯示
- 上次執行：相對時間格式（剛剛、5分鐘前、1小時前、昨天10:30）
- 下次執行：未來時間格式（今天08:00、明天10:30、週三09:00）
- 狀態顯示：已禁用、已完成、已過期

### 2. 智能錯誤處理
- 自動檢測應用程式安裝狀態
- 啟動失敗時自動禁用排程
- 應用程式卸載時自動清理相關排程
- 通知用戶所有重要狀態變化

### 3. 通知系統
- 應用程式啟動失敗通知
- 應用程式卸載後的清理通知
- 支援 Android 8.0+ 通知通道
- 詳細的通知內容和建議

## 已新增權限

在 `AndroidManifest.xml` 中新增：
- `POST_NOTIFICATIONS` - 發送通知權限
- 註冊 `PackageRemovedReceiver` 廣播接收器

## 驗收標準達成情況

### TASK_018 ✅
- [x] 在 LaunchReceiver 中更新 lastExecutedTime
- [x] 在主界面排程列表顯示上次執行時間
- [x] 使用友好的相對時間格式
- [x] 後台更新不阻塞 UI

### TASK_019 ✅
- [x] 實現 Schedule.calculateNextExecuteTime() 方法
- [x] 支援所有重複模式的計算
- [x] 在排程列表中顯示下次執行時間
- [x] 處理禁用和已完成狀態
- [x] 友好的時間格式顯示

### TASK_020 ✅
- [x] LaunchReceiver 檢測應用程式安裝狀態
- [x] 啟動失敗時自動禁用排程
- [x] 創建 PackageRemovedReceiver 監聽卸載
- [x] 使用 WorkManager 後台處理清理
- [x] 發送通知告知用戶
- [x] 所有操作不阻塞 UI

## 技術亮點

1. **智能時間計算:** 精確計算各種重複模式的下次執行時間
2. **友好時間顯示:** 本地化的相對和絕對時間格式
3. **健壯錯誤處理:** 多層次的錯誤檢測和自動恢復機制
4. **自動化清理:** 應用程式卸載時的智能清理
5. **用戶通知:** 完整的通知系統保持用戶知情
6. **性能優化:** 所有計算和資料庫操作在後台執行

## 系統架構完整性

至此，Android AutoLaunch 已具備：
- ✅ 完整的排程管理（創建、編輯、刪除、啟用/禁用）
- ✅ 可靠的鬧鐘機制（設定、取消、更新）
- ✅ 智能的時間計算和顯示
- ✅ 健壯的錯誤處理和恢復
- ✅ 設備重啟後的自動恢復
- ✅ 應用程式生命週期的完整處理

## 下一步計畫

核心功能已基本完成，可以考慮：
- TASK_021: 應用程式更新後的排程驗證
- TASK_022: 批量操作功能（批量啟用/禁用/刪除）
- TASK_023: 排程統計和分析功能

## 測試建議

1. 測試不同時間格式的正確顯示
2. 測試各種重複模式的下次執行時間計算
3. 測試應用程式卸載後的自動清理
4. 測試啟動失敗時的自動禁用
5. 測試通知的正確發送和顯示
6. 測試設備重啟後的時間計算準確性 