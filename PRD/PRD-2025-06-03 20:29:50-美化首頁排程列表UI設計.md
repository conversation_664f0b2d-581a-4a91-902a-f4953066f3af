# PRD - 美化首頁排程列表UI設計

**日期**: 2025-06-03 20:29:50  
**需求類型**: UI/UX改進  
**優先級**: 中  

## 需求描述

用戶希望美化首頁MainView的排版，讓時間和圖示資訊更加明顯，提升整體視覺效果和用戶體驗。

## 設計目標

### 主要目標
1. **突出時間顯示**：讓排程時間更加醒目和易讀
2. **增強圖示可見性**：讓App圖示更大更清晰
3. **改善整體視覺層次**：提升卡片設計和布局美感
4. **保持功能完整性**：在美化的同時保持所有原有功能

### 用戶體驗目標
- 提高信息掃描效率
- 增強視覺吸引力
- 改善可讀性和可用性

## 設計方案

### 1. 圖示優化
**改進前**：
- 圖示尺寸：48dp × 48dp
- 無特殊包裝，直接顯示

**改進後**：
- 圖示尺寸：64dp × 64dp（增大33%）
- 添加圓角卡片包裝（16dp圓角，2dp陰影）
- 更好的視覺層次和質感

### 2. 時間顯示優化
**改進前**：
- 時間以普通文字顯示
- 與其他信息混在一起

**改進後**：
- 獨立的彩色時間卡片
- 使用主題色彩背景（colorPrimaryContainer）
- 大字體顯示（HeadlineSmall + 粗體）
- 添加時鐘圖標
- 重複模式標籤直接顯示在時間下方

### 3. 整體布局優化
**改進前**：
- 卡片圓角：12dp
- 卡片陰影：2dp
- 內邊距：16dp

**改進後**：
- 卡片圓角：16dp（更現代）
- 卡片陰影：4dp（更有層次）
- 內邊距：20dp（更寬敞）
- App名稱使用更大字體（HeadlineSmall）

### 4. 信息架構優化
**新的信息層次**：
1. **主要信息區域**：
   - 大尺寸App圖示（左側）
   - App名稱（大字體，粗體）
   - 重複模式詳細說明

2. **時間顯示區域**：
   - 突出的時間卡片（右上角）
   - 時鐘圖標 + 大字體時間
   - 重複模式簡短標籤

3. **狀態信息區域**：
   - 下次執行時間（帶圖標）
   - 上次執行時間（帶圖標）
   - 啟用/禁用開關

## 技術實現

### 修改的文件
1. **`app/src/main/res/layout/item_schedule.xml`**
   - 重新設計整個布局結構
   - 添加時間顯示卡片
   - 優化圖示包裝
   - 調整間距和尺寸

2. **`app/src/main/java/com/example/autolaunch/ScheduleAdapter.kt`**
   - 更新數據綁定邏輯
   - 適配新的視圖ID
   - 優化重複模式顯示邏輯

### 關鍵設計元素

#### 時間卡片設計
```xml
<com.google.android.material.card.MaterialCardView
    android:id="@+id/cardTimeDisplay"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    app:cardCornerRadius="12dp"
    app:cardElevation="1dp"
    app:cardBackgroundColor="?attr/colorPrimaryContainer">
    
    <!-- 時鐘圖標 + 時間 + 重複模式 -->
    
</com.google.android.material.card.MaterialCardView>
```

#### 圖示包裝設計
```xml
<com.google.android.material.card.MaterialCardView
    android:id="@+id/cardAppIcon"
    android:layout_width="64dp"
    android:layout_height="64dp"
    app:cardCornerRadius="16dp"
    app:cardElevation="2dp">
    
    <ImageView android:id="@+id/ivAppIcon" />
    
</com.google.android.material.card.MaterialCardView>
```

## 效果驗證

### 視覺效果對比

**改進前**：
- 信息密集，層次不清
- 時間不夠突出
- 圖示較小，不夠醒目

**改進後**：
- ✅ 時間在彩色卡片中突出顯示
- ✅ 圖示增大33%，更加清晰
- ✅ 整體布局更有層次感
- ✅ 信息分組更清晰
- ✅ 視覺吸引力顯著提升

### 功能驗證
- ✅ 所有原有功能正常工作
- ✅ 點擊事件正常響應
- ✅ 開關切換正常
- ✅ 數據顯示準確

## 用戶反饋

### 預期改進
1. **可讀性提升**：時間和重要信息更容易識別
2. **視覺吸引力**：更現代、更美觀的界面
3. **掃描效率**：用戶能更快找到需要的信息
4. **專業感**：整體應用看起來更專業

### 後續優化建議
1. 考慮添加更多視覺狀態指示（如顏色編碼）
2. 可以考慮添加動畫效果提升交互體驗
3. 根據用戶反饋進一步調整間距和尺寸

## 技術細節

### 響應式設計
- 使用ConstraintLayout確保在不同屏幕尺寸下的適配
- 使用dp單位確保在不同密度屏幕上的一致性
- 使用Material Design 3主題色彩確保一致的視覺體驗

### 性能考慮
- 布局層次優化，避免過度嵌套
- 使用ViewBinding提高性能
- 圖片加載優化（PackageManager緩存）

---

**完成時間**: 2025-06-03 20:29:50  
**狀態**: ✅ 已完成並驗證通過  
**用戶滿意度**: 預期高度滿意 