# PRD - 完成排程數據模型定義 (TASK_002)

**日期時間：** 2025-06-02 23:09:07  
**任務編號：** TASK_002  
**需求摘要：** 排程數據模型定義

## 任務描述

根據 TASK_002 要求，定義應用程式排程的核心數據模型，包含排程的所有必要資訊，為 Room 資料庫做好準備。

## 實作內容

### 1. RepeatMode 枚舉 (`RepeatMode.kt`)
- 定義排程重複模式：ONCE（單次）、DAILY（每日）、WEEKLY（每週）、MONTHLY（每月）
- 包含 value 和 displayName 屬性
- 提供 fromValue() 靜態方法進行轉換

### 2. Schedule 實體類別 (`Schedule.kt`)
主要的排程數據模型，包含以下屬性：
- **基本識別資訊：**
  - `id`: 主鍵，自動生成
  - `appName`: App 顯示名稱
  - `packageName`: App 包名

- **時間相關：**
  - `hour`: 排程小時 (24小時制)
  - `minute`: 排程分鐘
  - `repeatMode`: 重複模式 (對應 RepeatMode.value)
  - `daysOfWeek`: 星期幾位元遮罩
  - `singleExecuteDate`: 單次執行日期 (Unix timestamp)

- **執行狀態：**
  - `isEnabled`: 是否啟用
  - `lastExecutedTime`: 上次執行時間
  - `nextExecutedTime`: 下次執行時間

- **系統時間戳：**
  - `createdTime`: 創建時間
  - `updatedTime`: 更新時間

**輔助方法：**
- `getRepeatModeEnum()`: 取得重複模式枚舉
- `isRecurring()`: 檢查是否為週期性排程
- `getFormattedTime()`: 取得格式化時間字串
- `isDayOfWeekSelected()`: 檢查指定星期幾是否被選中
- `getSelectedDaysOfWeek()`: 取得選中的星期幾列表
- `getDaysOfWeekDisplayName()`: 取得星期幾顯示名稱

### 3. DaysOfWeek 輔助類別 (`DaysOfWeek.kt`)
專門處理星期幾位元遮罩操作：
- 定義位元遮罩常數 (MONDAY=1, TUESDAY=2, 等等)
- 提供常用組合 (WEEKDAYS, WEEKEND, ALL_DAYS)
- 包含星期幾顯示名稱陣列
- 提供完整的位元遮罩操作方法：
  - `getDayMask()`: 取得星期幾位元遮罩
  - `setDay()`: 設定指定星期幾
  - `isSelected()`: 檢查是否被選中
  - `getSelectedDays()`: 取得選中列表
  - `createMask()`: 建立位元遮罩
  - `getDisplayName()`: 取得顯示名稱

### 4. AppInfo 數據類別 (`AppInfo.kt`)
表示應用程式資訊：
- 基本資訊：appName, packageName, icon, isSystemApp
- 版本資訊：versionName, versionCode
- 時間資訊：installTime, lastUpdateTime
- 輔助方法：isLaunchable(), getVersionInfo()

## Room 準備

所有類別都已準備好 Room 整合：
- Schedule 使用 `@Entity` 註解，表格名稱為 "schedules"
- 所有屬性都有適當的 `@ColumnInfo` 註解
- 主鍵使用 `@PrimaryKey(autoGenerate = true)`
- 資料型別已優化（使用 Long 儲存時間戳，Int 儲存位元遮罩）

## 編譯結果

✅ 專案編譯成功，無語法錯誤  
✅ 所有數據模型類別建立完成  
✅ Git commit 成功 (1c5141e)  

## 檔案清單

- `app/src/main/java/com/example/autolaunch/model/RepeatMode.kt`
- `app/src/main/java/com/example/autolaunch/model/Schedule.kt`
- `app/src/main/java/com/example/autolaunch/model/DaysOfWeek.kt`
- `app/src/main/java/com/example/autolaunch/model/AppInfo.kt`

## 後續行動

根據 interactive feedback，下一步需要開始實作 **TASK_003: Room 資料庫整合與 DAO**。

## 技術特點

1. **位元遮罩設計**: 使用 Int 儲存星期幾選擇，節省儲存空間且操作高效
2. **時間戳設計**: 統一使用 Unix timestamp (Long) 儲存所有時間相關資料
3. **擴展性**: 數據模型設計考慮未來擴展需求
4. **可讀性**: 提供豐富的顯示名稱和格式化方法
5. **Room 相容性**: 完全符合 Room ORM 要求 