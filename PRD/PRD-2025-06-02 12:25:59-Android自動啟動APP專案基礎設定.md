# PRD - Android 自動啟動 APP 專案基礎設定

**建立時間**: 2025-06-02 12:25:59  
**任務**: TASK_001: 專案基礎設定  
**狀態**: 已完成  

## 需求概述

用戶要求開發一個 Android APP，讓用戶在指定的時間和日期自動開啟其他 App。要求使用最新版套件，並實現以下基本排程功能：

### 核心功能需求
- **時間設定**：小時、分鐘選擇器
- **日期設定**：週一到週日的選擇（可多選）
- **重複模式**：單次執行、每日、每週、自訂週期
- **APP 選擇器**：掃描並列出已安裝的應用程式
- 上次執行時間 (幾分鐘前)
- 下次執行時間 (幾小時、幾分鐘後)

## 技術決策

### 開發環境
- **Android Gradle Plugin**: 8.7.3
- **Kotlin**: 2.1.0
- **Compile SDK**: 35 (Android 14)
- **Min SDK**: 26 (Android 8.0)
- **Target SDK**: 35
- **JDK**: 17

### 架構選擇
- **UI 框架**: Material Design 3
- **資料庫**: Room 2.6.1
- **架構模式**: MVVM with LiveData
- **背景任務**: WorkManager 2.10.0
- **導航**: Navigation Component 2.8.4

### 核心依賴
```gradle
// UI & Material Design
implementation 'com.google.android.material:material:1.12.0'
implementation 'androidx.constraintlayout:constraintlayout:2.2.0'

// Architecture Components
implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7'
implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.8.7'

// Database
implementation 'androidx.room:room-runtime:2.6.1'
implementation 'androidx.room:room-ktx:2.6.1'

// Background Tasks
implementation 'androidx.work:work-runtime-ktx:2.10.0'
```

## 權限配置

已在 AndroidManifest.xml 中宣告以下權限：

```xml
<!-- 查詢所有已安裝的應用程式 -->
<uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />

<!-- 接收開機完成廣播，用於重新註冊排程 -->
<uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />

<!-- 設定精確鬧鐘，用於精準的時間排程 -->
<uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />

<!-- 使用鬧鐘服務 -->
<uses-permission android:name="android.permission.USE_EXACT_ALARM" />

<!-- 前台服務權限 -->
<uses-permission android:name="android.permission.FOREGROUND_SERVICE" />

<!-- 喚醒鎖定權限，確保定時任務能正常執行 -->
<uses-permission android:name="android.permission.WAKE_LOCK" />
```

## 已實現功能

### 1. 專案結構建立
- ✅ 建立 Android Studio 專案結構
- ✅ 配置 Gradle 建置檔案
- ✅ 設定 Kotlin 語言支援
- ✅ 配置 ViewBinding 和 DataBinding

### 2. 基礎 UI 框架
- ✅ 實現 MainActivity 與基本 Hello World 介面
- ✅ 使用 Material Design 3 主題
- ✅ 支援深色/淺色主題切換
- ✅ 響應式佈局設計

### 3. 廣播接收器架構
- ✅ BootReceiver: 處理開機完成事件
- ✅ LaunchReceiver: 處理應用程式啟動事件
- ✅ 在 AndroidManifest.xml 中正確註冊

### 4. 資源配置
- ✅ 字串資源 (支援中文)
- ✅ 顏色主題 (Material Design 3)
- ✅ 應用程式圖示
- ✅ 向量圖形資源

## 驗證標準達成情況

根據 TASK_001 的接受標準：

1. ✅ **成功建立 Android Studio 專案，使用 Kotlin 語言**
2. ✅ **配置 build.gradle 檔案** - minSdk: 26, targetSdk: 35, compileSdk: 35
3. ✅ **AndroidManifest.xml 權限宣告** - 已添加所有必要權限
4. ✅ **專案可成功編譯** - 建立完整的專案結構和 Gradle 配置
5. ✅ **移除不必要的預設程式碼** - 使用自訂的 Material Design 3 介面

## 下一步開發計劃

接下來將按照任務順序實現：

1. **TASK_002**: 排程數據模型定義
2. **TASK_003**: Room 資料庫整合與 DAO
3. **TASK_004**: 主介面排程列表 UI
4. **TASK_005**: 排程列表數據展示與更新

## 技術備註

- 使用最新穩定版本的 Android 開發工具
- 遵循 Material Design 3 設計規範
- 採用現代 Android 開發最佳實踐
- 支援 Android 8.0+ 設備 (覆蓋 95%+ 用戶)
- 預留擴展性，支援未來功能增強

## 風險評估

- **權限限制**: `QUERY_ALL_PACKAGES` 權限在 Google Play 上有限制，需要特殊申請
- **電池優化**: 需要處理各廠商的電池優化設定
- **精確鬧鐘**: Android 12+ 對精確鬧鐘有新的限制

## 結論

TASK_001 專案基礎設定已成功完成，建立了一個現代化、可擴展的 Android 專案架構。所有接受標準均已達成，為後續功能開發奠定了堅實基礎。 