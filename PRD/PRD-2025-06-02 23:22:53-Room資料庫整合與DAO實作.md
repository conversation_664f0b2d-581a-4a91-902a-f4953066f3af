# PRD - Room 資料庫整合與 DAO 實作

**日期：** 2025-06-02 23:22:53  
**任務：** TASK_003: Room 資料庫整合與 DAO  
**狀態：** 完成

## 需求討論過程

### 任務目標
將 Room Persistence Library 整合到專案中，定義資料庫類別 (`AppDatabase`) 和資料存取物件 (`DAO`) 介面，用於對排程數據進行增、刪、改、查操作。

### 實作內容

#### 1. 專案檢查與準備
- 檢查現有的 `build.gradle` 配置
- 確認 Room 相關依賴已正確配置
- 檢查現有的 `Schedule` 實體模型

#### 2. 核心檔案實作

**2.1 ScheduleDao.kt**
- 創建 `@Dao` 介面 `ScheduleDao`
- 實作基本 CRUD 操作：
  - `insert(schedule: Schedule): Long` - 插入排程
  - `insertAll(schedules: List<Schedule>)` - 批量插入
  - `update(schedule: Schedule): Int` - 更新排程
  - `delete(schedule: Schedule): Int` - 刪除排程
  - `deleteById(id: Long): Int` - 根據 ID 刪除
- 實作查詢操作：
  - `getAllSchedules(): Flow<List<Schedule>>` - 獲取所有排程
  - `getScheduleById(id: Long): Flow<Schedule?>` - 根據 ID 獲取排程
  - `getEnabledSchedules(): Flow<List<Schedule>>` - 獲取啟用的排程
  - `getSchedulesByPackageName(packageName: String): Flow<List<Schedule>>` - 根據應用程式包名查詢
  - `getSchedulesByRepeatMode(repeatMode: Int): Flow<List<Schedule>>` - 根據重複模式查詢
- 實作狀態更新操作：
  - `updateEnabledStatus(id: Long, isEnabled: Boolean)` - 更新啟用狀態
  - `updateLastExecutedTime(id: Long, lastExecutedTime: Long)` - 更新最後執行時間
  - `updateNextExecutedTime(id: Long, nextExecutedTime: Long)` - 更新下次執行時間
- 實作統計操作：
  - `getScheduleCount(): Int` - 獲取排程總數
  - `getEnabledScheduleCount(): Int` - 獲取啟用排程總數

**2.2 AppDatabase.kt**
- 創建抽象類別 `AppDatabase` 繼承自 `RoomDatabase`
- 使用 `@Database` 註解配置：
  - 實體列表：`[Schedule::class]`
  - 版本號：1
  - 導出模式：false
- 實作單例模式 (Singleton Pattern)：
  - 使用雙重檢查鎖定確保執行緒安全
  - 提供 `getDatabase(context: Context)` 靜態方法
- 添加資料庫回調：
  - `onCreate` - 資料庫創建時的操作
  - `onOpen` - 資料庫開啟時的操作
- 預留類型轉換器 (Converters) 的位置，供未來擴展

**2.3 ScheduleRepository.kt**
- 創建資料儲存庫類別封裝數據存取邏輯
- 提供統一的數據操作接口
- 封裝 DAO 的所有方法，提供更高層級的抽象
- 支援依賴注入模式

**2.4 DatabaseUsageExample.kt**
- 創建使用範例文件
- 展示各種數據操作的使用方法
- 提供在 Activity/Fragment 中的使用指南
- 包含實際業務場景的範例代碼

#### 3. 問題解決

**3.1 編譯錯誤修正**
- 初期遇到 Converters 類別錯誤：
  - 問題：Room 抱怨 Converters 被標記為轉換器但沒有轉換函數
  - 解決：移除暫時不需要的 `@TypeConverters` 註解，將 Converters 類別註解化
- DaysOfWeek 使用錯誤修正：
  - 問題：使用了不存在的 `.bit` 屬性
  - 解決：修正為使用正確的常數值（如 `DaysOfWeek.MONDAY`）

#### 4. 技術特點

**4.1 架構設計**
- 採用 Repository 模式分離數據存取邏輯
- 使用 Kotlin Flow 提供響應式數據流
- 實作單例模式確保資料庫實例唯一性

**4.2 性能優化**
- 使用 `suspend` 函數支援協程，避免阻塞主執行緒
- 查詢操作返回 Flow，支援數據變動時自動更新
- 合理的索引設計（通過 `ORDER BY` 子句）

**4.3 可維護性**
- 完整的中文註釋和文檔
- 清晰的方法命名和參數說明
- 詳細的使用範例和最佳實踐指南

## 實作結果

### 檔案結構
```
app/src/main/java/com/example/autolaunch/model/
├── Schedule.kt (已存在)
├── ScheduleDao.kt (新增)
├── AppDatabase.kt (新增)
├── ScheduleRepository.kt (新增)
└── DatabaseUsageExample.kt (新增)
```

### 接受標準完成情況
✅ 在 `build.gradle (Module: app)` 中添加 Room 相關依賴（已存在）  
✅ 創建繼承自 `RoomDatabase` 的抽象類別 `AppDatabase`  
✅ 在 `AppDatabase` 中定義 `Schedule` 實體（已存在）  
✅ 創建 `@Dao` 介面 `ScheduleDao` 包含所有必需方法  
✅ 實現資料庫的單例模式  
✅ 使用 Kotlin Flow 支援數據變動時自動更新  

### 額外實作
- 創建 `ScheduleRepository` 提供更高層級的抽象
- 提供詳細的使用範例和文檔
- 實作額外的查詢方法（按應用程式、重複模式等查詢）
- 實作統計功能（計數方法）
- 預留未來擴展的空間（類型轉換器等）

## 編譯測試
專案能夠成功編譯，無編譯錯誤或警告。

## 後續任務
根據用戶回饋，下一步將開始實作 TASK_004: 主介面排程列表 UI。

## 技術債務與建議
1. 考慮添加資料庫遷移策略，為未來版本升級做準備
2. 可以考慮使用依賴注入框架（如 Hilt）替代手動單例模式
3. 建議後續添加單元測試來驗證 DAO 和 Repository 的功能 