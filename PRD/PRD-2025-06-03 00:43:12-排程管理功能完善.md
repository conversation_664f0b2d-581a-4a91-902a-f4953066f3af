# PRD - 排程管理功能完善

**建立時間:** 2025-06-03 00:43:12

## 需求討論

本次實作完善了 Android AutoLaunch 專案的排程管理功能，涵蓋三個重要任務：

### TASK_015: 設備重啟後重新註冊排程
- **目標:** 確保設備重啟後排程能自動恢復
- **實作內容:**
  - 完善 `BootReceiver` 廣播接收器
  - 創建 `ScheduleReregistrationWorker` 使用 WorkManager 處理後台任務
  - 監聽 `BOOT_COMPLETED`、`MY_PACKAGE_REPLACED`、`PACKAGE_REPLACED` 廣播
  - 自動重新註冊所有啟用的排程到 AlarmManager

### TASK_016: 排程啟用/禁用功能
- **目標:** 提供便捷的排程開關功能
- **實作內容:**
  - 在 `ScheduleAdapter` 中已有開關 UI 基礎
  - 在 `MainActivity` 中處理開關切換事件
  - 更新 `ScheduleRepository` 整合 AlarmManager 服務
  - 啟用時自動設定鬧鐘，禁用時自動取消鬧鐘
  - 提供即時的視覺回饋

### TASK_017: 排程刪除功能
- **目標:** 實現滑動刪除和撤銷功能
- **實作內容:**
  - 創建 `SwipeToDeleteCallback` 實現滑動刪除
  - 添加刪除確認對話框防止誤刪
  - 實現撤銷功能使用 Snackbar
  - 刪除時自動取消對應的鬧鐘
  - 創建刪除圖標資源

## 技術實作細節

### 1. 設備重啟恢復機制
```kotlin
class BootReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context, intent: Intent) {
        // 使用 WorkManager 處理後台任務
        val workRequest = OneTimeWorkRequestBuilder<ScheduleReregistrationWorker>().build()
        WorkManager.getInstance(context).enqueue(workRequest)
    }
}
```

### 2. WorkManager 後台處理
```kotlin
class ScheduleReregistrationWorker : CoroutineWorker {
    override suspend fun doWork(): Result {
        // 獲取所有啟用的排程
        val enabledSchedules = scheduleDao.getEnabledSchedules().first()
        // 重新註冊每個排程
        enabledSchedules.forEach { alarmService.setAlarm(it) }
    }
}
```

### 3. 滑動刪除實現
```kotlin
class SwipeToDeleteCallback : ItemTouchHelper.SimpleCallback {
    override fun onSwiped(viewHolder: RecyclerView.ViewHolder, direction: Int) {
        // 顯示確認對話框
        showDeleteConfirmationDialog(schedule, position)
    }
}
```

### 4. Repository 層整合
- 所有資料庫操作都同步更新 AlarmManager
- 插入排程時自動設定鬧鐘
- 更新排程時自動更新鬧鐘
- 刪除排程時自動取消鬧鐘
- 啟用/禁用時自動管理鬧鐘狀態

## 用戶體驗優化

### 1. 視覺回饋
- 開關切換時顯示 Toast 提示
- 滑動刪除時顯示紅色背景和刪除圖標
- 刪除後顯示 Snackbar 提供撤銷選項

### 2. 防誤操作
- 刪除前顯示確認對話框
- 提供撤銷功能避免意外刪除
- 取消對話框時恢復項目位置

### 3. 後台處理
- 使用 WorkManager 確保重啟恢復的可靠性
- 所有資料庫操作在後台線程執行
- 協程處理避免阻塞 UI

## 驗收標準達成情況

### TASK_015 ✅
- [x] 創建 BootReceiver 監聽重啟廣播
- [x] 在 AndroidManifest.xml 註冊接收器和權限
- [x] 使用 WorkManager 處理後台重新註冊
- [x] 查詢所有啟用排程並重新設定鬧鐘
- [x] 確保不阻塞主線程

### TASK_016 ✅
- [x] 排程列表項目包含開關控制項
- [x] 開關狀態正確反映 isEnabled 屬性
- [x] 切換時更新資料庫狀態
- [x] 自動設定或取消 AlarmManager 鬧鐘
- [x] 列表即時更新顯示狀態

### TASK_017 ✅
- [x] 實現滑動刪除交互
- [x] 刪除前顯示確認對話框
- [x] 刪除後從資料庫移除並取消鬧鐘
- [x] 更新列表移除被刪除項目
- [x] 提供撤銷功能

## 技術亮點

1. **完整的生命週期管理:** 從創建到刪除的完整排程管理
2. **可靠的重啟恢復:** 使用 WorkManager 確保重啟後排程恢復
3. **優雅的用戶交互:** 滑動刪除、確認對話框、撤銷功能
4. **一致的狀態管理:** Repository 層統一管理資料庫和鬧鐘狀態
5. **防誤操作設計:** 多重確認機制避免意外操作

## 已配置功能

### 廣播接收器
- `BootReceiver` - 監聽設備重啟
- `LaunchReceiver` - 處理鬧鐘觸發

### 後台任務
- `ScheduleReregistrationWorker` - 重新註冊排程

### UI 交互
- 開關切換啟用/禁用
- 滑動刪除排程
- 確認對話框
- 撤銷功能

## 下一步計畫

排程管理的核心功能已經完成，接下來可以繼續實作：
- TASK_018: 上次執行時間更新與顯示
- TASK_019: 下次執行時間計算與顯示
- TASK_020: 目標 APP 卸載或啟動失敗處理

## 測試建議

1. 測試設備重啟後排程是否正確恢復
2. 測試開關切換的鬧鐘設定/取消
3. 測試滑動刪除的完整流程
4. 測試撤銷功能的正確性
5. 測試應用程式更新後的排程恢復 