# PRD - Kotlin 編譯器檔案 gitignore 處理

**時間：** 2025-06-26 20:55:58
**需求摘要：** 專案內發現 .salive 檔案，需要了解用途並決定是否加入 gitignore

## 需求背景

用戶在專案中發現了 `.kotlin/sessions/` 目錄下的 `.salive` 檔案，不確定這些檔案的用途以及是否需要加入 gitignore 忽略清單。

## 問題分析

### 1. 檔案調查
- 發現位置：`.kotlin/sessions/kotlin-compiler-*.salive`
- 檔案大小：0 bytes
- 檔案數量：多個，具有不同的數字後綴

### 2. 技術研究
通過網路搜尋發現：
- **用途**：Kotlin K2 編譯器（Kotlin 2.0+）的會話檔案
- **生成時機**：編譯過程中自動產生
- **性質**：臨時檔案，記錄編譯器內部狀態
- **建議處理**：應該加入 gitignore

## 解決方案

### 1. 檔案系統檢查
- 確認現有 `.gitignore` 檔案狀態
- 檢查是否已有 Kotlin 相關規則

### 2. 更新 gitignore 規則
添加以下規則到 `.gitignore`：
```
# Kotlin compiler
.kotlin/
*.salive
```

### 3. Git 提交處理
- 執行 `git add .` 添加所有變更
- 執行 `git commit` 提交更改和 gitignore 更新

## 實施結果

### ✅ 成功完成
- 成功更新 `.gitignore` 檔案
- 添加 Kotlin 編譯器相關忽略規則
- 提交了當前所有變更，包括：
  - .gitignore 更新
  - 刪除現有 .salive 檔案
  - 其他專案檔案變更

### 📊 提交統計
- 檔案變更：12 個檔案
- 新增行數：686 行
- 刪除行數：37 行
- 新增檔案：4 個
- 刪除檔案：1 個

## 技術知識點

### Kotlin 編譯器會話檔案
- `.salive` 檔案是 Kotlin 2.0+ K2 編譯器產生的內部檔案
- 這些檔案用於記錄編譯器會話狀態
- 對版本控制無意義，應該被忽略
- 類似於其他編譯器產生的暫存檔案

### 最佳實踐
- 所有編譯器產生的臨時檔案都應加入 gitignore
- 定期清理和更新 gitignore 規則
- 保持版本庫乾淨，避免追蹤不必要的檔案

## 後續建議

1. **定期檢查**：定期檢查是否有新的編譯器產生檔案需要忽略
2. **團隊同步**：確保團隊成員都了解新的 gitignore 規則
3. **文件更新**：在專案文件中記錄 gitignore 規則的用途

## 參考資料

- [GitHub Issue: Add kotlin sessions compiler salive to .gitignore](https://github.com/toptal/gitignore.io/issues/651)
- [Kotlin 官方文件：編譯與快取](https://kotlinlang.org/docs/gradle-compilation-and-caches.html)
- Kotlin K2 編譯器相關討論和社群建議 