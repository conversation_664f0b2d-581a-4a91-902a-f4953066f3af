# PRD - 修復新增排程按鈕初始化失敗問題

**日期**: 2025-06-03 20:23:41  
**問題類型**: Bug修復  
**優先級**: 高  

## 問題描述

用戶反映點擊"新增排程"按鈕沒有反應，顯示初始化失敗。這是一個關鍵功能問題，影響用戶無法創建新的排程。

## 問題分析

### 錯誤現象
- 點擊主頁面的"新增排程"按鈕後沒有任何反應
- 頁面停留在主頁面，沒有跳轉到AddEditScheduleActivity
- 用戶無法創建新的排程

### 根本原因
通過logcat分析發現關鍵錯誤：
```
java.lang.IllegalStateException: This Activity already has an action bar supplied by the window decor. Do not request Window.FEATURE_SUPPORT_ACTION_BAR and set windowActionBar to false in your theme to use a Toolbar instead.
```

**問題根源**：
1. 應用程式主題`Theme.Material3.DayNight`默認提供了action bar
2. AddEditScheduleActivity的代碼中嘗試設置自定義toolbar作為action bar：
   ```kotlin
   setSupportActionBar(binding.toolbar)
   ```
3. 兩者衝突導致Activity初始化失敗

## 解決方案

### 採用的方法
修改應用程式主題配置，禁用默認的action bar，允許使用自定義toolbar。

### 具體修改
在`app/src/main/res/values/themes.xml`中添加：
```xml
<!-- 禁用默認的action bar，允許使用自定義toolbar -->
<item name="windowActionBar">false</item>
<item name="windowNoTitle">true</item>
```

### 修改後的完整主題
```xml
<style name="Theme.AutoLaunch" parent="Theme.Material3.DayNight">
    <item name="colorPrimary">@color/md_theme_light_primary</item>
    <item name="colorOnPrimary">@color/md_theme_light_onPrimary</item>
    <item name="colorSecondary">@color/md_theme_light_secondary</item>
    <item name="colorOnSecondary">@color/md_theme_light_onSecondary</item>
    <item name="colorSurface">@color/md_theme_light_surface</item>
    <item name="colorOnSurface">@color/md_theme_light_onSurface</item>
    <item name="android:colorBackground">@color/md_theme_light_background</item>
    <item name="colorOnBackground">@color/md_theme_light_onBackground</item>
    <!-- 禁用默認的action bar，允許使用自定義toolbar -->
    <item name="windowActionBar">false</item>
    <item name="windowNoTitle">true</item>
</style>
```

## 測試結果

### 修復前
- 點擊"新增排程"按鈕無反應
- logcat顯示IllegalStateException錯誤
- AddEditScheduleActivity無法正常啟動

### 修復後
- ✅ 點擊"新增排程"按鈕正常響應
- ✅ AddEditScheduleActivity成功啟動並顯示完整UI
- ✅ 所有功能區域正常顯示：
  - 選擇應用程式區域
  - 設定時間區域  
  - 重複設定區域
  - 星期幾選擇區域
  - 取消/儲存按鈕

## 影響範圍

### 正面影響
- 修復了核心功能，用戶可以正常創建新排程
- 提升了用戶體驗
- 解決了應用程式的關鍵bug

### 潛在風險
- 主題修改影響全應用程式，但經測試沒有發現負面影響
- 自定義toolbar在所有Activity中都可以正常使用

## 技術細節

### 相關文件
- `app/src/main/res/values/themes.xml` - 主題配置
- `app/src/main/java/com/example/autolaunch/AddEditScheduleActivity.kt` - 新增排程Activity
- `app/src/main/java/com/example/autolaunch/MainActivity.kt` - 主Activity

### 關鍵代碼
```kotlin
// AddEditScheduleActivity.kt - setupUI()方法
setSupportActionBar(binding.toolbar)
supportActionBar?.setDisplayHomeAsUpEnabled(true)
```

## 學習要點

1. **主題配置的重要性**：Material Design主題的默認配置可能與自定義UI組件產生衝突
2. **錯誤日誌分析**：logcat提供了明確的錯誤信息和解決建議
3. **測試的重要性**：核心功能必須經過充分測試確保正常工作

## 後續建議

1. 建立更完善的測試流程，確保核心功能在每次構建後都能正常工作
2. 考慮添加自動化測試來檢測此類UI初始化問題
3. 在開發過程中更頻繁地檢查logcat輸出

---

**修復完成時間**: 2025-06-03 20:23:41  
**修復狀態**: ✅ 已完成並測試通過 