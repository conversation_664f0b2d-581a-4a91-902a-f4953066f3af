# PRD - 排程保存邏輯與鬧鐘服務實作

**建立時間:** 2025-06-03 00:34:41

## 需求討論

本次實作涵蓋了 Android AutoLaunch 專案中三個核心任務的完整功能：

### TASK_012: 排程數據保存/更新邏輯
- **目標:** 實現新增/編輯排程介面中的"保存"按鈕功能
- **實作內容:**
  - 完善 `AddEditScheduleActivity` 的保存功能
  - 實現輸入驗證（應用程式選擇、日期選擇、星期選擇）
  - 新增模式：插入新排程到資料庫並設定鬧鐘
  - 編輯模式：更新現有排程並更新鬧鐘設定
  - 增加從資料庫載入現有排程資料的功能

### TASK_013: AlarmManager 核心調度服務
- **目標:** 實現應用程式的核心排程機制
- **實作內容:**
  - 創建 `AlarmManagerService` 類別
  - 提供設定鬧鐘功能：`setAlarm(schedule: Schedule)`
  - 提供取消鬧鐘功能：`cancelAlarm(scheduleId: Long)`
  - 提供更新鬧鐘功能：`updateAlarm(schedule: Schedule)`
  - 智能計算下次執行時間（支援單次、每日、每週模式）
  - 處理 Android 6.0+ 的休眠模式兼容性
  - 處理 Android 12+ 的精確鬧鐘權限要求

### TASK_014: APP 啟動廣播接收器與邏輯
- **目標:** 創建接收 AlarmManager 觸發廣播並啟動目標應用程式的接收器
- **實作內容:**
  - 完善 `LaunchReceiver` 類別
  - 實現 WakeLock 機制確保啟動過程中 CPU 不休眠
  - 使用 `goAsync()` 和協程處理後台操作
  - 實現應用程式啟動邏輯
  - 自動更新最後執行時間
  - 為重複排程自動設定下次鬧鐘
  - 單次排程執行後自動禁用

## 技術實作細節

### 1. AlarmManagerService 核心功能
```kotlin
class AlarmManagerService(private val context: Context) {
    fun setAlarm(schedule: Schedule)      // 設定鬧鐘
    fun cancelAlarm(scheduleId: Long)     // 取消鬧鐘
    fun updateAlarm(schedule: Schedule)   // 更新鬧鐘
}
```

### 2. 時間計算邏輯
- **單次模式:** 結合選定日期和時間
- **每日模式:** 如果當日時間已過則設定明天
- **每週模式:** 找到下一個符合條件的星期幾

### 3. 權限處理
- 支援 Android 12+ 的 `SCHEDULE_EXACT_ALARM` 權限
- 降級處理：當精確鬧鐘權限被拒絕時使用非精確鬧鐘

### 4. 接收器優化
- 使用 `PowerManager.PARTIAL_WAKE_LOCK` 保持 CPU 喚醒
- 協程處理資料庫操作避免阻塞主線程
- 自動管理重複排程的下次觸發

## 已配置權限

在 `AndroidManifest.xml` 中已配置所需權限：
- `SCHEDULE_EXACT_ALARM` - 精確鬧鐘權限
- `USE_EXACT_ALARM` - 使用鬧鐘服務
- `WAKE_LOCK` - 喚醒鎖定權限
- `RECEIVE_BOOT_COMPLETED` - 開機廣播接收

## 驗收標準達成情況

### TASK_012 ✅
- [x] 從介面收集所有輸入數據
- [x] 正確組合成 Schedule 實例
- [x] 判斷新增/編輯模式並執行對應操作
- [x] 保存後關閉介面並返回主介面
- [x] 呼叫 AlarmManager 設定鬧鐘
- [x] 基本輸入驗證

### TASK_013 ✅
- [x] 創建 AlarmManager 封裝類別
- [x] 實現 setAlarm 方法
- [x] 實現 cancelAlarm 方法
- [x] 實現 updateAlarm 方法
- [x] 處理 AlarmManager 權限問題
- [x] 可被 TASK_012 呼叫

### TASK_014 ✅
- [x] 創建 BroadcastReceiver 類別
- [x] 在 AndroidManifest.xml 註冊接收器
- [x] 正確接收 AlarmManager 廣播
- [x] 從 Intent 提取排程資訊
- [x] 啟動目標應用程式
- [x] 更新最後執行時間
- [x] 處理啟動失敗情況

## 技術亮點

1. **完整的生命週期管理:** 從排程創建到執行的完整流程
2. **Android 版本兼容性:** 支援不同 Android 版本的 AlarmManager API
3. **電源管理優化:** 使用 WakeLock 確保排程可靠執行
4. **協程架構:** 所有資料庫操作都在後台線程執行
5. **自動化管理:** 重複排程自動設定下次觸發，單次排程自動禁用

## 下一步計畫

這三個任務的完成為 Android AutoLaunch 專案奠定了核心的排程執行基礎。接下來可以繼續實作：
- TASK_015: 設備重啟後重新註冊排程
- TASK_016: 排程啟用/禁用功能
- TASK_017: 排程刪除功能

## 測試建議

1. 測試不同重複模式的排程創建和執行
2. 測試應用程式啟動失敗的處理
3. 測試設備重啟後的排程恢復
4. 測試電池優化設定對排程執行的影響 