#!/bin/bash

# 完整多語言功能測試腳本

echo "🌍 AutoLaunch 完整多語言功能測試"
echo "=================================="
echo ""

# 檢查是否有連接的設備
if ! adb devices | grep -q "device$"; then
    echo "❌ 錯誤：未找到連接的 Android 設備"
    echo "請確保："
    echo "1. Android 設備已連接並開啟 USB 調試"
    echo "2. 設備已授權此電腦進行調試"
    exit 1
fi

echo "📱 檢測到 Android 設備"
echo ""

# 編譯並安裝應用
echo "🔨 編譯並安裝應用..."
if ! ./gradlew assembleDebug; then
    echo "❌ 編譯失敗"
    exit 1
fi

if ! adb install -r app/build/outputs/apk/debug/app-debug.apk; then
    echo "❌ 安裝失敗"
    exit 1
fi

echo "✅ 應用安裝成功"
echo ""

# 啟動應用
echo "🚀 啟動 AutoLaunch 應用..."
adb shell am start -n com.example.autolaunch/.MainActivity
sleep 3

echo ""
echo "📋 完整多語言功能測試清單："
echo "============================="
echo ""

echo "🏠 主頁面測試："
echo "---------------"
echo "□ 主頁面標題顯示正確"
echo "□ 歡迎訊息文字正確翻譯"
echo "□ 空狀態文字正確翻譯"
echo "□ 新增排程按鈕文字正確"
echo "□ 選單按鈕描述正確"
echo "□ 排程分類標題（應用程式/網頁）正確翻譯"
echo "□ 側邊欄選單項目全部翻譯"
echo "□ 運行狀態指示文字正確"
echo ""

echo "➕ 新增排程頁面測試："
echo "-------------------"
echo "□ 頁面標題「新增排程」正確翻譯"
echo "□ 排程設定區塊標題正確"
echo "□ 「啟動應用程式」按鈕文字正確"
echo "□ 「開啟網址」按鈕文字正確"
echo "□ 「設定時間」標題正確"
echo "□ 「執行時間」標籤正確"
echo "□ 「重複設定」標題正確"
echo "□ 「單次」「每日」「每週」按鈕文字正確"
echo "□ 星期幾標籤正確翻譯"
echo "□ 「儲存」「取消」按鈕文字正確"
echo ""

echo "🌍 語言設定頁面測試："
echo "-------------------"
echo "□ 語言設定頁面標題正確"
echo "□ 語言選項列表顯示正確"
echo "□ 重啟對話框文字正確"
echo "□ 「立即重啟」「稍後重啟」按鈕文字正確"
echo ""

echo "🔄 語言切換測試："
echo "---------------"
echo "□ 中文 → 英文切換正常"
echo "□ 英文 → 日文切換正常"
echo "□ 日文 → 韓文切換正常"
echo "□ 韓文 → 中文切換正常"
echo "□ 跟隨系統語言功能正常"
echo ""

echo "📱 手動測試步驟："
echo "================="
echo ""
echo "1. 🏠 主頁面測試"
echo "   - 檢查主頁面所有文字是否正確翻譯"
echo "   - 驗證歡迎訊息根據權限狀態顯示不同文字"
echo "   - 檢查排程分類標題（應用程式/網頁）"
echo "   - 測試側邊欄選單所有項目翻譯"
echo ""
echo "2. ➕ 新增排程頁面測試"
echo "   - 點擊新增排程按鈕"
echo "   - 檢查所有標題、按鈕、標籤文字"
echo "   - 測試排程類型切換"
echo "   - 測試重複模式選擇"
echo ""
echo "3. 🌍 語言設定測試"
echo "   - 打開側邊欄選單"
echo "   - 點擊「語言設定」"
echo "   - 檢查語言選項列表"
echo "   - 測試語言切換功能"
echo ""
echo "4. 🔄 語言切換測試"
echo "   - 依序測試每種語言"
echo "   - 驗證重啟對話框功能"
echo "   - 確認語言變更後所有頁面都正確翻譯"
echo ""

echo "🧪 各語言測試重點："
echo "=================="
echo ""
echo "📝 中文 (預設)："
echo "- 新增排程 → 排程設定 → 啟動應用程式/開啟網址"
echo "- 設定時間 → 執行時間 → 重複設定"
echo "- 單次/每日/每週 → 週一~週日"
echo ""
echo "🇺🇸 English："
echo "- Add Schedule → Schedule Settings → Launch App/Open URL"
echo "- Time Settings → Execution Time → Repeat Settings"
echo "- Once/Daily/Weekly → Mon~Sun"
echo ""
echo "🇯🇵 日本語："
echo "- スケジュール追加 → スケジュール設定 → アプリ起動/URL開く"
echo "- 時間設定 → 実行時間 → 繰り返し設定"
echo "- 単次/毎日/毎週 → 月~日"
echo ""
echo "🇰🇷 한국어："
echo "- 일정 추가 → 일정 설정 → 앱 실행/URL 열기"
echo "- 시간 설정 → 실행 시간 → 반복 설정"
echo "- 단차/매일/매주 → 월~일"
echo ""

echo "⚠️  注意事項："
echo "============="
echo "- 語言切換後建議重啟應用以確保完全生效"
echo "- 檢查所有頁面的文字是否完整翻譯"
echo "- 驗證 UI 布局在不同語言下是否正常"
echo "- 確認日期時間格式符合各語言習慣"
echo ""

echo "🔍 如需查看詳細日誌，請執行："
echo "adb logcat | grep -E '(AutoLaunch|LanguageManager|LanguageSettings)'"
echo ""

echo "✅ 測試準備完成！請按照上述步驟進行手動測試。"
