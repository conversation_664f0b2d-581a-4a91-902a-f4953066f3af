# 網頁排程圖標優化方案

## 問題描述

首頁網頁排程在無法獲取網站 favicon 時，會顯示一個不太美觀的預設連結圖標 (`ic_link_24`)，用戶體驗不佳。

## 解決方案

創建了一個智能的文字圖標生成系統，當無法獲取網站 favicon 時，自動生成美觀的文字圖標。

### 1. 文字圖標生成器 (TextIconGenerator)

#### 核心功能：
- **智能文字提取**: 從 URL 中提取有意義的縮寫
- **美觀的視覺設計**: 使用漸層背景和圓角設計
- **顏色一致性**: 相同網站始終使用相同顏色
- **多種樣式**: 支援圓形和圓角矩形兩種樣式

#### 文字提取邏輯：
```kotlin
// 範例輸出
"https://www.google.com" → "GO"
"https://github.com" → "GI" 
"https://stackoverflow.com" → "ST"
"https://www.facebook.com" → "FA"
"https://www.baidu.com" → "BA"
```

### 2. 視覺設計

#### 漸層背景 (bg_web_icon.xml)
```xml
<gradient
    android:startColor="#4285F4"
    android:endColor="#1976D2"
    android:angle="135" />
```

#### 設計特點：
- **圓角矩形**: 8dp 圓角，現代化外觀
- **藍色漸層**: 使用 Google Material Design 藍色系
- **白色文字**: 高對比度，易於閱讀
- **適當大小**: 文字大小為圖標的 35%

### 3. 實現方式

#### 在適配器中的使用：
```kotlin
ScheduleType.URL -> {
    val faviconUrl = UrlUtils.generateFaviconUrl(schedule.url)
    if (!faviconUrl.isNullOrEmpty()) {
        // 嘗試載入 favicon，失敗時使用文字圖標
        val fallbackIcon = TextIconGenerator.generateWebIcon(itemView.context, schedule.url)
        Glide.with(itemView.context)
            .load(faviconUrl)
            .placeholder(fallbackIcon)
            .error(fallbackIcon)
            .fallback(fallbackIcon)
            .into(ivAppIcon)
    } else {
        // 直接使用文字圖標
        val textIcon = TextIconGenerator.generateWebIcon(itemView.context, schedule.url)
        ivAppIcon.setImageDrawable(textIcon)
    }
}
```

### 4. 智能文字提取算法

#### 處理流程：
1. **URL 清理**: 移除協議 (`https://`) 和 `www.` 前綴
2. **域名分析**: 分割域名各部分
3. **縮寫生成**: 
   - 多部分域名：取前兩部分的首字母
   - 單部分域名：取前兩個字符
4. **異常處理**: 無效 URL 時顯示 "WEB"

#### 特殊處理：
- **子域名**: `mail.google.com` → "MA"
- **國家域名**: `google.com.tw` → "GO"
- **移動版**: `m.facebook.com` → "FA"
- **無效 URL**: 任何無效輸入 → "WEB"

### 5. 顏色系統

#### 顏色調色板：
```kotlin
val colors = arrayOf(
    0xFF4285F4.toInt(), // Google Blue
    0xFF34A853.toInt(), // Google Green  
    0xFFEA4335.toInt(), // Google Red
    0xFFFBBC05.toInt(), // Google Yellow
    0xFF9C27B0.toInt(), // Purple
    0xFF00BCD4.toInt(), // Cyan
    0xFFFF9800.toInt(), // Orange
    0xFF795548.toInt()  // Brown
)
```

#### 顏色分配：
- 使用文字內容的 hash 值來確定顏色
- 確保相同網站始終使用相同顏色
- 提供 8 種 Material Design 顏色選擇

### 6. 兩種圖標樣式

#### 樣式 1: 圓角矩形 (預設)
- 使用 `bg_web_icon.xml` 背景
- 藍色漸層
- 8dp 圓角

#### 樣式 2: 圓形
- 動態生成圓形背景
- 根據網站內容選擇顏色
- 更加簡潔的設計

### 7. 使用範例

#### 常見網站的顯示效果：
- **Google**: 藍色背景，白色 "GO" 文字
- **GitHub**: 綠色背景，白色 "GI" 文字  
- **Facebook**: 紅色背景，白色 "FA" 文字
- **百度**: 黃色背景，白色 "BA" 文字
- **未知網站**: 藍色背景，白色 "WEB" 文字

### 8. 優勢

1. **更好的視覺體驗**: 比預設圖標更美觀
2. **智能識別**: 自動從 URL 提取有意義的縮寫
3. **一致性**: 相同網站始終顯示相同圖標
4. **性能優化**: 本地生成，無需網路請求
5. **容錯性**: 處理各種邊界情況
6. **可擴展性**: 易於添加新的樣式和顏色

### 9. 測試覆蓋

創建了完整的單元測試 (`TextIconGeneratorTest`)：
- 常見網站 URL 測試
- 特殊情況處理測試
- 中文網站測試
- 顏色生成一致性測試
- 邊界情況測試

這個解決方案完全替代了原來不美觀的預設圖標，為用戶提供了更好的視覺體驗和更直觀的網站識別。
