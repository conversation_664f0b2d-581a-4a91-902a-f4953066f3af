#!/bin/bash

# AutoLaunch 主題工具列可見性修復測試腳本
# 驗證在所有主題下，工具列和返回鍵都能正常顯示

echo "🎨 AutoLaunch 主題工具列可見性測試"
echo "=================================="

# 檢查編譯是否成功
echo ""
echo "📦 檢查應用程式編譯..."
./gradlew app:assembleDebug --quiet
if [ $? -eq 0 ]; then
    echo "✅ 應用程式編譯成功"
else
    echo "❌ 應用程式編譯失敗"
    exit 1
fi

# 檢查修復內容
echo ""
echo "🔍 檢查主題工具列修復..."

# 檢查是否移除了所有硬編碼的深色主題覆蓋
echo "檢查硬編碼主題覆蓋移除情況："
if ! grep -r "ThemeOverlay.Material3.Dark.ActionBar" app/src/main/res/ > /dev/null 2>&1; then
    echo "✅ 所有硬編碼深色主題覆蓋已移除"
else
    echo "❌ 仍有硬編碼深色主題覆蓋存在："
    grep -r "ThemeOverlay.Material3.Dark.ActionBar" app/src/main/res/
fi

# 檢查修復的文件
echo ""
echo "檢查已修復的文件："

files=(
    "app/src/main/res/layout/activity_system_log.xml"
    "app/src/main/res/layout/activity_about.xml"
    "app/src/main/res/layout/activity_tutorial.xml"
    "app/src/main/res/layout/activity_update_history.xml"
    "app/src/main/res/layout/activity_qa.xml"
    "app/src/main/res/layout/activity_backup_restore.xml"
    "app/src/main/res/layout/activity_backup_restore_new.xml"
)

for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        # 檢查是否使用了正確的主題屬性
        if grep -q "android:background=\"?attr/colorPrimary\"" "$file" && 
           grep -q "app:titleTextColor=\"?attr/colorOnPrimary\"" "$file" &&
           grep -q "app:navigationIconTint=\"?attr/colorOnPrimary\"" "$file"; then
            echo "✅ $file - 正確使用主題屬性"
        else
            echo "⚠️  $file - 可能需要檢查主題屬性配置"
        fi
    else
        echo "❌ $file - 文件不存在"
    fi
done

# 檢查主題配置
echo ""
echo "🎨 檢查主題配置..."

# 檢查經典淺色主題配置
if grep -q "Theme_AutoLaunch_Light_Classic" app/src/main/res/values/themes_variants.xml; then
    echo "✅ 經典淺色主題配置存在"
else
    echo "❌ 經典淺色主題配置缺失"
fi

# 檢查顏色資源
if grep -q "md_theme_light_primary" app/src/main/res/values/colors.xml; then
    echo "✅ 淺色主題顏色資源存在"
else
    echo "❌ 淺色主題顏色資源缺失"
fi

echo ""
echo "🎯 修復驗證完成！"
echo ""
echo "修復內容："
echo "- ✅ 移除所有硬編碼的深色主題覆蓋"
echo "- ✅ 使用動態主題屬性 (?attr/colorPrimary, ?attr/colorOnPrimary)"
echo "- ✅ 確保工具列文字和圖標顏色正確"
echo "- ✅ 修復系統紀錄、關於、教學、更新紀錄、Q&A、備份頁面"
echo ""
echo "現在在所有主題下，工具列和返回鍵都應該正常顯示！"
echo ""
echo "📋 測試建議："
echo "1. 切換到「經典淺色」主題"
echo "2. 進入「系統紀錄」頁面"
echo "3. 確認工具列標題和返回鍵清晰可見"
echo "4. 測試其他頁面（關於、教學、更新紀錄、Q&A、備份）"
echo "5. 在不同主題間切換測試"
