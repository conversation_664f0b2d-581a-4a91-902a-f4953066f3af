
# Landing Page: Android AutoLaunch

這份文件整理了 Android AutoLaunch 專案 landing page 製作所需的文案、圖示、配色等資料。

## 1. 專案標題與標語 (Project Title & Tagline)

### 主標題
- **Android AutoLaunch**

### 副標題/標語
- **您的智慧應用啟動排程工具**
- **根據您的時間需求，精確控制應用程式啟動時機**

### 描述文案
- 一個 Android 應用程式，讓用戶可以在指定的時間和日期自動開啟其他應用程式
- 支援應用程式和網頁排程，提供多語言介面，讓您在指定時間自動開啟所需的應用程式

## 2. 行動呼籲 (Call to Action - CTA)

### 主要 CTA
- **按鈕文字**: "Get it on Google Play"
- **連結**: (待補充 Google Play Store 連結)

### 次要 CTA
- **按鈕文字**: "Download on the App Store" (如有 iOS 版本)
- **GitHub**: "View on GitHub"

## 3. 核心功能 (Core Features)

參考 gobag.tw 的三大核心功能設計，以下為 AutoLaunch 的核心功能：

### 三大核心功能

| 功能名稱 | 圖示建議 | 說明文案 | 詳細描述 |
|---------|---------|---------|---------|
| **智能排程** | 🕐 時鐘/鬧鐘圖示 | 精確的時間控制，讓您的排程從不失誤 | 支援到分鐘級的精確時間控制，單次執行、每日、每週或自訂重複模式，完全符合您的需求 |
| **雙重啟動** | 🔗 連結/火箭圖示 | 不僅能啟動應用程式，還能開啟指定網頁 | 支援應用程式和網頁URL排程，一個工具雙重功能，滿足您的多元需求 |
| **智慧管理** | ⚙️ 齒輪/設定圖示 | 完整的數據管理與多語言支援 | 本地備份、Google Drive 雲端同步、系統紀錄追蹤，支援中文、English、日本語、한국어 |

### 完整功能列表

| 功能分類 | 功能名稱 | 說明 | 圖示建議 |
|---------|---------|-----|---------|
| **核心排程** | 智能排程 | 精確的時間設定（小時、分鐘選擇器） | ⏰ |
| **核心排程** | 靈活重複 | 單次執行、每日、每週、自訂週期 | 🔄 |
| **啟動功能** | 雙重啟動 | 支援應用程式和網頁URL排程 | 🚀 |
| **用戶體驗** | 直觀管理 | 拖拽排序、滑動刪除、長按編輯 | 📱 |
| **國際化** | 完整多語言 | 支援中文、English、日本語、한국어 | 🌍 |
| **個性化** | 豐富主題 | 淺色/深色模式，各3種主題變體，自動跟隨系統 | 🎨 |
| **數據管理** | 備份同步 | 安全的本地備份與 Google Drive 雲端同步 | ☁️ |
| **系統功能** | 系統紀錄 | 7天日誌輪替，完整追蹤操作記錄 | 📋 |

## 4. 常見問題 (FAQ)

### Q1: 這個 App 安全嗎？需要哪些權限？
**A1:** 絕對安全。為了實現核心功能，App 需要 `查詢已安裝應用`、`開機後重新註冊排程`、和 `設定精確鬧鐘` 等權限。所有權限都僅用於預定功能，我們絕不收集您的個人數據。

### Q2: 在我的手機上，排程有時候不會準時執行？
**A2:** 這通常是電池優化設定造成的。請到系統設定中，將「Android AutoLaunch」加入電池優化白名單，以確保系統不會為了省電而終止背景排程服務。我們的 App 已經針對各大廠商的省電機制進行優化。

### Q3: 支援哪些語言？
**A3:** 目前支援中文 (繁體)、English、日本語、한국어。我們會根據您的系統語言自動切換介面，您也可以在 App 內手動選擇偏好的語言。

### Q4: App 完全免費嗎？
**A4:** 是的，「Android AutoLaunch」完全免費，且沒有任何廣告。我們相信好的工具應該讓每個人都能輕鬆使用，不會有隱藏費用或付費牆。

### Q5: 支援哪些 Android 版本？
**A5:** 支援 Android 8.0 (API 26) 以上版本，涵蓋市面上絕大多數的 Android 裝置。

## 5. 視覺素材 (Visual Assets)

### 應用程式圖示 (App Icon)
- **主要圖示**: `app/src/main/res/mipmap-xxxhdpi/ic_launcher.webp`
- **設計概念**: 軌道發射設計，漸層色彩圓形軌道（藍到綠），簡約白色火箭沿切線飛行並帶有速度線
- **備用圖示**: 各解析度版本位於 `app/src/main/res/mipmap-*` 目錄

### 配色方案 (Color Palette)

#### 主要配色（淺色主題）
- **Primary**: `#5B7C99` (溫和藍灰)
- **On Primary**: `#FFFFFF` (白色文字)
- **Secondary**: `#6B7280` (中性灰)
- **Background**: `#F7F9FA` (淺灰背景)
- **Surface**: `#FAFBFC` (表面色)
- **On Surface**: `#2D3748` (深灰文字)

#### 主題變體配色
- **溫暖主題**: `#D2691E` (橘棕色)
- **清涼主題**: `#4682B4` (鋼藍色)
- **粉紅主題**: `#E91E63` (粉紅色)
- **陽光主題**: `#F59E0B` (金黃色)
- **薄荷主題**: `#10B981` (薄荷綠)
- **薰衣草主題**: `#8B5CF6` (紫色)

#### 深色主題配色
- **深藍主題**: `#82B1FF` (亮藍)
- **深綠主題**: `#69F0AE` (亮綠)
- **深紫主題**: `#CE93D8` (亮紫)

#### 狀態顏色
- **成功色**: `#22C55E` (綠色)
- **錯誤色**: `#EF4444` (紅色)
- **警告色**: `#F59E0B` (橘色)
- **資訊色**: `#3B82F6` (藍色)

## 6. 頁尾資訊 (Footer)

### 版權資訊
- © 2025 Android AutoLaunch

### 連結
- 隱私權政策 (Privacy Policy)
- 聯絡我們 (Contact Us)
- GitHub (專案連結)
- 使用教學
- 版本更新

### 聯絡資訊
- Email: <EMAIL> (範例)

## 7. 技術規格 (Technical Specs)

### 系統需求
- **最低版本**: Android 8.0 (API 26)
- **目標版本**: Android 14 (API 35)
- **開發語言**: Kotlin
- **UI 框架**: Material Design 3

### 權限需求
- `QUERY_ALL_PACKAGES`: 查詢已安裝的應用程式
- `RECEIVE_BOOT_COMPLETED`: 開機後重新註冊排程
- `SCHEDULE_EXACT_ALARM`: 設定精確的鬧鐘
- `USE_EXACT_ALARM`: 使用精確鬧鐘服務
- `REQUEST_IGNORE_BATTERY_OPTIMIZATIONS`: 電池優化白名單