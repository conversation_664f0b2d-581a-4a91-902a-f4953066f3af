# AutoLaunch 系統日志功能使用指南

## 📋 功能概述

AutoLaunch 应用现在包含了完整的系统日志功能，可以记录和查看应用的各种操作和事件，帮助用户了解应用的运行状态和排查问题。

## 🚀 如何访问系统日志

1. 打开 AutoLaunch 应用
2. 点击左上角的菜单按钮（三条横线）
3. 在侧边栏菜单中选择 **"系統紀錄"**

## 📊 日志类型说明

### 日志级别
- **🟢 成功 (SUCCESS)**: 操作成功完成
- **🔵 资讯 (INFO)**: 一般信息记录
- **🟡 警告 (WARNING)**: 需要注意的情况
- **🔴 错误 (ERROR)**: 操作失败或异常

### 操作类型
- **排程建立**: 创建新的排程时记录
- **排程修改**: 修改现有排程时记录
- **排程删除**: 删除排程时记录
- **排程执行**: 排程触发执行时记录
- **排程启用/停用**: 启用或停用排程时记录
- **应用启动**: 应用程序启动时记录
- **权限授予/拒绝**: 权限变更时记录
- **系统错误**: 系统异常时记录

## 🔍 日志查看功能

### 基本查看
- 日志按日期分组显示
- 每条日志显示时间、类型、操作和消息
- 点击日志条目可查看详细信息

### 统计信息
- 顶部显示日志统计：总计、成功、警告、错误数量
- 实时更新统计数据

### 筛选功能
点击工具栏的筛选按钮，可以按以下条件筛选：
- **全部日志**: 显示所有日志
- **成功日志**: 只显示成功操作
- **资讯日志**: 只显示一般信息
- **警告日志**: 只显示警告信息
- **错误日志**: 只显示错误信息
- **排程相关**: 只显示与排程相关的日志
- **今天的日志**: 只显示今天的日志

### 搜索功能
1. 点击工具栏的搜索按钮
2. 输入关键字（可搜索消息内容和排程名称）
3. 点击"搜索"查看结果
4. 点击"清除搜索"返回全部日志

## 📤 日志导出功能

1. 点击菜单中的 **"导出日志"**
2. 系统会生成包含最近100条日志的文本文件
3. 可以通过分享功能发送给其他应用或保存到文件

导出的日志包含：
- 导出时间
- 日志数量
- 每条日志的详细信息（时间、类型、操作、消息、详情）

## 🗑️ 日志管理

### 自动清理
- 系统会自动删除7天前的旧日志
- 防止数据库过大影响性能

### 手动清理
1. 点击菜单中的 **"清除日志"**
2. 确认删除所有日志
3. **注意**: 此操作无法撤销

## 🔧 故障排查

### 如果看不到日志
1. 确保应用已正常启动
2. 尝试执行一些操作（如创建排程）来生成日志
3. 检查是否有筛选条件限制了显示

### 如果日志显示异常
1. 尝试下拉刷新日志列表
2. 重启应用
3. 如果问题持续，可以导出日志进行分析

### 数据库迁移问题
如果遇到数据库迁移错误：
1. 应用会自动处理迁移失败的情况
2. 在极端情况下，可能需要重新安装应用
3. 重新安装会清除所有数据，请谨慎操作

## 💡 使用建议

1. **定期查看日志**: 了解应用运行状态
2. **关注错误日志**: 及时发现和解决问题
3. **使用搜索功能**: 快速找到特定事件的日志
4. **导出重要日志**: 保存重要的操作记录
5. **适时清理日志**: 避免占用过多存储空间

## 🔒 隐私说明

- 系统日志仅存储在本地设备上
- 不会自动上传到任何服务器
- 导出功能由用户主动触发
- 日志内容不包含敏感个人信息

## 📞 技术支持

如果在使用系统日志功能时遇到问题：
1. 首先查看错误日志了解具体问题
2. 尝试导出日志进行分析
3. 联系开发者并提供相关日志信息

---

**注意**: 系统日志功能主要用于调试和监控，建议开发者和高级用户使用。普通用户可以通过查看日志了解应用的运行状态。
