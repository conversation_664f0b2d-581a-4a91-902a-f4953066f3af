#!/bin/bash

echo "=== AutoLaunch 系统日志功能测试 ==="

# 清理日志
adb logcat -c

echo "1. 启动应用..."
adb shell am force-stop com.example.autolaunch
adb shell am start -n com.example.autolaunch/.MainActivity

echo "2. 等待应用启动..."
sleep 3

echo "3. 检查系统日志功能..."
# 启动日志监控
adb logcat -s "DatabaseTestHelper,SystemLogManager,ScheduleService,MainActivity" -v time > test_log.txt &
LOG_PID=$!

echo "4. 等待日志记录..."
sleep 5

echo "5. 停止日志监控..."
kill $LOG_PID

echo "6. 分析日志结果..."
if grep -q "数据库迁移验证成功" test_log.txt; then
    echo "✅ 数据库迁移成功"
else
    echo "❌ 数据库迁移失败"
fi

if grep -q "Log recorded: 應用程式啟動" test_log.txt; then
    echo "✅ 系统日志记录成功"
else
    echo "❌ 系统日志记录失败"
fi

if grep -q "ScheduleService started" test_log.txt; then
    echo "✅ 排程服务启动成功"
else
    echo "❌ 排程服务启动失败"
fi

if grep -q "Monitoring.*active schedules" test_log.txt; then
    echo "✅ 排程监控正常"
else
    echo "❌ 排程监控异常"
fi

echo ""
echo "=== 完整日志内容 ==="
cat test_log.txt

echo ""
echo "=== 测试完成 ==="

# 清理临时文件
rm -f test_log.txt
