#!/bin/bash

echo "=== 测试排程修改日志功能 ==="

echo "1. 启动应用..."
adb shell am start -n com.example.autolaunch/.MainActivity

echo "2. 等待应用启动..."
sleep 3

echo "3. 现在请手动执行以下操作来测试日志功能："
echo "   - 点击任意一个排程进入编辑页面"
echo "   - 修改排程的时间（例如从09:00改为10:00）"
echo "   - 修改排程的标题"
echo "   - 修改排程的重复模式"
echo "   - 保存修改"
echo ""
echo "4. 然后查看系统日志页面："
echo "   - 点击左上角菜单"
echo "   - 选择'系統紀錄'"
echo "   - 查看最新的修改日志是否包含详细信息"
echo ""
echo "5. 按任意键停止日志监控..."
read -n 1 -s

echo ""
echo "=== 测试完成 ==="
