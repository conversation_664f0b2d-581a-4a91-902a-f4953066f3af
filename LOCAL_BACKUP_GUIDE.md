# AutoLaunch 本地備份功能使用指南

## ✅ 重要說明：本地備份完全獨立，無需 Google 帳戶

AutoLaunch 的本地備份功能是**完全獨立**的，您可以在**不登入 Google 帳戶**的情況下正常使用所有本地備份功能。

## 📱 本地備份功能

### 🔧 功能特點
- ✅ **無需網路連接**：完全離線操作
- ✅ **無需 Google 帳戶**：不依賴任何雲端服務
- ✅ **自動管理**：自動清理舊備份文件
- ✅ **數據完整**：包含所有排程設定和配置
- ✅ **格式通用**：使用標準 JSON 格式

### 📂 備份位置
備份文件自動保存到：
```
/storage/emulated/0/Download/AutoLaunch/
```

### 🎯 使用步驟

#### 1. 創建本地備份
1. 打開 AutoLaunch 應用
2. 點擊側邊欄菜單 ☰
3. 選擇「備份與匯入」
4. 點擊「本地備份」按鈕
5. 系統會自動創建備份文件並顯示成功訊息

#### 2. 匯入本地備份
1. 在「備份與匯入」頁面
2. 點擊「從文件匯入」按鈕
3. 選擇要恢復的備份文件
4. 確認匯入操作
5. 系統會顯示匯入結果

#### 3. 管理本地備份
1. 點擊「本地備份」管理按鈕
2. 查看所有備份文件列表
3. 可以選擇恢復或刪除特定備份

### 📋 備份內容

每個備份文件包含：
- ✅ 所有排程任務（應用程式和網頁）
- ✅ 排程時間設定
- ✅ 重複模式配置
- ✅ 啟用/禁用狀態
- ✅ 創建和修改時間
- ✅ 設備信息（用於兼容性檢查）

### 🔒 權限需求

本地備份功能只需要：
- **存儲權限**：用於讀寫備份文件

**不需要**：
- ❌ 網路權限（本地操作）
- ❌ Google 帳戶權限
- ❌ 雲端服務權限

### 📄 備份文件格式

備份文件使用 JSON 格式，文件名格式：
```
AutoLaunch_Backup_YYYYMMDD_HHMMSS.json
```

例如：`AutoLaunch_Backup_20241227_143022.json`

### 🛡️ 數據安全

- **本地存儲**：數據完全保存在您的設備上
- **無雲端傳輸**：不會上傳到任何伺服器
- **格式開放**：使用標準 JSON 格式，可讀性強
- **版本兼容**：支持不同版本間的數據遷移

### 🔄 自動管理

系統會自動：
- 保留最新 10 個備份文件
- 清理過舊的備份文件
- 驗證備份文件完整性
- 提供詳細的操作反饋

### ⚠️ 注意事項

1. **權限確認**：首次使用時需要授予存儲權限
2. **存儲空間**：確保設備有足夠的存儲空間
3. **文件位置**：備份文件保存在 Downloads/AutoLaunch 目錄
4. **數據備份**：建議定期創建備份以防數據丟失

### 🆚 本地 vs 雲端備份

| 功能 | 本地備份 | 雲端備份 |
|------|----------|----------|
| 需要網路 | ❌ 不需要 | ✅ 需要 |
| 需要 Google 帳戶 | ❌ 不需要 | ✅ 需要 |
| 跨設備同步 | ❌ 不支持 | ✅ 支持 |
| 數據隱私 | ✅ 完全本地 | ⚠️ 存儲在雲端 |
| 操作速度 | ✅ 快速 | ⚠️ 依賴網路 |
| 存儲限制 | ⚠️ 設備空間 | ✅ 雲端空間 |

### 🎉 總結

AutoLaunch 的本地備份功能為您提供了一個**簡單、安全、獨立**的數據備份解決方案。您可以：

- 🚀 **立即使用**：無需任何額外設定
- 🔒 **保護隱私**：數據完全保存在本地
- 💾 **輕鬆管理**：直觀的操作界面
- 🛡️ **安全可靠**：完整的數據驗證機制

**開始使用本地備份功能，保護您的重要排程設定！**
