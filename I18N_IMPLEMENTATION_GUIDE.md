# AutoLaunch i18n 多國語系實作指南

## 概述

本文檔說明 AutoLaunch 應用程式的多國語系（i18n）功能實作，支援中文、英文、日文、韓文四種語言，並提供跟隨系統語言的選項。

## 功能特色

### 支援的語言
- 🇹🇼 中文（繁體）- 預設語言
- 🇺🇸 English - 英文
- 🇯🇵 日本語 - 日文  
- 🇰🇷 한국어 - 韓文
- 🔄 跟隨系統 - 自動跟隨系統語言設定

### 主要功能
- ✅ 語言設定頁面，提供直觀的語言選擇界面
- ✅ 即時語言切換，無需重新安裝應用程式
- ✅ 語言設定持久化，重新啟動後保持選擇
- ✅ 系統語言檢測，自動適配用戶系統語言
- ✅ 所有 Activity 自動應用語言設定

## 實作架構

### 核心組件

#### 1. LanguageManager
位置：`app/src/main/java/com/example/autolaunch/utils/LanguageManager.kt`

負責語言管理的核心類別，提供以下功能：
- 語言設定的儲存和讀取
- 系統語言檢測
- 語言設定應用到 Context
- 支援語言列表管理

```kotlin
// 主要方法
LanguageManager.getCurrentLanguage(context)     // 獲取當前語言
LanguageManager.setLanguage(context, code)      // 設定語言
LanguageManager.applyLanguage(context)          // 應用語言到 Context
LanguageManager.getSystemLanguage()             // 獲取系統語言
```

#### 2. BaseActivity
位置：`app/src/main/java/com/example/autolaunch/base/BaseActivity.kt`

基礎 Activity 類別，自動為所有繼承的 Activity 應用語言設定：

```kotlin
abstract class BaseActivity : AppCompatActivity() {
    override fun attachBaseContext(newBase: Context?) {
        super.attachBaseContext(newBase?.let { LanguageManager.applyLanguage(it) })
    }
}
```

#### 3. LanguageSettingsActivity
位置：`app/src/main/java/com/example/autolaunch/LanguageSettingsActivity.kt`

語言設定頁面，提供：
- 語言選項列表
- 即時語言切換
- 重新啟動提示
- 語言設定持久化

### 資源文件結構

```
app/src/main/res/
├── values/strings.xml           # 中文（預設）
├── values-en/strings.xml        # 英文
├── values-ja/strings.xml        # 日文
└── values-ko/strings.xml        # 韓文
```

每個語言資源文件包含完整的字串翻譯，確保應用程式在不同語言環境下的一致性。

## 使用方式

### 用戶操作流程

1. **開啟語言設定**
   - 點擊主頁面左上角漢堡選單
   - 選擇「語言設定」選項

2. **選擇語言**
   - 在語言設定頁面選擇偏好的語言
   - 系統會顯示重新啟動提示

3. **應用變更**
   - 返回主頁面或重新啟動應用程式
   - 語言變更立即生效

### 開發者集成

#### 新增 Activity 支援
所有新的 Activity 只需繼承 `BaseActivity` 即可自動支援多國語系：

```kotlin
class NewActivity : BaseActivity() {
    // Activity 實作
}
```

#### 新增字串資源
1. 在 `values/strings.xml` 中新增中文字串
2. 在對應的語言資源文件中新增翻譯
3. 使用 `getString(R.string.key)` 或 `@string/key` 引用

#### 語言檢測
```kotlin
// 獲取當前選擇的語言
val currentLanguage = LanguageManager.getCurrentLanguage(context)

// 獲取實際使用的語言（處理跟隨系統的情況）
val actualLanguage = LanguageManager.getActualLanguage(context)

// 檢查語言是否變更
val isChanged = LanguageManager.isLanguageChanged(context, newLanguage)
```

## 測試

### 單元測試
位置：`app/src/test/java/com/example/autolaunch/utils/LanguageManagerTest.kt`

測試 LanguageManager 的核心功能：
- 語言設定儲存和讀取
- 系統語言檢測
- 語言變更檢測

### Android 測試
位置：`app/src/androidTest/java/com/example/autolaunch/LanguageSettingsActivityTest.kt`

測試語言設定頁面的 UI 功能：
- 頁面正確顯示
- 語言選項列表
- 語言切換功能

### 測試腳本
執行 `./test_i18n.sh` 進行完整的 i18n 功能測試。

## 技術細節

### 語言代碼對應
- `zh` - 中文
- `en` - 英文  
- `ja` - 日文
- `ko` - 韓文
- `follow_system` - 跟隨系統

### 設定儲存
語言設定使用 SharedPreferences 儲存：
- 檔案名：`language_settings`
- 鍵名：`selected_language`
- 預設值：`follow_system`

### Context 處理
使用 `attachBaseContext()` 方法在 Activity 創建時應用語言設定，確保：
- 所有文字正確顯示為選擇的語言
- 系統組件（如日期選擇器）使用正確語言
- 語言變更立即生效

## 注意事項

1. **重新啟動需求**：語言變更後建議重新啟動應用程式以確保所有組件正確更新

2. **系統語言支援**：當選擇「跟隨系統」時，如果系統語言不在支援列表中，預設使用英文

3. **資源完整性**：確保所有語言資源文件包含相同的字串鍵，避免缺失翻譯

4. **測試覆蓋**：在不同語言環境下測試應用程式，確保 UI 布局和功能正常

## 未來擴展

### 新增語言支援
1. 創建新的 `values-{language_code}/strings.xml` 文件
2. 在 `LanguageManager.supportedLanguages` 中新增語言選項
3. 更新 `getSystemLanguage()` 方法支援新語言
4. 新增對應的測試案例

### 功能增強
- 語言包動態下載
- 地區化設定（如日期、數字格式）
- 語音識別語言同步
- 更多語言支援

## 總結

AutoLaunch 的 i18n 實作提供了完整的多國語系支援，通過模組化設計確保易於維護和擴展。用戶可以輕鬆切換語言，開發者可以簡單地新增新語言支援。
