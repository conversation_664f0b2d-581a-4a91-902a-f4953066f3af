# 語言切換重啟對話框功能

## 🎯 功能概述

在語言設定頁面，當用戶切換語言後，系統會主動彈出對話框詢問用戶是否要立即重啟應用程式，提供更好的用戶體驗。

## ✨ 功能特色

### 主動詢問重啟
- ✅ 語言切換後立即彈出確認對話框
- ✅ 用戶可選擇「立即重啟」或「稍後重啟」
- ✅ 對話框無法取消，確保用戶做出選擇
- ✅ 支援所有語言的本地化文字

### 用戶體驗優化
- 🚀 **立即重啟**：直接重啟應用，新語言立即生效
- ⏰ **稍後重啟**：關閉對話框，保留重啟提示卡片
- 📱 **返回鍵處理**：如果語言已變更，返回時自動重啟

## 🛠️ 實作細節

### 核心功能流程
```
用戶選擇新語言
    ↓
保存語言設定
    ↓
顯示重啟提示卡片
    ↓
彈出重啟確認對話框
    ↓
用戶選擇：立即重啟 / 稍後重啟
```

### 代碼實作

#### 1. 對話框顯示邏輯
```kotlin
private fun onLanguageSelected(languageCode: String) {
    if (LanguageManager.isLanguageChanged(this, languageCode)) {
        // 保存語言設定
        LanguageManager.setLanguage(this, languageCode)
        hasLanguageChanged = true
        
        // 顯示重新啟動提示
        binding.cardRestartHint.visibility = View.VISIBLE
        
        // 主動詢問用戶是否要重啟
        showRestartDialog()
    }
}
```

#### 2. 重啟確認對話框
```kotlin
private fun showRestartDialog() {
    AlertDialog.Builder(this)
        .setTitle(getString(R.string.restart_app_title))
        .setMessage(getString(R.string.restart_app_message))
        .setPositiveButton(getString(R.string.restart_now)) { _, _ ->
            restartApp()
        }
        .setNegativeButton(getString(R.string.restart_later)) { dialog, _ ->
            dialog.dismiss()
        }
        .setCancelable(false)
        .show()
}
```

## 🌍 多語言支援

### 中文 (values/strings.xml)
```xml
<string name="restart_app_title">重新啟動應用程式</string>
<string name="restart_app_message">語言設定已變更，是否要立即重新啟動應用程式以套用新的語言設定？</string>
<string name="restart_now">立即重啟</string>
<string name="restart_later">稍後重啟</string>
```

### 英文 (values-en/strings.xml)
```xml
<string name="restart_app_title">Restart Application</string>
<string name="restart_app_message">Language settings have been changed. Would you like to restart the app now to apply the new language settings?</string>
<string name="restart_now">Restart Now</string>
<string name="restart_later">Restart Later</string>
```

### 日文 (values-ja/strings.xml)
```xml
<string name="restart_app_title">アプリケーションの再起動</string>
<string name="restart_app_message">言語設定が変更されました。新しい言語設定を適用するために、今すぐアプリを再起動しますか？</string>
<string name="restart_now">今すぐ再起動</string>
<string name="restart_later">後で再起動</string>
```

### 韓文 (values-ko/strings.xml)
```xml
<string name="restart_app_title">애플리케이션 재시작</string>
<string name="restart_app_message">언어 설정이 변경되었습니다. 새로운 언어 설정을 적용하기 위해 지금 앱을 재시작하시겠습니까?</string>
<string name="restart_now">지금 재시작</string>
<string name="restart_later">나중에 재시작</string>
```

## 🧪 測試指南

### 手動測試步驟
1. **進入語言設定頁面**
   - 從側邊欄選單進入「語言設定」

2. **切換語言**
   - 選擇不同於當前的語言選項

3. **驗證對話框**
   - 確認重啟對話框立即彈出
   - 驗證對話框標題和內容文字正確
   - 確認有「立即重啟」和「稍後重啟」兩個按鈕

4. **測試立即重啟**
   - 點擊「立即重啟」按鈕
   - 驗證應用程式重新啟動
   - 確認新語言已生效

5. **測試稍後重啟**
   - 切換語言後點擊「稍後重啟」
   - 確認對話框關閉
   - 驗證重啟提示卡片仍然顯示
   - 按返回鍵，確認應用程式重新啟動

### 多語言測試
- 在每種支援的語言環境下測試對話框文字
- 確認所有語言的對話框文字都正確顯示
- 驗證按鈕文字符合各語言習慣

## 📋 功能優勢

### 用戶體驗提升
- **主動提醒**：不需要用戶手動返回觸發重啟
- **即時選擇**：用戶可以立即決定是否重啟
- **清晰指引**：明確說明重啟的原因和效果

### 操作便利性
- **一鍵重啟**：選擇立即重啟後自動執行
- **靈活選擇**：用戶可以選擇稍後重啟
- **狀態保持**：稍後重啟時保留提示狀態

### 國際化支援
- **完整翻譯**：所有支援語言都有對應翻譯
- **文化適應**：按鈕文字符合各語言習慣
- **一致體驗**：所有語言環境下功能一致

## 🔄 與現有功能的整合

### 保留原有功能
- ✅ 重啟提示卡片仍然顯示
- ✅ 返回鍵重啟邏輯保持不變
- ✅ 語言設定持久化正常工作

### 增強用戶體驗
- 🆕 主動詢問重啟選項
- 🆕 即時重啟功能
- 🆕 多語言對話框支援

這個功能讓語言切換變得更加用戶友好，提供了更直觀和便利的操作體驗。
