# 排程名稱編輯功能修改

## 修改概述
將排程名稱的編輯方式從彈出對話框改為直接在輸入框中編輯，移除筆形圖標，並在右上角添加整個排程的儲存按鈕。

## 修改的文件

### 1. activity_add_edit_schedule.xml
**修改位置**: 工具列標題區域 (第37-67行)

**主要變更**:
- 將 `MaterialTextView` 改為 `TextInputEditText`，允許直接編輯
- 移除筆形圖標 (`ic_edit_24`)
- 新增排程儲存按鈕 (`btnSaveSchedule`) 在標題輸入框右側，功能等同於下方儲存按鈕
- 設定 EditText 屬性：
  - `maxLines="1"` - 限制單行輸入
  - `imeOptions="actionDone"` - 鍵盤顯示完成按鈕
  - `hint="輸入排程名稱"` - 提示文字

### 2. AddEditScheduleActivity.kt
**修改位置**: 多個方法

**主要變更**:

#### setupUI() 方法 (第153-156行)
- 新增右上角排程儲存按鈕點擊事件監聽器，功能等同於下方儲存按鈕

#### updateToolbarTitle() 方法 (第169-179行)
- 將 `binding.tvToolbarTitle.text` 改為 `binding.etToolbarTitle.setText()`
- 移除編輯圖標的顯示/隱藏邏輯

#### 移除 saveTitleChange() 方法
- 移除專門的標題儲存方法，標題編輯直接整合到整個排程的儲存流程中

#### setupClickListeners() 方法 (第268-269行)
- 移除舊的標題點擊事件監聽器

#### 移除 showTaskNameEditDialog() 方法
- 完全移除彈出對話框相關代碼

#### saveSchedule() 方法 (第494-502行)
- 修改為從 EditText 獲取當前標題
- 正確處理預設標題，避免將其當作自訂標題
- 右上角儲存按鈕和下方儲存按鈕都調用此方法

## 功能特點

### 1. 直接編輯
- 用戶可以直接點擊標題區域進行編輯
- 不再需要彈出對話框

### 2. 儲存方式
- 點擊右上角儲存按鈕儲存整個排程（包含標題）
- 點擊下方儲存按鈕儲存整個排程（包含標題）
- 標題編輯無需單獨儲存，會在排程儲存時一併處理

### 3. 智能處理
- 空標題會被正確處理為 null
- 預設標題（"新增排程"、"編輯排程"）不會被當作自訂標題
- 顯示儲存成功提示

### 4. UI 改進
- 移除了筆形圖標，界面更簡潔
- 右上角儲存按鈕提供便捷的排程儲存入口
- 保持與現有設計風格一致
- 提供兩個儲存按鈕位置，方便用戶操作

## 測試結果
- ✅ 編譯成功
- ✅ 無語法錯誤
- ✅ 保持向後兼容性
- ✅ UI 布局正確

## 使用方式
1. 在新增/編輯排程頁面，直接點擊標題輸入框
2. 輸入自訂的排程名稱
3. 設定其他排程參數（時間、重複模式等）
4. 點擊右上角的「儲存」按鈕或下方的「儲存」按鈕來儲存整個排程
5. 標題會隨著排程一起儲存，無需單獨儲存
