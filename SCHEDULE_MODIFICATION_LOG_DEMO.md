# AutoLaunch 排程修改日志详细记录功能

## 🎯 功能概述

现在AutoLaunch应用的系统日志功能已经升级，可以详细记录排程修改的具体内容，让用户清楚了解每次修改了什么。

## ✨ 新增功能特性

### 📝 详细修改记录
当用户修改排程时，系统会自动比较修改前后的差异，并记录具体的变更内容：

#### 🕐 时间变更
- **记录格式**: `時間: 09:00 → 10:30`
- **触发条件**: 修改排程的小时或分钟
- **示例**: 将排程从早上9点改为10点30分

#### 🏷️ 标题变更
- **记录格式**: `標題: 旧标题 → 新标题`
- **触发条件**: 修改排程的任务名称
- **示例**: 将"启动音乐"改为"播放音乐"

#### 🔄 开关状态变更
- **记录格式**: `狀態: 啟用` 或 `狀態: 停用`
- **触发条件**: 启用或停用排程
- **示例**: 将排程从启用状态改为停用状态

#### 🔁 重复模式变更
- **记录格式**: `重複: 每日 → 每週`
- **触发条件**: 修改排程的重复模式
- **示例**: 从每日重复改为每周重复

#### 📱 应用变更 (APP类型排程)
- **记录格式**: `應用: 旧应用 → 新应用`
- **触发条件**: 修改要启动的应用程序
- **示例**: 从"音乐播放器"改为"Spotify"

#### 🌐 网址变更 (URL类型排程)
- **记录格式**: `網址: old.example.com → new.example.com`
- **触发条件**: 修改要打开的网址
- **示例**: 从Google改为百度

#### 📅 重复日期变更 (每周模式)
- **记录格式**: `重複日期: 週一,週三,週五 → 週二,週四`
- **触发条件**: 修改每周重复的具体日期
- **示例**: 从周一三五改为周二四

## 🔍 日志查看方式

### 1. 访问系统日志
1. 打开AutoLaunch应用
2. 点击左上角菜单按钮
3. 选择"系統紀錄"

### 2. 查看修改记录
- 修改类型的日志会显示为"排程修改"
- 日志消息格式：`修改排程：[排程名称]`
- 详细变更内容会显示在日志详情中

### 3. 日志详情示例
```
時間：2025-06-27 14:45:30
類型：資訊
操作：排程修改
消息：修改排程：启动音乐播放器
排程：启动音乐播放器

詳細信息：
時間: 09:00 → 10:30; 標題: 启动音乐 → 播放音乐; 重複: 每日 → 每週
```

## 🛠️ 技术实现

### 变更检测机制
系统会在保存排程前获取原始数据，然后与新数据进行逐字段比较：

1. **时间比较**: 检查hour和minute字段
2. **标题比较**: 检查taskName字段
3. **状态比较**: 检查isEnabled字段
4. **模式比较**: 检查repeatMode字段
5. **应用比较**: 检查appName字段（APP类型）
6. **网址比较**: 检查url和urlTitle字段（URL类型）
7. **日期比较**: 检查daysOfWeek字段（每周模式）

### 智能格式化
- **时间格式**: 自动格式化为HH:MM格式
- **网址简化**: 只显示域名部分，避免URL过长
- **日期转换**: 将位元遮罩转换为可读的星期几名称
- **空值处理**: 空值显示为"未設定"

## 📊 使用场景

### 1. 调试排程问题
当排程没有按预期工作时，可以查看修改日志确认设置是否正确。

### 2. 追踪变更历史
了解排程的修改历史，特别是在多人使用同一设备时。

### 3. 恢复误操作
通过查看修改日志，可以了解之前的设置并手动恢复。

### 4. 优化排程设置
分析修改模式，优化排程配置。

## 🔧 开发者信息

### 相关文件
- `ScheduleRepository.kt`: 主要实现逻辑
- `SystemLogManager.kt`: 日志记录管理
- `SystemLogActivity.kt`: 日志查看界面

### 关键方法
- `generateChangeDetails()`: 生成变更详情
- `updateSchedule()`: 更新排程并记录日志
- `logScheduleUpdated()`: 记录排程修改日志

## 🎉 总结

这个功能大大提升了AutoLaunch应用的可维护性和用户体验，让用户能够清楚地了解每次排程修改的具体内容，便于调试和管理排程设置。

---

**注意**: 此功能从应用版本4.0开始提供，需要数据库版本4支持。
