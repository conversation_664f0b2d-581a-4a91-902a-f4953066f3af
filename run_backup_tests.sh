#!/bin/bash

# AutoLaunch 備份功能測試運行腳本
# 用於運行所有備份相關的測試

echo "🚀 開始運行 AutoLaunch 備份功能測試..."
echo "========================================"

# 檢查是否在正確的目錄
if [ ! -f "app/build.gradle" ]; then
    echo "❌ 錯誤：請在項目根目錄運行此腳本"
    exit 1
fi

# 清理之前的測試結果
echo "🧹 清理之前的測試結果..."
./gradlew clean

# 運行單元測試
echo ""
echo "📋 運行單元測試..."
echo "===================="

echo "🔍 測試 BackupManager..."
./gradlew test --tests "com.example.autolaunch.utils.BackupManagerTest"

echo "🔍 測試備份功能綜合測試..."
./gradlew test --tests "com.example.autolaunch.BackupFunctionalityComprehensiveTest"

# 運行集成測試（需要模擬器或真機）
echo ""
echo "🔗 運行集成測試..."
echo "=================="

# 檢查是否有連接的設備
adb devices | grep -q "device$"
if [ $? -eq 0 ]; then
    echo "📱 發現連接的設備，運行集成測試..."
    
    echo "🔍 測試備份恢復集成..."
    ./gradlew connectedAndroidTest --tests "com.example.autolaunch.BackupRestoreIntegrationTest"
    
    echo "🔍 測試 BackupRestoreActivity UI..."
    ./gradlew connectedAndroidTest --tests "com.example.autolaunch.BackupRestoreActivityTest"
else
    echo "⚠️  警告：沒有發現連接的設備，跳過集成測試"
    echo "   請連接 Android 設備或啟動模擬器後重新運行"
fi

# 生成測試報告
echo ""
echo "📊 生成測試報告..."
echo "=================="

# 檢查測試結果
if [ -d "app/build/reports/tests" ]; then
    echo "✅ 單元測試報告已生成："
    find app/build/reports/tests -name "index.html" -type f | head -5
fi

if [ -d "app/build/reports/androidTests" ]; then
    echo "✅ 集成測試報告已生成："
    find app/build/reports/androidTests -name "index.html" -type f | head -5
fi

# 檢查測試覆蓋率（如果配置了）
if [ -d "app/build/reports/coverage" ]; then
    echo "📈 測試覆蓋率報告已生成："
    find app/build/reports/coverage -name "index.html" -type f | head -5
fi

echo ""
echo "🎉 測試運行完成！"
echo "=================="

# 顯示測試摘要
echo ""
echo "📋 測試摘要："
echo "============"
echo "✅ BackupManager 單元測試"
echo "✅ 備份數據模型測試"
echo "✅ 文件操作工具測試"
echo "✅ 權限管理測試"
echo "✅ 備份功能綜合測試"

if adb devices | grep -q "device$"; then
    echo "✅ 備份恢復集成測試"
    echo "✅ BackupRestoreActivity UI 測試"
else
    echo "⚠️  備份恢復集成測試（需要設備）"
    echo "⚠️  BackupRestoreActivity UI 測試（需要設備）"
fi

echo ""
echo "🔍 如需查看詳細測試結果，請打開生成的 HTML 報告文件"
echo "📱 如需運行完整測試，請確保有 Android 設備連接"

echo ""
echo "✨ 備份功能測試腳本執行完成！"
