# AutoLaunch 網站圖標和自動標題功能演示

## 新增功能概述

我們為 AutoLaunch 應用添加了以下新功能：

### 1. 自動從 URL 提取域名作為標題
- 當用戶輸入 URL 時，應用會自動提取域名並設置為排程標題
- 例如：輸入 `https://www.google.com` 會自動設置標題為 `Google`
- 用戶仍可以手動修改標題，修改後將停止自動更新

### 2. 顯示網站圖標
- URL 排程現在會顯示對應網站的圖標
- 使用 Google 的 favicon 服務來獲取網站圖標
- 如果無法載入圖標，會回退到預設的鏈接圖標

## 技術實現

### 新增的文件和功能

1. **UrlUtils.kt** - URL 工具類
   - `extractDomain()` - 提取域名
   - `extractDisplayName()` - 提取顯示名稱
   - `generateFaviconUrl()` - 生成圖標 URL
   - `isValidUrl()` - 驗證 URL
   - `normalizeUrl()` - 標準化 URL

2. **修改的文件**
   - `UrlInputHelper.kt` - 添加自動標題設置功能
   - `ScheduleAdapter.kt` - 添加網站圖標載入功能
   - `build.gradle` - 添加 Glide 圖像載入庫

3. **測試文件**
   - `UrlUtilsTest.kt` - 完整的單元測試覆蓋

### 依賴項
- 添加了 Glide 4.16.0 用於圖像載入和緩存

## 使用方式

### 創建 URL 排程
1. 點擊「新增排程」
2. 選擇「網頁」類型
3. 輸入 URL（例如：`https://www.github.com`）
4. 應用會自動設置標題為「Github」
5. 可以手動修改標題（修改後停止自動更新）
6. 設置時間和重複模式
7. 保存排程

### 查看排程列表
- URL 排程會顯示對應網站的圖標
- 如果圖標載入失敗，會顯示預設的鏈接圖標
- 標題顯示為域名的主要部分（首字母大寫）

## 示例

### 支持的 URL 格式
- `https://www.google.com` → 標題：`Google`，圖標：Google favicon
- `http://facebook.com` → 標題：`Facebook`，圖標：Facebook favicon
- `https://github.com/user/repo` → 標題：`Github`，圖標：Github favicon

### 自動補全
- 輸入 `google.com` 會自動補全為 `https://google.com`
- 支持 HTTP、HTTPS 和 FTP 協議

## 技術細節

### 圖標載入策略
- 使用 Google 的 favicon 服務：`https://www.google.com/s2/favicons?domain={domain}&sz=64`
- 啟用磁盤緩存以提高性能
- 設置 placeholder、error 和 fallback 圖標

### 域名提取邏輯
- 自動移除 `www.` 前綴
- 提取主域名部分並首字母大寫
- 處理子域名和端口號

### 錯誤處理
- 無效 URL 會顯示錯誤狀態
- 圖標載入失敗會回退到預設圖標
- 域名提取失敗會保持原始 URL

## 測試覆蓋

- 29 個單元測試全部通過
- 覆蓋各種 URL 格式和邊緣情況
- 測試域名提取、圖標 URL 生成和 URL 驗證功能
