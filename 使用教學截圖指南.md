# Android AutoLaunch 使用教學截圖指南

## 概述
本指南提供 Android AutoLaunch 應用程式所有主要功能頁面的截圖要求和操作步驟，用於製作使用教學文件。

## 截圖要求

### 1. 主頁面（MainActivity）
**檔案名稱：** `01_main_homepage.png`
**操作步驟：**
1. 啟動 Android AutoLaunch 應用程式
2. 等待主頁面完全載入
3. 確保顯示排程列表（如果有排程）或空狀態提示
4. 截圖包含：
   - 應用程式標題欄
   - 排程列表或空狀態顯示
   - 新增排程按鈕 (FAB)
   - 底部選單欄

### 2. 歡迎導覽頁面（WelcomeActivity）
**檔案名稱：** `02_welcome_guide.png`
**操作步驟：**
1. 清除應用程式資料或首次安裝時啟動
2. 截圖歡迎頁面的各個導覽頁面
3. 需要截圖：
   - 歡迎頁面第一頁
   - 功能介紹頁面
   - 權限說明頁面
   - 完成設定頁面

### 3. 權限檢查頁面
**檔案名稱：** `03_permission_check.png`
**操作步驟：**
1. 從主頁面點擊歡迎卡片或權限提示
2. 截圖權限檢查對話框
3. 包含：
   - 權限檢查清單
   - 各項權限狀態（已授予/未授予）
   - 前往設定按鈕

### 4. 新增排程頁面（AddEditScheduleActivity）
**檔案名稱：** `04_add_schedule.png`
**操作步驟：**
1. 點擊主頁面的新增排程按鈕
2. 截圖新增排程頁面
3. 包含：
   - 排程類型選擇（應用程式/網址）
   - 時間設定區域
   - 日期/重複設定
   - 保存按鈕

### 5. 應用程式選擇器頁面（AppSelectorActivity）
**檔案名稱：** `05_app_selector.png`
**操作步驟：**
1. 在新增排程頁面中選擇應用程式類型
2. 點擊選擇應用程式按鈕
3. 截圖應用程式選擇器頁面
4. 包含：
   - 應用程式清單
   - 搜尋功能
   - 應用程式圖示和名稱

### 6. 時間選擇器
**檔案名稱：** `06_time_picker.png`
**操作步驟：**
1. 在新增排程頁面點擊時間設定
2. 截圖時間選擇器對話框
3. 包含：
   - 時間轉盤或數字輸入
   - 確認/取消按鈕

### 7. 日期選擇器
**檔案名稱：** `07_date_picker.png`
**操作步驟：**
1. 在新增排程頁面點擊日期設定
2. 截圖日期選擇器
3. 包含：
   - 日曆視圖
   - 月份/年份選擇

### 8. 重複設定頁面
**檔案名稱：** `08_repeat_settings.png`
**操作步驟：**
1. 在新增排程頁面選擇重複模式
2. 截圖重複設定選項
3. 包含：
   - 重複類型（每日/每週/每月）
   - 週期選擇器
   - 星期選擇（如果是每週）

### 9. 備份與恢復頁面
**檔案名稱：** `09_backup_restore.png`
**操作步驟：**
1. 從主頁面選單進入備份功能
2. 截圖備份頁面
3. 包含：
   - 雲端備份選項
   - 本地備份選項
   - 備份檔案清單

### 10. 雲端備份頁面
**檔案名稱：** `10_cloud_backup.png`
**操作步驟：**
1. 進入備份功能後選擇雲端備份
2. 截圖雲端備份頁面
3. 包含：
   - Google Drive 連接狀態
   - 備份檔案清單
   - 上傳/下載按鈕

### 11. 語言設定頁面
**檔案名稱：** `11_language_settings.png`
**操作步驟：**
1. 從主頁面選單進入語言設定
2. 截圖語言選擇頁面
3. 包含：
   - 可用語言清單
   - 當前選擇的語言
   - 確認按鈕

### 12. 主題設定頁面
**檔案名稱：** `12_theme_settings.png`
**操作步驟：**
1. 從主頁面選單進入主題設定
2. 截圖主題選擇頁面
3. 包含：
   - 主題選項（淺色/深色/系統）
   - 主題預覽
   - 套用按鈕

### 13. 系統日誌頁面
**檔案名稱：** `13_system_log.png`
**操作步驟：**
1. 從主頁面選單進入系統日誌
2. 截圖系統日誌頁面
3. 包含：
   - 日誌清單
   - 時間戳記
   - 日誌級別（資訊/警告/錯誤）

### 14. 關於頁面（AboutActivity）
**檔案名稱：** `14_about_page.png`
**操作步驟：**
1. 從主頁面選單進入關於頁面
2. 截圖關於頁面
3. 包含：
   - 應用程式版本資訊
   - 開發者資訊
   - 聯絡資訊

### 15. 排程執行狀態
**檔案名稱：** `15_schedule_status.png`
**操作步驟：**
1. 建立一個測試排程
2. 等待排程觸發或手動觸發
3. 截圖排程執行狀態
4. 包含：
   - 執行成功/失敗提示
   - 通知顯示

### 16. 電池優化設定
**檔案名稱：** `16_battery_optimization.png`
**操作步驟：**
1. 從權限檢查進入電池優化設定
2. 截圖電池優化頁面
3. 包含：
   - 電池優化設定說明
   - 前往系統設定按鈕

### 17. 側邊選單
**檔案名稱：** `17_navigation_drawer.png`
**操作步驟：**
1. 在主頁面滑動或點擊選單按鈕
2. 截圖側邊選單
3. 包含：
   - 選單項目清單
   - 應用程式資訊
   - 設定選項

### 18. 排程編輯頁面
**檔案名稱：** `18_edit_schedule.png`
**操作步驟：**
1. 從排程清單點擊編輯現有排程
2. 截圖編輯頁面
3. 包含：
   - 預填的排程資訊
   - 編輯選項
   - 刪除按鈕

## 截圖技術要求

### 解析度與格式
- 格式：PNG 或 JPG
- 解析度：保持原始螢幕解析度
- 檔案大小：壓縮後不超過 2MB

### 截圖內容要求
- 確保完整顯示頁面內容
- 避免個人隱私資訊洩露
- 使用測試數據代替真實數據
- 確保文字清晰可讀

### 截圖命名規則
- 格式：`[序號]_[功能名稱]_[語言版本].png`
- 序號：兩位數字（01, 02, 03...）
- 功能名稱：英文小寫，使用底線分隔
- 語言版本：可選，如 `_zh_tw`, `_en`, `_ja`

## 後續處理

### 圖片編輯
1. 統一圖片尺寸比例
2. 添加標註和說明文字
3. 模糊敏感資訊
4. 調整亮度和對比度

### 文檔整合
1. 將截圖按功能分類
2. 添加操作步驟說明
3. 製作使用教學文檔
4. 準備多語言版本

## 測試設備建議

### Android 版本
- Android 8.0 (API 26) 以上
- 建議使用 Android 11 或 12

### 螢幕尺寸
- 建議使用 1080x1920 或以上解析度
- 支援不同螢幕比例（16:9, 18:9, 19:9）

### 測試環境
- 使用乾淨的測試環境
- 準備測試用的應用程式
- 確保網路連接正常

## 注意事項

1. **權限設定**：確保應用程式具有必要的權限
2. **測試數據**：使用測試數據而非真實個人資料
3. **多語言**：考慮截圖多種語言版本
4. **主題模式**：分別截圖淺色和深色主題
5. **錯誤處理**：包含錯誤狀態的截圖

---

**建立日期：** 2025-07-08 08:19:54
**用途：** Android AutoLaunch 應用程式使用教學製作 