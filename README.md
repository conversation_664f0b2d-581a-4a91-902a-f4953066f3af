# Android AutoLaunch

一個 Android 應用程式，讓用戶可以在指定的時間和日期自動開啟其他應用程式。

## 功能特色

### 🚀 核心功能
- **智能排程**：精確的時間設定（小時、分鐘選擇器）
- **靈活重複**：單次執行、每日、每週、自訂週期
- **雙重啟動**：支援應用程式和網頁URL排程
- **直觀管理**：拖拽排序、滑動刪除、長按編輯

### 🌍 國際化支援
- **完整多語言**：中文、English、日本語、한국어
- **智能本地化**：時間顯示、星期格式、UI文字完全翻譯
- **語言切換**：即時切換，重啟生效

### 🎨 個性化體驗
- **豐富主題**：淺色/深色模式，各3種主題變體
- **系統適配**：自動跟隨系統主題
- **Material Design 3**：現代化設計語言

### 📱 用戶體驗
- **歡迎導覽**：首次使用多頁面引導
- **權限管理**：智能檢測，一鍵設定
- **常駐通知**：快速訪問，狀態提醒
- **模擬器友好**：自動檢測並適配模擬器環境

### 💾 數據管理
- **本地備份**：無需登入，安全可靠
- **雲端同步**：Google Drive 整合
- **系統紀錄**：7天日誌輪替，完整追蹤
- **智能恢復**：一鍵匯入，覆蓋或合併

## 技術規格

- **最低 Android 版本**: Android 8.0 (API 26)
- **目標 Android 版本**: Android 14 (API 35)
- **開發語言**: Kotlin
- **UI 框架**: Material Design 3
- **資料庫**: Room
- **架構**: MVVM with LiveData

## 權限需求

- `QUERY_ALL_PACKAGES`: 查詢已安裝的應用程式
- `RECEIVE_BOOT_COMPLETED`: 開機後重新註冊排程
- `SCHEDULE_EXACT_ALARM`: 設定精確的鬧鐘
- `USE_EXACT_ALARM`: 使用精確鬧鐘服務
- `FOREGROUND_SERVICE`: 前台服務
- `WAKE_LOCK`: 喚醒鎖定
- `REQUEST_IGNORE_BATTERY_OPTIMIZATIONS`: 電池優化白名單（真機環境）

## 建置說明

1. 確保已安裝 Android Studio 和 JDK 17
2. 複製專案到本地
3. 使用 Android Studio 開啟專案
4. 等待 Gradle 同步完成
5. 連接 Android 設備或啟動模擬器
6. 點擊 Run 按鈕

### 編譯指令

```bash
# 清理並建置 Debug 版本
./gradlew clean assembleDebug

# 安裝到連接的設備
./gradlew installDebug
```

## 模擬器環境支援

### 自動適配功能

應用程式會自動檢測模擬器環境並進行以下適配：

- ✅ 跳過電池優化權限請求（避免 System UI crash）
- ✅ 調整權限請求策略
- ✅ 顯示模擬器適配提示
- ✅ 啟用模擬器友好的日誌記錄

### 支援的模擬器

- Android Studio AVD
- Genymotion
- 其他基於 x86/ARM 架構的模擬器

### 模擬器測試注意事項

1. **權限限制**: 某些系統級權限在模擬器中可能無法完全測試
2. **電池管理**: 電池優化相關功能僅在真機環境下生效
3. **效能差異**: 模擬器中的定時精度可能與真機有差異

## 故障排除

### 常見問題

#### 1. 模擬器環境 System UI Crash
**問題**: 在模擬器中啟動應用時出現 "System UI isn't responding"
**解決方案**: 應用已內建模擬器檢測，會自動跳過問題權限請求

#### 2. 編譯錯誤
**問題**: Kotlin 版本兼容性警告
**解決方案**: 
```bash
# 檢查 Kotlin 版本並清理建置
./gradlew clean
./gradlew assembleDebug --stacktrace
```

#### 3. 權限被拒絕
**問題**: 在真機上某些功能無法使用
**解決方案**: 
- 檢查是否已在應用設定中授予必要權限
- 將應用加入電池優化白名單
- 檢查廠商特定的權限管理設定

### 調試功能

在 Debug 模式下，長按版本號可以開啟調試選單：
- 創建測試數據
- 檢查系統健康狀態
- 驗證排程時間計算
- 重置電池優化提示

## 專案結構

```
app/
├── src/main/
│   ├── java/com/example/autolaunch/
│   │   ├── MainActivity.kt                    # 主介面
│   │   ├── AddEditScheduleActivity.kt         # 新增/編輯排程
│   │   ├── AppSelectorActivity.kt             # 應用選擇器
│   │   ├── ScheduleAdapter.kt                 # 排程列表適配器
│   │   ├── AppSelectorAdapter.kt              # 應用選擇適配器
│   │   ├── ScheduleViewModel.kt               # 排程視圖模型
│   │   ├── AppSelectorViewModel.kt            # 應用選擇視圖模型
│   │   ├── SwipeToDeleteCallback.kt           # 滑動刪除功能
│   │   ├── AlarmManagerService.kt             # 鬧鐘管理服務
│   │   ├── utils/
│   │   │   ├── EmulatorHelper.kt              # 模擬器環境檢測
│   │   │   ├── BatteryOptimizationHelper.kt   # 電池優化管理
│   │   │   ├── TestUtils.kt                   # 測試工具
│   │   │   └── TimeFormatUtils.kt             # 時間格式化工具
│   │   ├── model/
│   │   │   ├── Schedule.kt                    # 排程數據模型
│   │   │   ├── AppInfo.kt                     # 應用信息模型
│   │   │   ├── RepeatMode.kt                  # 重複模式枚舉
│   │   │   ├── DaysOfWeek.kt                  # 星期管理
│   │   │   ├── AppDatabase.kt                 # Room 數據庫
│   │   │   ├── ScheduleDao.kt                 # 排程數據訪問對象
│   │   │   ├── ScheduleRepository.kt          # 排程倉庫
│   │   │   ├── AppRepository.kt               # 應用倉庫
│   │   │   └── DatabaseUsageExample.kt        # 數據庫使用示例
│   │   ├── dialog/
│   │   │   └── BatteryOptimizationDialog.kt   # 電池優化對話框
│   │   ├── receiver/
│   │   │   ├── BootReceiver.kt                # 開機廣播接收器
│   │   │   ├── LaunchReceiver.kt              # 應用啟動接收器
│   │   │   └── PackageRemovedReceiver.kt      # 應用卸載接收器
│   │   └── worker/
│   │       ├── ScheduleReregistrationWorker.kt # 排程重新註冊工作
│   │       └── PackageRemovedWorker.kt        # 應用卸載處理工作
│   ├── res/
│   │   ├── layout/
│   │   │   ├── activity_main.xml              # 主介面布局
│   │   │   ├── activity_add_edit_schedule.xml # 新增/編輯排程布局
│   │   │   ├── activity_app_selector.xml      # 應用選擇器布局
│   │   │   ├── item_schedule.xml              # 排程項目布局
│   │   │   └── item_app_selector.xml          # 應用選擇項目布局
│   │   ├── values/
│   │   ├── drawable/
│   │   └── mipmap-*/
│   └── AndroidManifest.xml
├── build.gradle
└── PRD/                                       # 產品需求文檔
    └── PRD-2025-06-03 10:01:48-模擬器環境crash修復.md
```

## 開發狀態

### ✅ 已完成功能

#### 🏗️ 核心架構
- [x] 專案基礎設定與 Material Design 3 整合
- [x] Room 資料庫整合 (AppDatabase, ScheduleDao, Repository)
- [x] MVVM 架構 (ViewModel, LiveData, Repository)
- [x] 權限管理系統與廣播接收器架構

#### 📱 用戶介面
- [x] 主介面排程列表 (MainActivity, UnifiedScheduleAdapter)
- [x] 新增/編輯排程介面 (AddEditScheduleActivity)
- [x] APP 選擇器 (AppSelectorActivity, AppSelectorAdapter)
- [x] 歡迎導覽頁面 (WelcomeActivity, 多頁面輪播)
- [x] 側邊欄選單 (NavigationDrawer 風格)
- [x] 滑動刪除與拖拽排序功能

#### 🌍 國際化與主題
- [x] **完整多語言支援** (中文、English、日本語、한국어)
- [x] **智能語言切換** (LanguageManager, 重啟提示)
- [x] **主題系統** (淺色/深色模式，各3種變體)
- [x] **系統主題跟隨** (自動檢測系統設定)

#### ⚙️ 核心功能
- [x] 排程數據模型 (Schedule, RepeatMode, DaysOfWeek)
- [x] 網頁排程功能 (URL 排程支援，favicon 顯示)
- [x] AlarmManager 調度服務 (精確定時)
- [x] 開機重新註冊排程 (BootReceiver)
- [x] 應用卸載處理 (PackageRemovedReceiver, Worker)
- [x] 時間格式化工具 (本地化時間顯示)

#### 💾 數據與設定
- [x] **備份與匯入功能** (本地和 Google Drive 雲端)
- [x] **系統紀錄功能** (7天日誌輪替)
- [x] **設定頁面整合** (通知設定、語言設定)
- [x] **常駐通知功能** (狀態欄快速訪問)

#### 🔧 系統適配
- [x] **模擬器環境自動適配** (EmulatorHelper)
- [x] **電池優化管理** (BatteryOptimizationHelper)
- [x] **權限智能檢測** (動態權限狀態)

### 🚧 待優化功能
- [ ] 排程執行狀態實時更新
- [ ] 應用圖標快取優化
- [ ] 通知系統進階功能
- [ ] 效能監控與優化
- [ ] 進階排程規則 (節假日跳過等)

## 版本歷史

### v1.0.0 (2025-06-29) - 首次發布 🎉

#### 🆕 核心功能
- **智能排程系統**：支援應用程式和網頁URL定時啟動
- **靈活重複模式**：單次、每日、每週、自訂星期組合
- **直觀操作介面**：Material Design 3，滑動刪除，拖拽排序

#### 🌍 國際化支援
- **完整多語言**：中文、English、日本語、한국어
- **智能語言切換**：即時切換，重啟生效，設定持久化
- **本地化顯示**：時間格式、星期顯示、UI文字完全翻譯

#### 🎨 個性化體驗
- **豐富主題系統**：淺色/深色模式，各3種主題變體
- **系統主題跟隨**：自動檢測並適配系統設定
- **歡迎導覽**：首次使用多頁面引導體驗

#### 💾 數據管理
- **雙重備份**：本地備份（無需登入）+ Google Drive 雲端同步
- **系統紀錄**：完整操作日誌，7天自動輪替
- **智能匯入**：支援覆蓋或合併模式

#### 🔧 系統優化
- **模擬器友好**：自動檢測並適配模擬器環境，避免 System UI crash
- **電池優化智能管理**：真機/模擬器差異化處理
- **權限智能檢測**：動態狀態顯示，一鍵設定引導
- **常駐通知**：狀態欄快速訪問，可選開關

## 授權

此專案採用 MIT 授權條款。 