# AutoLaunch 主題功能演示

## 功能演示步驟

### 1. 進入主題設定
1. 開啟 AutoLaunch 應用程式
2. 點擊左上角的選單按鈕（漢堡選單）
3. 在彈出的選單中找到「主題設定」選項（位於「邀請朋友」下方）
4. 點擊「主題設定」進入主題選擇頁面

### 2. 主題設定頁面功能
- **跟隨系統主題開關**: 頁面頂部有一個開關，可以選擇是否跟隨系統的深色/淺色模式
- **主題分組顯示**: 主題按照「淺色主題」和「深色主題」分組顯示
- **主題預覽**: 每個主題都有顏色預覽圓圈，展示主題的主要顏色
- **即時切換**: 點擊任何主題都會立即應用，無需重啟應用程式

### 3. 可用主題

#### 淺色主題
1. **經典淺色** - 溫和舒適的淺色主題（原有主題）
   - 主色調：藍灰色 (#5B7C99)
   - 背景：淺灰白色 (#F7F9FA)

2. **溫暖淺色** - 溫暖柔和的淺色主題
   - 主色調：橙棕色 (#D2691E)
   - 背景：溫暖米色 (#FFF5E6)

3. **清涼淺色** - 清新涼爽的淺色主題
   - 主色調：鋼藍色 (#4682B4)
   - 背景：淺藍白色 (#E6F3FF)

#### 深色主題
1. **經典深色** - 經典優雅的深色主題（原有主題）
   - 主色調：淺紫色 (#D0BCFF)
   - 背景：深灰色 (#1C1B1F)

2. **深藍主題** - 深邃藍色的深色主題
   - 主色調：亮藍色 (#82B1FF)
   - 背景：深藍黑色 (#0A1018)

3. **深綠主題** - 自然綠色的深色主題
   - 主色調：亮綠色 (#69F0AE)
   - 背景：深綠黑色 (#0A1A0F)

### 4. 功能測試

#### 測試跟隨系統主題
1. 開啟「跟隨系統主題」開關
2. 到系統設定中切換深色/淺色模式
3. 返回應用程式，確認主題已自動切換

#### 測試手動主題切換
1. 關閉「跟隨系統主題」開關
2. 點擊任一主題選項
3. 確認應用程式立即切換到選中的主題
4. 返回主頁面，確認整體UI顏色已更新

#### 測試主題持久化
1. 選擇一個主題
2. 完全關閉應用程式
3. 重新開啟應用程式
4. 確認之前選擇的主題仍然生效

### 5. UI 變化觀察點

切換主題時，以下UI元素會發生顏色變化：
- **工具列背景和文字顏色**
- **卡片背景顏色**
- **按鈕顏色和文字顏色**
- **整體背景顏色**
- **文字顏色（主要文字、次要文字）**
- **分隔線顏色**
- **圖標顏色**

### 6. 預期效果

#### 淺色主題效果
- 整體明亮清爽
- 文字為深色，背景為淺色
- 適合白天或明亮環境使用

#### 深色主題效果
- 整體深沉優雅
- 文字為淺色，背景為深色
- 適合夜晚或暗光環境使用
- 減少眼睛疲勞

### 7. 故障排除

如果主題切換不生效：
1. 確認已完全關閉並重新開啟應用程式
2. 檢查是否開啟了「跟隨系統主題」（如果開啟，手動選擇會被覆蓋）
3. 嘗試選擇不同的主題進行對比

### 8. 開發者注意事項

- 主題設定儲存在 SharedPreferences 中
- 所有 Activity 都會自動應用選中的主題
- 主題切換會觸發 Activity 重新創建以確保完全應用新主題
- 新增的主題不會影響現有功能的正常運作
