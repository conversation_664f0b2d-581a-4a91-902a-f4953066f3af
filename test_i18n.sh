#!/bin/bash

# i18n 多國語系功能測試腳本

echo "=== AutoLaunch i18n 多國語系功能測試 ==="
echo

# 檢查是否有 Android 設備連接
if ! adb devices | grep -q "device$"; then
    echo "❌ 錯誤：沒有找到連接的 Android 設備"
    echo "請確保設備已連接並啟用 USB 調試"
    exit 1
fi

echo "✅ 找到連接的 Android 設備"

# 編譯並安裝應用程式
echo "📱 編譯並安裝應用程式..."
./gradlew assembleDebug
if [ $? -ne 0 ]; then
    echo "❌ 編譯失敗"
    exit 1
fi

adb install -r app/build/outputs/apk/debug/app-debug.apk
if [ $? -ne 0 ]; then
    echo "❌ 安裝失敗"
    exit 1
fi

echo "✅ 應用程式安裝成功"

# 運行單元測試
echo "🧪 運行 LanguageManager 單元測試..."
./gradlew test --tests "com.example.autolaunch.utils.LanguageManagerTest"
if [ $? -eq 0 ]; then
    echo "✅ LanguageManager 單元測試通過"
else
    echo "❌ LanguageManager 單元測試失敗"
fi

# 運行 Android 測試
echo "📱 運行語言設定頁面 Android 測試..."
./gradlew connectedAndroidTest --tests "com.example.autolaunch.LanguageSettingsActivityTest"
if [ $? -eq 0 ]; then
    echo "✅ 語言設定頁面 Android 測試通過"
else
    echo "❌ 語言設定頁面 Android 測試失敗"
fi

# 手動測試指導
echo
echo "=== 手動測試指導 ==="
echo "請按照以下步驟進行手動測試："
echo
echo "1. 📱 啟動 AutoLaunch 應用程式"
echo "2. 🍔 點擊左上角的漢堡選單按鈕"
echo "3. 🌐 點擊「語言設定」選項"
echo "4. ✅ 驗證語言設定頁面正確顯示"
echo "5. 🔄 嘗試切換不同語言："
echo "   - 跟隨系統"
echo "   - 中文"
echo "   - English"
echo "   - 日本語"
echo "   - 한국어"
echo "6. 🔄 驗證語言切換後重新啟動提示顯示"
echo "7. ↩️ 返回主頁面，驗證語言已變更"
echo "8. 🔄 重新啟動應用程式，驗證語言設定持久化"
echo

# 檢查資源文件
echo "=== 檢查多國語系資源文件 ==="
echo

languages=("values" "values-en" "values-ja" "values-ko")
for lang in "${languages[@]}"; do
    file="app/src/main/res/$lang/strings.xml"
    if [ -f "$file" ]; then
        echo "✅ $file 存在"
        # 檢查關鍵字串是否存在
        if grep -q "language_settings_title" "$file"; then
            echo "   ✅ 包含語言設定相關字串"
        else
            echo "   ⚠️  缺少語言設定相關字串"
        fi
    else
        echo "❌ $file 不存在"
    fi
done

echo
echo "=== 檢查語言管理器類別 ==="
if [ -f "app/src/main/java/com/example/autolaunch/utils/LanguageManager.kt" ]; then
    echo "✅ LanguageManager.kt 存在"
else
    echo "❌ LanguageManager.kt 不存在"
fi

echo
echo "=== 檢查語言設定頁面 ==="
if [ -f "app/src/main/java/com/example/autolaunch/LanguageSettingsActivity.kt" ]; then
    echo "✅ LanguageSettingsActivity.kt 存在"
else
    echo "❌ LanguageSettingsActivity.kt 不存在"
fi

if [ -f "app/src/main/res/layout/activity_language_settings.xml" ]; then
    echo "✅ activity_language_settings.xml 存在"
else
    echo "❌ activity_language_settings.xml 不存在"
fi

echo
echo "=== 檢查 BaseActivity ==="
if [ -f "app/src/main/java/com/example/autolaunch/base/BaseActivity.kt" ]; then
    echo "✅ BaseActivity.kt 存在"
else
    echo "❌ BaseActivity.kt 不存在"
fi

echo
echo "=== i18n 功能測試完成 ==="
echo "請根據上述結果和手動測試指導驗證多國語系功能是否正常工作"
