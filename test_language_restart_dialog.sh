#!/bin/bash

# 語言切換重啟對話框功能測試腳本

echo "🧪 語言切換重啟對話框功能測試"
echo "================================"
echo ""

# 檢查是否有連接的設備
if ! adb devices | grep -q "device$"; then
    echo "❌ 錯誤：未找到連接的 Android 設備"
    echo "請確保："
    echo "1. Android 設備已連接並開啟 USB 調試"
    echo "2. 設備已授權此電腦進行調試"
    exit 1
fi

echo "📱 檢測到 Android 設備"
echo ""

# 編譯並安裝應用
echo "🔨 編譯並安裝應用..."
if ! ./gradlew assembleDebug; then
    echo "❌ 編譯失敗"
    exit 1
fi

if ! adb install -r app/build/outputs/apk/debug/app-debug.apk; then
    echo "❌ 安裝失敗"
    exit 1
fi

echo "✅ 應用安裝成功"
echo ""

# 啟動應用
echo "🚀 啟動 AutoLaunch 應用..."
adb shell am start -n com.example.autolaunch/.MainActivity
sleep 3

echo ""
echo "📋 手動測試步驟："
echo "=================="
echo ""
echo "1. 📱 在應用中打開側邊欄選單"
echo "2. 🌍 點擊「語言設定」進入語言設定頁面"
echo "3. 🔄 選擇一個不同於當前的語言選項"
echo "4. 💬 驗證重啟對話框是否立即彈出"
echo "5. 📝 檢查對話框內容："
echo "   - 標題：重新啟動應用程式 / Restart Application"
echo "   - 訊息：語言設定已變更，是否要立即重新啟動..."
echo "   - 按鈕：立即重啟 / 稍後重啟"
echo ""
echo "6. 🧪 測試「立即重啟」功能："
echo "   - 點擊「立即重啟」按鈕"
echo "   - 驗證應用程式重新啟動"
echo "   - 確認新語言已生效"
echo ""
echo "7. 🧪 測試「稍後重啟」功能："
echo "   - 再次切換語言"
echo "   - 點擊「稍後重啟」按鈕"
echo "   - 確認對話框關閉"
echo "   - 驗證重啟提示卡片仍然顯示"
echo "   - 按返回鍵，確認應用程式重新啟動"
echo ""
echo "8. 🌍 多語言測試："
echo "   - 在不同語言環境下重複上述測試"
echo "   - 驗證對話框文字正確顯示各種語言"
echo ""

# 檢查功能點
echo "✅ 功能檢查清單："
echo "=================="
echo ""
echo "□ 語言切換後立即彈出重啟對話框"
echo "□ 對話框標題和訊息文字正確"
echo "□ 「立即重啟」按鈕功能正常"
echo "□ 「稍後重啟」按鈕功能正常"
echo "□ 對話框無法取消（點擊外部不會關閉）"
echo "□ 重啟提示卡片正常顯示"
echo "□ 返回鍵重啟邏輯正常"
echo "□ 中文介面文字正確"
echo "□ 英文介面文字正確"
echo "□ 日文介面文字正確"
echo "□ 韓文介面文字正確"
echo ""

echo "💡 提示："
echo "========"
echo "- 如果對話框沒有彈出，請檢查 logcat 輸出"
echo "- 如果重啟失敗，請檢查應用權限設定"
echo "- 測試時建議在不同語言環境下進行"
echo ""

# 提供 logcat 監控命令
echo "🔍 如需查看詳細日誌，請執行："
echo "adb logcat | grep -E '(AutoLaunch|LanguageSettings)'"
echo ""

echo "測試完成後，請在上方清單中標記 ✅ 已通過的項目"
