export const languages = {
  'en': 'English',
  'tw': '繁體中文',
  'jp': '日本語',
  'ko': '한국어'
};

export const defaultLang = 'en';

export const ui = {
  'tw': {
    // Navigation
    'nav.features': '核心功能',
    'nav.useCases': '應用情境',
    'nav.faq': '常見問題',
    'nav.contact': '聯絡我們',
    
    // Hero Section
    'hero.title': '定時啟動 APP',
    'hero.titleHighlight': '就這麼簡單！',
    'hero.subtitle': '讓 AutoLaunch 解放你的雙手和日常瑣事！',
    'hero.useCase.shopping': '🛍️ 搶優惠券',
    'hero.useCase.checkin': '📝 每日簽到',
    'hero.useCase.news': '📰 晨間新聞',
    'hero.useCase.work': '⏰ 上班打卡',
    'hero.useCase.music': '🎵 音樂播放',
    'hero.downloadPlay': 'Get it on Google Play',
    'hero.viewGithub': 'View on GitHub',
    
    // App Showcase
    'showcase.title': '為你的 APP',
    'showcase.titleHighlight': '設定專屬鬧鐘 x 智慧啟動',
    'showcase.description': '時間一到，APP 準時開啟來報到，不再忘記打卡、不再錯過搶購。把規律的事交給它，專注更重要的事。',
    'showcase.feature1.title': '精確排程',
    'showcase.feature1.desc': '支援到分鐘級的精確時間控制，讓您的排程從不失誤。',
    'showcase.feature2.title': '靈活重複',
    'showcase.feature2.desc': '單次、每日、每週或自訂重複模式，完全符合您的需求。',
    'showcase.feature3.title': '雙重啟動',
    'showcase.feature3.desc': '不僅能啟動 APP，還能開啟指定網頁，一個工具雙重功能。',
    
    // Core Features
    'features.title': 'AutoLaunch 三大核心功能',
    'features.scheduling.title': '精準排程',
    'features.scheduling.desc': '支援分鐘級的精確時間控制，單次執行、每日、每週或自訂重複模式。',
    'features.dual.title': '雙重啟動',
    'features.dual.desc': '不僅能啟動 APP，還能開啟指定網頁，一個工具雙重功能。滿足您的多元數位生活需求。',
    'features.smart.title': '智慧管理',
    'features.smart.desc': '完整的數據管理與多語言支援。本地備份、Google Drive 雲端同步、系統紀錄追蹤，支援多種語言介面。',
    
    // Use Cases
    'useCases.title': '多元應用情境',
    'useCases.subtitle': '設定ㄧ次，每天自動開啟工作應用、學習網站、或遊戲簽到。你的手機，從此更聰明。',
    'useCases.work.title': '工作效率',
    'useCases.work.desc': '準時打卡不再是難題，提早 10 分鐘自動開啟打卡 APP，讓您從容上班。',
    'useCases.work.item1': '• 上班打卡提醒',
    'useCases.work.item2': '• 工作流程啟動',
    'useCases.work.item3': '• 會議準備提醒',
    'useCases.shopping.title': '搶購優惠',
    'useCases.shopping.desc': '準點搶購限時優惠，精確到秒的時間控制，讓您永不錯過任何好康。',
    'useCases.shopping.item1': '• 限時搶購提醒',
    'useCases.shopping.item2': '• 電商優惠開搶',
    'useCases.shopping.item3': '• 演唱會搶票',
    'useCases.lifestyle.title': '生活習慣',
    'useCases.lifestyle.desc': '養成良好的數位生活習慣，每天固定時間自動開啟需要的應用程式。',
    'useCases.lifestyle.item1': '• 晨間新聞閱讀',
    'useCases.lifestyle.item2': '• 運動健身提醒',
    'useCases.lifestyle.item3': '• 睡前音樂播放',
    'useCases.learning.title': '學習成長',
    'useCases.learning.desc': '建立學習節奏，定時開啟學習應用，讓進步成為每日習慣。',
    'useCases.learning.item1': '• 英語學習提醒',
    'useCases.learning.item2': '• 線上課程開始',
    'useCases.learning.item3': '• 讀書計畫執行',
    'useCases.checkin.title': '定時簽到',
    'useCases.checkin.desc': '自動定時開啟簽到頁面，適用於學習、健康、社群等多種場景，輕鬆養成好習慣。',
    'useCases.checkin.item1': '• 線上課程每日簽到',
    'useCases.checkin.item2': '• 健身/運動打卡',
    'useCases.checkin.item3': '• 社群活動簽到提醒',
    'useCases.health.title': '健康管理',
    'useCases.health.desc': '關注身心健康，定時提醒您使用健康相關的應用程式。',
    'useCases.health.item1': '• 運動健身提醒',
    'useCases.health.item2': '• 冥想放鬆時間',
    'useCases.health.item3': '• 健康數據記錄',
    
    // FAQ
    'faq.title': '常見問題',
    'faq.free.question': 'App 完全免費嗎？',
    'faq.free.answer': '是的，「AutoLaunch」完全免費，且沒有任何廣告。我們相信好的工具應該讓每個人都能輕鬆使用，不會有隱藏費用或付費牆。',
    'faq.security.question': '這個 App 安全嗎？需要哪些權限？',
    'faq.security.answer': '絕對安全。為了實現核心功能，App 需要 查詢已安裝應用、開機後重新註冊排程、和 設定精確鬧鐘 等權限。所有權限都僅用於預定功能，我們絕不收集您的個人數據。',
    'faq.timing.question': '在我的手機上，排程有時候不會準時執行？',
    'faq.timing.answer': '這通常是電池優化設定造成的。請到系統設定中，將「AutoLaunch」加入電池優化白名單，以確保系統不會為了省電而終止背景排程服務。我們的 App 已經針對各大廠商的省電機制進行優化。',
    'faq.language.question': '支援哪些語言？',
    'faq.language.answer': '目前支援中文 (繁體)、English、日本語、한국어。我們會根據您的系統語言自動切換介面，您也可以在 App 內手動選擇偏好的語言。',
    'faq.reminder.question': '可以用來設定打卡提醒或搶購提醒嗎？',
    'faq.reminder.answer': '絕對可以！您可以設定在上班前 10 分鐘自動開啟打卡 APP，或在限時搶購開始前精準開啟購物 APP。支援精確到分鐘的時間控制，讓您永遠不會錯過重要時刻。',
    
    // Footer
    'footer.brand.desc': '讓您的 Android 裝置更加智慧化，透過精確的排程管理，提升您的數位生活品質。',
    'footer.product': '產品',
    'footer.product.features': '功能介紹',
    'footer.product.useCases': '應用情境',
    'footer.product.faq': '常見問題',
    'footer.product.tutorial': '使用教學',
    'footer.product.updates': '版本更新',
    'footer.support': '支援',
    'footer.support.contact': '聯絡我們',
    'footer.support.privacy': '隱私權政策',
    'footer.support.terms': '服務條款',
    'footer.support.github': 'GitHub',
    'footer.copyright': '© 2025 AutoLaunch. All Rights Reserved.',
    'footer.made': 'Made with ❤️ for Android users',
    
    // Meta
    'meta.title': 'AutoLaunch - 您的智慧應用啟動排程工具',
    'meta.description': '根據您的時間需求，精確控制應用程式啟動時機。支援 APP 和網頁排程，提供多語言介面，讓您在指定時間自動開啟所需的應用程式。'
  },
  'en': {
    // Navigation
    'nav.features': 'Features',
    'nav.useCases': 'Use Cases',
    'nav.faq': 'FAQ',
    'nav.contact': 'Contact Us',

    // Hero Section
    'hero.title': 'Schedule App Launch',
    'hero.titleHighlight': 'Made Simple!',
    'hero.subtitle': 'Let AutoLaunch free your hands from daily routines!',
    'hero.useCase.shopping': '🛍️ Flash Sales',
    'hero.useCase.checkin': '📝 Daily Check-in',
    'hero.useCase.news': '📰 Morning News',
    'hero.useCase.work': '⏰ Work Clock-in',
    'hero.useCase.music': '🎵 Music Player',
    'hero.downloadPlay': 'Get it on Google Play',
    'hero.viewGithub': 'View on GitHub',

    // App Showcase
    'showcase.title': 'Set Custom Alarms',
    'showcase.titleHighlight': 'for Your Apps x Smart Launch',
    'showcase.description': 'When the time comes, apps launch on schedule. Never forget to clock in, never miss flash sales. Let it handle the routine, focus on what matters.',
    'showcase.feature1.title': 'Precise Scheduling',
    'showcase.feature1.desc': 'Minute-level precision control ensures your schedules never fail.',
    'showcase.feature2.title': 'Flexible Repeats',
    'showcase.feature2.desc': 'One-time, daily, weekly, or custom repeat patterns to match your needs.',
    'showcase.feature3.title': 'Dual Launch',
    'showcase.feature3.desc': 'Launch apps and open specific web pages - one tool, dual functionality.',

    // Core Features
    'features.title': 'AutoLaunch Three Core Features',
    'features.scheduling.title': 'Precise Scheduling',
    'features.scheduling.desc': 'Minute-level precision control with one-time, daily, weekly, or custom repeat modes.',
    'features.dual.title': 'Dual Launch',
    'features.dual.desc': 'Launch apps and open web pages - one tool, dual functionality for your diverse digital lifestyle.',
    'features.smart.title': 'Smart Management',
    'features.smart.desc': 'Complete data management and multi-language support. Local backup, Google Drive cloud sync, system log tracking, and multiple language interfaces.',

    // Use Cases
    'useCases.title': 'Diverse Application Scenarios',
    'useCases.subtitle': 'Set once, automatically open work apps, learning websites, or game check-ins daily. Make your phone smarter.',
    'useCases.work.title': 'Work Efficiency',
    'useCases.work.desc': 'Punctual clock-in is no longer a problem. Auto-open clock-in app 10 minutes early for a relaxed start to work.',
    'useCases.work.item1': '• Work clock-in reminders',
    'useCases.work.item2': '• Workflow startup',
    'useCases.work.item3': '• Meeting preparation alerts',
    'useCases.shopping.title': 'Flash Sales',
    'useCases.shopping.desc': 'Precise flash sale timing with second-level control, never miss any great deals.',
    'useCases.shopping.item1': '• Limited-time sale alerts',
    'useCases.shopping.item2': '• E-commerce deal launches',
    'useCases.shopping.item3': '• Concert ticket rushes',
    'useCases.lifestyle.title': 'Lifestyle Habits',
    'useCases.lifestyle.desc': 'Build good digital lifestyle habits by auto-opening needed apps at fixed times daily.',
    'useCases.lifestyle.item1': '• Morning news reading',
    'useCases.lifestyle.item2': '• Exercise reminders',
    'useCases.lifestyle.item3': '• Bedtime music',
    'useCases.learning.title': 'Learning Growth',
    'useCases.learning.desc': 'Establish learning rhythm, schedule learning apps to make progress a daily habit.',
    'useCases.learning.item1': '• English learning reminders',
    'useCases.learning.item2': '• Online course starts',
    'useCases.learning.item3': '• Study plan execution',
    'useCases.checkin.title': 'Scheduled Check-ins',
    'useCases.checkin.desc': 'Auto-schedule check-in pages for learning, health, social scenarios - easily build good habits.',
    'useCases.checkin.item1': '• Daily online course check-ins',
    'useCases.checkin.item2': '• Fitness/exercise tracking',
    'useCases.checkin.item3': '• Social activity check-in alerts',
    'useCases.health.title': 'Health Management',
    'useCases.health.desc': 'Focus on physical and mental health with timed reminders for health-related apps.',
    'useCases.health.item1': '• Exercise reminders',
    'useCases.health.item2': '• Meditation time',
    'useCases.health.item3': '• Health data recording',

    // FAQ
    'faq.title': 'Frequently Asked Questions',
    'faq.free.question': 'Is the app completely free?',
    'faq.free.answer': 'Yes, "AutoLaunch" is completely free with no ads. We believe good tools should be accessible to everyone without hidden fees or paywalls.',
    'faq.security.question': 'Is this app safe? What permissions does it need?',
    'faq.security.answer': 'Absolutely safe. To achieve core functionality, the app needs permissions to query installed apps, re-register schedules after boot, and set precise alarms. All permissions are used only for intended functions - we never collect your personal data.',
    'faq.timing.question': 'Sometimes schedules don\'t execute on time on my phone?',
    'faq.timing.answer': 'This is usually caused by battery optimization settings. Please go to system settings and add "AutoLaunch" to the battery optimization whitelist to ensure the system doesn\'t terminate background scheduling services to save power. Our app is optimized for major manufacturers\' power-saving mechanisms.',
    'faq.language.question': 'What languages are supported?',
    'faq.language.answer': 'Currently supports Traditional Chinese, English, Japanese, and Korean. We automatically switch interfaces based on your system language, and you can manually select your preferred language within the app.',
    'faq.reminder.question': 'Can it be used for clock-in or flash sale reminders?',
    'faq.reminder.answer': 'Absolutely! You can set it to auto-open clock-in apps 10 minutes before work, or precisely open shopping apps before flash sales start. With minute-level precision control, you\'ll never miss important moments.',

    // Footer
    'footer.brand.desc': 'Make your Android device smarter through precise schedule management, enhancing your digital lifestyle quality.',
    'footer.product': 'Product',
    'footer.product.features': 'Features',
    'footer.product.useCases': 'Use Cases',
    'footer.product.faq': 'FAQ',
    'footer.product.tutorial': 'Tutorial',
    'footer.product.updates': 'Updates',
    'footer.support': 'Support',
    'footer.support.contact': 'Contact Us',
    'footer.support.privacy': 'Privacy Policy',
    'footer.support.terms': 'Terms of Service',
    'footer.support.github': 'GitHub',
    'footer.copyright': '© 2025 AutoLaunch. All Rights Reserved.',
    'footer.made': 'Made with ❤️ for Android users',

    // Meta
    'meta.title': 'AutoLaunch - Your Smart App Launch Scheduler',
    'meta.description': 'Precisely control app launch timing based on your schedule needs. Support app and web scheduling with multi-language interface, automatically open required apps at specified times.'
  },
  'jp': {
    // Navigation
    'nav.features': '主要機能',
    'nav.useCases': '利用シーン',
    'nav.faq': 'よくある質問',
    'nav.contact': 'お問い合わせ',

    // Hero Section
    'hero.title': 'アプリの定時起動',
    'hero.titleHighlight': 'こんなに簡単！',
    'hero.subtitle': 'AutoLaunchで日常の手間を解放しましょう！',
    'hero.useCase.shopping': '🛍️ セール争奪',
    'hero.useCase.checkin': '📝 毎日チェックイン',
    'hero.useCase.news': '📰 朝のニュース',
    'hero.useCase.work': '⏰ 出勤打刻',
    'hero.useCase.music': '🎵 音楽再生',
    'hero.downloadPlay': 'Google Playで入手',
    'hero.viewGithub': 'GitHubで見る',

    // App Showcase
    'showcase.title': 'あなたのアプリに',
    'showcase.titleHighlight': '専用アラーム x スマート起動を設定',
    'showcase.description': '時間になったら、アプリが時間通りに起動して報告。もう打刻を忘れることも、セールを逃すこともありません。ルーティンはお任せして、重要なことに集中しましょう。',
    'showcase.feature1.title': '精密スケジューリング',
    'showcase.feature1.desc': '分単位の精密な時間制御で、スケジュールが失敗することはありません。',
    'showcase.feature2.title': '柔軟な繰り返し',
    'showcase.feature2.desc': '一回限り、毎日、毎週、またはカスタム繰り返しパターンで、あなのニーズに完全対応。',
    'showcase.feature3.title': 'デュアル起動',
    'showcase.feature3.desc': 'アプリ起動だけでなく、指定したウェブページも開ける - 一つのツールで二つの機能。',

    // Core Features
    'features.title': 'AutoLaunch 3つのコア機能',
    'features.scheduling.title': '精密スケジューリング',
    'features.scheduling.desc': '分単位の精密な時間制御で、一回実行、毎日、毎週、またはカスタム繰り返しモード。',
    'features.dual.title': 'デュアル起動',
    'features.dual.desc': 'アプリ起動だけでなく、指定したウェブページも開ける - 一つのツールで二つの機能。多様なデジタルライフのニーズを満たします。',
    'features.smart.title': 'スマート管理',
    'features.smart.desc': '完全なデータ管理と多言語サポート。ローカルバックアップ、Google Driveクラウド同期、システムログ追跡、複数言語インターフェース対応。',

    // Use Cases
    'useCases.title': '多様な利用シーン',
    'useCases.subtitle': '一度設定すれば、毎日自動で仕事アプリ、学習サイト、ゲームチェックインを開きます。あなたのスマホがもっとスマートに。',
    'useCases.work.title': '仕事効率',
    'useCases.work.desc': '時間通りの打刻はもう問題ありません。10分前に自動で打刻アプリを開いて、余裕を持って出勤できます。',
    'useCases.work.item1': '• 出勤打刻リマインダー',
    'useCases.work.item2': '• ワークフロー起動',
    'useCases.work.item3': '• 会議準備アラート',
    'useCases.shopping.title': 'セール争奪',
    'useCases.shopping.desc': '限定セールの正確なタイミングで、秒単位の制御により、お得な情報を逃しません。',
    'useCases.shopping.item1': '• 限定セールアラート',
    'useCases.shopping.item2': '• ECサイト特価開始',
    'useCases.shopping.item3': '• コンサートチケット争奪',
    'useCases.lifestyle.title': '生活習慣',
    'useCases.lifestyle.desc': '良いデジタル生活習慣を築き、毎日決まった時間に必要なアプリを自動で開きます。',
    'useCases.lifestyle.item1': '• 朝のニュース読み',
    'useCases.lifestyle.item2': '• 運動リマインダー',
    'useCases.lifestyle.item3': '• 就寝前音楽',
    'useCases.learning.title': '学習成長',
    'useCases.learning.desc': '学習リズムを確立し、学習アプリを定時起動して、進歩を日常の習慣にします。',
    'useCases.learning.item1': '• 英語学習リマインダー',
    'useCases.learning.item2': '• オンライン講座開始',
    'useCases.learning.item3': '• 学習計画実行',
    'useCases.checkin.title': '定時チェックイン',
    'useCases.checkin.desc': '学習、健康、ソーシャルなど様々なシーンでチェックインページを自動定時開放、良い習慣を簡単に築けます。',
    'useCases.checkin.item1': '• オンライン講座毎日チェックイン',
    'useCases.checkin.item2': '• フィットネス/運動記録',
    'useCases.checkin.item3': '• ソーシャル活動チェックインアラート',
    'useCases.health.title': '健康管理',
    'useCases.health.desc': '心身の健康に注目し、健康関連アプリの使用を定時でリマインドします。',
    'useCases.health.item1': '• 運動リマインダー',
    'useCases.health.item2': '• 瞑想リラックス時間',
    'useCases.health.item3': '• 健康データ記録',

    // FAQ
    'faq.title': 'よくある質問',
    'faq.free.question': 'アプリは完全無料ですか？',
    'faq.free.answer': 'はい、「AutoLaunch」は完全無料で、広告もありません。良いツールは誰でも簡単に使えるべきだと信じており、隠れた費用や有料の壁はありません。',
    'faq.security.question': 'このアプリは安全ですか？どんな権限が必要ですか？',
    'faq.security.answer': '絶対に安全です。コア機能を実現するため、アプリはインストール済みアプリの照会、起動後のスケジュール再登録、精密アラーム設定などの権限が必要です。すべての権限は予定された機能のみに使用され、個人データは一切収集しません。',
    'faq.timing.question': '私のスマホでは、スケジュールが時々時間通りに実行されません？',
    'faq.timing.answer': 'これは通常、バッテリー最適化設定が原因です。システム設定で「AutoLaunch」をバッテリー最適化ホワイトリストに追加して、システムが省電力のためにバックグラウンドスケジュールサービスを終了しないようにしてください。私たちのアプリは各メーカーの省電力メカニズムに最適化されています。',
    'faq.language.question': 'どの言語がサポートされていますか？',
    'faq.language.answer': '現在、中国語（繁体字）、英語、日本語、韓国語をサポートしています。システム言語に基づいて自動でインターフェースを切り替え、アプリ内で好みの言語を手動選択することもできます。',
    'faq.reminder.question': '打刻リマインダーやセール争奪リマインダーに使えますか？',
    'faq.reminder.answer': '絶対にできます！出勤10分前に自動で打刻アプリを開いたり、限定セール開始前に正確にショッピングアプリを開いたりできます。分単位の精密制御で、重要な瞬間を逃すことはありません。',

    // Footer
    'footer.brand.desc': '精密なスケジュール管理を通じてAndroidデバイスをよりスマートにし、デジタルライフの質を向上させます。',
    'footer.product': '製品',
    'footer.product.features': '機能紹介',
    'footer.product.useCases': '利用シーン',
    'footer.product.faq': 'よくある質問',
    'footer.product.tutorial': '使用チュートリアル',
    'footer.product.updates': 'バージョン更新',
    'footer.support': 'サポート',
    'footer.support.contact': 'お問い合わせ',
    'footer.support.privacy': 'プライバシーポリシー',
    'footer.support.terms': '利用規約',
    'footer.support.github': 'GitHub',
    'footer.copyright': '© 2025 AutoLaunch. All Rights Reserved.',
    'footer.made': 'Androidユーザーのために❤️で作成',

    // Meta
    'meta.title': 'AutoLaunch - あなたのスマートアプリ起動スケジューラー',
    'meta.description': 'スケジュールニーズに基づいてアプリ起動タイミングを精密制御。アプリとウェブスケジューリングをサポートし、多言語インターフェースで指定時間に必要なアプリを自動開放。'
  },
  'ko': {
    // Navigation
    'nav.features': '핵심 기능',
    'nav.useCases': '활용 사례',
    'nav.faq': '자주 묻는 질문',
    'nav.contact': '문의하기',

    // Hero Section
    'hero.title': '앱 정시 실행',
    'hero.titleHighlight': '이렇게 간단해요!',
    'hero.subtitle': 'AutoLaunch로 일상의 번거로움에서 해방되세요!',
    'hero.useCase.shopping': '🛍️ 할인 쿠폰 쟁탈',
    'hero.useCase.checkin': '📝 매일 체크인',
    'hero.useCase.news': '📰 아침 뉴스',
    'hero.useCase.work': '⏰ 출근 체크',
    'hero.useCase.music': '🎵 음악 재생',
    'hero.downloadPlay': 'Google Play에서 받기',
    'hero.viewGithub': 'GitHub에서 보기',

    // App Showcase
    'showcase.title': '당신의 앱을 위한',
    'showcase.titleHighlight': '전용 알람 x 스마트 실행 설정',
    'showcase.description': '시간이 되면 앱이 정시에 실행되어 보고합니다. 더 이상 체크인을 잊거나 할인을 놓치지 마세요. 루틴은 맡기고 중요한 일에 집중하세요.',
    'showcase.feature1.title': '정밀 스케줄링',
    'showcase.feature1.desc': '분 단위의 정밀한 시간 제어로 스케줄이 실패하는 일이 없습니다.',
    'showcase.feature2.title': '유연한 반복',
    'showcase.feature2.desc': '일회성, 매일, 매주 또는 사용자 정의 반복 패턴으로 당신의 요구에 완벽 대응.',
    'showcase.feature3.title': '이중 실행',
    'showcase.feature3.desc': '앱 실행뿐만 아니라 지정된 웹페이지도 열 수 있는 - 하나의 도구로 두 가지 기능.',

    // Core Features
    'features.title': 'AutoLaunch 3가지 핵심 기능',
    'features.scheduling.title': '정밀 스케줄링',
    'features.scheduling.desc': '분 단위의 정밀한 시간 제어로 일회 실행, 매일, 매주 또는 사용자 정의 반복 모드.',
    'features.dual.title': '이중 실행',
    'features.dual.desc': '앱 실행뿐만 아니라 지정된 웹페이지도 열 수 있는 - 하나의 도구로 두 가지 기능. 다양한 디지털 라이프 요구를 충족합니다.',
    'features.smart.title': '스마트 관리',
    'features.smart.desc': '완전한 데이터 관리와 다국어 지원. 로컬 백업, Google Drive 클라우드 동기화, 시스템 로그 추적, 다중 언어 인터페이스 지원.',

    // Use Cases
    'useCases.title': '다양한 활용 시나리오',
    'useCases.subtitle': '한 번 설정하면 매일 자동으로 업무 앱, 학습 웹사이트, 게임 체크인을 엽니다. 당신의 스마트폰이 더욱 스마트해집니다.',
    'useCases.work.title': '업무 효율성',
    'useCases.work.desc': '정시 체크인은 더 이상 문제가 되지 않습니다. 10분 전에 자동으로 체크인 앱을 열어 여유롭게 출근하세요.',
    'useCases.work.item1': '• 출근 체크인 알림',
    'useCases.work.item2': '• 워크플로우 시작',
    'useCases.work.item3': '• 회의 준비 알림',
    'useCases.shopping.title': '할인 쟁탈',
    'useCases.shopping.desc': '한정 할인의 정확한 타이밍으로 초 단위 제어를 통해 어떤 좋은 기회도 놓치지 않습니다.',
    'useCases.shopping.item1': '• 한정 할인 알림',
    'useCases.shopping.item2': '• 전자상거래 특가 시작',
    'useCases.shopping.item3': '• 콘서트 티켓 쟁탈',
    'useCases.lifestyle.title': '생활 습관',
    'useCases.lifestyle.desc': '좋은 디지털 생활 습관을 구축하여 매일 정해진 시간에 필요한 앱을 자동으로 엽니다.',
    'useCases.lifestyle.item1': '• 아침 뉴스 읽기',
    'useCases.lifestyle.item2': '• 운동 알림',
    'useCases.lifestyle.item3': '• 취침 전 음악',
    'useCases.learning.title': '학습 성장',
    'useCases.learning.desc': '학습 리듬을 확립하고 학습 앱을 정시 실행하여 진보를 일상 습관으로 만듭니다.',
    'useCases.learning.item1': '• 영어 학습 알림',
    'useCases.learning.item2': '• 온라인 강의 시작',
    'useCases.learning.item3': '• 학습 계획 실행',
    'useCases.checkin.title': '정시 체크인',
    'useCases.checkin.desc': '학습, 건강, 소셜 등 다양한 시나리오에서 체크인 페이지를 자동 정시 개방하여 좋은 습관을 쉽게 구축할 수 있습니다.',
    'useCases.checkin.item1': '• 온라인 강의 매일 체크인',
    'useCases.checkin.item2': '• 피트니스/운동 기록',
    'useCases.checkin.item3': '• 소셜 활동 체크인 알림',
    'useCases.health.title': '건강 관리',
    'useCases.health.desc': '신체적, 정신적 건강에 주목하여 건강 관련 앱 사용을 정시 알림합니다.',
    'useCases.health.item1': '• 운동 알림',
    'useCases.health.item2': '• 명상 휴식 시간',
    'useCases.health.item3': '• 건강 데이터 기록',

    // FAQ
    'faq.title': '자주 묻는 질문',
    'faq.free.question': '앱이 완전 무료인가요?',
    'faq.free.answer': '네, "AutoLaunch"는 완전 무료이며 광고도 없습니다. 좋은 도구는 모든 사람이 쉽게 사용할 수 있어야 한다고 믿으며, 숨겨진 비용이나 유료 벽이 없습니다.',
    'faq.security.question': '이 앱은 안전한가요? 어떤 권한이 필요한가요?',
    'faq.security.answer': '절대적으로 안전합니다. 핵심 기능을 구현하기 위해 앱은 설치된 앱 조회, 부팅 후 스케줄 재등록, 정밀 알람 설정 등의 권한이 필요합니다. 모든 권한은 예정된 기능에만 사용되며, 개인 데이터는 절대 수집하지 않습니다.',
    'faq.timing.question': '제 스마트폰에서는 스케줄이 때때로 정시에 실행되지 않나요?',
    'faq.timing.answer': '이는 보통 배터리 최적화 설정 때문입니다. 시스템 설정에서 "AutoLaunch"를 배터리 최적화 화이트리스트에 추가하여 시스템이 전력 절약을 위해 백그라운드 스케줄 서비스를 종료하지 않도록 해주세요. 저희 앱은 각 제조사의 절전 메커니즘에 최적화되어 있습니다.',
    'faq.language.question': '어떤 언어가 지원되나요?',
    'faq.language.answer': '현재 중국어(번체), 영어, 일본어, 한국어를 지원합니다. 시스템 언어에 따라 자동으로 인터페이스를 전환하며, 앱 내에서 선호하는 언어를 수동으로 선택할 수도 있습니다.',
    'faq.reminder.question': '체크인 알림이나 할인 쟁탈 알림으로 사용할 수 있나요?',
    'faq.reminder.answer': '절대적으로 가능합니다! 출근 10분 전에 자동으로 체크인 앱을 열거나 한정 할인 시작 전에 정확히 쇼핑 앱을 열 수 있습니다. 분 단위의 정밀 제어로 중요한 순간을 놓치는 일이 없습니다.',

    // Footer
    'footer.brand.desc': '정밀한 스케줄 관리를 통해 Android 기기를 더욱 스마트하게 만들어 디지털 라이프의 품질을 향상시킵니다.',
    'footer.product': '제품',
    'footer.product.features': '기능 소개',
    'footer.product.useCases': '활용 사례',
    'footer.product.faq': '자주 묻는 질문',
    'footer.product.tutorial': '사용 튜토리얼',
    'footer.product.updates': '버전 업데이트',
    'footer.support': '지원',
    'footer.support.contact': '문의하기',
    'footer.support.privacy': '개인정보 보호정책',
    'footer.support.terms': '서비스 약관',
    'footer.support.github': 'GitHub',
    'footer.copyright': '© 2025 AutoLaunch. All Rights Reserved.',
    'footer.made': 'Android 사용자를 위해 ❤️로 제작',

    // Meta
    'meta.title': 'AutoLaunch - 당신의 스마트 앱 실행 스케줄러',
    'meta.description': '스케줄 요구에 따라 앱 실행 타이밍을 정밀 제어. 앱과 웹 스케줄링을 지원하며 다국어 인터페이스로 지정된 시간에 필요한 앱을 자동 실행.'
  }
} as const;

export function getLangFromUrl(url: URL) {
  const [, lang] = url.pathname.split('/');
  if (lang in ui) return lang as keyof typeof ui;
  return defaultLang;
}

export function useTranslations(lang: keyof typeof ui) {
  return function t(key: keyof typeof ui[typeof defaultLang]) {
    return ui[lang][key] || ui[defaultLang][key];
  }
}
