@import "tailwindcss";

/* Custom styles */
.gradient-text {
  background: linear-gradient(135deg, #5b7c99 0%, #3b82f6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-gradient {
  background: linear-gradient(135deg, #f0f8ff 0%, #ffffff 50%, #f3e8ff 100%);
}

.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Smooth animations */
.fade-in {
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* FAQ accordion styles */
.faq-item {
  border-radius: 1.5rem;
  box-shadow: 0 4px 24px 0 rgba(80, 80, 120, 0.08), 0 1.5px 4px 0 rgba(80, 80, 120, 0.04);
  border: 1.5px solid #f3f4f6;
  margin-bottom: 1.5rem;
  background: #fff;
  transition: box-shadow 0.2s, border-color 0.2s;
  overflow: hidden;
}
.faq-item:hover, .faq-item.active {
  box-shadow: 0 8px 32px 0 rgba(80, 80, 120, 0.16), 0 2px 8px 0 rgba(80, 80, 120, 0.08);
  border-color: #a5b4fc;
}

.faq-question {
  display: flex;
  align-items: center;
  gap: 1rem;
  border-left: 6px solid #6366f1;
  background: linear-gradient(90deg, #f5f7ff 0%, #fff 100%);
  padding: 1.5rem 2rem;
  cursor: pointer;
  transition: background 0.2s, border-color 0.2s;
}
.faq-item.active .faq-question {
  background: linear-gradient(90deg, #e0e7ff 0%, #fff 100%);
  border-left-color: #6366f1;
}

.faq-question .faq-icon {
  transition: transform 0.3s cubic-bezier(.4,2,.6,1);
}
.faq-item.active .faq-icon {
  transform: rotate(180deg);
}

.faq-answer {
  max-height: 0;
  overflow: hidden;
  opacity: 0;
  transition: max-height 0.4s cubic-bezier(.4,2,.6,1), opacity 0.3s, padding 0.3s;
  padding: 0 2rem;
}
.faq-answer.open {
  max-height: 400px;
  opacity: 1;
  padding-bottom: 2rem;
  margin-top: 1rem;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #5b7c99;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #4a6b85;
}

.usecase-chip {
  display: inline-block;
  font-size: 0.95rem;
  padding: 0.25rem 0.9rem;
  border-radius: 9999px;
  border: 1px solid #e5e7eb;
  font-weight: 500;
  box-shadow: none;
  cursor: default;
  opacity: 0.92;
  transition: background 0.2s, color 0.2s;
  margin-bottom: 2px;
}

.usecase-block {
  display: inline-block;
  font-size: 1rem;
  padding: 0.45rem 1.2rem;
  border-radius: 12px;
  border: 1.5px solid #e5e7eb;
  font-weight: 500;
  background: inherit;
  box-shadow: 0 2px 8px 0 rgba(80, 80, 120, 0.07);
  cursor: default;
  opacity: 0.97;
  transition: box-shadow 0.2s, border-color 0.2s, background 0.2s;
  margin-bottom: 2px;
}
.usecase-block:hover {
  box-shadow: 0 4px 16px 0 rgba(80, 80, 120, 0.13);
  border-color: #c7d2fe;
  background: #f9fafb;
}

.usecase-underline {
  display: inline-block;
  font-size: 1rem;
  font-weight: 600;
  padding: 0.2rem 0.7rem 0.1rem 0.7rem;
  background: none;
  border: none;
  border-bottom: 3px solid currentColor;
  border-radius: 0;
  box-shadow: none;
  cursor: default;
  opacity: 0.98;
  transition: border-color 0.2s, color 0.2s;
  margin-bottom: 2px;
}
.usecase-underline:hover {
  color: #111827;
  border-bottom-width: 4px;
  opacity: 1;
}

.usecase-plain {
  display: inline-block;
  font-size: 1rem;
  font-weight: 600;
  padding: 0.2rem 0.7rem;
  background: none;
  border: none;
  border-radius: 8px;
  box-shadow: none;
  cursor: default;
  opacity: 0.98;
  transition: background 0.2s, box-shadow 0.2s, color 0.2s;
  margin-bottom: 2px;
}
.usecase-plain.text-purple-800:hover {
  background: #f3e8ff;
  box-shadow: 0 2px 8px 0 rgba(139, 92, 246, 0.08);
}
.usecase-plain.text-red-800:hover {
  background: #fee2e2;
  box-shadow: 0 2px 8px 0 rgba(239, 68, 68, 0.08);
}
.usecase-plain.text-blue-800:hover {
  background: #dbeafe;
  box-shadow: 0 2px 8px 0 rgba(37, 99, 235, 0.08);
}
.usecase-plain.text-green-800:hover {
  background: #d1fae5;
  box-shadow: 0 2px 8px 0 rgba(16, 185, 129, 0.08);
}
.usecase-plain.text-orange-800:hover {
  background: #ffedd5;
  box-shadow: 0 2px 8px 0 rgba(251, 146, 60, 0.08);
}

/* Anchor scroll offset for sticky header */
#features, #use-cases, #faq {
  scroll-margin-top: 80px;
}
