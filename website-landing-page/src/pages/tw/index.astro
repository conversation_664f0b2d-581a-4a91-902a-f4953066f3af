---
import Layout from '../../layouts/Layout.astro';
import LanguageSwitcher from '../../components/LanguageSwitcher.astro';
import { useTranslations } from '../../i18n';

const t = useTranslations('tw');

const navLinks = [
  { name: t('nav.features'), href: '#features' },
  { name: t('nav.useCases'), href: '#use-cases' },
  { name: t('nav.faq'), href: '#faq' },
  { name: t('nav.contact'), href: 'mailto:<EMAIL>' },
];
---

<Layout title={t('meta.title')} description={t('meta.description')} lang="zh-TW">
	<!-- Navigation -->
	<header class="sticky top-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-100">
		<div class="container mx-auto px-4">
			<div class="flex items-center justify-between h-16">
				<div class="flex items-center">
					<img src="/ic_launcher.webp" alt="AutoLaunch Logo" class="h-10 w-10 mr-3 rounded-lg shadow-sm">
					<span class="text-xl font-bold text-gray-900">AutoLaunch</span>
				</div>
				<nav class="hidden md:flex items-center space-x-8">
					{navLinks.map(link =>
						<a href={link.href} class="text-gray-600 hover:text-blue-600 transition-colors font-medium">
							{link.name}
						</a>
					)}
					<LanguageSwitcher />
				</nav>
				<div class="md:hidden flex items-center space-x-2">
					<LanguageSwitcher />
					<button class="text-gray-600">
						<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
						</svg>
					</button>
				</div>
			</div>
		</div>
	</header>

	<!-- Hero Section -->
	<section class="relative overflow-hidden hero-gradient pt-8 md:pt-8 pb-20">
		<div class="absolute inset-0" style="background-image: url('data:image/svg+xml,%3Csvg width=\'60\' height=\'60\' viewBox=\'0 0 60 60\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'none\' fill-rule=\'evenodd\'%3E%3Cg fill=\'%23f0f0f0\' fill-opacity=\'0.3\'%3E%3Ccircle cx=\'30\' cy=\'30\' r=\'1\'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E'); opacity: 0.2;"></div>
		<div class="container mx-auto px-4 relative">
			<div class="text-center max-w-4xl mx-auto fade-in">
				<!-- App Icon (改為大 logo) -->
				<div class="flex justify-center mb-8">
					<img src="/AutoLaunch-Icon-focus-in.png" alt="AutoLaunch 品牌 Logo" class="w-36 max-w-full drop-shadow-lg rounded-3xl" />
				</div>

				<!-- Main Heading -->
				<h1 class="text-5xl md:text-7xl font-extrabold text-gray-900 leading-tight mb-8">
					定時啟動 APP
					<span class="gradient-text block md:inline mt-2 md:mt-0">
						就這麼簡單！
					</span>
				</h1>

				<!-- Subtitle -->
				<p class="text-lg md:text-2xl text-gray-400 mb-8 font-light leading-relaxed">
					讓 AutoLaunch 解放你的雙手和日常瑣事！
				</p>
		
				<!-- Use Cases -->
				<div class="flex flex-wrap justify-center gap-3 mb-20">
					<span class="usecase-plain text-purple-800">🛍️ 搶優惠券</span>

					<span class="usecase-plain text-red-800">📝 每日簽到</span>
					<span class="usecase-plain text-blue-800">📰 晨間新聞</span>
					<span class="usecase-plain text-green-800">⏰ 上班打卡</span>
					<span class="usecase-plain text-orange-800">🎵 音樂播放</span>
				</div>

				<!-- Download Buttons -->
				<div class="flex flex-col sm:flex-row items-center justify-center gap-6 mb-16">
					<a href="#" class="inline-flex items-center bg-black text-white px-8 py-4 rounded-2xl hover:bg-gray-800 transition-all duration-300 transform hover:scale-105 shadow-lg">
						<svg class="w-6 h-6 mr-3" viewBox="0 0 24 24" fill="currentColor">
							<path d="M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.61 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.9 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z"/>
						</svg>
						Get it on Google Play
					</a>
					<a href="#" class="inline-flex items-center bg-gray-100 text-gray-800 px-8 py-4 rounded-2xl hover:bg-gray-200 transition-all duration-300 transform hover:scale-105 shadow-lg">
						<svg class="w-6 h-6 mr-3" fill="currentColor" viewBox="0 0 24 24">
							<path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.09.111.104.209.077.32-.085.36-.276 1.122-.314 1.279-.049.2-.402.244-.402.244-.402-.244-.925-2.070-.925-3.33 0-3.771 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.750-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.90-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z"/>
						</svg>
						View on GitHub
					</a>
				</div>
			</div>
		</div>
	</section>

	<!-- App Showcase Section -->
	<section class="py-20 bg-white">
		<div class="container mx-auto px-4">
			<div class="flex flex-col lg:flex-row items-center gap-20">
				<!-- Phone Mockup -->
				<div class="flex-1 flex justify-center">
					<div class="relative">
						<!-- Phone Frame -->
						<div class="relative w-80 h-[640px] bg-gray-900 rounded-[3rem] p-3 shadow-xl">
							<div class="w-full h-full bg-white rounded-[2.5rem] overflow-hidden relative">
								<!-- Status Bar -->
								<div class="h-6 bg-gray-100 flex items-center justify-between px-6 text-xs font-semibold text-gray-800">
									<span>9:41</span>
									<div class="flex items-center space-x-1">
										<div class="w-4 h-2 bg-green-500 rounded-sm"></div>
										<div class="w-4 h-2 bg-gray-300 rounded-sm"></div>
									</div>
								</div>
								<!-- App Content Preview -->
								<div class="p-4 h-full bg-gradient-to-b from-blue-50 to-white">
									<h3 class="text-xl font-bold text-gray-900 mb-4">我的排程</h3>
									<div class="space-y-3">
										<div class="bg-white rounded-xl p-3 shadow-sm border border-gray-100">
											<div class="flex items-center justify-between">
												<div class="flex items-center space-x-2">
													<div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
														<svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
															<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
														</svg>
													</div>
													<div>
														<p class="font-semibold text-gray-900 text-sm">上班打卡</p>
														<p class="text-xs text-gray-500">週一至週五 08:50</p>
													</div>
												</div>
												<div class="w-10 h-5 bg-green-500 rounded-full"></div>
											</div>
										</div>
										<div class="bg-white rounded-xl p-3 shadow-sm border border-gray-100">
											<div class="flex items-center justify-between">
												<div class="flex items-center space-x-2">
													<div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
														<svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
															<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
														</svg>
													</div>
													<div>
														<p class="font-semibold text-gray-900 text-sm">搶優惠券</p>
														<p class="text-xs text-gray-500">11/11 00:00</p>
													</div>
												</div>
												<div class="w-10 h-5 bg-green-500 rounded-full"></div>
											</div>
										</div>
										<div class="bg-white rounded-xl p-3 shadow-sm border border-gray-100">
											<div class="flex items-center justify-between">
												<div class="flex items-center space-x-2">
													<div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
														<svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
															<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
														</svg>
													</div>
													<div>
														<p class="font-semibold text-gray-900 text-sm">晨間新聞</p>
														<p class="text-xs text-gray-500">每日 07:00</p>
													</div>
												</div>
												<div class="w-10 h-5 bg-gray-300 rounded-full"></div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- Feature Highlights -->
				<div class="flex-1 space-y-8">
					<div>
						<h2 class="text-4xl font-bold text-gray-900 mb-6">
							為你的 APP
							<br />
							<span class="text-blue-600">設定專屬鬧鐘 x 智慧啟動</span>
						</h2>
						<p class="text-xl text-gray-600 leading-relaxed">
							時間一到，APP 準時開啟來報到，不再忘記打卡、不再錯過搶購。把規律的事交給它，專注更重要的事。
						</p>
					</div>

					<div class="grid gap-6">
						<div class="flex items-start space-x-4">
							<div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
								<svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
								</svg>
							</div>
							<div>
								<h3 class="text-xl font-semibold text-gray-900 mb-2">精確排程</h3>
								<p class="text-gray-600">支援到分鐘級的精確時間控制，讓您的排程從不失誤。</p>
							</div>
						</div>

						<div class="flex items-start space-x-4">
							<div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
								<svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h5M20 20v-5h-5"></path>
								</svg>
							</div>
							<div>
								<h3 class="text-xl font-semibold text-gray-900 mb-2">靈活重複</h3>
								<p class="text-gray-600">單次、每日、每週或自訂重複模式，完全符合您的需求。</p>
							</div>
						</div>

						<div class="flex items-start space-x-4">
							<div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
								<svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101"></path>
								</svg>
							</div>
							<div>
								<h3 class="text-xl font-semibold text-gray-900 mb-2">雙重啟動</h3>
								<p class="text-gray-600">不僅能啟動 APP，還能開啟指定網頁，一個工具雙重功能。</p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Three Core Features Section -->
	<section id="features" class="py-24 bg-gray-50">
		<div class="container mx-auto px-4">
			<div class="text-center mb-16">
				<h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">AutoLaunch 三大核心功能</h2>
				
			</div>

			<div class="grid md:grid-cols-3 gap-8">
				<div class="text-center group">
					<div class="bg-white rounded-3xl p-8 shadow-lg card-hover">
						<div class="flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 text-white rounded-2xl mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
							<svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
							</svg>
						</div>
						<h3 class="text-2xl font-bold text-gray-900 mb-4">精準排程</h3>
						<p class="text-gray-600 leading-relaxed">
							支援分鐘級的精確時間控制，單次執行、每日、每週或自訂重複模式。
						</p>
					</div>
				</div>

				<div class="text-center group">
					<div class="bg-white rounded-3xl p-8 shadow-lg card-hover">
						<div class="flex items-center justify-center w-20 h-20 bg-gradient-to-br from-purple-500 to-purple-600 text-white rounded-2xl mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
							<svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
							</svg>
						</div>
						<h3 class="text-2xl font-bold text-gray-900 mb-4">雙重啟動</h3>
						<p class="text-gray-600 leading-relaxed">
							不僅能啟動 APP，還能開啟指定網頁，一個工具雙重功能。滿足您的多元數位生活需求。
						</p>
					</div>
				</div>

				<div class="text-center group">
					<div class="bg-white rounded-3xl p-8 shadow-lg card-hover">
						<div class="flex items-center justify-center w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 text-white rounded-2xl mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
							<svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
							</svg>
						</div>
						<h3 class="text-2xl font-bold text-gray-900 mb-4">智慧管理</h3>
						<p class="text-gray-600 leading-relaxed">
							完整的數據管理與多語言支援。本地備份、Google Drive 雲端同步、系統紀錄追蹤，支援多種語言介面。
						</p>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Use Cases Section -->
	<section id="use-cases" class="py-24 bg-white">
		<div class="container mx-auto px-4">
			<div class="text-center mb-16">
				<h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">多元應用情境</h2>
				<p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
					設定ㄧ次，每天自動開啟工作應用、學習網站、或遊戲簽到。你的手機，從此更聰明。
				</p>
			</div>

			<div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
				<!-- 工作場景 -->
				<div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-3xl p-8 hover:shadow-lg transition-shadow duration-300">
					<div class="w-16 h-16 bg-blue-500 rounded-2xl flex items-center justify-center mb-6">
						<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
						</svg>
					</div>
					<h3 class="text-2xl font-bold text-gray-900 mb-4">工作效率</h3>
					<p class="text-gray-600 mb-4">準時打卡不再是難題，提早 10 分鐘自動開啟打卡 APP，讓您從容上班。</p>
					<ul class="text-gray-600 space-y-2">
						<li>• 上班打卡提醒</li>
						<li>• 工作流程啟動</li>
						<li>• 會議準備提醒</li>
					</ul>
				</div>

				<!-- 購物場景 -->
				<div class="bg-gradient-to-br from-red-50 to-red-100 rounded-3xl p-8 hover:shadow-lg transition-shadow duration-300">
					<div class="w-16 h-16 bg-red-500 rounded-2xl flex items-center justify-center mb-6">
						<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
						</svg>
					</div>
					<h3 class="text-2xl font-bold text-gray-900 mb-4">搶購優惠</h3>
					<p class="text-gray-600 mb-4">準點搶購限時優惠，精確到秒的時間控制，讓您永不錯過任何好康。</p>
					<ul class="text-gray-600 space-y-2">
						<li>• 限時搶購提醒</li>
						<li>• 電商優惠開搶</li>
						<li>• 演唱會搶票</li>
					</ul>
				</div>

				<!-- 生活場景 -->
				<div class="bg-gradient-to-br from-green-50 to-green-100 rounded-3xl p-8 hover:shadow-lg transition-shadow duration-300">
					<div class="w-16 h-16 bg-green-500 rounded-2xl flex items-center justify-center mb-6">
						<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
						</svg>
					</div>
					<h3 class="text-2xl font-bold text-gray-900 mb-4">生活習慣</h3>
					<p class="text-gray-600 mb-4">養成良好的數位生活習慣，每天固定時間自動開啟需要的應用程式。</p>
					<ul class="text-gray-600 space-y-2">
						<li>• 晨間新聞閱讀</li>
						<li>• 運動健身提醒</li>
						<li>• 睡前音樂播放</li>
					</ul>
				</div>

				<!-- 學習場景 -->
				<div class="bg-gradient-to-br from-purple-50 to-purple-100 rounded-3xl p-8 hover:shadow-lg transition-shadow duration-300">
					<div class="w-16 h-16 bg-purple-500 rounded-2xl flex items-center justify-center mb-6">
						<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
						</svg>
					</div>
					<h3 class="text-2xl font-bold text-gray-900 mb-4">學習成長</h3>
					<p class="text-gray-600 mb-4">建立學習節奏，定時開啟學習應用，讓進步成為每日習慣。</p>
					<ul class="text-gray-600 space-y-2">
						<li>• 英語學習提醒</li>
						<li>• 線上課程開始</li>
						<li>• 讀書計畫執行</li>
					</ul>
				</div>

				<!-- 定時簽到場景 -->
				<div class="bg-gradient-to-br from-cyan-50 to-cyan-100 rounded-3xl p-8 hover:shadow-lg transition-shadow duration-300">
					<div class="w-16 h-16 bg-cyan-500 rounded-2xl flex items-center justify-center mb-6">
						<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
						</svg>
					</div>
					<h3 class="text-2xl font-bold text-gray-900 mb-4">定時簽到</h3>
					<p class="text-gray-600 mb-4">自動定時開啟簽到頁面，適用於學習、健康、社群等多種場景，輕鬆養成好習慣。</p>
					<ul class="text-gray-600 space-y-2">
						<li>• 線上課程每日簽到</li>
						<li>• 健身/運動打卡</li>
						<li>• 社群活動簽到提醒</li>
					</ul>
				</div>

				<!-- 健康場景 -->
				<div class="bg-gradient-to-br from-pink-50 to-pink-100 rounded-3xl p-8 hover:shadow-lg transition-shadow duration-300">
					<div class="w-16 h-16 bg-pink-500 rounded-2xl flex items-center justify-center mb-6">
						<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
						</svg>
					</div>
					<h3 class="text-2xl font-bold text-gray-900 mb-4">健康管理</h3>
					<p class="text-gray-600 mb-4">關注身心健康，定時提醒您使用健康相關的應用程式。</p>
					<ul class="text-gray-600 space-y-2">
						<li>• 運動健身提醒</li>
						<li>• 冥想放鬆時間</li>
						<li>• 健康數據記錄</li>
					</ul>
				</div>
			</div>
		</div>
	</section>

	<!-- FAQ Section -->
	<section id="faq" class="py-8 bg-white">
		<div class="container mx-auto px-4">
			<div class="text-center mb-16">
				<h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">常見問題</h2>
				
			</div>

			<div class="max-w-4xl mx-auto">
				<div class="space-y-4" id="faq-list">

					<div class="faq-item">
						<button class="faq-question flex items-center justify-between w-full text-left" type="button">
							<span class="faq-icon flex items-center justify-center w-8 h-8 bg-purple-100 text-purple-600 rounded-full mr-2">
								<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
								</svg>
							</span>
							<span class="text-lg font-semibold text-gray-900">App 完全免費嗎？</span>
							<svg class="faq-icon h-6 w-6 text-gray-400 ml-auto" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
							</svg>
						</button>
						<div class="faq-answer px-6 pb-6 text-gray-600">
							<p class="leading-relaxed">是的，「AutoLaunch」完全免費，且沒有任何廣告。我們相信好的工具應該讓每個人都能輕鬆使用，不會有隱藏費用或付費牆。</p>
						</div>
					</div>

					<div class="faq-item">
						<button class="faq-question flex items-center justify-between w-full text-left" type="button">
							<span class="faq-icon flex items-center justify-center w-8 h-8 bg-gray-100 text-gray-500 rounded-full mr-2">
								<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M12 14a4 4 0 10-4-4 4 4 0 004 4z" />
								</svg>
							</span>
							<span class="text-lg font-semibold text-gray-900">這個 App 安全嗎？需要哪些權限？</span>
							<svg class="faq-icon h-6 w-6 text-gray-400 ml-auto" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
							</svg>
						</button>
						<div class="faq-answer px-6 pb-6 text-gray-600">
							<p class="leading-relaxed">絕對安全。為了實現核心功能，App 需要 <code class="bg-gray-200 px-2 py-1 rounded text-sm">查詢已安裝應用</code>、<code class="bg-gray-200 px-2 py-1 rounded text-sm">開機後重新註冊排程</code>、和 <code class="bg-gray-200 px-2 py-1 rounded text-sm">設定精確鬧鐘</code> 等權限。所有權限都僅用於預定功能，我們絕不收集您的個人數據。</p>
						</div>
					</div>

					<div class="faq-item">
						<button class="faq-question flex items-center justify-between w-full text-left" type="button">
							<span class="faq-icon flex items-center justify-center w-8 h-8 bg-red-100 text-red-600 rounded-full mr-2">
								<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
								</svg>
							</span>
							<span class="text-lg font-semibold text-gray-900">在我的手機上，排程有時候不會準時執行？</span>
							<svg class="faq-icon h-6 w-6 text-gray-400 ml-auto" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
							</svg>
						</button>
						<div class="faq-answer px-6 pb-6 text-gray-600">
							<p class="leading-relaxed">這通常是電池優化設定造成的。請到系統設定中，將「AutoLaunch」加入電池優化白名單，以確保系統不會為了省電而終止背景排程服務。我們的 App 已經針對各大廠商的省電機制進行優化。</p>
						</div>
					</div>

					<div class="faq-item">
						<button class="faq-question flex items-center justify-between w-full text-left" type="button">
							<span class="faq-icon flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-600 rounded-full mr-2">
								<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
								</svg>
							</span>
							<span class="text-lg font-semibold text-gray-900">支援哪些語言？</span>
							<svg class="faq-icon h-6 w-6 text-gray-400 ml-auto" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
							</svg>
						</button>
						<div class="faq-answer px-6 pb-6 text-gray-600">
							<p class="leading-relaxed">目前支援中文 (繁體)、English、日本語、한국어。我們會根據您的系統語言自動切換介面，您也可以在 App 內手動選擇偏好的語言。</p>
						</div>
					</div>

					<div class="faq-item">
						<button class="faq-question flex items-center justify-between w-full text-left" type="button">
							<span class="faq-icon flex items-center justify-center w-8 h-8 bg-green-100 text-green-600 rounded-full mr-2">
								<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
								</svg>
							</span>
							<span class="text-lg font-semibold text-gray-900">可以用來設定打卡提醒或搶購提醒嗎？</span>
							<svg class="faq-icon h-6 w-6 text-gray-400 ml-auto" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
							</svg>
						</button>
						<div class="faq-answer px-6 pb-6 text-gray-600">
							<p class="leading-relaxed">絕對可以！您可以設定在上班前 10 分鐘自動開啟打卡 APP，或在限時搶購開始前精準開啟購物 APP。支援精確到分鐘的時間控制，讓您永遠不會錯過重要時刻。</p>
						</div>
					</div>

				</div>
			</div>
		</div>
	</section>

	<!-- FAQ Section 結束後新增下載按鈕 -->
	<div class="flex justify-center mt-8 mb-20">
		<a href="#" class="inline-flex items-center bg-black text-white px-8 py-4 rounded-2xl hover:bg-gray-800 transition-all duration-300 transform hover:scale-105 shadow-lg">
			<svg class="w-6 h-6 mr-3" viewBox="0 0 24 24" fill="currentColor">
				<path d="M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.61 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.9 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z"/>
			</svg>
			Get it on Google Play
		</a>
	</div>

	<!-- Footer -->
	<footer class="bg-gray-900 text-white">
		<div class="container mx-auto px-4 py-12">
			<div class="grid md:grid-cols-4 gap-8">
				<!-- Brand -->
				<div class="md:col-span-2">
					<div class="flex items-center mb-4">
						<img src="/ic_launcher.webp" alt="AutoLaunch Logo" class="h-10 w-10 mr-3 rounded-lg">
						<span class="text-xl font-bold">AutoLaunch</span>
					</div>
					<p class="text-gray-400 mb-6 max-w-md">
						讓您的 Android 裝置更加智慧化，透過精確的排程管理，提升您的數位生活品質。
					</p>
					<div class="flex space-x-4">
						<a href="#" class="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-gray-700 transition-colors">
							<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
								<path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
							</svg>
						</a>
						<a href="#" class="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-gray-700 transition-colors">
							<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
								<path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.09.111.104.209.077.32-.085.36-.276 1.122-.314 1.279-.049.2-.402.244-.402.244-.402-.244-.925-2.070-.925-3.33 0-3.771 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.750-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.90-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z"/>
							</svg>
						</a>
					</div>
				</div>

				<!-- Links -->
				<div>
					<h4 class="font-semibold mb-4">產品</h4>
					<ul class="space-y-2 text-gray-400">
						<li><a href="#features" class="hover:text-white transition-colors">功能介紹</a></li>
						<li><a href="#use-cases" class="hover:text-white transition-colors">應用情境</a></li>
						<li><a href="#faq" class="hover:text-white transition-colors">常見問題</a></li>
						<li><a href="#" class="hover:text-white transition-colors">使用教學</a></li>
						<li><a href="#" class="hover:text-white transition-colors">版本更新</a></li>
					</ul>
				</div>

				<div>
					<h4 class="font-semibold mb-4">支援</h4>
					<ul class="space-y-2 text-gray-400">
						<li><a href="mailto:<EMAIL>" class="hover:text-white transition-colors">聯絡我們</a></li>
						<li><a href="#" class="hover:text-white transition-colors">隱私權政策</a></li>
						<li><a href="#" class="hover:text-white transition-colors">服務條款</a></li>
						<li><a href="#" class="hover:text-white transition-colors">GitHub</a></li>
					</ul>
				</div>
			</div>

			<div class="border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
				<p class="text-gray-400 text-sm">&copy; 2025 AutoLaunch. All Rights Reserved.</p>
				<p class="text-gray-500 text-sm mt-2 md:mt-0">Made with ❤️ for Android users</p>
			</div>
		</div>
	</footer>

	<script>
		document.addEventListener('DOMContentLoaded', () => {
			const faqItems = document.querySelectorAll('.faq-item');
			faqItems.forEach(item => {
				const question = item.querySelector('.faq-question');
				const answer = item.querySelector('.faq-answer');
				const icon = item.querySelector('.faq-icon:last-child');

				if (question && answer && icon) {
					question.addEventListener('click', () => {
						const isOpen = answer.classList.contains('open');

						// Close all other FAQ items
						faqItems.forEach(otherItem => {
							const otherAnswer = otherItem.querySelector('.faq-answer');
							const otherIcon = otherItem.querySelector('.faq-icon:last-child');
							otherItem.classList.remove('active');
							if (otherAnswer && otherIcon) {
								otherAnswer.classList.remove('open');
							}
						});

						// Toggle current item
						if (!isOpen) {
							answer.classList.add('open');
							item.classList.add('active');
						}
					});
				}
			});

			// Smooth scrolling for navigation links
			document.querySelectorAll('a[href^="#"]').forEach(anchor => {
				anchor.addEventListener('click', function (e) {
					e.preventDefault();
					const href = (e.target as HTMLAnchorElement).getAttribute('href');
					if (href) {
						const target = document.querySelector(href);
						if (target) {
							target.scrollIntoView({
								behavior: 'smooth',
								block: 'start'
							});
						}
					}
				});
			});
		});
	</script>
</Layout>
