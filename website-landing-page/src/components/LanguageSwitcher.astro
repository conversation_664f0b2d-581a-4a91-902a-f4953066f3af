---
import { languages } from '../i18n';

const currentLang = Astro.currentLocale || 'en';
---

<div class="language-switcher relative">
  <button 
    id="language-button" 
    class="flex items-center space-x-2 px-3 py-2 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors text-sm font-medium text-gray-700"
    aria-label="選擇語言 / Select Language"
  >
    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"></path>
    </svg>
    <span id="current-lang">{languages[currentLang as keyof typeof languages]}</span>
    <svg class="w-4 h-4 transition-transform duration-200" id="dropdown-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
    </svg>
  </button>
  
  <div 
    id="language-dropdown" 
    class="absolute right-0 mt-2 w-40 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50 opacity-0 invisible transition-all duration-200 transform scale-95"
  >
    {Object.entries(languages).map(([code, name]) => (
      <a 
        href={code === 'en' ? '/' : `/${code}/`}
        class={`block px-4 py-2 text-sm hover:bg-gray-100 transition-colors ${
          currentLang === code ? 'text-blue-600 font-medium bg-blue-50' : 'text-gray-700'
        }`}
        data-lang={code}
      >
        {name}
      </a>
    ))}
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const button = document.getElementById('language-button');
    const dropdown = document.getElementById('language-dropdown');
    const arrow = document.getElementById('dropdown-arrow');
    
    if (button && dropdown && arrow) {
      let isOpen = false;
      
      button.addEventListener('click', (e) => {
        e.stopPropagation();
        isOpen = !isOpen;
        
        if (isOpen) {
          dropdown.classList.remove('opacity-0', 'invisible', 'scale-95');
          dropdown.classList.add('opacity-100', 'visible', 'scale-100');
          arrow.style.transform = 'rotate(180deg)';
        } else {
          dropdown.classList.add('opacity-0', 'invisible', 'scale-95');
          dropdown.classList.remove('opacity-100', 'visible', 'scale-100');
          arrow.style.transform = 'rotate(0deg)';
        }
      });
      
      // Close dropdown when clicking outside
      document.addEventListener('click', () => {
        if (isOpen) {
          isOpen = false;
          dropdown.classList.add('opacity-0', 'invisible', 'scale-95');
          dropdown.classList.remove('opacity-100', 'visible', 'scale-100');
          arrow.style.transform = 'rotate(0deg)';
        }
      });
      
      // Prevent dropdown from closing when clicking inside
      dropdown.addEventListener('click', (e) => {
        e.stopPropagation();
      });
    }
  });
</script>

<style>
  .language-switcher {
    user-select: none;
  }
  
  #language-dropdown {
    min-width: 160px;
  }
  
  @media (max-width: 768px) {
    .language-switcher {
      position: static;
    }
    
    #language-dropdown {
      position: fixed;
      right: 1rem;
      left: 1rem;
      width: auto;
      min-width: auto;
    }
  }
</style>
