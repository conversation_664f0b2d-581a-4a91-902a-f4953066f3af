---
export interface Props {
	title: string;
	description?: string;
	lang?: string;
}

const { title, description = "AutoLaunch - 您的智慧應用啟動排程工具", lang = "zh-TW" } = Astro.props;

import '../styles/global.css';
---

<!doctype html>
<html lang={lang}>
	<head>
		<meta charset="UTF-8" />
		<meta name="description" content={description} />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<link rel="icon" type="image/webp" href="/ic_launcher.webp" />
		<meta name="generator" content={Astro.generator} />
		
		<!-- Open Graph / Facebook -->
		<meta property="og:type" content="website" />
		<meta property="og:title" content={title} />
		<meta property="og:description" content={description} />
		<meta property="og:image" content="/ic_launcher.webp" />
		
		<!-- Twitter -->
		<meta property="twitter:card" content="summary_large_image" />
		<meta property="twitter:title" content={title} />
		<meta property="twitter:description" content={description} />
		<meta property="twitter:image" content="/ic_launcher.webp" />
		
		<title>{title}</title>
	</head>
	<body>
		<slot />
	</body>
</html>

<style is:global>
	html {
		font-family: system-ui, sans-serif;
		scroll-behavior: smooth;
	}
	
	body {
		margin: 0;
		padding: 0;
		line-height: 1.6;
	}
	
	* {
		box-sizing: border-box;
	}
</style>
