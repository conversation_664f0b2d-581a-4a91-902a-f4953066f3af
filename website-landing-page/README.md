# AutoLaunch Landing Page

AutoLaunch 應用程式的多語言 Landing Page，使用 Astro.js 建構。

## 支援語言

- 🇹🇼 繁體中文 (預設) - `/`
- 🇺🇸 English - `/en/`
- 🇯🇵 日本語 - `/ja/`
- 🇰🇷 한국어 - `/ko/`

## 功能特色

- ✨ 響應式設計，支援各種裝置
- 🌍 完整的多語言支援
- 🎨 現代化的 UI 設計
- 🚀 使用 Astro.js + Vite + Tailwind CSS
- 📱 語言切換器組件
- 🔧 TypeScript 支援

## 開發

### 安裝依賴

```bash
npm install
```

### 啟動開發服務器

```bash
npm run dev
```

### 建構生產版本

```bash
npm run build
```

### 預覽生產版本

```bash
npm run preview
```

## 專案結構

```
src/
├── components/
│   └── LanguageSwitcher.astro    # 語言切換器組件
├── i18n/
│   └── index.ts                  # 多語言翻譯文件
├── layouts/
│   └── Layout.astro              # 基礎佈局
├── pages/
│   ├── index.astro               # 繁體中文首頁
│   ├── en/
│   │   └── index.astro           # 英文首頁
│   ├── ja/
│   │   └── index.astro           # 日文首頁
│   └── ko/
│       └── index.astro           # 韓文首頁
└── styles/
    └── global.css                # 全域樣式
```

## 多語言系統

### 翻譯文件

所有翻譯內容都存放在 `src/i18n/index.ts` 中，包含：

- 導航選單
- 頁面標題和描述
- 功能介紹
- 使用情境
- 常見問題
- 頁腳資訊

### 語言切換器

語言切換器組件 (`LanguageSwitcher.astro`) 提供：

- 下拉式語言選擇
- 當前語言顯示
- 響應式設計
- 平滑動畫效果

### 路由結構

- 繁體中文：`/` (預設語言，不需前綴)
- 英文：`/en/`
- 日文：`/ja/`
- 韓文：`/ko/`

## 技術棧

- **框架**: Astro.js 5.11.0
- **樣式**: Tailwind CSS
- **建構工具**: Vite
- **語言**: TypeScript
- **國際化**: Astro i18n

## 部署

此專案可以部署到任何支援靜態網站的平台：

- Vercel
- Netlify
- GitHub Pages
- Cloudflare Pages

## 貢獻

歡迎提交 Pull Request 來改善翻譯或添加新功能！

## 授權

MIT License
