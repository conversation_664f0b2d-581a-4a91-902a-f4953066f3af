#!/bin/bash

# Google Drive 雲端備份修復測試腳本
# 測試 Google 登入和雲端備份功能的錯誤處理改進

echo "🔧 Google Drive 雲端備份修復測試"
echo "=================================="

# 檢查編譯狀態
echo "📋 1. 檢查編譯狀態..."
if ./gradlew assembleDebug > /dev/null 2>&1; then
    echo "✅ 編譯成功"
else
    echo "❌ 編譯失敗"
    exit 1
fi

# 檢查關鍵文件是否存在
echo ""
echo "📋 2. 檢查關鍵文件..."

files_to_check=(
    "app/src/main/java/com/example/autolaunch/fragment/CloudBackupFragment.kt"
    "app/src/main/java/com/example/autolaunch/utils/GoogleDriveManager.kt"
    "app/src/main/java/com/example/autolaunch/utils/CloudBackupManager.kt"
    "GOOGLE_DRIVE_API_SETUP_GUIDE.md"
)

for file in "${files_to_check[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file 存在"
    else
        echo "❌ $file 不存在"
    fi
done

# 檢查修復內容
echo ""
echo "📋 3. 檢查修復內容..."

# 檢查 CloudBackupFragment 的錯誤處理改進
if grep -q "GoogleSignInStatusCodes" app/src/main/java/com/example/autolaunch/fragment/CloudBackupFragment.kt; then
    echo "✅ CloudBackupFragment: 添加了詳細的錯誤狀態碼處理"
else
    echo "❌ CloudBackupFragment: 缺少錯誤狀態碼處理"
fi

if grep -q "Google sign-in result:" app/src/main/java/com/example/autolaunch/fragment/CloudBackupFragment.kt; then
    echo "✅ CloudBackupFragment: 添加了詳細的登入結果日誌"
else
    echo "❌ CloudBackupFragment: 缺少登入結果日誌"
fi

if grep -q "RESULT_CANCELED" app/src/main/java/com/example/autolaunch/fragment/CloudBackupFragment.kt; then
    echo "✅ CloudBackupFragment: 正確處理用戶取消登入"
else
    echo "❌ CloudBackupFragment: 未正確處理用戶取消登入"
fi

# 檢查 GoogleDriveManager 的改進
if grep -q "Google Drive API integration requires additional configuration" app/src/main/java/com/example/autolaunch/utils/GoogleDriveManager.kt; then
    echo "✅ GoogleDriveManager: 添加了配置需求說明"
else
    echo "❌ GoogleDriveManager: 缺少配置需求說明"
fi

if grep -q "Enable Drive API in Google Cloud Console" app/src/main/java/com/example/autolaunch/utils/GoogleDriveManager.kt; then
    echo "✅ GoogleDriveManager: 添加了詳細的配置步驟說明"
else
    echo "❌ GoogleDriveManager: 缺少配置步驟說明"
fi

# 檢查用戶友好的錯誤信息
if grep -q "雲端備份功能需要完整的 Google Drive API 配置" app/src/main/java/com/example/autolaunch/utils/GoogleDriveManager.kt; then
    echo "✅ GoogleDriveManager: 添加了用戶友好的錯誤信息"
else
    echo "❌ GoogleDriveManager: 缺少用戶友好的錯誤信息"
fi

# 檢查雲端操作的預檢查
if grep -q "先檢查 Google Drive 是否可用" app/src/main/java/com/example/autolaunch/fragment/CloudBackupFragment.kt; then
    echo "✅ CloudBackupFragment: 添加了雲端操作預檢查"
else
    echo "❌ CloudBackupFragment: 缺少雲端操作預檢查"
fi

# 檢查字串資源
echo ""
echo "📋 4. 檢查字串資源..."

if grep -q "google_login_cancelled" app/src/main/res/values/strings.xml; then
    echo "✅ 字串資源: google_login_cancelled 存在"
else
    echo "❌ 字串資源: google_login_cancelled 不存在"
fi

# 檢查依賴項
echo ""
echo "📋 5. 檢查 Google Drive API 依賴項..."

if grep -q "google-api-services-drive" app/build.gradle; then
    echo "✅ Google Drive API 依賴項已添加"
else
    echo "❌ Google Drive API 依賴項缺失"
fi

if grep -q "play-services-auth" app/build.gradle; then
    echo "✅ Google Play Services Auth 依賴項已添加"
else
    echo "❌ Google Play Services Auth 依賴項缺失"
fi

# 檢查配置文件狀態
echo ""
echo "📋 6. 檢查 Google Services 配置..."

if [ -f "app/google-services.json" ]; then
    echo "✅ google-services.json 文件存在"
else
    echo "⚠️  google-services.json 文件不存在（需要完整配置）"
fi

if grep -q "com.google.gms.google-services" build.gradle; then
    echo "✅ Google Services 插件已配置"
else
    echo "⚠️  Google Services 插件未配置（需要完整配置）"
fi

# 總結
echo ""
echo "📋 7. 修復總結..."
echo "✅ 已修復的問題："
echo "   • 改進了 Google 登入錯誤處理"
echo "   • 添加了詳細的錯誤狀態碼識別"
echo "   • 提供了用戶友好的錯誤信息"
echo "   • 添加了雲端操作預檢查"
echo "   • 創建了完整的配置指南"

echo ""
echo "⚠️  仍需完成的配置："
echo "   • Google Cloud Console 項目設置"
echo "   • 啟用 Google Drive API"
echo "   • 配置 OAuth 2.0 憑證"
echo "   • 添加 google-services.json 文件"
echo "   • 更新 build.gradle 插件配置"

echo ""
echo "📖 詳細配置步驟請參考："
echo "   GOOGLE_DRIVE_API_SETUP_GUIDE.md"

echo ""
echo "🎯 測試建議："
echo "   1. 在完整配置前，使用本地備份功能"
echo "   2. 測試 Google 登入時的錯誤處理改進"
echo "   3. 驗證用戶友好的錯誤信息顯示"

echo ""
echo "✅ Google Drive 修復測試完成！"
