# Google Drive API 配置指南

## 問題說明

目前 AutoLaunch 應用的雲端備份功能顯示 "Google 登入已取消" 錯誤，這是因為 Google Drive API 尚未完全配置。雖然 Google 登入本身可以成功，但後續的 Drive API 操作需要額外的配置步驟。

## 當前狀態

✅ **已完成的部分：**
- Google Sign-In 基本配置
- Google Drive API 依賴項已添加
- 雲端備份框架代碼已實現
- 錯誤處理和用戶反饋已改進

❌ **需要完成的配置：**
- Google Cloud Console 項目設置
- Google Drive API 啟用
- OAuth 2.0 憑證配置
- google-services.json 文件
- Google Services 插件配置

## 完整配置步驟

### 1. Google Cloud Console 設置

1. 前往 [Google Cloud Console](https://console.cloud.google.com/)
2. 創建新項目或選擇現有項目
3. 啟用 Google Drive API：
   - 導航到 "APIs & Services" > "Library"
   - 搜索 "Google Drive API"
   - 點擊啟用

### 2. OAuth 2.0 憑證配置

1. 在 Google Cloud Console 中：
   - 導航到 "APIs & Services" > "Credentials"
   - 點擊 "Create Credentials" > "OAuth 2.0 Client IDs"
   - 選擇 "Android" 應用類型
   - 輸入包名：`com.example.autolaunch`
   - 獲取 SHA-1 指紋：
     ```bash
     keytool -list -v -keystore ~/.android/debug.keystore -alias androiddebugkey -storepass android -keypass android
     ```
   - 輸入 SHA-1 指紋

### 3. google-services.json 配置

1. 在 Google Cloud Console 中下載 `google-services.json`
2. 將文件放置在 `app/` 目錄下
3. 確保文件包含正確的項目配置

### 4. 更新 build.gradle 文件

#### 項目級 build.gradle
```gradle
plugins {
    id 'com.android.application' version '8.11.0' apply false
    id 'com.android.library' version '8.11.0' apply false
    id 'org.jetbrains.kotlin.android' version '2.1.0' apply false
    id 'org.jetbrains.kotlin.kapt' version '2.1.0' apply false
    id 'com.google.gms.google-services' version '4.4.0' apply false  // 添加這行
}
```

#### 應用級 build.gradle
```gradle
plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'kotlin-kapt'
    id 'com.google.gms.google-services'  // 添加這行
}
```

### 5. 更新 GoogleDriveManager.kt

需要實現實際的 Drive API 調用，替換當前的佔位符實現：

```kotlin
suspend fun initializeDriveService(): Boolean = withContext(Dispatchers.IO) {
    try {
        val account = getCurrentAccount()
        if (account == null) {
            Log.e(TAG, "No Google account signed in")
            return@withContext false
        }

        val credential = GoogleAccountCredential.usingOAuth2(
            context, 
            setOf(DriveScopes.DRIVE_FILE)
        )
        credential.selectedAccount = account.account

        driveService = Drive.Builder(
            AndroidHttp.newCompatibleTransport(),
            GsonFactory(),
            credential
        )
            .setApplicationName(APPLICATION_NAME)
            .build()

        true
    } catch (e: Exception) {
        Log.e(TAG, "Failed to initialize Drive service", e)
        false
    }
}
```

## 測試步驟

配置完成後，測試流程：

1. **Google 登入測試**：
   - 點擊 "登入 Google 帳戶"
   - 確認登入成功且無 "已取消" 錯誤

2. **雲端備份測試**：
   - 創建一些排程
   - 點擊 "創建雲端備份"
   - 確認備份成功上傳到 Google Drive

3. **雲端恢復測試**：
   - 點擊 "從雲端匯入"
   - 確認可以看到備份文件列表
   - 測試恢復功能

## 臨時解決方案

在完整配置之前，用戶可以：

1. **使用本地備份功能**：
   - 點擊 "本機備份" 標籤
   - 使用 "創建本地備份" 和 "從文件匯入" 功能

2. **手動備份**：
   - 本地備份文件保存在 `Downloads/AutoLaunch/` 目錄
   - 可以手動複製到雲端存儲服務

## 錯誤修復總結

已修復的問題：

1. **改進錯誤處理**：
   - 添加詳細的登入狀態日誌
   - 區分不同類型的登入錯誤
   - 提供更清楚的錯誤信息

2. **用戶體驗改進**：
   - 登入成功時顯示 API 配置狀態
   - 雲端操作前檢查配置狀態
   - 提供替代方案建議

3. **代碼健壯性**：
   - 添加權限檢查
   - 改進異常處理
   - 增加操作日誌

## 後續步驟

1. 完成 Google Cloud Console 配置
2. 添加 google-services.json 文件
3. 更新 build.gradle 配置
4. 實現完整的 Drive API 調用
5. 進行端到端測試

完成這些步驟後，雲端備份功能將完全可用，不再出現 "Google 登入已取消" 錯誤。
