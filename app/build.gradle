plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'kotlin-kapt'
    id 'com.google.gms.google-services'
}

android {
    namespace 'com.example.autolaunch'
    compileSdk 35

    defaultConfig {
        applicationId "com.example.autolaunch"
        minSdk 26
        targetSdk 35
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        debug {
            debuggable true
            minifyEnabled false
        }
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    packaging {
        resources {
            excludes += ['META-INF/INDEX.LIST', 'META-INF/DEPENDENCIES']
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = '17'
    }

    buildFeatures {
        viewBinding true
        dataBinding true
    }
}

dependencies {
    implementation 'androidx.core:core-ktx:1.16.0'
    implementation 'androidx.appcompat:appcompat:1.7.0'
    implementation 'com.google.android.material:material:1.12.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.2.1'
    
    // Activity & Fragment
    implementation 'androidx.activity:activity-ktx:1.10.1'
    implementation 'androidx.fragment:fragment-ktx:1.8.7'
    
    // Lifecycle
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0'
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.9.0'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.9.0'
    
    // Room database
    implementation 'androidx.room:room-runtime:2.7.1'
    implementation 'androidx.room:room-ktx:2.7.1'
    kapt 'androidx.room:room-compiler:2.7.1'
    
    // WorkManager for background tasks
    implementation 'androidx.work:work-runtime-ktx:2.10.1'
    
    // Navigation
    implementation 'androidx.navigation:navigation-fragment-ktx:2.9.0'
    implementation 'androidx.navigation:navigation-ui-ktx:2.9.0'
    
    // RecyclerView
    implementation 'androidx.recyclerview:recyclerview:1.4.0'

    // SwipeRefreshLayout
    implementation 'androidx.swiperefreshlayout:swiperefreshlayout:1.1.0'

    // Image loading
    implementation 'com.github.bumptech.glide:glide:4.16.0'

    // JSON serialization
    implementation 'com.google.code.gson:gson:2.10.1'

    // Google Drive API
    implementation 'com.google.android.gms:play-services-auth:21.2.0'
    implementation 'com.google.apis:google-api-services-drive:v3-rev20240914-2.0.0'
    implementation 'com.google.api-client:google-api-client-android:2.6.0'
    implementation 'com.google.api-client:google-api-client-gson:2.6.0'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.8.0'

    // Testing
    testImplementation 'junit:junit:4.13.2'
    testImplementation 'org.mockito:mockito-core:3.12.4'
    testImplementation 'org.mockito:mockito-inline:3.12.4'
    testImplementation 'androidx.arch.core:core-testing:2.2.0'
    testImplementation 'org.robolectric:robolectric:4.10.3'
    testImplementation 'androidx.test:core:1.5.0'
    testImplementation 'androidx.test.ext:junit:1.1.5'
    testImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    testImplementation 'org.jetbrains.kotlinx:kotlinx-coroutines-test:1.7.1'
    testImplementation "io.mockk:mockk:1.12.0"
    testImplementation "androidx.work:work-testing:2.8.1"

    // Android Instrumentation Testing
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    androidTestImplementation 'androidx.test.uiautomator:uiautomator:2.2.0'
    androidTestImplementation 'org.mockito:mockito-android:3.12.4'
    androidTestImplementation "io.mockk:mockk-android:1.12.0"
} 