package com.example.autolaunch

import android.content.Context
import androidx.room.Room
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.example.autolaunch.model.*
import com.example.autolaunch.utils.SystemLogManager
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.runBlocking
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith

/**
 * 系統日誌整合測試
 * 測試系統日誌功能的完整流程
 */
@RunWith(AndroidJUnit4::class)
class SystemLogIntegrationTest {
    
    private lateinit var database: AppDatabase
    private lateinit var systemLogDao: SystemLogDao
    private lateinit var context: Context
    
    @Before
    fun setup() {
        context = ApplicationProvider.getApplicationContext()
        database = Room.inMemoryDatabaseBuilder(
            context,
            AppDatabase::class.java
        ).allowMainThreadQueries().build()
        
        systemLogDao = database.systemLogDao()
    }
    
    @After
    fun tearDown() {
        database.close()
    }
    
    @Test
    fun testSystemLogCreationAndRetrieval() = runBlocking {
        // 建立測試日誌
        val testLog = SystemLog(
            logType = LogType.SUCCESS.value,
            actionType = ActionType.SCHEDULE_EXECUTED.value,
            scheduleId = 1L,
            scheduleName = "測試排程",
            message = "執行排程：測試排程",
            details = "測試詳情"
        )

        // 插入日誌
        val logId = systemLogDao.insert(testLog)
        assertTrue("日誌ID應該大於0", logId > 0)

        // 檢索日誌
        val retrievedLogs = systemLogDao.getAllLogs().first()
        assertEquals("應該有1條日誌", 1, retrievedLogs.size)

        val retrievedLog = retrievedLogs.first()
        assertEquals("日誌類型應該符合", testLog.logType, retrievedLog.logType)
        assertEquals("操作類型應該符合", testLog.actionType, retrievedLog.actionType)
        assertEquals("排程ID應該符合", testLog.scheduleId, retrievedLog.scheduleId)
        assertEquals("訊息應該符合", testLog.message, retrievedLog.message)
    }
    
    @Test
    fun testSystemLogFiltering() = runBlocking {
        // 建立不同類型的日誌
        val logs = listOf(
            SystemLog(logType = LogType.SUCCESS.value, actionType = ActionType.SCHEDULE_EXECUTED.value, message = "成功1"),
            SystemLog(logType = LogType.ERROR.value, actionType = ActionType.SYSTEM_ERROR.value, message = "錯誤1"),
            SystemLog(logType = LogType.WARNING.value, actionType = ActionType.PERMISSION_DENIED.value, message = "警告1"),
            SystemLog(logType = LogType.INFO.value, actionType = ActionType.APP_STARTED.value, message = "資訊1")
        )

        // 插入所有日誌
        systemLogDao.insertAll(logs)

        // 測試按類型篩選
        val successLogs = systemLogDao.getLogsByType(LogType.SUCCESS.value).first()
        assertEquals("應該有1條成功日誌", 1, successLogs.size)
        assertEquals("成功日誌訊息應該符合", "成功1", successLogs.first().message)

        val errorLogs = systemLogDao.getLogsByType(LogType.ERROR.value).first()
        assertEquals("應該有1條錯誤日誌", 1, errorLogs.size)
        assertEquals("錯誤日誌訊息應該符合", "錯誤1", errorLogs.first().message)

        // 測試按操作類型篩選
        val scheduleExecutedLogs = systemLogDao.getLogsByAction(ActionType.SCHEDULE_EXECUTED.value).first()
        assertEquals("應該有1條排程執行日誌", 1, scheduleExecutedLogs.size)

        val appStartedLogs = systemLogDao.getLogsByAction(ActionType.APP_STARTED.value).first()
        assertEquals("應該有1條應用程式啟動日誌", 1, appStartedLogs.size)
    }
    
    @Test
    fun testSystemLogSearch() = runBlocking {
        // 建立測試日誌
        val logs = listOf(
            SystemLog(logType = LogType.INFO.value, actionType = ActionType.APP_STARTED.value, message = "應用程式啟動", scheduleName = "測試排程A"),
            SystemLog(logType = LogType.SUCCESS.value, actionType = ActionType.SCHEDULE_EXECUTED.value, message = "執行排程", scheduleName = "測試排程B"),
            SystemLog(logType = LogType.ERROR.value, actionType = ActionType.SYSTEM_ERROR.value, message = "系統錯誤", scheduleName = "其他排程")
        )

        // 插入日誌
        systemLogDao.insertAll(logs)

        // 搜尋包含"測試"的日誌
        val searchResults = systemLogDao.searchLogs("測試").first()
        assertEquals("應該找到2條包含'測試'的日誌", 2, searchResults.size)

        // 搜尋包含"排程"的日誌
        val scheduleResults = systemLogDao.searchLogs("排程").first()
        assertEquals("應該找到3條包含'排程'的日誌", 3, scheduleResults.size)

        // 搜尋不存在的內容
        val noResults = systemLogDao.searchLogs("不存在").first()
        assertEquals("應該找不到任何結果", 0, noResults.size)
    }
    
    @Test
    fun testSystemLogStatistics() = runBlocking {
        // 建立不同類型的日誌
        val logs = listOf(
            SystemLog(logType = LogType.SUCCESS.value, actionType = ActionType.SCHEDULE_EXECUTED.value, message = "成功1"),
            SystemLog(logType = LogType.SUCCESS.value, actionType = ActionType.SCHEDULE_CREATED.value, message = "成功2"),
            SystemLog(logType = LogType.ERROR.value, actionType = ActionType.SYSTEM_ERROR.value, message = "錯誤1"),
            SystemLog(logType = LogType.WARNING.value, actionType = ActionType.PERMISSION_DENIED.value, message = "警告1"),
            SystemLog(logType = LogType.INFO.value, actionType = ActionType.APP_STARTED.value, message = "資訊1")
        )

        // 插入日誌
        systemLogDao.insertAll(logs)
        
        // 檢查統計
        val totalCount = systemLogDao.getLogCount()
        assertEquals("總數應該是5", 5, totalCount)
        
        val successCount = systemLogDao.getLogCountByType(LogType.SUCCESS.value)
        assertEquals("成功數應該是2", 2, successCount)
        
        val errorCount = systemLogDao.getLogCountByType(LogType.ERROR.value)
        assertEquals("錯誤數應該是1", 1, errorCount)
        
        val warningCount = systemLogDao.getLogCountByType(LogType.WARNING.value)
        assertEquals("警告數應該是1", 1, warningCount)
        
        val infoCount = systemLogDao.getLogCountByType(LogType.INFO.value)
        assertEquals("資訊數應該是1", 1, infoCount)
    }
    
    @Test
    fun testSystemLogRotation() = runBlocking {
        // 建立舊日誌（8天前）
        val oldTimestamp = System.currentTimeMillis() - (8 * 24 * 60 * 60 * 1000L)
        val oldLog = SystemLog(
            logType = LogType.INFO.value,
            actionType = ActionType.APP_STARTED.value,
            message = "舊日誌",
            timestamp = oldTimestamp
        )

        // 建立新日誌
        val newLog = SystemLog(
            logType = LogType.INFO.value,
            actionType = ActionType.APP_STARTED.value,
            message = "新日誌",
            timestamp = System.currentTimeMillis()
        )

        // 插入日誌
        systemLogDao.insert(oldLog)
        systemLogDao.insert(newLog)

        // 驗證兩條日誌都存在
        val allLogs = systemLogDao.getAllLogs().first()
        assertEquals("應該有2條日誌", 2, allLogs.size)

        // 執行日誌輪轉（刪除7天前的日誌）
        val cutoffTime = System.currentTimeMillis() - (7 * 24 * 60 * 60 * 1000L)
        val deletedCount = systemLogDao.deleteLogsBefore(cutoffTime)
        assertEquals("應該刪除1條舊日誌", 1, deletedCount)

        // 驗證只剩下新日誌
        val remainingLogs = systemLogDao.getAllLogs().first()
        assertEquals("應該只剩1條日誌", 1, remainingLogs.size)
        assertEquals("剩下的應該是新日誌", "新日誌", remainingLogs.first().message)
    }
    
    @Test
    fun testSystemLogManagerIntegration() = runBlocking {
        // 測試應用程式啟動日誌記錄
        SystemLogManager.logAppStarted(context, "整合測試")

        // 等待非同步操作完成
        Thread.sleep(1000)

        // 驗證日誌是否被記錄
        val logs = systemLogDao.getAllLogs().first()
        assertTrue("應該有日誌記錄", logs.isNotEmpty())

        val appStartLog = logs.find { it.getActionTypeEnum() == ActionType.APP_STARTED }
        assertNotNull("應該有應用程式啟動日誌", appStartLog)
        assertEquals("日誌類型應該是INFO", LogType.INFO, appStartLog?.getLogTypeEnum())
        assertTrue("訊息應該包含'應用程式啟動'", appStartLog?.message?.contains("應用程式啟動") == true)
    }
}
