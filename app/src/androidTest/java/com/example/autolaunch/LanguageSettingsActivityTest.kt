package com.example.autolaunch

import androidx.test.core.app.ActivityScenario
import androidx.test.espresso.Espresso.onView
import androidx.test.espresso.action.ViewActions.click
import androidx.test.espresso.assertion.ViewAssertions.matches
import androidx.test.espresso.contrib.RecyclerViewActions
import androidx.test.espresso.matcher.ViewMatchers.*
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.filters.LargeTest
import androidx.recyclerview.widget.RecyclerView
import com.example.autolaunch.utils.LanguageManager
import org.junit.Test
import org.junit.runner.RunWith

/**
 * 語言設定頁面的 Android 測試
 */
@RunWith(AndroidJUnit4::class)
@LargeTest
class LanguageSettingsActivityTest {

    @Test
    fun testLanguageSettingsActivityDisplaysCorrectly() {
        ActivityScenario.launch(LanguageSettingsActivity::class.java).use { scenario ->
            // 檢查工具列標題是否正確顯示
            onView(withText(R.string.language_settings_title))
                .check(matches(isDisplayed()))
            
            // 檢查語言列表是否顯示
            onView(withId(R.id.recyclerViewLanguages))
                .check(matches(isDisplayed()))
        }
    }

    @Test
    fun testLanguageOptionsAreDisplayed() {
        ActivityScenario.launch(LanguageSettingsActivity::class.java).use { scenario ->
            // 檢查所有支援的語言選項是否顯示
            val supportedLanguages = LanguageManager.supportedLanguages
            
            for (i in supportedLanguages.indices) {
                onView(withId(R.id.recyclerViewLanguages))
                    .perform(RecyclerViewActions.scrollToPosition<RecyclerView.ViewHolder>(i))
                
                // 檢查語言名稱是否顯示
                onView(withText(supportedLanguages[i].displayName))
                    .check(matches(isDisplayed()))
            }
        }
    }

    @Test
    fun testLanguageSelectionWorks() {
        ActivityScenario.launch(LanguageSettingsActivity::class.java).use { scenario ->
            // 點擊英文選項
            onView(withText("English"))
                .perform(click())
            
            // 檢查重新啟動提示是否顯示
            onView(withId(R.id.cardRestartHint))
                .check(matches(isDisplayed()))
        }
    }

    @Test
    fun testBackButtonWorks() {
        ActivityScenario.launch(LanguageSettingsActivity::class.java).use { scenario ->
            // 點擊返回按鈕
            onView(withContentDescription("Navigate up"))
                .perform(click())
            
            // Activity 應該結束
            scenario.onActivity { activity ->
                assert(activity.isFinishing)
            }
        }
    }
}
