package com.example.autolaunch

import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.platform.app.InstrumentationRegistry
import androidx.test.ext.junit.rules.ActivityScenarioRule
import androidx.test.espresso.Espresso.onView
import androidx.test.espresso.action.ViewActions.*
import androidx.test.espresso.assertion.ViewAssertions.*
import androidx.test.espresso.matcher.ViewMatchers.*
import com.example.autolaunch.model.*
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.Rule
import org.junit.Assert.*

/**
 * Integration tests for URL scheduling functionality
 */
@RunWith(AndroidJUnit4::class)
class UrlScheduleIntegrationTest {

    @get:Rule
    val activityRule = ActivityScenarioRule(MainActivity::class.java)

    @Test
    fun testCreateUrlScheduleFlow() {
        // Click add schedule button
        onView(withId(R.id.fabAddSchedule))
            .perform(click())

        // Select URL type
        onView(withId(R.id.btnTypeUrl))
            .perform(click())

        // Verify URL input is visible
        onView(withId(R.id.layoutUrlInputContainer))
            .check(matches(isDisplayed()))

        // Verify app selection is hidden
        onView(withId(R.id.cardAppSelectionContainer))
            .check(matches(not(isDisplayed())))

        // Enter URL
        onView(withId(R.id.etUrl))
            .perform(typeText("https://www.google.com"))

        // Enter URL title
        onView(withId(R.id.etUrlTitle))
            .perform(typeText("Google Search"))

        // Save schedule
        onView(withId(R.id.btnSave))
            .perform(click())

        // Verify we're back to main activity
        onView(withId(R.id.recyclerViewSchedules))
            .check(matches(isDisplayed()))
    }

    @Test
    fun testUrlValidationInUI() {
        // Navigate to add schedule
        onView(withId(R.id.fabAddSchedule))
            .perform(click())

        // Select URL type
        onView(withId(R.id.btnTypeUrl))
            .perform(click())

        // Enter invalid URL
        onView(withId(R.id.etUrl))
            .perform(typeText("not-a-valid-url"))

        // Check that validation status shows error
        onView(withId(R.id.layoutUrlStatus))
            .check(matches(isDisplayed()))

        // Preview button should be disabled
        onView(withId(R.id.btnPreviewUrl))
            .check(matches(not(isEnabled())))

        // Clear and enter valid URL
        onView(withId(R.id.etUrl))
            .perform(clearText(), typeText("https://www.example.com"))

        // Preview button should be enabled
        onView(withId(R.id.btnPreviewUrl))
            .check(matches(isEnabled()))
    }

    @Test
    fun testScheduleTypeToggle() {
        // Navigate to add schedule
        onView(withId(R.id.fabAddSchedule))
            .perform(click())

        // Initially APP should be selected
        onView(withId(R.id.cardAppSelectionContainer))
            .check(matches(isDisplayed()))
        onView(withId(R.id.layoutUrlInputContainer))
            .check(matches(not(isDisplayed())))

        // Switch to URL
        onView(withId(R.id.btnTypeUrl))
            .perform(click())

        onView(withId(R.id.cardAppSelectionContainer))
            .check(matches(not(isDisplayed())))
        onView(withId(R.id.layoutUrlInputContainer))
            .check(matches(isDisplayed()))

        // Switch back to APP
        onView(withId(R.id.btnTypeApp))
            .perform(click())

        onView(withId(R.id.cardAppSelectionContainer))
            .check(matches(isDisplayed()))
        onView(withId(R.id.layoutUrlInputContainer))
            .check(matches(not(isDisplayed())))
    }
}

/**
 * Database integration tests for URL schedules
 */
@RunWith(AndroidJUnit4::class)
class UrlScheduleDatabaseTest {

    private lateinit var database: AppDatabase
    private lateinit var scheduleDao: ScheduleDao

    @Test
    fun testUrlScheduleCRUD() = runBlocking {
        val context = InstrumentationRegistry.getInstrumentation().targetContext
        database = AppDatabase.getDatabase(context)
        scheduleDao = database.scheduleDao()

        // Create URL schedule
        val urlSchedule = Schedule(
            scheduleType = ScheduleType.URL.value,
            url = "https://www.google.com",
            urlTitle = "Google",
            hour = 9,
            minute = 30,
            repeatMode = RepeatMode.DAILY.value
        )

        // Insert
        val scheduleId = scheduleDao.insert(urlSchedule)
        assertTrue(scheduleId > 0)

        // Read
        val retrievedSchedule = scheduleDao.getScheduleById(scheduleId).kotlinx.coroutines.flow.first()
        assertNotNull(retrievedSchedule)
        assertEquals(ScheduleType.URL.value, retrievedSchedule!!.scheduleType)
        assertEquals("https://www.google.com", retrievedSchedule.url)
        assertEquals("Google", retrievedSchedule.urlTitle)

        // Update
        val updatedSchedule = retrievedSchedule.copy(
            url = "https://www.example.com",
            urlTitle = "Example"
        )
        val updateResult = scheduleDao.update(updatedSchedule)
        assertEquals(1, updateResult)

        // Verify update
        val updatedRetrieved = scheduleDao.getScheduleById(scheduleId).kotlinx.coroutines.flow.first()
        assertEquals("https://www.example.com", updatedRetrieved!!.url)
        assertEquals("Example", updatedRetrieved.urlTitle)

        // Delete
        val deleteResult = scheduleDao.deleteById(scheduleId)
        assertEquals(1, deleteResult)

        // Verify deletion
        val deletedSchedule = scheduleDao.getScheduleById(scheduleId).kotlinx.coroutines.flow.first()
        assertNull(deletedSchedule)
    }

    @Test
    fun testMixedScheduleTypes() = runBlocking {
        val context = InstrumentationRegistry.getInstrumentation().targetContext
        database = AppDatabase.getDatabase(context)
        scheduleDao = database.scheduleDao()

        // Create APP schedule
        val appSchedule = Schedule(
            scheduleType = ScheduleType.APP.value,
            appName = "Test App",
            packageName = "com.test.app",
            hour = 8,
            minute = 0,
            repeatMode = RepeatMode.DAILY.value
        )

        // Create URL schedule
        val urlSchedule = Schedule(
            scheduleType = ScheduleType.URL.value,
            url = "https://www.google.com",
            urlTitle = "Google",
            hour = 9,
            minute = 0,
            repeatMode = RepeatMode.DAILY.value
        )

        // Insert both
        val appId = scheduleDao.insert(appSchedule)
        val urlId = scheduleDao.insert(urlSchedule)

        // Query by type
        val appSchedules = scheduleDao.getAppSchedules().kotlinx.coroutines.flow.first()
        val urlSchedules = scheduleDao.getUrlSchedules().kotlinx.coroutines.flow.first()

        assertEquals(1, appSchedules.size)
        assertEquals(1, urlSchedules.size)
        assertEquals(ScheduleType.APP.value, appSchedules[0].scheduleType)
        assertEquals(ScheduleType.URL.value, urlSchedules[0].scheduleType)

        // Clean up
        scheduleDao.deleteById(appId)
        scheduleDao.deleteById(urlId)
    }
}
