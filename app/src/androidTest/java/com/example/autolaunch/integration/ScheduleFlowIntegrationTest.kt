package com.example.autolaunch.integration

import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.platform.app.InstrumentationRegistry
import androidx.test.ext.junit.rules.ActivityScenarioRule
import androidx.test.espresso.Espresso.onView
import androidx.test.espresso.action.ViewActions.*
import androidx.test.espresso.assertion.ViewAssertions.*
import androidx.test.espresso.matcher.ViewMatchers.*
import com.example.autolaunch.MainActivity
import com.example.autolaunch.R
import com.example.autolaunch.model.*
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.Rule
import org.junit.Assert.*

/**
 * 排程流程的集成測試
 * 測試從創建到執行的完整排程流程
 */
@RunWith(AndroidJUnit4::class)
class ScheduleFlowIntegrationTest {

    @get:Rule
    val activityRule = ActivityScenarioRule(MainActivity::class.java)

    @Test
    fun testCompleteAppScheduleFlow() {
        // 1. 點擊添加排程按鈕
        onView(withId(R.id.fabAddSchedule))
            .perform(click())

        // 2. 選擇應用類型
        onView(withId(R.id.btnTypeApp))
            .perform(click())

        // 3. 驗證應用選擇界面顯示
        onView(withId(R.id.cardAppSelectionContainer))
            .check(matches(isDisplayed()))

        // 4. 點擊選擇應用按鈕
        onView(withId(R.id.btnSelectApp))
            .perform(click())

        // 5. 返回並設置時間
        onView(withId(R.id.cardTimeSelection))
            .perform(click())

        // 6. 設置重複模式
        onView(withId(R.id.cardRepeatMode))
            .perform(click())

        // 7. 保存排程
        onView(withId(R.id.btnSave))
            .perform(click())

        // 8. 驗證返回主界面
        onView(withId(R.id.recyclerViewSchedules))
            .check(matches(isDisplayed()))
    }

    @Test
    fun testCompleteUrlScheduleFlow() {
        // 1. 點擊添加排程按鈕
        onView(withId(R.id.fabAddSchedule))
            .perform(click())

        // 2. 選擇 URL 類型
        onView(withId(R.id.btnTypeUrl))
            .perform(click())

        // 3. 驗證 URL 輸入界面顯示
        onView(withId(R.id.layoutUrlInputContainer))
            .check(matches(isDisplayed()))

        // 4. 輸入 URL
        onView(withId(R.id.etUrl))
            .perform(typeText("https://www.google.com"))

        // 5. 輸入標題
        onView(withId(R.id.etUrlTitle))
            .perform(typeText("Google Search"))

        // 6. 設置時間
        onView(withId(R.id.cardTimeSelection))
            .perform(click())

        // 7. 保存排程
        onView(withId(R.id.btnSave))
            .perform(click())

        // 8. 驗證返回主界面
        onView(withId(R.id.recyclerViewSchedules))
            .check(matches(isDisplayed()))
    }

    @Test
    fun testScheduleEditFlow() {
        // 首先創建一個排程
        testCompleteUrlScheduleFlow()

        // 等待一下讓排程顯示
        Thread.sleep(1000)

        // 長按排程項目進入編輯模式
        onView(withId(R.id.recyclerViewSchedules))
            .perform(longClick())

        // 驗證編輯界面
        // 注意：這裡需要根據實際的編輯流程調整
    }

    @Test
    fun testScheduleDeleteFlow() {
        // 首先創建一個排程
        testCompleteUrlScheduleFlow()

        // 等待一下讓排程顯示
        Thread.sleep(1000)

        // 測試滑動刪除功能
        // 注意：這裡需要根據實際的刪除流程調整
        onView(withId(R.id.recyclerViewSchedules))
            .check(matches(isDisplayed()))
    }

    @Test
    fun testPermissionCheckFlow() {
        // 1. 點擊歡迎卡片進行權限檢查
        onView(withId(R.id.welcomeCard))
            .perform(click())

        // 2. 驗證權限檢查對話框顯示
        // 注意：這裡需要根據實際的權限檢查流程調整
    }

    @Test
    fun testMenuNavigationFlow() {
        // 1. 點擊菜單按鈕
        onView(withId(R.id.btnMenu))
            .perform(click())

        // 2. 驗證底部菜單顯示
        // 注意：這裡需要根據實際的菜單實現調整

        // 3. 點擊教學選項
        // onView(withText("教學"))
        //     .perform(click())

        // 4. 驗證教學頁面
        // 注意：這裡需要根據實際的教學頁面調整
    }

    @Test
    fun testScheduleValidationFlow() {
        // 1. 點擊添加排程按鈕
        onView(withId(R.id.fabAddSchedule))
            .perform(click())

        // 2. 選擇 URL 類型
        onView(withId(R.id.btnTypeUrl))
            .perform(click())

        // 3. 輸入無效 URL
        onView(withId(R.id.etUrl))
            .perform(typeText("invalid-url"))

        // 4. 驗證驗證狀態顯示
        onView(withId(R.id.layoutUrlStatus))
            .check(matches(isDisplayed()))

        // 5. 驗證預覽按鈕被禁用
        onView(withId(R.id.btnPreviewUrl))
            .check(matches(not(isEnabled())))

        // 6. 輸入有效 URL
        onView(withId(R.id.etUrl))
            .perform(clearText(), typeText("https://www.google.com"))

        // 7. 驗證預覽按鈕被啟用
        onView(withId(R.id.btnPreviewUrl))
            .check(matches(isEnabled()))
    }

    @Test
    fun testScheduleTypeToggleFlow() {
        // 1. 點擊添加排程按鈕
        onView(withId(R.id.fabAddSchedule))
            .perform(click())

        // 2. 驗證默認選中應用類型
        onView(withId(R.id.btnTypeApp))
            .check(matches(isChecked()))

        // 3. 切換到 URL 類型
        onView(withId(R.id.btnTypeUrl))
            .perform(click())

        // 4. 驗證 URL 界面顯示
        onView(withId(R.id.layoutUrlInputContainer))
            .check(matches(isDisplayed()))

        // 5. 驗證應用選擇界面隱藏
        onView(withId(R.id.cardAppSelectionContainer))
            .check(matches(not(isDisplayed())))

        // 6. 切換回應用類型
        onView(withId(R.id.btnTypeApp))
            .perform(click())

        // 7. 驗證應用選擇界面顯示
        onView(withId(R.id.cardAppSelectionContainer))
            .check(matches(isDisplayed()))

        // 8. 驗證 URL 界面隱藏
        onView(withId(R.id.layoutUrlInputContainer))
            .check(matches(not(isDisplayed())))
    }

    @Test
    fun testBackNavigationFlow() {
        // 1. 點擊添加排程按鈕
        onView(withId(R.id.fabAddSchedule))
            .perform(click())

        // 2. 點擊返回按鈕
        onView(withId(R.id.btnBack))
            .perform(click())

        // 3. 驗證返回主界面
        onView(withId(R.id.recyclerViewSchedules))
            .check(matches(isDisplayed()))
    }

    @Test
    fun testCancelScheduleCreationFlow() {
        // 1. 點擊添加排程按鈕
        onView(withId(R.id.fabAddSchedule))
            .perform(click())

        // 2. 輸入一些數據
        onView(withId(R.id.btnTypeUrl))
            .perform(click())

        onView(withId(R.id.etUrl))
            .perform(typeText("https://www.example.com"))

        // 3. 點擊取消按鈕
        onView(withId(R.id.btnCancel))
            .perform(click())

        // 4. 驗證返回主界面
        onView(withId(R.id.recyclerViewSchedules))
            .check(matches(isDisplayed()))
    }
}
