package com.example.autolaunch.integration

import android.content.Context
import androidx.room.Room
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.example.autolaunch.model.*
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.runBlocking
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.Assert.*

/**
 * 數據庫集成測試
 * 測試數據庫、DAO 和 Repository 的集成功能
 */
@RunWith(AndroidJUnit4::class)
class DatabaseIntegrationTest {

    private lateinit var database: AppDatabase
    private lateinit var scheduleDao: ScheduleDao
    private lateinit var repository: ScheduleRepository
    private lateinit var context: Context

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
        
        // 創建內存數據庫用於測試
        database = Room.inMemoryDatabaseBuilder(
            context,
            AppDatabase::class.java
        ).allowMainThreadQueries().build()
        
        scheduleDao = database.scheduleDao()
        repository = ScheduleRepository(context)
    }

    @After
    fun tearDown() {
        database.close()
    }

    @Test
    fun testCompleteScheduleLifecycle() = runBlocking {
        // 1. 創建排程
        val schedule = Schedule(
            scheduleType = ScheduleType.APP.value,
            appName = "Test App",
            packageName = "com.test.app",
            hour = 9,
            minute = 30,
            repeatMode = RepeatMode.DAILY.value,
            isEnabled = true
        )

        // 2. 插入排程
        val scheduleId = scheduleDao.insert(schedule)
        assertTrue("插入應該返回有效的 ID", scheduleId > 0)

        // 3. 獲取排程
        val retrievedSchedule = scheduleDao.getScheduleById(scheduleId).first()
        assertNotNull("應該能找到插入的排程", retrievedSchedule)
        assertEquals("應用名稱應該匹配", "Test App", retrievedSchedule?.appName)

        // 4. 更新排程
        val updatedSchedule = retrievedSchedule!!.copy(
            appName = "Updated App",
            hour = 10
        )
        val updateCount = scheduleDao.update(updatedSchedule)
        assertEquals("應該更新一條記錄", 1, updateCount)

        // 5. 驗證更新
        val updatedRetrievedSchedule = scheduleDao.getScheduleById(scheduleId).first()
        assertEquals("應用名稱應該已更新", "Updated App", updatedRetrievedSchedule?.appName)
        assertEquals("時間應該已更新", 10, updatedRetrievedSchedule?.hour)

        // 6. 刪除排程
        val deleteCount = scheduleDao.deleteById(scheduleId)
        assertEquals("應該刪除一條記錄", 1, deleteCount)

        // 7. 驗證刪除
        val deletedSchedule = scheduleDao.getScheduleById(scheduleId).first()
        assertNull("排程應該已被刪除", deletedSchedule)
    }

    @Test
    fun testMultipleScheduleTypes() = runBlocking {
        // 創建不同類型的排程
        val appSchedule = Schedule(
            scheduleType = ScheduleType.APP.value,
            appName = "Test App",
            packageName = "com.test.app",
            hour = 9,
            minute = 0,
            repeatMode = RepeatMode.DAILY.value,
            isEnabled = true
        )

        val urlSchedule = Schedule(
            scheduleType = ScheduleType.URL.value,
            url = "https://www.google.com",
            urlTitle = "Google",
            hour = 10,
            minute = 0,
            repeatMode = RepeatMode.ONCE.value,
            isEnabled = true
        )

        // 插入排程
        val appId = scheduleDao.insert(appSchedule)
        val urlId = scheduleDao.insert(urlSchedule)

        // 測試按類型查詢
        val appSchedules = scheduleDao.getAppSchedules().first()
        val urlSchedules = scheduleDao.getUrlSchedules().first()

        assertEquals("應該有一個應用排程", 1, appSchedules.size)
        assertEquals("應該有一個 URL 排程", 1, urlSchedules.size)

        assertEquals("應用排程類型應該正確", ScheduleType.APP.value, appSchedules[0].scheduleType)
        assertEquals("URL 排程類型應該正確", ScheduleType.URL.value, urlSchedules[0].scheduleType)

        // 清理
        scheduleDao.deleteById(appId)
        scheduleDao.deleteById(urlId)
    }

    @Test
    fun testEnabledDisabledSchedules() = runBlocking {
        // 創建啟用和禁用的排程
        val enabledSchedule = Schedule(
            scheduleType = ScheduleType.APP.value,
            appName = "Enabled App",
            packageName = "com.enabled.app",
            hour = 9,
            minute = 0,
            repeatMode = RepeatMode.DAILY.value,
            isEnabled = true
        )

        val disabledSchedule = Schedule(
            scheduleType = ScheduleType.APP.value,
            appName = "Disabled App",
            packageName = "com.disabled.app",
            hour = 10,
            minute = 0,
            repeatMode = RepeatMode.DAILY.value,
            isEnabled = false
        )

        // 插入排程
        val enabledId = scheduleDao.insert(enabledSchedule)
        val disabledId = scheduleDao.insert(disabledSchedule)

        // 測試獲取啟用的排程
        val enabledSchedules = scheduleDao.getEnabledSchedules().first()
        assertTrue("應該有啟用的排程", enabledSchedules.isNotEmpty())
        assertTrue("所有返回的排程都應該是啟用的", 
            enabledSchedules.all { it.isEnabled })

        // 測試獲取所有排程
        val allSchedules = scheduleDao.getAllSchedules().first()
        assertTrue("應該有至少兩個排程", allSchedules.size >= 2)

        // 清理
        scheduleDao.deleteById(enabledId)
        scheduleDao.deleteById(disabledId)
    }

    @Test
    fun testBatchOperations() = runBlocking {
        // 創建多個排程
        val schedules = listOf(
            Schedule(
                scheduleType = ScheduleType.APP.value,
                appName = "Batch App 1",
                packageName = "com.batch1",
                hour = 9,
                minute = 0,
                repeatMode = RepeatMode.DAILY.value,
                isEnabled = true
            ),
            Schedule(
                scheduleType = ScheduleType.APP.value,
                appName = "Batch App 2",
                packageName = "com.batch2",
                hour = 10,
                minute = 0,
                repeatMode = RepeatMode.WEEKLY.value,
                isEnabled = true
            ),
            Schedule(
                scheduleType = ScheduleType.URL.value,
                url = "https://www.example.com",
                urlTitle = "Example",
                hour = 11,
                minute = 0,
                repeatMode = RepeatMode.ONCE.value,
                isEnabled = false
            )
        )

        // 批量插入
        scheduleDao.insertAll(schedules)

        // 驗證插入
        val allSchedules = scheduleDao.getAllSchedules().first()
        assertTrue("應該包含批量插入的排程", allSchedules.size >= 3)

        // 驗證不同類型的排程都存在
        val appSchedules = scheduleDao.getAppSchedules().first()
        val urlSchedules = scheduleDao.getUrlSchedules().first()
        
        assertTrue("應該有應用排程", appSchedules.size >= 2)
        assertTrue("應該有 URL 排程", urlSchedules.size >= 1)
    }

    @Test
    fun testRepositoryIntegration() = runBlocking {
        // 測試 Repository 與數據庫的集成
        val schedule = Schedule(
            scheduleType = ScheduleType.URL.value,
            url = "https://www.github.com",
            urlTitle = "GitHub",
            hour = 14,
            minute = 30,
            repeatMode = RepeatMode.DAILY.value,
            isEnabled = true
        )

        // 通過 Repository 插入
        assertDoesNotThrow {
            repository.insertSchedule(schedule)
        }

        // 通過 Repository 獲取
        val allSchedules = repository.getAllSchedules()
        assertNotNull("Repository 應該返回 Flow", allSchedules)

        val enabledSchedules = repository.getEnabledSchedules()
        assertNotNull("Repository 應該返回啟用排程的 Flow", enabledSchedules)
    }

    @Test
    fun testDatabaseMigration() = runBlocking {
        // 測試數據庫遷移（如果有的話）
        // 這裡主要測試數據庫能正常創建和使用
        
        val schedule = Schedule(
            scheduleType = ScheduleType.APP.value,
            appName = "Migration Test",
            packageName = "com.migration.test",
            hour = 12,
            minute = 0,
            repeatMode = RepeatMode.DAILY.value,
            isEnabled = true
        )

        val scheduleId = scheduleDao.insert(schedule)
        assertTrue("遷移後應該能正常插入數據", scheduleId > 0)

        val retrievedSchedule = scheduleDao.getScheduleById(scheduleId).first()
        assertNotNull("遷移後應該能正常查詢數據", retrievedSchedule)
    }

    @Test
    fun testConcurrentOperations() = runBlocking {
        // 測試並發操作
        val schedules = mutableListOf<Schedule>()
        
        // 創建多個排程
        repeat(10) { index ->
            schedules.add(
                Schedule(
                    scheduleType = if (index % 2 == 0) ScheduleType.APP.value else ScheduleType.URL.value,
                    appName = if (index % 2 == 0) "Concurrent App $index" else null,
                    packageName = if (index % 2 == 0) "com.concurrent$index" else null,
                    url = if (index % 2 == 1) "https://example$index.com" else null,
                    urlTitle = if (index % 2 == 1) "Example $index" else null,
                    hour = 9 + (index % 12),
                    minute = index * 5,
                    repeatMode = RepeatMode.DAILY.value,
                    isEnabled = true
                )
            )
        }

        // 批量插入
        scheduleDao.insertAll(schedules)

        // 驗證所有數據都正確插入
        val allSchedules = scheduleDao.getAllSchedules().first()
        assertTrue("應該有足夠的排程", allSchedules.size >= 10)
    }
}
