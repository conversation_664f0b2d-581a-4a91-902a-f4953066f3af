package com.example.autolaunch.automation

import android.content.Context
import androidx.room.Room
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.example.autolaunch.model.AppDatabase
import com.example.autolaunch.model.AutomationStep
import com.example.autolaunch.model.Schedule
import com.example.autolaunch.model.ScheduleType
import com.example.autolaunch.model.StepType
import com.example.autolaunch.model.TargetType
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.runBlocking
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.Assert.*

/**
 * 自動化功能整合測試
 */
@RunWith(AndroidJUnit4::class)
class AutomationIntegrationTest {

    private lateinit var database: AppDatabase
    private lateinit var context: Context

    @Before
    fun setup() {
        context = ApplicationProvider.getApplicationContext()
        database = Room.inMemoryDatabaseBuilder(context, AppDatabase::class.java)
            .allowMainThreadQueries()
            .build()
    }

    @After
    fun teardown() {
        database.close()
    }

    @Test
    fun testScheduleWithAutomationSteps() = runBlocking {
        // 創建測試排程
        val schedule = Schedule(
            name = "測試自動化排程",
            packageName = "com.example.testapp",
            scheduleType = ScheduleType.APP.value,
            hour = 9,
            minute = 0,
            isEnabled = true
        )
        
        val scheduleId = database.scheduleDao().insert(schedule)
        
        // 創建自動化步驟
        val steps = listOf(
            AutomationStep(
                scheduleId = scheduleId,
                stepOrder = 1,
                stepType = StepType.CLICK.value,
                stepName = "點擊登入按鈕",
                targetType = TargetType.TEXT.value,
                targetText = "登入",
                retryCount = 3,
                timeoutMs = 10000,
                isEnabled = true,
                isOptional = false
            ),
            AutomationStep(
                scheduleId = scheduleId,
                stepOrder = 2,
                stepType = StepType.INPUT_TEXT.value,
                stepName = "輸入用戶名",
                targetType = TargetType.ID.value,
                targetId = "username",
                inputText = "testuser",
                retryCount = 3,
                timeoutMs = 10000,
                isEnabled = true,
                isOptional = false
            ),
            AutomationStep(
                scheduleId = scheduleId,
                stepOrder = 3,
                stepType = StepType.INPUT_TEXT.value,
                stepName = "輸入密碼",
                targetType = TargetType.ID.value,
                targetId = "password",
                inputText = "testpass",
                retryCount = 3,
                timeoutMs = 10000,
                isEnabled = true,
                isOptional = false
            ),
            AutomationStep(
                scheduleId = scheduleId,
                stepOrder = 4,
                stepType = StepType.WAIT.value,
                stepName = "等待頁面載入",
                waitDuration = 2000,
                retryCount = 1,
                timeoutMs = 3000,
                isEnabled = true,
                isOptional = true
            ),
            AutomationStep(
                scheduleId = scheduleId,
                stepOrder = 5,
                stepType = StepType.CLICK.value,
                stepName = "點擊確認按鈕",
                targetType = TargetType.TEXT.value,
                targetText = "確認",
                retryCount = 3,
                timeoutMs = 10000,
                isEnabled = true,
                isOptional = false
            )
        )
        
        // 插入步驟
        database.automationStepDao().insertAll(steps)
        
        // 驗證步驟已正確插入
        val insertedSteps = database.automationStepDao().getStepsByScheduleId(scheduleId).first()
        assertEquals("插入的步驟數量不正確", 5, insertedSteps.size)
        
        // 驗證步驟順序
        val sortedSteps = insertedSteps.sortedBy { it.stepOrder }
        for (i in sortedSteps.indices) {
            assertEquals("步驟順序不正確", i + 1, sortedSteps[i].stepOrder)
        }
        
        // 驗證步驟內容
        assertEquals("第一個步驟名稱不正確", "點擊登入按鈕", sortedSteps[0].stepName)
        assertEquals("第一個步驟類型不正確", StepType.CLICK, sortedSteps[0].getStepType())
        assertEquals("第一個步驟目標文字不正確", "登入", sortedSteps[0].targetText)
        
        assertEquals("第二個步驟名稱不正確", "輸入用戶名", sortedSteps[1].stepName)
        assertEquals("第二個步驟類型不正確", StepType.INPUT_TEXT, sortedSteps[1].getStepType())
        assertEquals("第二個步驟目標ID不正確", "username", sortedSteps[1].targetId)
        assertEquals("第二個步驟輸入文字不正確", "testuser", sortedSteps[1].inputText)
        
        assertEquals("第四個步驟名稱不正確", "等待頁面載入", sortedSteps[3].stepName)
        assertEquals("第四個步驟類型不正確", StepType.WAIT, sortedSteps[3].getStepType())
        assertEquals("第四個步驟等待時間不正確", 2000L, sortedSteps[3].waitDuration)
        assertTrue("第四個步驟應該是可選的", sortedSteps[3].isOptional)
    }

    @Test
    fun testAutomationStepValidation() = runBlocking {
        // 創建測試排程
        val schedule = Schedule(
            name = "驗證測試排程",
            packageName = "com.example.testapp",
            scheduleType = ScheduleType.APP.value,
            hour = 10,
            minute = 0,
            isEnabled = true
        )
        
        val scheduleId = database.scheduleDao().insert(schedule)
        
        // 測試有效步驟
        val validStep = AutomationStep(
            scheduleId = scheduleId,
            stepOrder = 1,
            stepType = StepType.CLICK.value,
            targetType = TargetType.TEXT.value,
            targetText = "有效按鈕",
            retryCount = 3,
            timeoutMs = 10000,
            isEnabled = true
        )
        
        assertTrue("有效步驟應該通過驗證", validStep.isValid())
        
        // 測試無效步驟（缺少目標文字）
        val invalidStep = AutomationStep(
            scheduleId = scheduleId,
            stepOrder = 2,
            stepType = StepType.CLICK.value,
            targetType = TargetType.TEXT.value,
            targetText = null,
            retryCount = 3,
            timeoutMs = 10000,
            isEnabled = true
        )
        
        assertFalse("無效步驟應該驗證失敗", invalidStep.isValid())
        
        // 插入有效步驟
        val stepId = database.automationStepDao().insert(validStep)
        assertTrue("步驟ID應該大於0", stepId > 0)
        
        // 驗證步驟已插入
        val retrievedStep = database.automationStepDao().getStepById(stepId).first()
        assertNotNull("應該能夠檢索插入的步驟", retrievedStep)
        assertEquals("檢索的步驟目標文字不正確", "有效按鈕", retrievedStep?.targetText)
    }

    @Test
    fun testAutomationStepOperations() = runBlocking {
        // 創建測試排程
        val schedule = Schedule(
            name = "操作測試排程",
            packageName = "com.example.testapp",
            scheduleType = ScheduleType.APP.value,
            hour = 11,
            minute = 0,
            isEnabled = true
        )
        
        val scheduleId = database.scheduleDao().insert(schedule)
        
        // 創建多個步驟
        val steps = (1..3).map { order ->
            AutomationStep(
                scheduleId = scheduleId,
                stepOrder = order,
                stepType = StepType.CLICK.value,
                stepName = "步驟 $order",
                targetType = TargetType.TEXT.value,
                targetText = "按鈕 $order",
                isEnabled = true
            )
        }
        
        database.automationStepDao().insertAll(steps)
        
        // 測試獲取步驟數量
        val stepCount = database.automationStepDao().getStepCountByScheduleId(scheduleId)
        assertEquals("步驟數量不正確", 3, stepCount)
        
        // 測試獲取啟用步驟數量
        val enabledStepCount = database.automationStepDao().getEnabledStepCountByScheduleId(scheduleId)
        assertEquals("啟用步驟數量不正確", 3, enabledStepCount)
        
        // 測試禁用一個步驟
        val allSteps = database.automationStepDao().getStepsByScheduleId(scheduleId).first()
        val firstStep = allSteps.first()
        database.automationStepDao().updateEnabledStatus(firstStep.id, false)
        
        val enabledStepCountAfterDisable = database.automationStepDao().getEnabledStepCountByScheduleId(scheduleId)
        assertEquals("禁用步驟後啟用步驟數量不正確", 2, enabledStepCountAfterDisable)
        
        // 測試檢查是否有步驟
        assertTrue("應該有步驟", database.automationStepDao().hasSteps(scheduleId))
        assertTrue("應該有啟用的步驟", database.automationStepDao().hasEnabledSteps(scheduleId))
        
        // 測試清空步驟
        database.automationStepDao().clearStepsByScheduleId(scheduleId)
        val stepCountAfterClear = database.automationStepDao().getStepCountByScheduleId(scheduleId)
        assertEquals("清空後步驟數量應該為0", 0, stepCountAfterClear)
        
        assertFalse("清空後不應該有步驟", database.automationStepDao().hasSteps(scheduleId))
        assertFalse("清空後不應該有啟用的步驟", database.automationStepDao().hasEnabledSteps(scheduleId))
    }

    @Test
    fun testStepOrderOperations() = runBlocking {
        // 創建測試排程
        val schedule = Schedule(
            name = "順序測試排程",
            packageName = "com.example.testapp",
            scheduleType = ScheduleType.APP.value,
            hour = 12,
            minute = 0,
            isEnabled = true
        )
        
        val scheduleId = database.scheduleDao().insert(schedule)
        
        // 創建步驟
        val steps = listOf(
            AutomationStep(
                scheduleId = scheduleId,
                stepOrder = 1,
                stepType = StepType.CLICK.value,
                stepName = "第一步",
                targetType = TargetType.TEXT.value,
                targetText = "按鈕1",
                isEnabled = true
            ),
            AutomationStep(
                scheduleId = scheduleId,
                stepOrder = 2,
                stepType = StepType.WAIT.value,
                stepName = "第二步",
                waitDuration = 1000,
                isEnabled = true
            ),
            AutomationStep(
                scheduleId = scheduleId,
                stepOrder = 3,
                stepType = StepType.CLICK.value,
                stepName = "第三步",
                targetType = TargetType.TEXT.value,
                targetText = "按鈕3",
                isEnabled = true
            )
        )
        
        database.automationStepDao().insertAll(steps)
        
        // 測試獲取最大步驟順序
        val maxOrder = database.automationStepDao().getMaxStepOrder(scheduleId)
        assertEquals("最大步驟順序不正確", 3, maxOrder)
        
        // 測試更新步驟順序
        val allSteps = database.automationStepDao().getStepsByScheduleId(scheduleId).first()
        val stepIds = allSteps.map { it.id }
        val newOrders = listOf(3, 1, 2) // 重新排序
        
        database.automationStepDao().updateStepOrders(stepIds, newOrders)
        
        // 驗證順序已更新
        val reorderedSteps = database.automationStepDao().getStepsByScheduleId(scheduleId).first()
            .sortedBy { it.stepOrder }
        
        assertEquals("重新排序後第一步名稱不正確", "第二步", reorderedSteps[0].stepName)
        assertEquals("重新排序後第二步名稱不正確", "第三步", reorderedSteps[1].stepName)
        assertEquals("重新排序後第三步名稱不正確", "第一步", reorderedSteps[2].stepName)
    }
}
