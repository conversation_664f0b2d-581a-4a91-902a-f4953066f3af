package com.example.autolaunch

import android.content.Intent
import androidx.test.espresso.Espresso.onView
import androidx.test.espresso.action.ViewActions.click
import androidx.test.espresso.assertion.ViewAssertions.matches
import androidx.test.espresso.matcher.ViewMatchers.*
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.rule.ActivityTestRule
import androidx.test.rule.GrantPermissionRule
import com.example.autolaunch.model.ScheduleRepository
import com.example.autolaunch.utils.BackupTestUtils
import kotlinx.coroutines.runBlocking
import org.junit.After
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

/**
 * BackupRestoreActivity UI 測試
 * 測試備份與匯入界面的用戶交互
 */
@RunWith(AndroidJUnit4::class)
class BackupRestoreActivityTest {
    
    @get:Rule
    val activityRule = ActivityTestRule(BackupRestoreActivity::class.java, false, false)
    
    @get:Rule
    val permissionRule: GrantPermissionRule = GrantPermissionRule.grant(
        android.Manifest.permission.READ_EXTERNAL_STORAGE,
        android.Manifest.permission.WRITE_EXTERNAL_STORAGE,
        android.Manifest.permission.INTERNET,
        android.Manifest.permission.ACCESS_NETWORK_STATE
    )
    
    private lateinit var repository: ScheduleRepository
    
    @Before
    fun setUp() {
        repository = ScheduleRepository(androidx.test.core.app.ApplicationProvider.getApplicationContext())
        
        // 清理現有數據並插入測試數據
        runBlocking {
            repository.deleteAllSchedules()
            val testSchedules = BackupTestUtils.createTestSchedules()
            testSchedules.forEach { schedule ->
                repository.insertSchedule(schedule)
            }
        }
        
        // 啟動Activity
        activityRule.launchActivity(Intent())
    }
    
    @After
    fun tearDown() {
        runBlocking {
            repository.deleteAllSchedules()
        }
    }
    
    @Test
    fun testActivityLaunch() {
        // 驗證Activity正確啟動
        onView(withId(R.id.toolbar))
            .check(matches(isDisplayed()))
        
        onView(withText("備份與匯入"))
            .check(matches(isDisplayed()))
    }
    
    @Test
    fun testGoogleAccountStatusDisplay() {
        // 驗證Google帳戶狀態卡片顯示
        onView(withId(R.id.cardGoogleAccount))
            .check(matches(isDisplayed()))
        
        onView(withId(R.id.tvGoogleAccountStatus))
            .check(matches(isDisplayed()))
        
        onView(withId(R.id.btnGoogleSignIn))
            .check(matches(isDisplayed()))
    }
    
    @Test
    fun testBackupOperationButtons() {
        // 驗證備份操作按鈕顯示
        onView(withId(R.id.btnCreateLocalBackup))
            .check(matches(isDisplayed()))
            .check(matches(isEnabled()))
        
        onView(withId(R.id.btnCreateCloudBackup))
            .check(matches(isDisplayed()))
        
        onView(withId(R.id.btnSyncBackup))
            .check(matches(isDisplayed()))
    }
    
    @Test
    fun testImportOperationButtons() {
        // 驗證匯入操作按鈕顯示
        onView(withId(R.id.btnImportFromFile))
            .check(matches(isDisplayed()))
            .check(matches(isEnabled()))
        
        onView(withId(R.id.btnImportFromCloud))
            .check(matches(isDisplayed()))
    }
    
    @Test
    fun testBackupFileManagementButtons() {
        // 驗證備份文件管理按鈕顯示
        onView(withId(R.id.btnManageLocalBackups))
            .check(matches(isDisplayed()))
            .check(matches(isEnabled()))
        
        onView(withId(R.id.btnManageCloudBackups))
            .check(matches(isDisplayed()))
    }
    
    @Test
    fun testLocalBackupCreation() {
        // 測試本地備份創建
        onView(withId(R.id.btnCreateLocalBackup))
            .perform(click())
        
        // 驗證進度指示器顯示
        onView(withId(R.id.progressIndicator))
            .check(matches(isDisplayed()))
        
        // 等待操作完成（簡單的等待，實際測試中可能需要更複雜的同步機制）
        Thread.sleep(3000)
        
        // 驗證進度指示器隱藏
        onView(withId(R.id.progressIndicator))
            .check(matches(withEffectiveVisibility(Visibility.GONE)))
    }
    
    @Test
    fun testLocalBackupManagement() {
        // 先創建一個備份
        onView(withId(R.id.btnCreateLocalBackup))
            .perform(click())
        
        // 等待備份完成
        Thread.sleep(3000)
        
        // 測試本地備份管理
        onView(withId(R.id.btnManageLocalBackups))
            .perform(click())
        
        // 等待對話框顯示
        Thread.sleep(1000)
        
        // 驗證對話框標題顯示
        onView(withText("本地備份文件"))
            .check(matches(isDisplayed()))
    }
    
    @Test
    fun testGoogleSignInButton() {
        // 測試Google登錄按鈕點擊
        onView(withId(R.id.btnGoogleSignIn))
            .perform(click())
        
        // 注意：實際的Google登錄流程在測試環境中可能無法完成
        // 這裡主要測試按鈕是否可點擊和響應
    }
    
    @Test
    fun testCloudBackupButtonsDisabledWhenNotSignedIn() {
        // 驗證未登錄時雲端相關按鈕被禁用
        // 注意：這個測試假設測試環境中沒有登錄Google帳戶
        
        onView(withId(R.id.btnCreateCloudBackup))
            .check(matches(not(isEnabled())))
        
        onView(withId(R.id.btnImportFromCloud))
            .check(matches(not(isEnabled())))
        
        onView(withId(R.id.btnManageCloudBackups))
            .check(matches(not(isEnabled())))
    }
    
    @Test
    fun testSyncBackupButton() {
        // 測試同步備份按鈕
        onView(withId(R.id.btnSyncBackup))
            .perform(click())
        
        // 驗證進度指示器顯示
        onView(withId(R.id.progressIndicator))
            .check(matches(isDisplayed()))
        
        // 等待操作完成
        Thread.sleep(3000)
        
        // 驗證進度指示器隱藏
        onView(withId(R.id.progressIndicator))
            .check(matches(withEffectiveVisibility(Visibility.GONE)))
    }
    
    @Test
    fun testImportFromFileButton() {
        // 測試從文件匯入按鈕
        onView(withId(R.id.btnImportFromFile))
            .perform(click())
        
        // 注意：這會觸發文件選擇器，在測試環境中可能無法完全測試
        // 主要測試按鈕是否響應點擊
    }
    
    @Test
    fun testNavigationBack() {
        // 測試返回導航
        onView(withContentDescription("Navigate up"))
            .perform(click())
        
        // 驗證Activity是否結束（這個測試可能需要調整，取決於具體的導航實現）
    }
    
    @Test
    fun testProgressIndicatorInitiallyHidden() {
        // 驗證進度指示器初始狀態為隱藏
        onView(withId(R.id.progressIndicator))
            .check(matches(withEffectiveVisibility(Visibility.GONE)))
    }
    
    @Test
    fun testAllCardViewsDisplayed() {
        // 驗證所有卡片視圖都正確顯示
        onView(withId(R.id.cardGoogleAccount))
            .check(matches(isDisplayed()))
        
        // 滾動到其他卡片並驗證
        onView(withText("創建備份"))
            .check(matches(isDisplayed()))
        
        onView(withText("匯入備份"))
            .check(matches(isDisplayed()))
        
        onView(withText("備份文件管理"))
            .check(matches(isDisplayed()))
    }
    
    @Test
    fun testButtonTextsCorrect() {
        // 驗證按鈕文字正確
        onView(withId(R.id.btnCreateLocalBackup))
            .check(matches(withText("本地備份")))
        
        onView(withId(R.id.btnCreateCloudBackup))
            .check(matches(withText("雲端備份")))
        
        onView(withId(R.id.btnSyncBackup))
            .check(matches(withText("同步備份（本地+雲端）")))
        
        onView(withId(R.id.btnImportFromFile))
            .check(matches(withText("從文件匯入")))
        
        onView(withId(R.id.btnImportFromCloud))
            .check(matches(withText("從雲端匯入")))
        
        onView(withId(R.id.btnManageLocalBackups))
            .check(matches(withText("本地備份")))
        
        onView(withId(R.id.btnManageCloudBackups))
            .check(matches(withText("雲端備份")))
    }
}
