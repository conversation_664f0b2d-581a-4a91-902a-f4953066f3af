package com.example.autolaunch

import androidx.test.core.app.ActivityScenario
import androidx.test.espresso.Espresso.onView
import androidx.test.espresso.action.ViewActions.click
import androidx.test.espresso.assertion.ViewAssertions.matches
import androidx.test.espresso.matcher.ViewMatchers.*
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.filters.LargeTest
import com.example.autolaunch.utils.SettingsManager
import org.junit.Test
import org.junit.runner.RunWith

/**
 * 設置頁面的 UI 測試
 */
@RunWith(AndroidJUnit4::class)
@LargeTest
class SettingsActivityTest {

    @Test
    fun testSettingsActivityDisplaysCorrectly() {
        ActivityScenario.launch(SettingsActivity::class.java).use { scenario ->
            // 檢查工具列標題是否顯示
            onView(withText(R.string.settings_title))
                .check(matches(isDisplayed()))
            
            // 檢查常駐通知設置是否顯示
            onView(withText(R.string.settings_persistent_notification_title))
                .check(matches(isDisplayed()))
            
            onView(withText(R.string.settings_persistent_notification_description))
                .check(matches(isDisplayed()))
            
            // 檢查開關是否顯示
            onView(withId(R.id.switchPersistentNotification))
                .check(matches(isDisplayed()))
        }
    }

    @Test
    fun testPersistentNotificationSwitchWorks() {
        ActivityScenario.launch(SettingsActivity::class.java).use { scenario ->
            scenario.onActivity { activity ->
                val settingsManager = SettingsManager.getInstance(activity)
                val initialState = settingsManager.isPersistentNotificationEnabled()
                
                // 點擊開關
                onView(withId(R.id.switchPersistentNotification))
                    .perform(click())
                
                // 驗證設置已更改
                val newState = settingsManager.isPersistentNotificationEnabled()
                assert(newState != initialState)
            }
        }
    }
}
