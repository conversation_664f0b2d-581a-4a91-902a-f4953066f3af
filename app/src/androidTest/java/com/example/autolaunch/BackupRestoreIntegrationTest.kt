package com.example.autolaunch

import android.content.Context
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.rule.ActivityTestRule
import com.example.autolaunch.model.*
import com.example.autolaunch.utils.BackupManager
import com.example.autolaunch.utils.CloudBackupManager
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.runBlocking
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

/**
 * 備份與匯入功能整合測試
 * 測試完整的備份和恢復流程
 */
@RunWith(AndroidJUnit4::class)
class BackupRestoreIntegrationTest {
    
    @get:Rule
    val activityRule = ActivityTestRule(MainActivity::class.java)
    
    private lateinit var context: Context
    private lateinit var repository: ScheduleRepository
    private lateinit var backupManager: BackupManager
    private lateinit var cloudBackupManager: CloudBackupManager
    private lateinit var testSchedules: List<Schedule>
    
    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
        repository = ScheduleRepository(context)
        backupManager = BackupManager(context)
        cloudBackupManager = CloudBackupManager(context)
        
        // 清理現有數據
        runBlocking {
            repository.deleteAllSchedules()
        }
        
        // 創建測試排程數據
        testSchedules = listOf(
            Schedule(
                scheduleType = ScheduleType.APP.value,
                appName = "測試應用1",
                packageName = "com.test.app1",
                taskName = "整合測試任務1",
                hour = 10,
                minute = 30,
                repeatMode = RepeatMode.DAILY.value,
                isEnabled = true
            ),
            Schedule(
                scheduleType = ScheduleType.URL.value,
                url = "https://www.example.com",
                urlTitle = "Example",
                taskName = "整合測試網頁任務",
                hour = 15,
                minute = 45,
                repeatMode = RepeatMode.WEEKLY.value,
                daysOfWeek = 62, // 週二到週六
                isEnabled = true
            ),
            Schedule(
                scheduleType = ScheduleType.APP.value,
                appName = "測試應用2",
                packageName = "com.test.app2",
                taskName = "整合測試任務2",
                hour = 8,
                minute = 0,
                repeatMode = RepeatMode.ONCE.value,
                singleExecuteDate = System.currentTimeMillis() + 86400000, // 明天
                isEnabled = false // 測試禁用狀態
            )
        )
    }
    
    @After
    fun tearDown() {
        // 清理測試數據
        runBlocking {
            repository.deleteAllSchedules()
            
            // 清理測試備份文件
            val backupFiles = backupManager.getLocalBackupFiles()
            backupFiles.forEach { file ->
                if (file.name.contains("test") || file.name.contains("Test") || file.name.contains("整合")) {
                    file.delete()
                }
            }
        }
    }
    
    @Test
    fun testCompleteBackupAndRestoreFlow() = runBlocking {
        // 1. 插入測試排程
        val insertedIds = mutableListOf<Long>()
        testSchedules.forEach { schedule ->
            val id = repository.insertSchedule(schedule)
            assertTrue("排程插入應該成功", id > 0)
            insertedIds.add(id)
        }
        
        // 驗證排程已插入
        val allSchedules = repository.getAllSchedules().first()
        assertEquals("應該有3個排程", 3, allSchedules.size)
        
        // 2. 創建本地備份
        val backupResult = backupManager.createLocalBackup()
        assertTrue("備份創建應該成功", backupResult.success)
        assertNotNull("備份文件路徑不應為空", backupResult.filePath)
        assertEquals("備份排程數量應該匹配", 3, backupResult.scheduleCount)
        assertTrue("備份文件大小應該大於0", backupResult.fileSize > 0)
        
        // 3. 清空數據庫
        val deleteCount = repository.deleteAllSchedules()
        assertEquals("應該刪除3個排程", 3, deleteCount)
        
        // 驗證數據庫已清空
        val emptySchedules = repository.getAllSchedules().first()
        assertTrue("數據庫應該為空", emptySchedules.isEmpty())
        
        // 4. 從備份恢復
        val restoreResult = backupManager.restoreFromLocalFile(backupResult.filePath!!)
        assertTrue("恢復應該成功", restoreResult.success)
        assertEquals("應該匯入3個排程", 3, restoreResult.importedCount)
        assertEquals("不應該跳過任何排程", 0, restoreResult.skippedCount)
        assertEquals("不應該有錯誤", 0, restoreResult.errorCount)
        
        // 5. 驗證恢復的數據
        val restoredSchedules = repository.getAllSchedules().first()
        assertEquals("應該恢復3個排程", 3, restoredSchedules.size)
        
        // 驗證排程內容（按任務名稱排序以便比較）
        val originalSorted = testSchedules.sortedBy { it.taskName }
        val restoredSorted = restoredSchedules.sortedBy { it.taskName }
        
        for (i in originalSorted.indices) {
            val original = originalSorted[i]
            val restored = restoredSorted[i]
            
            assertEquals("排程類型應該匹配", original.scheduleType, restored.scheduleType)
            assertEquals("應用名稱應該匹配", original.appName, restored.appName)
            assertEquals("包名應該匹配", original.packageName, restored.packageName)
            assertEquals("任務名稱應該匹配", original.taskName, restored.taskName)
            assertEquals("URL應該匹配", original.url, restored.url)
            assertEquals("URL標題應該匹配", original.urlTitle, restored.urlTitle)
            assertEquals("小時應該匹配", original.hour, restored.hour)
            assertEquals("分鐘應該匹配", original.minute, restored.minute)
            assertEquals("重複模式應該匹配", original.repeatMode, restored.repeatMode)
            assertEquals("星期設定應該匹配", original.daysOfWeek, restored.daysOfWeek)
            assertEquals("單次執行日期應該匹配", original.singleExecuteDate, restored.singleExecuteDate)
            assertEquals("啟用狀態應該匹配", original.isEnabled, restored.isEnabled)
        }
    }
    
    @Test
    fun testBackupFileManagement() = runBlocking {
        // 插入測試排程
        testSchedules.forEach { schedule ->
            repository.insertSchedule(schedule)
        }
        
        // 創建多個備份文件
        val backupResults = mutableListOf<String>()
        repeat(3) { index ->
            val result = backupManager.createLocalBackup()
            assertTrue("備份$index 創建應該成功", result.success)
            backupResults.add(result.filePath!!)
            
            // 等待一秒確保文件名不同
            Thread.sleep(1000)
        }
        
        // 獲取備份文件列表
        val backupFiles = backupManager.getLocalBackupFiles()
        assertTrue("應該至少有3個備份文件", backupFiles.size >= 3)
        
        // 測試備份文件信息獲取
        val firstFile = backupFiles.first()
        val fileInfo = backupManager.getBackupFileInfo(firstFile)
        assertNotNull("文件信息不應為空", fileInfo)
        assertEquals("排程數量應該匹配", 3, fileInfo!!.scheduleCount)
        assertTrue("文件應該有效", fileInfo.isValid)
        assertFalse("文件名不應為空", fileInfo.fileName.isBlank())
        assertTrue("文件大小應該大於0", fileInfo.fileSize > 0)
        
        // 測試文件刪除
        val deleteSuccess = backupManager.deleteLocalBackupFile(firstFile)
        assertTrue("文件刪除應該成功", deleteSuccess)
        assertFalse("文件應該不存在", firstFile.exists())
    }
    
    @Test
    fun testInvalidBackupHandling() = runBlocking {
        // 測試空數據庫備份
        repository.deleteAllSchedules()
        
        val emptyBackupResult = backupManager.createLocalBackup()
        assertFalse("空數據庫備份應該失敗", emptyBackupResult.success)
        assertTrue("錯誤消息應該提到沒有數據", emptyBackupResult.message.contains("沒有排程數據"))
        
        // 測試無效JSON恢復
        val invalidJson = """{"invalid": "json", "format": true}"""
        val invalidRestoreResult = backupManager.restoreFromJson(invalidJson)
        assertFalse("無效JSON恢復應該失敗", invalidRestoreResult.success)
        assertTrue("錯誤消息應該提到格式問題", 
            invalidRestoreResult.message.contains("格式") || invalidRestoreResult.message.contains("無效"))
        
        // 測試不存在的文件恢復
        val nonExistentFileResult = backupManager.restoreFromLocalFile("/non/existent/file.json")
        assertFalse("不存在文件恢復應該失敗", nonExistentFileResult.success)
        assertTrue("錯誤消息應該提到文件不存在", nonExistentFileResult.message.contains("不存在"))
    }
    
    @Test
    fun testPartialRestoreScenario() = runBlocking {
        // 創建包含有效和無效排程的備份數據
        val validSchedule = testSchedules[0]
        val invalidSchedule = Schedule(
            scheduleType = ScheduleType.APP.value,
            appName = "", // 無效：空應用名稱
            packageName = "", // 無效：空包名
            hour = 25, // 無效：超出範圍的小時
            minute = 30,
            repeatMode = RepeatMode.DAILY.value,
            isEnabled = true
        )
        
        // 插入有效排程並創建備份
        repository.insertSchedule(validSchedule)
        val backupResult = backupManager.createLocalBackup()
        assertTrue("備份創建應該成功", backupResult.success)
        
        // 手動修改備份文件以包含無效數據
        val backupFile = java.io.File(backupResult.filePath!!)
        val originalContent = backupFile.readText()
        val backupData = backupManager.gson.fromJson(originalContent, BackupData::class.java)
        
        // 添加無效排程到備份數據
        val invalidScheduleBackup = ScheduleBackup.fromSchedule(invalidSchedule)
        val modifiedBackupData = backupData.copy(
            schedules = backupData.schedules + invalidScheduleBackup,
            totalSchedules = backupData.totalSchedules + 1
        )
        
        val modifiedContent = backupManager.gson.toJson(modifiedBackupData)
        backupFile.writeText(modifiedContent)
        
        // 清空數據庫並恢復
        repository.deleteAllSchedules()
        val restoreResult = backupManager.restoreFromLocalFile(backupResult.filePath!!)
        
        // 驗證部分恢復結果
        assertTrue("恢復應該成功（至少有一個有效排程）", restoreResult.success)
        assertEquals("應該匯入1個有效排程", 1, restoreResult.importedCount)
        assertEquals("應該跳過1個無效排程", 1, restoreResult.skippedCount)
        assertTrue("錯誤列表不應為空", restoreResult.errors.isNotEmpty())
        
        // 驗證只有有效排程被恢復
        val restoredSchedules = repository.getAllSchedules().first()
        assertEquals("應該只有1個排程被恢復", 1, restoredSchedules.size)
        assertEquals("恢復的排程應該是有效的", validSchedule.appName, restoredSchedules[0].appName)
    }
    
    @Test
    fun testCloudBackupManagerInitialization() {
        // 測試雲端備份管理器初始化
        assertNotNull("雲端備份管理器不應為空", cloudBackupManager)
        
        // 測試Google登錄狀態檢查（在沒有登錄的情況下）
        val isSignedIn = cloudBackupManager.isGoogleSignedIn()
        // 注意：在測試環境中通常沒有登錄，所以應該是false
        // 但這取決於測試環境的設置
        
        // 測試Google Sign-In客戶端獲取
        val signInClient = cloudBackupManager.getGoogleSignInClient()
        assertNotNull("Google Sign-In客戶端不應為空", signInClient)
    }
}
