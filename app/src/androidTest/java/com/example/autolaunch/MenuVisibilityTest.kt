package com.example.autolaunch

import androidx.test.espresso.Espresso.onView
import androidx.test.espresso.assertion.ViewAssertions.matches
import androidx.test.espresso.matcher.ViewMatchers.*
import androidx.test.ext.junit.rules.ActivityScenarioRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.filters.LargeTest
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

/**
 * 測試選單按鈕在各種狀態下的可見性
 */
@RunWith(AndroidJUnit4::class)
@LargeTest
class MenuVisibilityTest {

    @get:Rule
    val activityRule = ActivityScenarioRule(MainActivity::class.java)

    @Test
    fun testMenuButtonVisibleInEmptyState() {
        // 等待UI載入
        Thread.sleep(1000)
        
        // 驗證選單按鈕始終可見
        onView(withId(R.id.btnMenu))
            .check(matches(isDisplayed()))
        
        // 驗證頂部工具列可見
        onView(withId(R.id.topToolbar))
            .check(matches(isDisplayed()))
        
        // 驗證空狀態卡片可見（當沒有排程時）
        onView(withId(R.id.emptyStateCard))
            .check(matches(isDisplayed()))
    }

    @Test
    fun testMenuButtonVisibleWithSchedules() {
        // 等待UI載入
        Thread.sleep(1000)
        
        // 驗證選單按鈕始終可見
        onView(withId(R.id.btnMenu))
            .check(matches(isDisplayed()))
        
        // 驗證頂部工具列可見
        onView(withId(R.id.topToolbar))
            .check(matches(isDisplayed()))
    }

    @Test
    fun testTopToolbarAlwaysVisible() {
        // 等待UI載入
        Thread.sleep(1000)
        
        // 驗證頂部工具列的所有元素都可見
        onView(withId(R.id.topToolbar))
            .check(matches(isDisplayed()))
        
        onView(withId(R.id.btnMenu))
            .check(matches(isDisplayed()))
        
        // 驗證 AutoLaunch 標題可見
        onView(withText("AutoLaunch"))
            .check(matches(isDisplayed()))
    }
}
