package com.example.autolaunch

import com.example.autolaunch.adapter.UnifiedScheduleAdapterTest
import com.example.autolaunch.model.*
import com.example.autolaunch.receiver.LaunchReceiverTest
import com.example.autolaunch.service.ScheduleServiceTest
import com.example.autolaunch.utils.*
import com.example.autolaunch.worker.ScheduleMonitorWorkerTest
import org.junit.runner.RunWith
import org.junit.runners.Suite

/**
 * 所有測試的測試套件
 * 用於一次性運行所有單元測試
 */
@RunWith(Suite::class)
@Suite.SuiteClasses(
    // 模型測試
    ScheduleTest::class,
    
    // 數據層測試
    ScheduleDaoTest::class,
    ScheduleRepositoryTest::class,
    
    // 服務測試
    ScheduleServiceTest::class,
    AlarmManagerServiceTest::class,
    
    // 接收器測試
    LaunchReceiverTest::class,
    
    // Worker 測試
    ScheduleMonitorWorkerTest::class,
    
    // 適配器測試
    UnifiedScheduleAdapterTest::class,
    
    // 工具類測試
    UrlUtilsTest::class,
    UrlValidationTest::class,
    BatteryOptimizationHelperTest::class,
    SystemPermissionHelperTest::class,
    BackgroundExecutionHelperTest::class,
    TextIconGeneratorTest::class,
    ExceptionHandlerTest::class,
    ResourceManagerTest::class,
    LoggerTest::class,
    UIPerformanceManagerTest::class,
    
    // 其他測試
    DefaultTemplateTest::class
)
class AllTestsSuite
