package com.example.autolaunch

import com.example.autolaunch.utils.ThemeType
import org.junit.Test
import org.junit.Assert.*

/**
 * 主題系統測試
 */
class ThemeTest {

    @Test
    fun testThemeCount() {
        val allThemes = ThemeType.values()
        assertEquals("應該有16個主題", 16, allThemes.size)
    }

    @Test
    fun testLightThemes() {
        val lightThemes = ThemeType.getLightThemes()
        assertEquals("應該有7個淺色主題", 7, lightThemes.size)

        val expectedLightThemes = listOf(
            ThemeType.LIGHT_CLASSIC,
            ThemeType.LIGHT_WARM,
            ThemeType.LIGHT_COOL,
            ThemeType.LIGHT_PINK,
            ThemeType.LIGHT_YELLOW,
            ThemeType.LIGHT_MINT,
            ThemeType.LIGHT_LAVENDER
        )

        assertTrue("淺色主題應該包含所有預期的主題", lightThemes.containsAll(expectedLightThemes))
    }

    @Test
    fun testDarkThemes() {
        val darkThemes = ThemeType.getDarkThemes()
        assertEquals("應該有9個深色主題", 9, darkThemes.size)

        val expectedDarkThemes = listOf(
            ThemeType.DARK_CLASSIC,
            ThemeType.DARK_BLUE,
            ThemeType.DARK_GREEN,
            ThemeType.DARK_PURPLE,
            ThemeType.DARK_ORANGE,
            ThemeType.DARK_ROSE,
            ThemeType.DARK_GOLD,
            ThemeType.DARK_TEAL,
            ThemeType.DARK_CRIMSON
        )

        assertTrue("深色主題應該包含所有預期的主題", darkThemes.containsAll(expectedDarkThemes))
    }

    @Test
    fun testNewDarkThemes() {
        // 測試新增的深紫主題
        assertEquals("深紫主題ID應該正確", "dark_purple", ThemeType.DARK_PURPLE.id)
        assertEquals("深紫主題顯示名稱應該正確", "深紫主題", ThemeType.DARK_PURPLE.displayName)
        assertEquals("深紫主題描述應該正確", "魔法師的神秘紫色 🔮", ThemeType.DARK_PURPLE.description)
        assertTrue("深紫主題應該是深色模式", ThemeType.DARK_PURPLE.isDarkMode)

        // 測試新增的深橙主題
        assertEquals("深橙主題ID應該正確", "dark_orange", ThemeType.DARK_ORANGE.id)
        assertEquals("深橙主題顯示名稱應該正確", "深橙主題", ThemeType.DARK_ORANGE.displayName)
        assertEquals("深橙主題描述應該正確", "篝火般的溫暖橙光 🔥", ThemeType.DARK_ORANGE.description)
        assertTrue("深橙主題應該是深色模式", ThemeType.DARK_ORANGE.isDarkMode)
    }

    @Test
    fun testThemeFromId() {
        // 測試現有主題
        assertEquals("應該能從ID獲取經典淺色主題", ThemeType.LIGHT_CLASSIC, ThemeType.fromId("light_classic"))
        assertEquals("應該能從ID獲取經典深色主題", ThemeType.DARK_CLASSIC, ThemeType.fromId("dark_classic"))
        
        // 測試新增的主題
        assertEquals("應該能從ID獲取深紫主題", ThemeType.DARK_PURPLE, ThemeType.fromId("dark_purple"))
        assertEquals("應該能從ID獲取深橙主題", ThemeType.DARK_ORANGE, ThemeType.fromId("dark_orange"))

        // 測試無效ID
        assertEquals("無效ID應該返回預設主題", ThemeType.LIGHT_CLASSIC, ThemeType.fromId("invalid_id"))
    }

    @Test
    fun testDefaultTheme() {
        assertEquals("淺色模式預設主題應該是經典淺色", ThemeType.LIGHT_CLASSIC, ThemeType.getDefaultTheme(false))
        assertEquals("深色模式預設主題應該是經典深色", ThemeType.DARK_CLASSIC, ThemeType.getDefaultTheme(true))
    }

    @Test
    fun testThemeIdsUnique() {
        val allThemes = ThemeType.values()
        val ids = allThemes.map { it.id }
        val uniqueIds = ids.toSet()

        assertEquals("所有主題ID應該是唯一的", ids.size, uniqueIds.size)
    }

}
