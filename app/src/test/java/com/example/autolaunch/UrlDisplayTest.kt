package com.example.autolaunch

import com.example.autolaunch.utils.UrlUtils
import org.junit.Test
import org.junit.Assert.*

/**
 * 測試URL顯示功能
 * 驗證在UI中顯示域名而不是完整URL的功能
 */
class UrlDisplayTest {

    @Test
    fun `test extractDomain returns correct domain for various URLs`() {
        val testCases = mapOf(
            "https://www.google.com/search?q=test" to "google.com",
            "http://facebook.com/page" to "facebook.com",
            "https://github.com/user/repo" to "github.com",
            "http://www.example.org/path/to/page" to "example.org",
            "https://stackoverflow.com/questions/123" to "stackoverflow.com",
            "http://www.youtube.com/watch?v=abc" to "youtube.com",
            "https://mail.google.com" to "mail.google.com",
            "http://subdomain.example.com" to "subdomain.example.com"
        )

        testCases.forEach { (url, expectedDomain) ->
            val result = UrlUtils.extractDomain(url)
            assertEquals("Failed for URL: $url", expectedDomain, result)
        }
    }

    @Test
    fun `test extractDomain handles edge cases`() {
        // 測試空值和無效URL
        assertNull(UrlUtils.extractDomain(null))
        assertNull(UrlUtils.extractDomain(""))
        assertNull(UrlUtils.extractDomain("   "))
        assertNull(UrlUtils.extractDomain("not-a-url"))
        assertNull(UrlUtils.extractDomain("ftp://"))
        
        // 測試有效但簡單的URL
        assertEquals("example.com", UrlUtils.extractDomain("https://example.com"))
        assertEquals("test.org", UrlUtils.extractDomain("http://test.org"))
    }

    @Test
    fun `test extractDomain removes www prefix`() {
        val testCases = mapOf(
            "https://www.google.com" to "google.com",
            "http://www.facebook.com" to "facebook.com",
            "https://www.github.com" to "github.com",
            "http://www.example.org" to "example.org"
        )

        testCases.forEach { (url, expectedDomain) ->
            val result = UrlUtils.extractDomain(url)
            assertEquals("Failed to remove www for URL: $url", expectedDomain, result)
        }
    }

    @Test
    fun `test extractDomain preserves subdomains`() {
        val testCases = mapOf(
            "https://mail.google.com" to "mail.google.com",
            "http://api.github.com" to "api.github.com",
            "https://docs.example.org" to "docs.example.org",
            "http://blog.stackoverflow.com" to "blog.stackoverflow.com"
        )

        testCases.forEach { (url, expectedDomain) ->
            val result = UrlUtils.extractDomain(url)
            assertEquals("Failed to preserve subdomain for URL: $url", expectedDomain, result)
        }
    }

    @Test
    fun `test extractDomain with different protocols`() {
        val testCases = mapOf(
            "https://example.com" to "example.com",
            "http://example.com" to "example.com",
            "ftp://example.com" to "example.com"
        )

        testCases.forEach { (url, expectedDomain) ->
            val result = UrlUtils.extractDomain(url)
            assertEquals("Failed for protocol in URL: $url", expectedDomain, result)
        }
    }

    @Test
    fun `test extractDomain with ports and paths`() {
        val testCases = mapOf(
            "https://example.com:8080/path" to "example.com",
            "http://test.org:3000/api/v1" to "test.org",
            "https://www.github.com:443/user/repo" to "github.com"
        )

        testCases.forEach { (url, expectedDomain) ->
            val result = UrlUtils.extractDomain(url)
            assertEquals("Failed to handle port/path for URL: $url", expectedDomain, result)
        }
    }
}
