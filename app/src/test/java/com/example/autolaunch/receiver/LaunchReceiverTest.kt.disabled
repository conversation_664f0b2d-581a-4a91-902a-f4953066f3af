package com.example.autolaunch.receiver

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.example.autolaunch.receiver.LaunchReceiver
import com.example.autolaunch.utils.assertDoesNotThrow
import io.mockk.MockKAnnotations
import io.mockk.impl.annotations.MockK
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.Before
import org.junit.Assert.*
import org.robolectric.annotation.Config

/**
 * LaunchReceiver 的單元測試
 * 測試廣播接收器的核心功能
 */
@RunWith(AndroidJUnit4::class)
@Config(manifest = Config.NONE)
class LaunchReceiverTest {

    private lateinit var context: Context
    private lateinit var launchReceiver: LaunchReceiver
    
    @MockK
    private lateinit var mockPackageManager: PackageManager

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        context = ApplicationProvider.getApplicationContext()
        launchReceiver = LaunchReceiver()
    }

    @Test
    fun `receiver should be created successfully`() {
        assertNotNull(launchReceiver)
    }

    @Test
    fun `onReceive with ACTION_LAUNCH_APP should not throw`() {
        val intent = Intent().apply {
            action = LaunchReceiver.ACTION_LAUNCH_APP
            putExtra(LaunchReceiver.EXTRA_PACKAGE_NAME, "com.test.app")
            putExtra(LaunchReceiver.EXTRA_SCHEDULE_ID, 123L)
        }
        assertDoesNotThrow("Receiving ACTION_LAUNCH_APP should not throw") {
            launchReceiver.onReceive(context, intent)
        }
    }

    @Test
    fun `onReceive with ACTION_LAUNCH_URL should not throw`() {
        val intent = Intent().apply {
            action = LaunchReceiver.ACTION_LAUNCH_URL
            putExtra(LaunchReceiver.EXTRA_URL, "https://www.google.com")
            putExtra(LaunchReceiver.EXTRA_SCHEDULE_ID, 456L)
        }
        assertDoesNotThrow("Receiving ACTION_LAUNCH_URL should not throw") {
            launchReceiver.onReceive(context, intent)
        }
    }

    @Test
    fun `onReceive with invalid ACTION_LAUNCH_APP parameters should not throw`() {
        val intent = Intent(LaunchReceiver.ACTION_LAUNCH_APP)
        assertDoesNotThrow("Receiver should handle missing extras for LAUNCH_APP gracefully") {
            launchReceiver.onReceive(context, intent)
        }
    }

    @Test
    fun `onReceive with invalid ACTION_LAUNCH_URL parameters should not throw`() {
        val intent = Intent(LaunchReceiver.ACTION_LAUNCH_URL)
        assertDoesNotThrow("Receiver should handle missing extras for LAUNCH_URL gracefully") {
            launchReceiver.onReceive(context, intent)
        }
    }

    @Test
    fun `onReceive with empty package name should not throw`() {
        val intent = Intent().apply {
            action = LaunchReceiver.ACTION_LAUNCH_APP
            putExtra(LaunchReceiver.EXTRA_PACKAGE_NAME, "")
            putExtra(LaunchReceiver.EXTRA_SCHEDULE_ID, 123L)
        }
        assertDoesNotThrow("Receiver should handle empty package name gracefully") {
            launchReceiver.onReceive(context, intent)
        }
    }

    @Test
    fun `onReceive with empty URL should not throw`() {
        val intent = Intent().apply {
            action = LaunchReceiver.ACTION_LAUNCH_URL
            putExtra(LaunchReceiver.EXTRA_URL, "")
            putExtra(LaunchReceiver.EXTRA_SCHEDULE_ID, 456L)
        }
        assertDoesNotThrow("Receiver should handle empty URL gracefully") {
            launchReceiver.onReceive(context, intent)
        }
    }

    @Test
    fun `onReceive with invalid schedule ID should not throw`() {
        val intent = Intent().apply {
            action = LaunchReceiver.ACTION_LAUNCH_APP
            putExtra(LaunchReceiver.EXTRA_PACKAGE_NAME, "com.test.app")
            putExtra(LaunchReceiver.EXTRA_SCHEDULE_ID, -1L)
        }
        assertDoesNotThrow("Receiver should handle invalid schedule ID gracefully") {
            launchReceiver.onReceive(context, intent)
        }
    }

    @Test
    fun `onReceive with unknown action should not throw`() {
        val intent = Intent("com.unknown.action")
        assertDoesNotThrow("Receiver should ignore unknown actions gracefully") {
            launchReceiver.onReceive(context, intent)
        }
    }

    @Test
    fun `receiver constants should be correct`() {
        assertEquals("com.example.autolaunch.action.LAUNCH_APP", LaunchReceiver.ACTION_LAUNCH_APP)
        assertEquals("com.example.autolaunch.action.LAUNCH_URL", LaunchReceiver.ACTION_LAUNCH_URL)
        assertEquals("extra_package_name", LaunchReceiver.EXTRA_PACKAGE_NAME)
        assertEquals("extra_url", LaunchReceiver.EXTRA_URL)
        assertEquals("extra_schedule_id", LaunchReceiver.EXTRA_SCHEDULE_ID)
    }

    @Test
    fun `onReceive with valid app launch parameters should not throw`() {
        val intent = Intent().apply {
            action = LaunchReceiver.ACTION_LAUNCH_APP
            putExtra(LaunchReceiver.EXTRA_PACKAGE_NAME, "com.android.settings")
            putExtra(LaunchReceiver.EXTRA_SCHEDULE_ID, 789L)
        }
        assertDoesNotThrow("Receiving valid app launch intent should not throw") {
            launchReceiver.onReceive(context, intent)
        }
    }

    @Test
    fun `onReceive with valid URL launch parameters should not throw`() {
        val intent = Intent().apply {
            action = LaunchReceiver.ACTION_LAUNCH_URL
            putExtra(LaunchReceiver.EXTRA_URL, "https://www.example.com")
            putExtra(LaunchReceiver.EXTRA_SCHEDULE_ID, 101112L)
        }
        assertDoesNotThrow("Receiving valid URL launch intent should not throw") {
            launchReceiver.onReceive(context, intent)
        }
    }
}
