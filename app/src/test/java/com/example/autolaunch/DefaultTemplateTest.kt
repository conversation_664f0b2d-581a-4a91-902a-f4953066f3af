package com.example.autolaunch

import com.example.autolaunch.model.ScheduleType
import org.junit.Test
import org.junit.Assert.*

/**
 * 預設範本功能測試
 */
class DefaultTemplateTest {

    @Test
    fun testScheduleTypeEnum() {
        // 測試排程類型枚舉
        assertEquals(0, ScheduleType.APP.value)
        assertEquals(1, ScheduleType.URL.value)
        
        // 測試從值轉換
        assertEquals(ScheduleType.APP, ScheduleType.fromValue(0))
        assertEquals(ScheduleType.URL, ScheduleType.fromValue(1))
        assertEquals(ScheduleType.APP, ScheduleType.fromValue(-1)) // 預設值
    }

    @Test
    fun testDefaultWebTemplate() {
        // 測試預設 Web 範本的常量
        val defaultUrl = "https://www.google.com"
        val defaultTitle = "Google"
        
        // 驗證 URL 格式
        assertTrue("預設 URL 應該以 https:// 開頭", defaultUrl.startsWith("https://"))
        assertTrue("預設 URL 應該包含 google.com", defaultUrl.contains("google.com"))
        assertFalse("預設標題不應該為空", defaultTitle.isEmpty())
        assertEquals("預設標題應該是 Google", "Google", defaultTitle)
    }

    @Test
    fun testScheduleTypeValues() {
        // 測試所有排程類型值
        val allTypes = ScheduleType.values()
        assertEquals("應該有兩種排程類型", 2, allTypes.size)
        
        // 確保包含所有預期的類型
        assertTrue("應該包含 APP 類型", allTypes.contains(ScheduleType.APP))
        assertTrue("應該包含 URL 類型", allTypes.contains(ScheduleType.URL))
    }

    @Test
    fun testDefaultTemplateConstants() {
        // 測試預設範本相關常量
        val appPackageName = "com.example.autolaunch"
        val webUrl = "https://www.google.com"
        val webTitle = "Google"
        
        // 驗證包名格式
        assertTrue("包名應該包含 autolaunch", appPackageName.contains("autolaunch"))
        assertTrue("包名應該以 com. 開頭", appPackageName.startsWith("com."))
        
        // 驗證 Web 預設值
        assertTrue("Web URL 應該是有效的 HTTPS 網址", webUrl.matches(Regex("https://[\\w.-]+\\.[a-z]{2,}")))
        assertTrue("Web 標題應該不為空且合理長度", webTitle.isNotEmpty() && webTitle.length <= 20)
    }
}
