package com.example.autolaunch.adapter

import android.content.Context
import androidx.recyclerview.widget.RecyclerView
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.example.autolaunch.R
import com.example.autolaunch.model.Schedule
import com.example.autolaunch.model.ScheduleType
import com.example.autolaunch.model.RepeatMode
import com.example.autolaunch.utils.MainCoroutineRule
import io.mockk.mockk
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.Assert.*

/**
 * UnifiedScheduleAdapter 的單元測試
 * 測試統一排程適配器的功能
 */
@ExperimentalCoroutinesApi
@RunWith(AndroidJUnit4::class)
class UnifiedScheduleAdapterTest {

    @get:Rule
    val mainCoroutineRule = MainCoroutineRule()

    private lateinit var context: Context
    private lateinit var adapter: UnifiedScheduleAdapter

    private val mockOnItemClick: (Schedule) -> Unit = mockk(relaxed = true)
    private val mockOnToggleEnabled: (Schedule, Boolean) -> Unit = mockk(relaxed = true)

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
        adapter = UnifiedScheduleAdapter(
            onItemClick = mockOnItemClick,
            onToggleEnabled = mockOnToggleEnabled
        )

        // 為測試創建一個 dummy RecyclerView
        val recyclerView = RecyclerView(context)
        adapter.registerAdapterDataObserver(object : RecyclerView.AdapterDataObserver() {
            // 這個 observer 可以幫助同步 ListAdapter 的異步操作
        })
        recyclerView.adapter = adapter
    }

    private fun getTransformedList(schedules: List<Schedule>): List<UnifiedScheduleAdapter.ScheduleItem> {
        val appSchedules = schedules.filter { it.isAppSchedule() }
        val urlSchedules = schedules.filter { it.isUrlSchedule() }
        val items = mutableListOf<UnifiedScheduleAdapter.ScheduleItem>()
        if (appSchedules.isNotEmpty()) {
            items.add(UnifiedScheduleAdapter.ScheduleItem.Header("應用程式", appSchedules.size, R.drawable.ic_apps_24))
            items.addAll(appSchedules.map { UnifiedScheduleAdapter.ScheduleItem.ScheduleData(it) })
        }
        if (urlSchedules.isNotEmpty()) {
            items.add(UnifiedScheduleAdapter.ScheduleItem.Header("網頁書籤", urlSchedules.size, R.drawable.ic_web_24))
            items.addAll(urlSchedules.map { UnifiedScheduleAdapter.ScheduleItem.ScheduleData(it) })
        }
        return items
    }

    @Test
    fun `adapter creation should be successful`() {
        assertNotNull("Adapter should be created successfully", adapter)
        assertEquals("Initial item count should be 0", 0, adapter.itemCount)
    }

    @Test
    fun `submitList with empty list should result in zero items`() = runTest {
        adapter.submitList(emptyList())
        mainCoroutineRule.testDispatcher.scheduler.advanceUntilIdle()
        assertEquals("Submitting an empty list should result in 0 items", 0, adapter.itemCount)
    }

    @Test
    fun `submitList with app schedules should create header and items`() = runTest {
        val schedules = listOf(
            Schedule(id = 1, scheduleType = ScheduleType.APP.value, appName = "App 1", packageName = "com.app1", hour = 9, minute = 0, repeatMode = RepeatMode.DAILY.value, isEnabled = true),
            Schedule(id = 2, scheduleType = ScheduleType.APP.value, appName = "App 2", packageName = "com.app2", hour = 10, minute = 0, repeatMode = RepeatMode.WEEKLY.value, isEnabled = false)
        )
        val expectedList = getTransformedList(schedules)
        adapter.submitList(expectedList)
        mainCoroutineRule.testDispatcher.scheduler.advanceUntilIdle()

        assertEquals("Should have 1 header + 2 schedule items", 3, adapter.itemCount)
        assertTrue(adapter.currentList[0] is UnifiedScheduleAdapter.ScheduleItem.Header)
        assertEquals("應用程式", (adapter.currentList[0] as UnifiedScheduleAdapter.ScheduleItem.Header).title)
        assertEquals(2, (adapter.currentList[0] as UnifiedScheduleAdapter.ScheduleItem.Header).count)
    }

    @Test
    fun `submitList with url schedules should create header and items`() = runTest {
        val schedules = listOf(
            Schedule(id = 3, scheduleType = ScheduleType.URL.value, url = "https://a.com", urlTitle = "A", hour = 11, minute = 0, repeatMode = RepeatMode.DAILY.value, isEnabled = true)
        )
        val expectedList = getTransformedList(schedules)
        adapter.submitList(expectedList)
        mainCoroutineRule.testDispatcher.scheduler.advanceUntilIdle()

        assertEquals("Should have 1 header + 1 schedule item", 2, adapter.itemCount)
        assertTrue(adapter.currentList[0] is UnifiedScheduleAdapter.ScheduleItem.Header)
        assertEquals("網頁書籤", (adapter.currentList[0] as UnifiedScheduleAdapter.ScheduleItem.Header).title)
    }

    @Test
    fun `submitList with mixed schedules should create two sections`() = runTest {
        val schedules = listOf(
            Schedule(id = 1, scheduleType = ScheduleType.APP.value, appName = "App 1", packageName = "com.app1", hour = 9, minute = 0, repeatMode = RepeatMode.DAILY.value, isEnabled = true),
            Schedule(id = 3, scheduleType = ScheduleType.URL.value, url = "https://a.com", urlTitle = "A", hour = 11, minute = 0, repeatMode = RepeatMode.DAILY.value, isEnabled = true)
        )
        val expectedList = getTransformedList(schedules)
        adapter.submitList(expectedList)
        mainCoroutineRule.testDispatcher.scheduler.advanceUntilIdle()

        assertEquals("Should have 2 headers + 2 schedule items", 4, adapter.itemCount)
        assertEquals("應用程式", (adapter.currentList[0] as UnifiedScheduleAdapter.ScheduleItem.Header).title)
        assertEquals("網頁書籤", (adapter.currentList[2] as UnifiedScheduleAdapter.ScheduleItem.Header).title)
    }

    @Test
    fun `getItemViewType should return correct types`() = runTest {
        val schedules = listOf(
            Schedule(id = 1, scheduleType = ScheduleType.APP.value, appName = "App 1", packageName = "com.app1", hour = 9, minute = 0, repeatMode = RepeatMode.DAILY.value, isEnabled = true),
            Schedule(id = 3, scheduleType = ScheduleType.URL.value, url = "https://a.com", urlTitle = "A", hour = 11, minute = 0, repeatMode = RepeatMode.DAILY.value, isEnabled = true)
        )
        val expectedList = getTransformedList(schedules)
        adapter.submitList(expectedList)
        mainCoroutineRule.testDispatcher.scheduler.advanceUntilIdle()

        assertEquals(UnifiedScheduleAdapter.VIEW_TYPE_HEADER, adapter.getItemViewType(0))
        assertEquals(UnifiedScheduleAdapter.VIEW_TYPE_SCHEDULE, adapter.getItemViewType(1))
        assertEquals(UnifiedScheduleAdapter.VIEW_TYPE_HEADER, adapter.getItemViewType(2))
        assertEquals(UnifiedScheduleAdapter.VIEW_TYPE_SCHEDULE, adapter.getItemViewType(3))
    }
}
