package com.example.autolaunch.worker

import android.content.Context
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.work.ListenableWorker
import androidx.work.testing.TestListenableWorkerBuilder
import androidx.work.testing.WorkManagerTestInitHelper
import com.example.autolaunch.utils.MainCoroutineRule
import com.example.autolaunch.utils.assertDoesNotThrow
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.Assert.*

/**
 * ScheduleMonitorWorker 的單元測試
 * 測試排程監控 Worker 的功能
 */
@ExperimentalCoroutinesApi
@RunWith(AndroidJUnit4::class)
class ScheduleMonitorWorkerTest {

    @get:Rule
    val mainCoroutineRule = MainCoroutineRule()

    private lateinit var context: Context

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
        WorkManagerTestInitHelper.initializeTestWorkManager(context)
    }

    @Test
    fun `worker should be created successfully`() {
        val worker = TestListenableWorkerBuilder<ScheduleMonitorWorker>(context).build()
        assertNotNull("Worker should be created successfully", worker)
    }

    @Test
    fun `doWork should return a success result`() = runTest {
        val worker = TestListenableWorkerBuilder<ScheduleMonitorWorker>(context).build()
        val result = worker.doWork()
        assertTrue(
            "doWork should return Success",
            result is ListenableWorker.Result.Success
        )
    }

    @Test
    fun `doWork should not throw any exceptions`() = runTest {
        val worker = TestListenableWorkerBuilder<ScheduleMonitorWorker>(context).build()
        assertDoesNotThrow("doWork should not throw any exceptions") {
            worker.doWork()
        }
    }
}
