package com.example.autolaunch

import com.example.autolaunch.utils.ThemeType
import org.junit.Test
import org.junit.Assert.*

/**
 * 主題管理器測試
 */
class ThemeManagerTest {
    
    @Test
    fun testThemeTypeFromId() {
        // 測試有效的主題ID
        assertEquals(ThemeType.LIGHT_CLASSIC, ThemeType.fromId("light_classic"))
        assertEquals(ThemeType.DARK_BLUE, ThemeType.fromId("dark_blue"))
        
        // 測試無效的主題ID，應該返回預設主題
        assertEquals(ThemeType.LIGHT_CLASSIC, ThemeType.fromId("invalid_theme"))
    }
    
    @Test
    fun testGetLightThemes() {
        val lightThemes = ThemeType.getLightThemes()
        
        // 確保所有淺色主題都被包含
        assertTrue(lightThemes.contains(ThemeType.LIGHT_CLASSIC))
        assertTrue(lightThemes.contains(ThemeType.LIGHT_WARM))
        assertTrue(lightThemes.contains(ThemeType.LIGHT_COOL))
        
        // 確保沒有深色主題
        assertFalse(lightThemes.any { it.isDarkMode })
        
        // 確保有7個淺色主題
        assertEquals(7, lightThemes.size)
    }
    
    @Test
    fun testGetDarkThemes() {
        val darkThemes = ThemeType.getDarkThemes()

        // 確保所有深色主題都被包含
        assertTrue(darkThemes.contains(ThemeType.DARK_CLASSIC))
        assertTrue(darkThemes.contains(ThemeType.DARK_BLUE))
        assertTrue(darkThemes.contains(ThemeType.DARK_GREEN))
        assertTrue(darkThemes.contains(ThemeType.DARK_PURPLE))
        assertTrue(darkThemes.contains(ThemeType.DARK_ORANGE))

        // 確保都是深色主題
        assertTrue(darkThemes.all { it.isDarkMode })

        // 確保有9個深色主題
        assertEquals(9, darkThemes.size)
    }
    
    @Test
    fun testGetDefaultTheme() {
        // 測試系統深色模式時的預設主題
        assertEquals(ThemeType.DARK_CLASSIC, ThemeType.getDefaultTheme(true))
        
        // 測試系統淺色模式時的預設主題
        assertEquals(ThemeType.LIGHT_CLASSIC, ThemeType.getDefaultTheme(false))
    }
    
    @Test
    fun testThemeProperties() {
        // 測試淺色主題屬性
        val lightClassic = ThemeType.LIGHT_CLASSIC
        assertEquals("light_classic", lightClassic.id)
        assertEquals("經典淺色", lightClassic.displayName)
        assertFalse(lightClassic.isDarkMode)
        
        // 測試深色主題屬性
        val darkBlue = ThemeType.DARK_BLUE
        assertEquals("dark_blue", darkBlue.id)
        assertEquals("深藍主題", darkBlue.displayName)
        assertTrue(darkBlue.isDarkMode)
    }
}
