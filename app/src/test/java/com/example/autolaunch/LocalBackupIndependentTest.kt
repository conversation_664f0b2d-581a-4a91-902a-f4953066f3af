package com.example.autolaunch

import android.content.Context
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.example.autolaunch.model.*
import com.example.autolaunch.utils.BackupManager
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.runBlocking
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import java.io.File

/**
 * 本地備份獨立性測試
 * 驗證本地備份功能可以在沒有 Google 帳戶的情況下正常工作
 */
@RunWith(AndroidJUnit4::class)
class LocalBackupIndependentTest {
    
    private lateinit var context: Context
    private lateinit var repository: ScheduleRepository
    private lateinit var backupManager: BackupManager
    
    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
        repository = ScheduleRepository(context)
        backupManager = BackupManager(context)
        
        // 清理現有數據
        runBlocking {
            repository.deleteAllSchedules()
        }
    }
    
    @After
    fun tearDown() {
        runBlocking {
            repository.deleteAllSchedules()
            
            // 清理測試備份文件
            val backupFiles = backupManager.getLocalBackupFiles()
            backupFiles.forEach { file ->
                if (file.name.contains("test") || file.name.contains("Test")) {
                    file.delete()
                }
            }
        }
    }
    
    @Test
    fun testLocalBackupWithoutGoogleAccount() = runBlocking {
        println("=== 測試本地備份功能（無需 Google 帳戶）===")
        
        // 1. 創建測試排程數據
        val testSchedules = listOf(
            Schedule(
                scheduleType = ScheduleType.APP.value,
                appName = "測試應用",
                packageName = "com.test.app",
                taskName = "本地備份測試任務",
                hour = 10,
                minute = 30,
                repeatMode = RepeatMode.DAILY.value,
                isEnabled = true
            ),
            Schedule(
                scheduleType = ScheduleType.URL.value,
                url = "https://www.example.com",
                urlTitle = "測試網站",
                taskName = "本地備份測試網頁",
                hour = 15,
                minute = 0,
                repeatMode = RepeatMode.WEEKLY.value,
                daysOfWeek = 31, // 週一到週五
                isEnabled = true
            )
        )
        
        // 2. 插入測試數據
        testSchedules.forEach { schedule ->
            val id = repository.insertSchedule(schedule)
            assertTrue("排程插入應該成功", id > 0)
            println("插入排程：${schedule.taskName} (ID: $id)")
        }
        
        // 驗證數據插入
        val insertedSchedules = repository.getAllSchedules().first()
        assertEquals("應該插入2個排程", 2, insertedSchedules.size)
        println("成功插入 ${insertedSchedules.size} 個排程")
        
        // 3. 創建本地備份（無需 Google 帳戶）
        println("\n--- 創建本地備份 ---")
        val backupResult = backupManager.createLocalBackup()
        
        assertTrue("本地備份創建應該成功", backupResult.success)
        assertNotNull("備份文件路徑不應為空", backupResult.filePath)
        assertEquals("備份排程數量應該匹配", 2, backupResult.scheduleCount)
        assertTrue("備份文件大小應該大於0", backupResult.fileSize > 0)
        
        val backupFile = File(backupResult.filePath!!)
        assertTrue("備份文件應該存在", backupFile.exists())
        println("本地備份創建成功：${backupFile.name} (${backupResult.fileSize} bytes)")
        
        // 4. 驗證備份文件內容
        println("\n--- 驗證備份文件 ---")
        val backupFileInfo = backupManager.getBackupFileInfo(backupFile)
        assertNotNull("備份文件信息不應為空", backupFileInfo)
        assertTrue("備份文件應該有效", backupFileInfo!!.isValid)
        assertEquals("文件中排程數量應該匹配", 2, backupFileInfo.scheduleCount)
        println("備份文件驗證通過：${backupFileInfo.scheduleCount} 個排程")
        
        // 5. 清空數據庫
        println("\n--- 清空數據庫 ---")
        val deleteCount = repository.deleteAllSchedules()
        assertEquals("應該刪除2個排程", 2, deleteCount)
        
        val emptySchedules = repository.getAllSchedules().first()
        assertTrue("數據庫應該為空", emptySchedules.isEmpty())
        println("數據庫已清空")
        
        // 6. 從本地備份恢復（無需 Google 帳戶）
        println("\n--- 從本地備份恢復 ---")
        val restoreResult = backupManager.restoreFromLocalFile(backupResult.filePath!!)
        
        assertTrue("數據恢復應該成功", restoreResult.success)
        assertEquals("應該匯入2個排程", 2, restoreResult.importedCount)
        assertEquals("不應該跳過任何排程", 0, restoreResult.skippedCount)
        assertEquals("不應該有錯誤", 0, restoreResult.errorCount)
        println("數據恢復成功：匯入 ${restoreResult.importedCount} 個排程")
        
        // 7. 驗證恢復的數據
        println("\n--- 驗證恢復的數據 ---")
        val restoredSchedules = repository.getAllSchedules().first()
        assertEquals("應該恢復2個排程", 2, restoredSchedules.size)
        
        // 驗證排程內容
        val originalSorted = testSchedules.sortedBy { it.taskName }
        val restoredSorted = restoredSchedules.sortedBy { it.taskName }
        
        for (i in originalSorted.indices) {
            val original = originalSorted[i]
            val restored = restoredSorted[i]
            
            assertEquals("排程類型應該匹配", original.scheduleType, restored.scheduleType)
            assertEquals("應用名稱應該匹配", original.appName, restored.appName)
            assertEquals("包名應該匹配", original.packageName, restored.packageName)
            assertEquals("任務名稱應該匹配", original.taskName, restored.taskName)
            assertEquals("URL應該匹配", original.url, restored.url)
            assertEquals("小時應該匹配", original.hour, restored.hour)
            assertEquals("分鐘應該匹配", original.minute, restored.minute)
            assertEquals("重複模式應該匹配", original.repeatMode, restored.repeatMode)
            assertEquals("啟用狀態應該匹配", original.isEnabled, restored.isEnabled)
            
            println("✓ 排程驗證通過：${original.taskName}")
        }
        
        // 8. 測試備份文件管理
        println("\n--- 測試備份文件管理 ---")
        val backupFiles = backupManager.getLocalBackupFiles()
        assertTrue("應該至少有1個備份文件", backupFiles.isNotEmpty())
        println("找到 ${backupFiles.size} 個本地備份文件")
        
        // 清理測試文件
        val deleteSuccess = backupManager.deleteLocalBackupFile(backupFile)
        assertTrue("文件刪除應該成功", deleteSuccess)
        assertFalse("文件應該不存在", backupFile.exists())
        println("測試備份文件已清理")
        
        println("\n=== 本地備份功能測試完成 ===")
        println("✅ 本地備份功能完全獨立，無需 Google 帳戶即可正常工作")
    }
    
    @Test
    fun testLocalBackupFileOperations() = runBlocking {
        println("=== 測試本地備份文件操作 ===")
        
        // 創建測試排程
        val testSchedule = Schedule(
            scheduleType = ScheduleType.APP.value,
            appName = "文件操作測試",
            packageName = "com.test.fileops",
            taskName = "文件操作測試任務",
            hour = 12,
            minute = 0,
            repeatMode = RepeatMode.DAILY.value,
            isEnabled = true
        )
        
        repository.insertSchedule(testSchedule)
        
        // 創建備份
        val backupResult = backupManager.createLocalBackup()
        assertTrue("備份創建應該成功", backupResult.success)
        
        // 測試備份文件列表
        val backupFiles = backupManager.getLocalBackupFiles()
        assertTrue("應該有備份文件", backupFiles.isNotEmpty())
        
        val latestFile = backupFiles.first()
        
        // 測試文件信息獲取
        val fileInfo = backupManager.getBackupFileInfo(latestFile)
        assertNotNull("文件信息不應為空", fileInfo)
        assertTrue("文件應該有效", fileInfo!!.isValid)
        assertEquals("排程數量應該為1", 1, fileInfo.scheduleCount)
        
        println("✅ 本地備份文件操作測試通過")
    }
    
    @Test
    fun testLocalBackupWithoutPermissions() {
        println("=== 測試權限檢查 ===")
        
        // 注意：這個測試主要驗證邏輯，實際權限檢查需要在真實設備上測試
        // 在測試環境中，我們主要驗證代碼邏輯的正確性
        
        // 驗證 BackupManager 不依賴 Google 服務
        assertNotNull("BackupManager 應該可以創建", backupManager)
        
        // 驗證本地備份方法存在且可調用
        assertTrue("createLocalBackup 方法應該存在", 
            BackupManager::class.java.methods.any { it.name == "createLocalBackup" })
        
        assertTrue("restoreFromLocalFile 方法應該存在",
            BackupManager::class.java.methods.any { it.name == "restoreFromLocalFile" })
        
        println("✅ 權限檢查邏輯驗證通過")
    }
}
