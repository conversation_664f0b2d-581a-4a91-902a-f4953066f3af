package com.example.autolaunch.utils

import android.app.AlarmManager
import android.content.Context
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.graphics.drawable.Drawable
import com.example.autolaunch.model.AppInfo
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify

/**
 * Mock 幫助工具
 * 提供創建 Mock 對象的便利方法
 */
object MockHelper {

    /**
     * 創建 Mock Context
     */
    fun createMockContext(): Context {
        val mockContext = mockk<Context>(relaxed = true)
        
        // 設置包名
        every { mockContext.packageName } returns "com.example.autolaunch"
        
        // 設置應用程序名稱
        every { mockContext.applicationContext } returns mockContext
        
        return mockContext
    }

    /**
     * 創建 Mock PackageManager
     */
    fun createMockPackageManager(): PackageManager {
        val mockPackageManager = mockk<PackageManager>(relaxed = true)
        
        // 創建一些測試應用信息
        val testApps = createTestApplicationInfos()
        
        every { mockPackageManager.getInstalledApplications(PackageManager.GET_META_DATA) } returns testApps
        
        // 模擬獲取應用標籤
        testApps.forEach { appInfo ->
            every { mockPackageManager.getApplicationLabel(appInfo) } returns "App ${appInfo.packageName}"
        }
        
        // 模擬獲取應用圖標
        val mockDrawable = mockk<Drawable>(relaxed = true)
        testApps.forEach { appInfo ->
            every { mockPackageManager.getApplicationIcon(appInfo) } returns mockDrawable
        }
        
        return mockPackageManager
    }

    /**
     * 創建 Mock AlarmManager
     */
    fun createMockAlarmManager(): AlarmManager {
        val mockAlarmManager = mockk<AlarmManager>(relaxed = true)
        
        // 模擬 canScheduleExactAlarms 方法（API 31+）
        try {
            every { mockAlarmManager.canScheduleExactAlarms() } returns true
        } catch (e: Exception) {
            // 在較低 API 級別上可能不存在此方法
        }
        
        return mockAlarmManager
    }

    /**
     * 創建測試用的 ApplicationInfo 列表
     */
    private fun createTestApplicationInfos(): List<ApplicationInfo> {
        return listOf(
            createApplicationInfo("com.android.settings", "Settings"),
            createApplicationInfo("com.android.chrome", "Chrome"),
            createApplicationInfo("com.google.android.gm", "Gmail"),
            createApplicationInfo("com.whatsapp", "WhatsApp"),
            createApplicationInfo("com.facebook.katana", "Facebook"),
            createApplicationInfo("com.instagram.android", "Instagram"),
            createApplicationInfo("com.twitter.android", "Twitter"),
            createApplicationInfo("com.spotify.music", "Spotify"),
            createApplicationInfo("com.netflix.mediaclient", "Netflix"),
            createApplicationInfo("com.youtube.android", "YouTube")
        )
    }

    /**
     * 創建單個 ApplicationInfo
     */
    private fun createApplicationInfo(packageName: String, name: String): ApplicationInfo {
        return ApplicationInfo().apply {
            this.packageName = packageName
            this.name = name
            this.flags = ApplicationInfo.FLAG_INSTALLED
        }
    }

    /**
     * 創建測試用的 AppInfo 列表
     */
    fun createTestAppInfos(): List<AppInfo> {
        return listOf(
            AppInfo("com.android.settings", "設定", null),
            AppInfo("com.android.chrome", "Chrome", null),
            AppInfo("com.google.android.gm", "Gmail", null),
            AppInfo("com.whatsapp", "WhatsApp", null),
            AppInfo("com.facebook.katana", "Facebook", null),
            AppInfo("com.instagram.android", "Instagram", null),
            AppInfo("com.twitter.android", "Twitter", null),
            AppInfo("com.spotify.music", "Spotify", null),
            AppInfo("com.netflix.mediaclient", "Netflix", null),
            AppInfo("com.youtube.android", "YouTube", null)
        )
    }

    /**
     * 創建 Mock Context 並設置系統服務
     */
    fun createMockContextWithServices(): Context {
        val mockContext = createMockContext()
        val mockPackageManager = createMockPackageManager()
        val mockAlarmManager = createMockAlarmManager()
        
        // 設置 PackageManager
        every { mockContext.packageManager } returns mockPackageManager
        
        // 設置 AlarmManager
        every { mockContext.getSystemService(Context.ALARM_SERVICE) } returns mockAlarmManager
        
        return mockContext
    }

    /**
     * 創建 Mock Context 用於權限測試
     */
    fun createMockContextForPermissions(
        hasSystemAlertWindow: Boolean = true,
        isIgnoringBatteryOptimizations: Boolean = true
    ): Context {
        val mockContext = createMockContext()
        
        // 這裡可以根據需要添加更多權限相關的 Mock 設置
        
        return mockContext
    }

    /**
     * 創建 Mock Context 用於數據庫測試
     */
    fun createMockContextForDatabase(): Context {
        val mockContext = createMockContext()
        
        // 設置數據庫相關的 Mock
        // 注意：實際的數據庫測試通常使用真實的 Context
        
        return mockContext
    }

    /**
     * 驗證 Mock 對象的交互
     */
    fun verifyMockInteractions(mockObject: Any, times: Int = 1) {
        // 這裡可以添加通用的驗證邏輯
        verify(exactly = times) { mockObject }
    }

    /**
     * 重置 Mock 對象
     */
    fun resetMock(mockObject: Any) {
        // 在 MockK 中，我們通常不直接重置 mock，而是重新創建它們
    }

    /**
     * 創建 Mock 異常情況
     */
    fun setupMockException(mockObject: Any, methodCall: () -> Unit, exception: Exception) {
        every { methodCall() } throws exception
    }

    /**
     * 創建用於測試的 Mock 回調
     */
    fun createMockCallback(): () -> Unit {
        return mockk()
    }

    /**
     * 創建用於測試的 Mock 點擊監聽器
     */
    fun createMockClickListener(): (Any) -> Unit {
        return mockk<(Any) -> Unit>()
    }

    /**
     * 創建用於測試的 Mock 長按監聽器
     */
    fun createMockLongClickListener(): (Any) -> Boolean {
        return mockk<(Any) -> Boolean>()
    }

    /**
     * 設置 Mock 對象的默認行為
     */
    fun setupDefaultMockBehavior(mockContext: Context) {
        // 設置一些常用的默認行為
        every { mockContext.getString(any()) } returns "Mock String"
        every { mockContext.getColor(any()) } returns 0xFF000000.toInt()
    }

    /**
     * 創建用於性能測試的 Mock 對象
     */
    fun createPerformanceTestMocks(): Map<String, Any> {
        return mapOf<String, Any>(
            "context" to createMockContext(),
            "packageManager" to createMockPackageManager(),
            "alarmManager" to createMockAlarmManager()
        )
    }
}
