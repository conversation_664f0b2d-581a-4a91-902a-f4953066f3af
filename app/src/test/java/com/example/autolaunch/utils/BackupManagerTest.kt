package com.example.autolaunch.utils

import android.content.Context
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.example.autolaunch.model.*
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.flow.first
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import java.io.File

/**
 * BackupManager 單元測試
 */
@RunWith(AndroidJUnit4::class)
class BackupManagerTest {
    
    private lateinit var context: Context
    private lateinit var backupManager: BackupManager
    private lateinit var testSchedules: List<Schedule>
    
    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
        backupManager = BackupManager(context)
        
        // 創建測試排程數據
        testSchedules = listOf(
            Schedule(
                id = 1,
                scheduleType = ScheduleType.APP.value,
                appName = "測試應用1",
                packageName = "com.test.app1",
                taskName = "測試任務1",
                hour = 9,
                minute = 30,
                repeatMode = RepeatMode.DAILY.value,
                isEnabled = true,
                createdTime = System.currentTimeMillis(),
                updatedTime = System.currentTimeMillis()
            ),
            Schedule(
                id = 2,
                scheduleType = ScheduleType.URL.value,
                url = "https://www.google.com",
                urlTitle = "Google",
                taskName = "測試網頁任務",
                hour = 14,
                minute = 0,
                repeatMode = RepeatMode.WEEKLY.value,
                daysOfWeek = 31, // 週一到週五
                isEnabled = true,
                createdTime = System.currentTimeMillis(),
                updatedTime = System.currentTimeMillis()
            )
        )
    }
    
    @After
    fun tearDown() {
        // 清理測試文件
        val backupFiles = runBlocking { backupManager.getLocalBackupFiles() }
        backupFiles.forEach { file ->
            if (file.name.contains("test") || file.name.contains("Test")) {
                file.delete()
            }
        }
    }
    
    @Test
    fun testCreateBackupData() {
        val backupData = backupManager.createBackupData(testSchedules)
        
        assertNotNull("備份數據不應為空", backupData)
        assertEquals("排程數量應該匹配", testSchedules.size, backupData.schedules.size)
        assertEquals("總排程數應該匹配", testSchedules.size, backupData.totalSchedules)
        assertTrue("備份數據應該有效", backupData.isValid())
        
        // 檢查設備信息
        assertNotNull("設備信息不應為空", backupData.deviceInfo)
        assertFalse("應用版本不應為空", backupData.deviceInfo.appVersion.isBlank())
        assertFalse("Android版本不應為空", backupData.deviceInfo.androidVersion.isBlank())
        assertFalse("設備型號不應為空", backupData.deviceInfo.deviceModel.isBlank())
    }
    
    @Test
    fun testScheduleBackupConversion() {
        val originalSchedule = testSchedules[0]
        val scheduleBackup = ScheduleBackup.fromSchedule(originalSchedule)
        val convertedSchedule = scheduleBackup.toSchedule()
        
        // 檢查關鍵字段是否正確轉換
        assertEquals("排程類型應該匹配", originalSchedule.scheduleType, convertedSchedule.scheduleType)
        assertEquals("應用名稱應該匹配", originalSchedule.appName, convertedSchedule.appName)
        assertEquals("包名應該匹配", originalSchedule.packageName, convertedSchedule.packageName)
        assertEquals("任務名稱應該匹配", originalSchedule.taskName, convertedSchedule.taskName)
        assertEquals("小時應該匹配", originalSchedule.hour, convertedSchedule.hour)
        assertEquals("分鐘應該匹配", originalSchedule.minute, convertedSchedule.minute)
        assertEquals("重複模式應該匹配", originalSchedule.repeatMode, convertedSchedule.repeatMode)
        assertEquals("星期設定應該匹配", originalSchedule.daysOfWeek, convertedSchedule.daysOfWeek)
        assertEquals("啟用狀態應該匹配", originalSchedule.isEnabled, convertedSchedule.isEnabled)
        
        // 注意：ID 和時間戳會在匯入時重新生成，所以不檢查這些字段
    }
    
    @Test
    fun testBackupDataValidation() {
        // 測試有效的備份數據
        val validBackupData = backupManager.createBackupData(testSchedules)
        assertTrue("有效的備份數據應該通過驗證", validBackupData.isValid())
        
        // 測試無效的備份數據（空排程列表）
        val invalidBackupData = backupManager.createBackupData(emptyList())
        assertFalse("空排程列表的備份數據應該無效", invalidBackupData.isValid())
    }
    
    @Test
    fun testScheduleBackupValidation() {
        // 測試有效的APP排程
        val validAppSchedule = ScheduleBackup.fromSchedule(testSchedules[0])
        assertTrue("有效的APP排程應該通過驗證", validAppSchedule.isValid())
        
        // 測試有效的URL排程
        val validUrlSchedule = ScheduleBackup.fromSchedule(testSchedules[1])
        assertTrue("有效的URL排程應該通過驗證", validUrlSchedule.isValid())
        
        // 測試無效的APP排程（缺少包名）
        val invalidAppSchedule = ScheduleBackup(
            scheduleType = ScheduleType.APP.value,
            appName = "測試應用",
            packageName = null, // 缺少包名
            hour = 9,
            minute = 30,
            repeatMode = RepeatMode.DAILY.value,
            createdTime = System.currentTimeMillis(),
            updatedTime = System.currentTimeMillis()
        )
        assertFalse("缺少包名的APP排程應該無效", invalidAppSchedule.isValid())
        
        // 測試無效的URL排程（無效URL）
        val invalidUrlSchedule = ScheduleBackup(
            scheduleType = ScheduleType.URL.value,
            url = "invalid-url", // 無效URL
            hour = 9,
            minute = 30,
            repeatMode = RepeatMode.DAILY.value,
            createdTime = System.currentTimeMillis(),
            updatedTime = System.currentTimeMillis()
        )
        assertFalse("無效URL的排程應該無效", invalidUrlSchedule.isValid())
        
        // 測試無效的時間設定
        val invalidTimeSchedule = ScheduleBackup(
            scheduleType = ScheduleType.APP.value,
            appName = "測試應用",
            packageName = "com.test.app",
            hour = 25, // 無效小時
            minute = 30,
            repeatMode = RepeatMode.DAILY.value,
            createdTime = System.currentTimeMillis(),
            updatedTime = System.currentTimeMillis()
        )
        assertFalse("無效時間的排程應該無效", invalidTimeSchedule.isValid())
    }
    
    @Test
    fun testBackupFileInfo() {
        val backupData = backupManager.createBackupData(testSchedules)
        
        // 測試建議文件名生成
        val fileName = backupData.getSuggestedFileName()
        assertTrue("文件名應該包含AutoLaunch", fileName.contains("AutoLaunch"))
        assertTrue("文件名應該包含Backup", fileName.contains("Backup"))
        assertTrue("文件名應該以.json結尾", fileName.endsWith(".json"))
        
        // 測試格式化時間戳
        val formattedTime = backupData.getFormattedTimestamp()
        assertFalse("格式化時間不應為空", formattedTime.isBlank())
        assertTrue("格式化時間應該包含年份", formattedTime.contains("202"))
    }
    
    @Test
    fun testJsonSerialization() {
        val backupData = backupManager.createBackupData(testSchedules)
        
        // 序列化為JSON
        val jsonString = backupManager.gson.toJson(backupData)
        assertFalse("JSON字符串不應為空", jsonString.isBlank())
        assertTrue("JSON應該包含version字段", jsonString.contains("\"version\""))
        assertTrue("JSON應該包含schedules字段", jsonString.contains("\"schedules\""))
        assertTrue("JSON應該包含device_info字段", jsonString.contains("\"device_info\""))
        
        // 反序列化
        val deserializedData = backupManager.gson.fromJson(jsonString, BackupData::class.java)
        assertNotNull("反序列化數據不應為空", deserializedData)
        assertEquals("版本應該匹配", backupData.version, deserializedData.version)
        assertEquals("排程數量應該匹配", backupData.schedules.size, deserializedData.schedules.size)
        assertEquals("總排程數應該匹配", backupData.totalSchedules, deserializedData.totalSchedules)
        assertTrue("反序列化數據應該有效", deserializedData.isValid())
    }
    
    @Test
    fun testBackupResultCreation() {
        // 測試成功結果
        val successResult = BackupResult(
            success = true,
            message = "備份成功",
            filePath = "/test/path/backup.json",
            fileSize = 1024,
            scheduleCount = 2
        )
        
        assertTrue("成功結果應該為true", successResult.success)
        assertEquals("消息應該匹配", "備份成功", successResult.message)
        assertEquals("文件路徑應該匹配", "/test/path/backup.json", successResult.filePath)
        assertEquals("文件大小應該匹配", 1024L, successResult.fileSize)
        assertEquals("排程數量應該匹配", 2, successResult.scheduleCount)
        
        // 測試失敗結果
        val failureResult = BackupResult(
            success = false,
            message = "備份失敗",
            error = RuntimeException("測試錯誤")
        )
        
        assertFalse("失敗結果應該為false", failureResult.success)
        assertEquals("錯誤消息應該匹配", "備份失敗", failureResult.message)
        assertNotNull("錯誤對象不應為空", failureResult.error)
        assertTrue("錯誤應該是RuntimeException", failureResult.error is RuntimeException)
    }
    
    @Test
    fun testRestoreResultCreation() {
        // 測試成功恢復結果
        val successResult = RestoreResult(
            success = true,
            message = "恢復成功",
            importedCount = 2,
            skippedCount = 0,
            errorCount = 0
        )
        
        assertTrue("成功結果應該為true", successResult.success)
        assertEquals("匯入數量應該匹配", 2, successResult.importedCount)
        assertEquals("跳過數量應該匹配", 0, successResult.skippedCount)
        assertEquals("錯誤數量應該匹配", 0, successResult.errorCount)
        assertTrue("錯誤列表應該為空", successResult.errors.isEmpty())
        
        // 測試部分成功結果
        val partialResult = RestoreResult(
            success = true,
            message = "部分恢復成功",
            importedCount = 1,
            skippedCount = 1,
            errorCount = 1,
            errors = listOf("排程1恢復失敗")
        )
        
        assertTrue("部分成功結果應該為true", partialResult.success)
        assertEquals("匯入數量應該匹配", 1, partialResult.importedCount)
        assertEquals("跳過數量應該匹配", 1, partialResult.skippedCount)
        assertEquals("錯誤數量應該匹配", 1, partialResult.errorCount)
        assertEquals("錯誤列表大小應該匹配", 1, partialResult.errors.size)
    }
}
