package com.example.autolaunch.utils

import android.os.Handler
import android.os.Looper
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkConstructor
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import io.mockk.verify
import kotlinx.coroutines.test.runTest
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit

/**
 * UIPerformanceManager 單元測試
 */
class UIPerformanceManagerTest {
    
    private lateinit var mockMainLooper: Looper
    private lateinit var mockHandler: Handler
    
    @Before
    fun setUp() {
        // Mock Android components
        mockMainLooper = mockk()
        mockHandler = mockk(relaxed = true)
        
        mockkStatic(Looper::class)
        every { Looper.getMainLooper() } returns mockMainLooper
        every { Looper.myLooper() } returns mockMainLooper
        
        mockkConstructor(Handler::class)
        every { anyConstructed<Handler>().post(any()) } answers {
            // Execute the runnable immediately for testing
            val runnable = firstArg<Runnable>()
            runnable.run()
            true
        }
        every { anyConstructed<Handler>().postDelayed(any(), any()) } answers {
            // Execute the runnable immediately for testing
            val runnable = firstArg<Runnable>()
            runnable.run()
            true
        }
    }
    
    @After
    fun tearDown() {
        unmockkAll()
    }
    
    @Test
    fun `runOnMainThread should execute action immediately when already on main thread`() {
        // Given
        var actionExecuted = false
        
        // When
        UIPerformanceManager.runOnMainThread {
            actionExecuted = true
        }
        
        // Then
        assertTrue(actionExecuted)
    }
    
    @Test
    fun `runOnMainThread should post to handler when not on main thread`() {
        // Given
        every { Looper.myLooper() } returns mockk() // Different looper
        var actionExecuted = false
        
        // When
        UIPerformanceManager.runOnMainThread {
            actionExecuted = true
        }
        
        // Then
        assertTrue(actionExecuted) // Should still execute due to our mock
    }
    
    @Test
    fun `runOnMainThreadDelayed should post delayed action`() {
        // Given
        var actionExecuted = false
        val delayMs = 1000L
        
        // When
        UIPerformanceManager.runOnMainThreadDelayed(delayMs) {
            actionExecuted = true
        }
        
        // Then
        assertTrue(actionExecuted)
        verify { anyConstructed<Handler>().postDelayed(any(), delayMs) }
    }
    
    @Test
    fun `runInBackground should execute action in background thread`() {
        // Given
        var actionExecuted = false
        val latch = CountDownLatch(1)
        
        // When
        UIPerformanceManager.runInBackground {
            actionExecuted = true
            latch.countDown()
        }
        
        // Then
        assertTrue(latch.await(1, TimeUnit.SECONDS))
        assertTrue(actionExecuted)
    }
    
    @Test
    fun `runInBackgroundWithCallback should execute background operation and main thread callback`() {
        // Given
        var backgroundExecuted = false
        var callbackExecuted = false
        var callbackResult: String? = null
        val expectedResult = "background result"
        
        // When
        UIPerformanceManager.runInBackgroundWithCallback(
            backgroundOperation = {
                backgroundExecuted = true
                expectedResult
            },
            mainThreadCallback = { result ->
                callbackExecuted = true
                callbackResult = result
            }
        )
        
        // Give some time for background execution
        Thread.sleep(100)
        
        // Then
        assertTrue(backgroundExecuted)
        assertTrue(callbackExecuted)
        assertEquals(expectedResult, callbackResult)
    }
    
    @Test
    fun `runInBackgroundWithCallback should handle background exception`() {
        // Given
        var callbackExecuted = false
        var callbackResult: String? = null
        
        // When
        UIPerformanceManager.runInBackgroundWithCallback(
            backgroundOperation = {
                throw RuntimeException("Background error")
            },
            mainThreadCallback = { result ->
                callbackExecuted = true
                callbackResult = result
            }
        )
        
        // Give some time for background execution
        Thread.sleep(100)
        
        // Then
        assertTrue(callbackExecuted)
        assertNull(callbackResult)
    }
    
    @Test
    fun `runInBackgroundCoroutine should execute operation in background`() = runTest {
        // Given
        var operationExecuted = false
        val expectedResult = "coroutine result"
        
        // When
        val result = UIPerformanceManager.runInBackgroundCoroutine {
            operationExecuted = true
            expectedResult
        }
        
        // Then
        assertTrue(operationExecuted)
        assertEquals(expectedResult, result)
    }
    
    @Test
    fun `runInBackgroundCoroutine should handle exception and return null`() = runTest {
        // When
        val result = UIPerformanceManager.runInBackgroundCoroutine {
            throw RuntimeException("Coroutine error")
        }
        
        // Then
        assertNull(result)
    }
    
    @Test
    fun `safeUpdateUI should execute action safely`() {
        // Given
        var actionExecuted = false
        
        // When
        UIPerformanceManager.safeUpdateUI {
            actionExecuted = true
        }
        
        // Then
        assertTrue(actionExecuted)
    }
    
    @Test
    fun `safeUpdateUI should handle exception gracefully`() {
        // When/Then - Should not throw exception
        UIPerformanceManager.safeUpdateUI {
            throw RuntimeException("UI update error")
        }
    }
    
    @Test
    fun `batchUpdateUI should execute all actions`() {
        // Given
        var action1Executed = false
        var action2Executed = false
        var action3Executed = false
        
        val actions = listOf(
            { action1Executed = true },
            { action2Executed = true },
            { action3Executed = true }
        )
        
        // When
        UIPerformanceManager.batchUpdateUI(actions)
        
        // Then
        assertTrue(action1Executed)
        assertTrue(action2Executed)
        assertTrue(action3Executed)
    }
    
    @Test
    fun `batchUpdateUI should continue execution even if one action fails`() {
        // Given
        var action1Executed = false
        var action3Executed = false
        
        val actions = listOf(
            { action1Executed = true },
            { throw RuntimeException("Action 2 failed") },
            { action3Executed = true }
        )
        
        // When
        UIPerformanceManager.batchUpdateUI(actions)
        
        // Then
        assertTrue(action1Executed)
        assertTrue(action3Executed)
    }
    
    @Test
    fun `Debouncer should execute action after delay`() {
        // Given
        val debouncer = UIPerformanceManager.Debouncer(100L)
        var actionExecuted = false
        
        // When
        debouncer.execute {
            actionExecuted = true
        }
        
        // Then
        assertTrue(actionExecuted) // Should execute immediately due to our mock
    }
    
    @Test
    fun `Debouncer should cancel previous action when new one is submitted`() {
        // Given
        val debouncer = UIPerformanceManager.Debouncer(100L)
        var firstActionExecuted = false
        var secondActionExecuted = false
        
        // When
        debouncer.execute {
            firstActionExecuted = true
        }
        debouncer.execute {
            secondActionExecuted = true
        }
        
        // Then
        // Due to our mock, both will execute, but in real scenario only the second would
        assertTrue(secondActionExecuted)
    }
    
    @Test
    fun `Debouncer cancel should remove pending action`() {
        // Given
        val debouncer = UIPerformanceManager.Debouncer(100L)
        
        // When
        debouncer.execute { /* some action */ }
        debouncer.cancel()
        
        // Then
        verify { anyConstructed<Handler>().removeCallbacks(any()) }
    }
    
    @Test
    fun `Throttler should execute action immediately first time`() {
        // Given
        val throttler = UIPerformanceManager.Throttler(1000L)
        var actionExecuted = false
        
        // When
        throttler.execute {
            actionExecuted = true
        }
        
        // Then
        assertTrue(actionExecuted)
    }
    
    @Test
    fun `Throttler cancel should remove pending action`() {
        // Given
        val throttler = UIPerformanceManager.Throttler(1000L)
        
        // When
        throttler.execute { /* some action */ }
        throttler.cancel()
        
        // Then
        verify { anyConstructed<Handler>().removeCallbacks(any()) }
    }
    
    @Test
    fun `isMainThread should return true when on main thread`() {
        // When
        val result = UIPerformanceManager.isMainThread()
        
        // Then
        assertTrue(result)
    }
    
    @Test
    fun `isMainThread should return false when not on main thread`() {
        // Given
        every { Looper.myLooper() } returns mockk() // Different looper
        
        // When
        val result = UIPerformanceManager.isMainThread()
        
        // Then
        assertFalse(result)
    }
    
    @Test
    fun `ensureMainThread should not throw when on main thread`() {
        // When/Then - Should not throw
        UIPerformanceManager.ensureMainThread()
    }
    
    @Test
    fun `ensureMainThread should throw when not on main thread`() {
        // Given
        every { Looper.myLooper() } returns mockk() // Different looper
        
        // When/Then
        assertThrows(IllegalStateException::class.java) {
            UIPerformanceManager.ensureMainThread()
        }
    }
    
    @Test
    fun `ensureBackgroundThread should throw when on main thread`() {
        // When/Then
        assertThrows(IllegalStateException::class.java) {
            UIPerformanceManager.ensureBackgroundThread()
        }
    }
    
    @Test
    fun `ensureBackgroundThread should not throw when not on main thread`() {
        // Given
        every { Looper.myLooper() } returns mockk() // Different looper
        
        // When/Then - Should not throw
        UIPerformanceManager.ensureBackgroundThread()
    }
    
    @Test
    fun `runOnMainThread extension should execute action on main thread`() {
        // Given
        var actionExecuted = false
        val action = { actionExecuted = true }
        
        // When
        action.runOnMainThread()
        
        // Then
        assertTrue(actionExecuted)
    }
    
    @Test
    fun `runInBackground extension should execute action in background`() {
        // Given
        var actionExecuted = false
        val latch = CountDownLatch(1)
        val action = { 
            actionExecuted = true
            latch.countDown()
        }
        
        // When
        action.runInBackground()
        
        // Then
        assertTrue(latch.await(1, TimeUnit.SECONDS))
        assertTrue(actionExecuted)
    }
}
