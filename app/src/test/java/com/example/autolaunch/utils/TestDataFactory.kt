package com.example.autolaunch.utils

import com.example.autolaunch.model.Schedule
import com.example.autolaunch.model.ScheduleType
import com.example.autolaunch.model.RepeatMode

/**
 * 測試數據工廠
 * 提供創建測試數據的便利方法
 */
object TestDataFactory {

    /**
     * 創建基本的應用排程
     */
    fun createBasicAppSchedule(
        id: Long = 0,
        appName: String = "Test App",
        packageName: String = "com.test.app",
        hour: Int = 9,
        minute: Int = 30,
        repeatMode: Int = RepeatMode.DAILY.value,
        isEnabled: Boolean = true
    ): Schedule {
        return Schedule(
            id = id,
            scheduleType = ScheduleType.APP.value,
            appName = appName,
            packageName = packageName,
            hour = hour,
            minute = minute,
            repeatMode = repeatMode,
            isEnabled = isEnabled
        )
    }

    /**
     * 創建基本的 URL 排程
     */
    fun createBasicUrlSchedule(
        id: Long = 0,
        url: String = "https://www.google.com",
        urlTitle: String = "Google",
        hour: Int = 14,
        minute: Int = 0,
        repeatMode: Int = RepeatMode.ONCE.value,
        isEnabled: Boolean = true
    ): Schedule {
        return Schedule(
            id = id,
            scheduleType = ScheduleType.URL.value,
            url = url,
            urlTitle = urlTitle,
            hour = hour,
            minute = minute,
            repeatMode = repeatMode,
            isEnabled = isEnabled
        )
    }

    /**
     * 創建多個應用排程
     */
    fun createMultipleAppSchedules(count: Int = 5): List<Schedule> {
        return (1..count).map { index ->
            createBasicAppSchedule(
                id = index.toLong(),
                appName = "App $index",
                packageName = "com.app$index",
                hour = 9 + (index % 12),
                minute = index * 10 % 60
            )
        }
    }

    /**
     * 創建多個 URL 排程
     */
    fun createMultipleUrlSchedules(count: Int = 5): List<Schedule> {
        return (1..count).map { index ->
            createBasicUrlSchedule(
                id = index.toLong(),
                url = "https://example$index.com",
                urlTitle = "Example $index",
                hour = 10 + (index % 12),
                minute = index * 15 % 60
            )
        }
    }

    /**
     * 創建混合類型的排程
     */
    fun createMixedSchedules(appCount: Int = 3, urlCount: Int = 3): List<Schedule> {
        val schedules = mutableListOf<Schedule>()
        
        // 添加應用排程
        schedules.addAll(createMultipleAppSchedules(appCount))
        
        // 添加 URL 排程，ID 從應用排程之後開始
        schedules.addAll(
            createMultipleUrlSchedules(urlCount).mapIndexed { index, schedule ->
                schedule.copy(id = (appCount + index + 1).toLong())
            }
        )
        
        return schedules
    }

    /**
     * 創建禁用的排程
     */
    fun createDisabledSchedules(count: Int = 3): List<Schedule> {
        return (1..count).map { index ->
            if (index % 2 == 0) {
                createBasicAppSchedule(
                    id = index.toLong(),
                    appName = "Disabled App $index",
                    packageName = "com.disabled$index",
                    isEnabled = false
                )
            } else {
                createBasicUrlSchedule(
                    id = index.toLong(),
                    url = "https://disabled$index.com",
                    urlTitle = "Disabled $index",
                    isEnabled = false
                )
            }
        }
    }

    /**
     * 創建不同重複模式的排程
     */
    fun createSchedulesWithDifferentRepeatModes(): List<Schedule> {
        return listOf(
            createBasicAppSchedule(
                id = 1,
                appName = "Once App",
                repeatMode = RepeatMode.ONCE.value
            ),
            createBasicAppSchedule(
                id = 2,
                appName = "Daily App",
                repeatMode = RepeatMode.DAILY.value
            ),
            createBasicAppSchedule(
                id = 3,
                appName = "Weekly App",
                repeatMode = RepeatMode.WEEKLY.value
            ),
            createBasicUrlSchedule(
                id = 4,
                url = "https://once.com",
                urlTitle = "Once URL",
                repeatMode = RepeatMode.ONCE.value
            ),
            createBasicUrlSchedule(
                id = 5,
                url = "https://daily.com",
                urlTitle = "Daily URL",
                repeatMode = RepeatMode.DAILY.value
            )
        )
    }

    /**
     * 創建邊緣案例的排程
     */
    fun createEdgeCaseSchedules(): List<Schedule> {
        return listOf(
            // 午夜時間
            createBasicAppSchedule(
                id = 1,
                appName = "Midnight App",
                hour = 0,
                minute = 0
            ),
            // 深夜時間
            createBasicAppSchedule(
                id = 2,
                appName = "Late Night App",
                hour = 23,
                minute = 59
            ),
            // 長 URL
            createBasicUrlSchedule(
                id = 3,
                url = "https://very-long-domain-name-for-testing-purposes.example.com/very/long/path/with/many/segments",
                urlTitle = "Very Long URL Title for Testing Purposes"
            ),
            // 空標題的 URL
            createBasicUrlSchedule(
                id = 4,
                url = "https://no-title.com",
                urlTitle = ""
            ),
            // 特殊字符
            createBasicAppSchedule(
                id = 5,
                appName = "App with 特殊字符 & Symbols!",
                packageName = "com.special.chars"
            )
        )
    }

    /**
     * 創建無效的排程（用於測試驗證）
     */
    fun createInvalidSchedules(): List<Schedule> {
        return listOf(
            // 缺少包名的應用排程
            Schedule(
                id = 1,
                scheduleType = ScheduleType.APP.value,
                appName = "Invalid App",
                packageName = null,
                hour = 9,
                minute = 30,
                repeatMode = RepeatMode.DAILY.value,
                isEnabled = true
            ),
            // 缺少 URL 的 URL 排程
            Schedule(
                id = 2,
                scheduleType = ScheduleType.URL.value,
                url = null,
                urlTitle = "Invalid URL",
                hour = 14,
                minute = 0,
                repeatMode = RepeatMode.ONCE.value,
                isEnabled = true
            ),
            // 無效時間
            Schedule(
                id = 3,
                scheduleType = ScheduleType.APP.value,
                appName = "Invalid Time App",
                packageName = "com.invalid.time",
                hour = 25, // 無效小時
                minute = 70, // 無效分鐘
                repeatMode = RepeatMode.DAILY.value,
                isEnabled = true
            )
        )
    }

    /**
     * 創建大量排程（用於性能測試）
     */
    fun createLargeDataset(count: Int = 1000): List<Schedule> {
        return (1..count).map { index ->
            if (index % 2 == 0) {
                createBasicAppSchedule(
                    id = index.toLong(),
                    appName = "Stress App $index",
                    packageName = "com.stress$index",
                    hour = index % 24,
                    minute = (index * 7) % 60,
                    isEnabled = index % 4 != 0 // 75% 啟用率
                )
            } else {
                createBasicUrlSchedule(
                    id = index.toLong(),
                    url = "https://stress$index.com",
                    urlTitle = "Stress URL $index",
                    hour = index % 24,
                    minute = (index * 11) % 60,
                    isEnabled = index % 4 != 0 // 75% 啟用率
                )
            }
        }
    }

    /**
     * 創建自定義任務名稱的排程
     */
    fun createSchedulesWithCustomTaskNames(): List<Schedule> {
        return listOf(
            createBasicAppSchedule(
                id = 1,
                appName = "Original App Name"
            ).copy(taskName = "Custom Task Name"),
            createBasicUrlSchedule(
                id = 2,
                url = "https://example.com",
                urlTitle = "Original URL Title"
            ).copy(taskName = "Custom URL Task")
        )
    }
}
