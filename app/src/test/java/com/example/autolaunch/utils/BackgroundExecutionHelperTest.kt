package com.example.autolaunch.utils

import android.app.ActivityManager
import android.content.Context
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import io.mockk.MockKAnnotations
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.Before
import org.junit.Assert.*
import org.robolectric.annotation.Config

/**
 * BackgroundExecutionHelper 的單元測試
 * 測試後台執行管理功能
 */
@RunWith(AndroidJUnit4::class)
@Config(manifest = Config.NONE)
class BackgroundExecutionHelperTest {

    private lateinit var context: Context

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        context = ApplicationProvider.getApplicationContext()
    }

    @Test
    fun `isScheduleServiceRunning should not throw exception`() {
        assertDoesNotThrow("isScheduleServiceRunning should handle calls gracefully") {
            BackgroundExecutionHelper.isScheduleServiceRunning(context)
        }
    }

    @Test
    fun `isScheduleServiceRunning should return boolean`() {
        val result = BackgroundExecutionHelper.isScheduleServiceRunning(context)
        assertTrue("Result should be a boolean value", result is Boolean)
    }

    @Test
    fun `isScheduleServiceRunning should be consistent`() {
        val result1 = BackgroundExecutionHelper.isScheduleServiceRunning(context)
        val result2 = BackgroundExecutionHelper.isScheduleServiceRunning(context)
        assertEquals("Multiple calls should return consistent results", result1, result2)
    }

    @Test
    fun `performance of service check should be reasonable`() {
        val startTime = System.currentTimeMillis()
        BackgroundExecutionHelper.isScheduleServiceRunning(context)
        val duration = System.currentTimeMillis() - startTime
        assertTrue("Service check should be reasonably fast", duration < 1000)
    }
}
