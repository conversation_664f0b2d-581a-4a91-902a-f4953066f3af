package com.example.autolaunch.utils

import android.content.Context
import androidx.room.Room
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.example.autolaunch.model.*
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith

/**
 * 系統日志管理器測試
 */
class SystemLogManagerTest {

    @Test
    fun `test SystemLog data class functionality`() {
        val systemLog = SystemLog(
            logType = LogType.SUCCESS.value,
            actionType = ActionType.SCHEDULE_EXECUTED.value,
            scheduleId = 1L,
            scheduleName = "測試排程",
            message = "執行排程：測試排程",
            timestamp = System.currentTimeMillis(),
            details = "測試詳情"
        )

        // 測試枚舉轉換
        assertEquals("日志類型應該是SUCCESS", LogType.SUCCESS, systemLog.getLogTypeEnum())
        assertEquals("操作類型應該是SCHEDULE_EXECUTED", ActionType.SCHEDULE_EXECUTED, systemLog.getActionTypeEnum())

        // 測試時間格式化
        assertNotNull("格式化時間不應該為空", systemLog.getFormattedTime())
        assertNotNull("格式化日期不應該為空", systemLog.getFormattedDate())
        assertNotNull("完整日期時間不應該為空", systemLog.getFullDateTime())
        assertNotNull("相對時間不應該為空", systemLog.getRelativeTime())

        // 測試今天檢查
        assertTrue("應該是今天的日志", systemLog.isToday())
    }

    @Test
    fun `test LogType enum`() {
        assertEquals("INFO值應該是0", 0, LogType.INFO.value)
        assertEquals("WARNING值應該是1", 1, LogType.WARNING.value)
        assertEquals("ERROR值應該是2", 2, LogType.ERROR.value)
        assertEquals("SUCCESS值應該是3", 3, LogType.SUCCESS.value)

        assertEquals("INFO顯示名稱", "資訊", LogType.INFO.displayName)
        assertEquals("WARNING顯示名稱", "警告", LogType.WARNING.displayName)
        assertEquals("ERROR顯示名稱", "錯誤", LogType.ERROR.displayName)
        assertEquals("SUCCESS顯示名稱", "成功", LogType.SUCCESS.displayName)
    }

    @Test
    fun `test ActionType enum`() {
        assertEquals("SCHEDULE_CREATED值應該是0", 0, ActionType.SCHEDULE_CREATED.value)
        assertEquals("SCHEDULE_UPDATED值應該是1", 1, ActionType.SCHEDULE_UPDATED.value)
        assertEquals("SCHEDULE_DELETED值應該是2", 2, ActionType.SCHEDULE_DELETED.value)
        assertEquals("SCHEDULE_EXECUTED值應該是3", 3, ActionType.SCHEDULE_EXECUTED.value)

        assertEquals("SCHEDULE_CREATED顯示名稱", "排程建立", ActionType.SCHEDULE_CREATED.displayName)
        assertEquals("SCHEDULE_UPDATED顯示名稱", "排程修改", ActionType.SCHEDULE_UPDATED.displayName)
        assertEquals("SCHEDULE_DELETED顯示名稱", "排程刪除", ActionType.SCHEDULE_DELETED.displayName)
        assertEquals("SCHEDULE_EXECUTED顯示名稱", "排程執行", ActionType.SCHEDULE_EXECUTED.displayName)
    }
    
    @Test
    fun `test LogStatistics data class`() {
        val statistics = LogStatistics(
            totalCount = 10,
            infoCount = 3,
            warningCount = 2,
            errorCount = 1,
            successCount = 4,
            oldestTimestamp = System.currentTimeMillis() - 86400000L, // 1 day ago
            latestTimestamp = System.currentTimeMillis()
        )

        assertEquals("總數應該是10", 10, statistics.totalCount)
        assertEquals("資訊數應該是3", 3, statistics.infoCount)
        assertEquals("警告數應該是2", 2, statistics.warningCount)
        assertEquals("錯誤數應該是1", 1, statistics.errorCount)
        assertEquals("成功數應該是4", 4, statistics.successCount)
        assertNotNull("最舊時間戳不應該為空", statistics.oldestTimestamp)
        assertNotNull("最新時間戳不應該為空", statistics.latestTimestamp)
    }
}
