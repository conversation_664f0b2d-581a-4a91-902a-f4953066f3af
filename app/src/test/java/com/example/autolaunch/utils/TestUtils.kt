package com.example.autolaunch.utils

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.*
import org.junit.rules.TestWatcher
import org.junit.runner.Description
import org.junit.Assert.fail

/**
 * Executes a block of code and asserts that it does not throw any exception.
 *
 * @param block The block of code to execute.
 */
inline fun assertDoesNotThrow(message: String, block: () -> Unit) {
    try {
        block()
    } catch (e: Exception) {
        fail("$message. Expected block not to throw an exception, but it threw ${e::class.simpleName}: ${e.message}")
    }
}

/**
 * A JUnit rule for managing coroutine dispatchers in tests.
 * This rule sets the main dispatcher to a test dispatcher before each test
 * and resets it after the test finishes.
 */
@ExperimentalCoroutinesApi
class MainCoroutineRule(
    val testDispatcher: TestDispatcher = StandardTestDispatcher()
) : TestWatcher() {
    
    val testScope = TestScope(testDispatcher)

    override fun starting(description: Description) {
        super.starting(description)
        Dispatchers.setMain(testDispatcher)
    }

    override fun finished(description: Description) {
        super.finished(description)
        Dispatchers.resetMain()
    }
} 