package com.example.autolaunch.utils

import android.database.Cursor
import io.mockk.every
import io.mockk.mockk
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.After
import org.junit.Before
import org.junit.Test
import java.io.Closeable
import java.io.InputStream
import java.io.OutputStream
import java.net.HttpURLConnection

/**
 * ResourceManager 單元測試
 */
class ResourceManagerTest {
    
    private lateinit var mockCursor: Cursor
    private lateinit var mockInputStream: InputStream
    private lateinit var mockOutputStream: OutputStream
    private lateinit var mockHttpConnection: HttpURLConnection
    private lateinit var mockCloseable: Closeable
    
    @Before
    fun setUp() {
        mockCursor = mockk(relaxed = true)
        mockInputStream = mockk(relaxed = true)
        mockOutputStream = mockk(relaxed = true)
        mockHttpConnection = mockk(relaxed = true)
        mockCloseable = mockk(relaxed = true)
    }
    
    @After
    fun tearDown() {
        unmockkAll()
    }
    
    @Test
    fun `safeCloseCursor should close cursor when not null`() {
        // When
        ResourceManager.safeCloseCursor(mockCursor)
        
        // Then
        verify { mockCursor.close() }
    }
    
    @Test
    fun `safeCloseCursor should handle null cursor gracefully`() {
        // When
        ResourceManager.safeCloseCursor(null)
        
        // Then
        // Should not throw exception
    }
    
    @Test
    fun `safeCloseCursor should handle exception during close`() {
        // Given
        every { mockCursor.close() } throws RuntimeException("Close failed")
        
        // When
        ResourceManager.safeCloseCursor(mockCursor)
        
        // Then
        // Should not throw exception
        verify { mockCursor.close() }
    }
    
    @Test
    fun `safeCloseInputStream should close stream when not null`() {
        // When
        ResourceManager.safeCloseInputStream(mockInputStream)
        
        // Then
        verify { mockInputStream.close() }
    }
    
    @Test
    fun `safeCloseInputStream should handle null stream gracefully`() {
        // When
        ResourceManager.safeCloseInputStream(null)
        
        // Then
        // Should not throw exception
    }
    
    @Test
    fun `safeCloseInputStream should handle exception during close`() {
        // Given
        every { mockInputStream.close() } throws RuntimeException("Close failed")
        
        // When
        ResourceManager.safeCloseInputStream(mockInputStream)
        
        // Then
        // Should not throw exception
        verify { mockInputStream.close() }
    }
    
    @Test
    fun `safeCloseOutputStream should close stream when not null`() {
        // When
        ResourceManager.safeCloseOutputStream(mockOutputStream)
        
        // Then
        verify { mockOutputStream.close() }
    }
    
    @Test
    fun `safeCloseOutputStream should handle null stream gracefully`() {
        // When
        ResourceManager.safeCloseOutputStream(null)
        
        // Then
        // Should not throw exception
    }
    
    @Test
    fun `safeClose should close closeable when not null`() {
        // When
        ResourceManager.safeClose(mockCloseable)
        
        // Then
        verify { mockCloseable.close() }
    }
    
    @Test
    fun `safeClose should handle null closeable gracefully`() {
        // When
        ResourceManager.safeClose(null)
        
        // Then
        // Should not throw exception
    }
    
    @Test
    fun `safeDisconnect should disconnect HTTP connection when not null`() {
        // When
        ResourceManager.safeDisconnect(mockHttpConnection)
        
        // Then
        verify { mockHttpConnection.disconnect() }
    }
    
    @Test
    fun `safeDisconnect should handle null connection gracefully`() {
        // When
        ResourceManager.safeDisconnect(null as HttpURLConnection?)
        
        // Then
        // Should not throw exception
    }
    
    @Test
    fun `safeCloseAll should close all provided resources`() {
        // Given
        val mockCloseable1 = mockk<Closeable>(relaxed = true)
        val mockCloseable2 = mockk<Closeable>(relaxed = true)
        val mockCloseable3 = mockk<Closeable>(relaxed = true)
        
        // When
        ResourceManager.safeCloseAll(mockCloseable1, mockCloseable2, mockCloseable3)
        
        // Then
        verify { mockCloseable1.close() }
        verify { mockCloseable2.close() }
        verify { mockCloseable3.close() }
    }
    
    @Test
    fun `safeCloseAll should handle null resources in varargs`() {
        // When
        ResourceManager.safeCloseAll(mockCloseable, null, mockInputStream)
        
        // Then
        verify { mockCloseable.close() }
        verify { mockInputStream.close() }
    }
    
    @Test
    fun `safeExecute should execute operation and close resources`() {
        // Given
        val mockCloseable1 = mockk<Closeable>(relaxed = true)
        val mockCloseable2 = mockk<Closeable>(relaxed = true)
        val resources = listOf(mockCloseable1, mockCloseable2)
        var operationExecuted = false
        
        // When
        val result = ResourceManager.safeExecute(resources) {
            operationExecuted = true
            "success"
        }
        
        // Then
        assert(operationExecuted)
        assert(result == "success")
        verify { mockCloseable1.close() }
        verify { mockCloseable2.close() }
    }
    
    @Test
    fun `safeExecute should close resources even when operation throws exception`() {
        // Given
        val mockCloseable1 = mockk<Closeable>(relaxed = true)
        val resources = listOf(mockCloseable1)
        
        // When
        val result = ResourceManager.safeExecute(resources) {
            throw RuntimeException("Operation failed")
        }
        
        // Then
        assert(result == null)
        verify { mockCloseable1.close() }
    }
    
    @Test
    fun `needsClosing should return true for open cursor`() {
        // Given
        every { mockCursor.isClosed } returns false
        
        // When
        val result = ResourceManager.needsClosing(mockCursor)
        
        // Then
        assert(result)
    }
    
    @Test
    fun `needsClosing should return false for closed cursor`() {
        // Given
        every { mockCursor.isClosed } returns true
        
        // When
        val result = ResourceManager.needsClosing(mockCursor)
        
        // Then
        assert(!result)
    }
    
    @Test
    fun `needsClosing should return false for null cursor`() {
        // When
        val result = ResourceManager.needsClosing(null)
        
        // Then
        assert(!result)
    }
    
    @Test
    fun `cursor safeUse extension should execute block and close cursor`() {
        // Given
        every { mockCursor.isClosed } returns false
        var blockExecuted = false
        
        // When
        val result = mockCursor.safeUse { cursor ->
            blockExecuted = true
            "success"
        }
        
        // Then
        assert(blockExecuted)
        assert(result == "success")
        verify { mockCursor.close() }
    }
    
    @Test
    fun `cursor safeUse extension should return null for null cursor`() {
        // When
        val result = (null as Cursor?).safeUse { "success" }
        
        // Then
        assert(result == null)
    }
    
    @Test
    fun `cursor safeUse extension should return null for closed cursor`() {
        // Given
        every { mockCursor.isClosed } returns true
        
        // When
        val result = mockCursor.safeUse { "success" }
        
        // Then
        assert(result == null)
    }
    
    @Test
    fun `cursor safeUse extension should handle exception and close cursor`() {
        // Given
        every { mockCursor.isClosed } returns false
        
        // When
        val result = mockCursor.safeUse { 
            throw RuntimeException("Block failed")
        }
        
        // Then
        assert(result == null)
        verify { mockCursor.close() }
    }
    
    @Test
    fun `inputStream safeUse extension should execute block and close stream`() {
        // Given
        var blockExecuted = false
        
        // When
        val result = mockInputStream.safeUse { stream ->
            blockExecuted = true
            "success"
        }
        
        // Then
        assert(blockExecuted)
        assert(result == "success")
        verify { mockInputStream.close() }
    }
    
    @Test
    fun `inputStream safeUse extension should return null for null stream`() {
        // When
        val result = (null as InputStream?).safeUse { "success" }
        
        // Then
        assert(result == null)
    }
    
    @Test
    fun `outputStream safeUse extension should execute block and close stream`() {
        // Given
        var blockExecuted = false
        
        // When
        val result = mockOutputStream.safeUse { stream ->
            blockExecuted = true
            "success"
        }
        
        // Then
        assert(blockExecuted)
        assert(result == "success")
        verify { mockOutputStream.close() }
    }
    
    @Test
    fun `outputStream safeUse extension should return null for null stream`() {
        // When
        val result = (null as OutputStream?).safeUse { "success" }
        
        // Then
        assert(result == null)
    }
}
