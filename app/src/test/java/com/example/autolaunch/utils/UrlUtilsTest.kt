package com.example.autolaunch.utils

import org.junit.Test
import org.junit.Assert.*

/**
 * Unit tests for UrlUtils
 */
class UrlUtilsTest {

    @Test
    fun `test extractDomain with valid URLs`() {
        val testCases = mapOf(
            "https://www.google.com" to "google.com",
            "http://facebook.com" to "facebook.com",
            "https://github.com/user/repo" to "github.com",
            "http://www.example.org/path?query=1" to "example.org",
            "https://subdomain.example.com" to "subdomain.example.com",
            "http://192.168.1.1:8080" to "192.168.1.1"
        )

        testCases.forEach { (url, expectedDomain) ->
            val result = UrlUtils.extractDomain(url)
            assertEquals("Failed for URL: $url", expectedDomain, result)
        }
    }

    @Test
    fun `test extractDomain with invalid URLs`() {
        val invalidUrls = listOf(
            null,
            "",
            "   ",
            "not-a-url",
            "google.com", // Missing protocol
            "http://",
            "https://"
        )

        invalidUrls.forEach { url ->
            val result = UrlUtils.extractDomain(url)
            assertNull("Should return null for invalid URL: '$url'", result)
        }
    }

    @Test
    fun `test extractDisplayName with valid URLs`() {
        val testCases = mapOf(
            "https://www.google.com" to "Google",
            "http://facebook.com" to "Facebook",
            "https://github.com/user/repo" to "Github",
            "http://www.example.org/path" to "Example",
            "https://stackoverflow.com" to "Stackoverflow",
            "http://www.youtube.com" to "Youtube"
        )

        testCases.forEach { (url, expectedName) ->
            val result = UrlUtils.extractDisplayName(url)
            assertEquals("Failed for URL: $url", expectedName, result)
        }
    }

    @Test
    fun `test extractDisplayName with edge cases`() {
        val testCases = mapOf(
            "https://a.b" to "A",
            "http://single" to "Single",
            "https://www.multi-word.com" to "Multi-word"
        )

        testCases.forEach { (url, expectedName) ->
            val result = UrlUtils.extractDisplayName(url)
            assertEquals("Failed for URL: $url", expectedName, result)
        }
    }

    @Test
    fun `test generateFaviconUrl with valid URLs`() {
        val testCases = mapOf(
            "https://www.google.com" to "https://www.google.com/s2/favicons?domain=google.com&sz=64",
            "http://facebook.com" to "https://www.google.com/s2/favicons?domain=facebook.com&sz=64",
            "https://github.com/user/repo" to "https://www.google.com/s2/favicons?domain=github.com&sz=64"
        )

        testCases.forEach { (url, expectedFaviconUrl) ->
            val result = UrlUtils.generateFaviconUrl(url)
            assertEquals("Failed for URL: $url", expectedFaviconUrl, result)
        }
    }

    @Test
    fun `test generateFaviconUrl with invalid URLs`() {
        val invalidUrls = listOf(null, "", "   ", "not-a-url")

        invalidUrls.forEach { url ->
            val result = UrlUtils.generateFaviconUrl(url)
            assertNull("Should return null for invalid URL: '$url'", result)
        }
    }

    @Test
    fun `test isValidUrl`() {
        val validUrls = listOf(
            "https://www.google.com",
            "http://example.org",
            "ftp://files.example.com",
            "https://subdomain.example.com/path?query=1"
        )

        val invalidUrls = listOf(
            null,
            "",
            "   ",
            "not-a-url",
            "google.com", // Missing protocol
            "http://",
            "https://",
            "mailto:<EMAIL>"
        )

        validUrls.forEach { url ->
            assertTrue("Should be valid: $url", UrlUtils.isValidUrl(url))
        }

        invalidUrls.forEach { url ->
            assertFalse("Should be invalid: '$url'", UrlUtils.isValidUrl(url))
        }
    }

    @Test
    fun `test normalizeUrl`() {
        val testCases = mapOf(
            "google.com" to "https://google.com",
            "www.example.org" to "https://www.example.org",
            "https://github.com" to "https://github.com",
            "http://facebook.com" to "http://facebook.com",
            "ftp://files.example.com" to "ftp://files.example.com",
            "  example.com  " to "https://example.com"
        )

        testCases.forEach { (input, expected) ->
            val result = UrlUtils.normalizeUrl(input)
            assertEquals("Failed for input: '$input'", expected, result)
        }
    }

    @Test
    fun `test normalizeUrl with invalid input`() {
        val invalidInputs = listOf(null, "", "   ")

        invalidInputs.forEach { input ->
            val result = UrlUtils.normalizeUrl(input)
            assertNull("Should return null for invalid input: '$input'", result)
        }
    }
}
