package com.example.autolaunch.utils

import android.content.Context
import android.content.SharedPreferences
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.Mockito.*
import org.mockito.junit.MockitoJUnitRunner
import org.junit.Assert.*

/**
 * LanguageManager 單元測試
 */
@RunWith(MockitoJUnitRunner::class)
class LanguageManagerTest {

    @Mock
    private lateinit var mockContext: Context

    @Mock
    private lateinit var mockSharedPreferences: SharedPreferences

    @Mock
    private lateinit var mockEditor: SharedPreferences.Editor

    @Before
    fun setup() {
        `when`(mockContext.getSharedPreferences("language_settings", Context.MODE_PRIVATE))
            .thenReturn(mockSharedPreferences)
        `when`(mockSharedPreferences.edit()).thenReturn(mockEditor)
        `when`(mockEditor.putString(any(), any())).thenReturn(mockEditor)
    }

    @Test
    fun `getCurrentLanguage should return follow_system by default`() {
        `when`(mockSharedPreferences.getString("selected_language", LanguageManager.LANGUAGE_FOLLOW_SYSTEM))
            .thenReturn(LanguageManager.LANGUAGE_FOLLOW_SYSTEM)

        val result = LanguageManager.getCurrentLanguage(mockContext)

        assertEquals(LanguageManager.LANGUAGE_FOLLOW_SYSTEM, result)
    }

    @Test
    fun `setLanguage should save language preference`() {
        LanguageManager.setLanguage(mockContext, LanguageManager.LANGUAGE_ENGLISH)

        verify(mockEditor).putString("selected_language", LanguageManager.LANGUAGE_ENGLISH)
        verify(mockEditor).apply()
    }

    @Test
    fun `getSystemLanguage should return correct language code`() {
        val systemLanguage = LanguageManager.getSystemLanguage()
        
        assertTrue("System language should be one of supported languages", 
            systemLanguage in listOf(
                LanguageManager.LANGUAGE_CHINESE,
                LanguageManager.LANGUAGE_ENGLISH,
                LanguageManager.LANGUAGE_JAPANESE,
                LanguageManager.LANGUAGE_KOREAN
            ))
    }

    @Test
    fun `getActualLanguage should return system language when follow_system is selected`() {
        `when`(mockSharedPreferences.getString("selected_language", LanguageManager.LANGUAGE_FOLLOW_SYSTEM))
            .thenReturn(LanguageManager.LANGUAGE_FOLLOW_SYSTEM)

        val result = LanguageManager.getActualLanguage(mockContext)
        val systemLanguage = LanguageManager.getSystemLanguage()

        assertEquals(systemLanguage, result)
    }

    @Test
    fun `getActualLanguage should return selected language when not follow_system`() {
        `when`(mockSharedPreferences.getString("selected_language", LanguageManager.LANGUAGE_FOLLOW_SYSTEM))
            .thenReturn(LanguageManager.LANGUAGE_JAPANESE)

        val result = LanguageManager.getActualLanguage(mockContext)

        assertEquals(LanguageManager.LANGUAGE_JAPANESE, result)
    }

    @Test
    fun `isLanguageChanged should return true when language is different`() {
        `when`(mockSharedPreferences.getString("selected_language", LanguageManager.LANGUAGE_FOLLOW_SYSTEM))
            .thenReturn(LanguageManager.LANGUAGE_CHINESE)

        val result = LanguageManager.isLanguageChanged(mockContext, LanguageManager.LANGUAGE_ENGLISH)

        assertTrue(result)
    }

    @Test
    fun `isLanguageChanged should return false when language is same`() {
        `when`(mockSharedPreferences.getString("selected_language", LanguageManager.LANGUAGE_FOLLOW_SYSTEM))
            .thenReturn(LanguageManager.LANGUAGE_ENGLISH)

        val result = LanguageManager.isLanguageChanged(mockContext, LanguageManager.LANGUAGE_ENGLISH)

        assertFalse(result)
    }

    @Test
    fun `supportedLanguages should contain all expected languages`() {
        val languages = LanguageManager.supportedLanguages
        
        assertEquals(5, languages.size)
        
        val languageCodes = languages.map { it.code }
        assertTrue(languageCodes.contains(LanguageManager.LANGUAGE_FOLLOW_SYSTEM))
        assertTrue(languageCodes.contains(LanguageManager.LANGUAGE_CHINESE))
        assertTrue(languageCodes.contains(LanguageManager.LANGUAGE_ENGLISH))
        assertTrue(languageCodes.contains(LanguageManager.LANGUAGE_JAPANESE))
        assertTrue(languageCodes.contains(LanguageManager.LANGUAGE_KOREAN))
    }

    @Test
    fun `getLanguageNativeName should return correct native names`() {
        assertEquals("Follow System", LanguageManager.getLanguageNativeName(LanguageManager.LANGUAGE_FOLLOW_SYSTEM))
        assertEquals("中文", LanguageManager.getLanguageNativeName(LanguageManager.LANGUAGE_CHINESE))
        assertEquals("English", LanguageManager.getLanguageNativeName(LanguageManager.LANGUAGE_ENGLISH))
        assertEquals("日本語", LanguageManager.getLanguageNativeName(LanguageManager.LANGUAGE_JAPANESE))
        assertEquals("한국어", LanguageManager.getLanguageNativeName(LanguageManager.LANGUAGE_KOREAN))
    }
}
