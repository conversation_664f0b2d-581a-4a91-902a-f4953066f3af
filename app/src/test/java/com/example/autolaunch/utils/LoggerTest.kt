package com.example.autolaunch.utils

import android.util.Log
import io.mockk.every
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test

/**
 * Logger 單元測試
 */
class LoggerTest {
    
    @Before
    fun setUp() {
        // Mock Android Log
        mockkStatic(Log::class)
        every { Log.v(any(), any(), any()) } returns 0
        every { Log.d(any(), any(), any()) } returns 0
        every { Log.i(any(), any(), any()) } returns 0
        every { Log.w(any(), any(), any()) } returns 0
        every { Log.e(any(), any(), any()) } returns 0
        every { Log.v(any(), any()) } returns 0
        every { Log.d(any(), any()) } returns 0
        every { Log.i(any(), any()) } returns 0
        every { Log.w(any(), any()) } returns 0
        every { Log.e(any(), any()) } returns 0
        
        // Initialize Logger with test settings
        Logger.initialize(
            debugMode = true,
            minLevel = Logger.Level.VERBOSE,
            systemLog = true,
            memoryLog = true
        )
        
        // Clear any existing logs
        Logger.clearMemoryLogs()
    }
    
    @After
    fun tearDown() {
        unmockkAll()
        Logger.clearMemoryLogs()
    }
    
    @Test
    fun `initialize should set logger configuration`() {
        // When
        Logger.initialize(
            debugMode = false,
            minLevel = Logger.Level.INFO,
            systemLog = false,
            memoryLog = false
        )
        
        // Then
        // Logger should be configured (verified through behavior in other tests)
    }
    
    @Test
    fun `verbose logging should call Android Log v`() {
        // When
        Logger.v("TestTag", "Test message")
        
        // Then
        verify { Log.v("TestTag", match { it.contains("Test message") }) }
    }
    
    @Test
    fun `debug logging should call Android Log d`() {
        // When
        Logger.d("TestTag", "Test message")
        
        // Then
        verify { Log.d("TestTag", match { it.contains("Test message") }) }
    }
    
    @Test
    fun `info logging should call Android Log i`() {
        // When
        Logger.i("TestTag", "Test message")
        
        // Then
        verify { Log.i("TestTag", match { it.contains("Test message") }) }
    }
    
    @Test
    fun `warning logging should call Android Log w`() {
        // When
        Logger.w("TestTag", "Test message")
        
        // Then
        verify { Log.w("TestTag", match { it.contains("Test message") }) }
    }
    
    @Test
    fun `error logging should call Android Log e`() {
        // When
        Logger.e("TestTag", "Test message")
        
        // Then
        verify { Log.e("TestTag", match { it.contains("Test message") }) }
    }
    
    @Test
    fun `logging with throwable should call Android Log with exception`() {
        // Given
        val exception = RuntimeException("Test exception")
        
        // When
        Logger.e("TestTag", "Test message", exception)
        
        // Then
        verify { Log.e("TestTag", match { it.contains("Test message") }, exception) }
    }
    
    @Test
    fun `enter should log method entry in debug mode`() {
        // When
        Logger.enter("TestTag", "testMethod", "param1", "param2")
        
        // Then
        verify { Log.d("TestTag", match { it.contains("→ testMethod(param1, param2)") }) }
    }
    
    @Test
    fun `exit should log method exit in debug mode`() {
        // When
        Logger.exit("TestTag", "testMethod", "result")
        
        // Then
        verify { Log.d("TestTag", match { it.contains("← testMethod -> result") }) }
    }
    
    @Test
    fun `performance should log performance metrics`() {
        // When
        Logger.performance("TestTag", "testOperation", 1500L)
        
        // Then
        verify { Log.i("TestTag", match { it.contains("⏱ testOperation took 1500ms") }) }
    }
    
    @Test
    fun `userAction should log user actions`() {
        // When
        Logger.userAction("TestTag", "button_click", "save button")
        
        // Then
        verify { Log.i("TestTag", match { it.contains("👤 button_click - save button") }) }
    }
    
    @Test
    fun `systemEvent should log system events`() {
        // When
        Logger.systemEvent("TestTag", "service_started", "background service")
        
        // Then
        verify { Log.i("TestTag", match { it.contains("🔧 service_started - background service") }) }
    }
    
    @Test
    fun `network should log network requests`() {
        // When
        Logger.network("TestTag", "GET", "https://example.com", 200)
        
        // Then
        verify { Log.i("TestTag", match { it.contains("🌐 GET https://example.com [200]") }) }
    }
    
    @Test
    fun `database should log database operations`() {
        // When
        Logger.database("TestTag", "INSERT", "schedules", 5)
        
        // Then
        verify { Log.i("TestTag", match { it.contains("💾 INSERT on schedules (5 records)") }) }
    }
    
    @Test
    fun `getMemoryLogs should return logged entries`() {
        // Given
        Logger.i("TestTag", "Test message 1")
        Logger.w("TestTag", "Test message 2")
        Logger.e("TestTag", "Test message 3")
        
        // When
        val logs = Logger.getMemoryLogs()
        
        // Then
        assertEquals(3, logs.size)
        assertTrue(logs.any { it.message == "Test message 1" && it.level == Logger.Level.INFO })
        assertTrue(logs.any { it.message == "Test message 2" && it.level == Logger.Level.WARN })
        assertTrue(logs.any { it.message == "Test message 3" && it.level == Logger.Level.ERROR })
    }
    
    @Test
    fun `getMemoryLogs should filter by level`() {
        // Given
        Logger.d("TestTag", "Debug message")
        Logger.i("TestTag", "Info message")
        Logger.w("TestTag", "Warning message")
        Logger.e("TestTag", "Error message")
        
        // When
        val logs = Logger.getMemoryLogs(level = Logger.Level.WARN)
        
        // Then
        assertEquals(2, logs.size) // Only WARN and ERROR
        assertTrue(logs.all { it.level.priority >= Logger.Level.WARN.priority })
    }
    
    @Test
    fun `getMemoryLogs should filter by tag`() {
        // Given
        Logger.i("Tag1", "Message 1")
        Logger.i("Tag2", "Message 2")
        Logger.i("Tag1", "Message 3")
        
        // When
        val logs = Logger.getMemoryLogs(tag = "Tag1")
        
        // Then
        assertEquals(2, logs.size)
        assertTrue(logs.all { it.tag.contains("Tag1") })
    }
    
    @Test
    fun `getMemoryLogs should respect limit`() {
        // Given
        repeat(10) { i ->
            Logger.i("TestTag", "Message $i")
        }
        
        // When
        val logs = Logger.getMemoryLogs(limit = 5)
        
        // Then
        assertEquals(5, logs.size)
    }
    
    @Test
    fun `clearMemoryLogs should remove all logs`() {
        // Given
        Logger.i("TestTag", "Test message")
        assertEquals(1, Logger.getMemoryLogs().size)
        
        // When
        Logger.clearMemoryLogs()
        
        // Then
        assertEquals(0, Logger.getMemoryLogs().size)
    }
    
    @Test
    fun `exportLogs should return formatted log string`() {
        // Given
        Logger.i("TestTag", "Test message 1")
        Logger.w("TestTag", "Test message 2")
        
        // When
        val exportedLogs = Logger.exportLogs()
        
        // Then
        assertTrue(exportedLogs.contains("Test message 1"))
        assertTrue(exportedLogs.contains("Test message 2"))
        assertTrue(exportedLogs.contains("I/TestTag"))
        assertTrue(exportedLogs.contains("W/TestTag"))
    }
    
    @Test
    fun `getLogStatistics should return correct statistics`() {
        // Given
        Logger.i("TestTag", "Info message")
        Logger.w("TestTag", "Warning message")
        Logger.e("TestTag", "Error message")
        
        // When
        val stats = Logger.getLogStatistics()
        
        // Then
        assertEquals(3, stats.totalLogs)
        assertEquals(3, stats.recentLogs) // All logs are recent
        assertEquals(1, stats.levelCounts[Logger.Level.INFO])
        assertEquals(1, stats.levelCounts[Logger.Level.WARN])
        assertEquals(1, stats.levelCounts[Logger.Level.ERROR])
    }
    
    @Test
    fun `measureTime extension should log performance and return result`() {
        // Given
        val expectedResult = "operation result"
        
        // When
        val result = Logger.measureTime("TestTag", "test operation") {
            Thread.sleep(10) // Small delay
            expectedResult
        }
        
        // Then
        assertEquals(expectedResult, result)
        verify { Log.i("TestTag", match { it.contains("⏱ test operation took") && it.contains("ms") }) }
    }
    
    @Test
    fun `safeExecute extension should log entry and exit`() {
        // Given
        val expectedResult = "operation result"
        
        // When
        val result = Logger.safeExecute("TestTag", "test operation") {
            expectedResult
        }
        
        // Then
        assertEquals(expectedResult, result)
        verify { Log.d("TestTag", match { it.contains("→ test operation") }) }
        verify { Log.d("TestTag", match { it.contains("← test operation") }) }
    }
    
    @Test
    fun `safeExecute extension should handle exceptions and return null`() {
        // When
        val result = Logger.safeExecute("TestTag", "failing operation") {
            throw RuntimeException("Test exception")
        }
        
        // Then
        assertNull(result)
        verify { Log.e("TestTag", match { it.contains("Failed to execute failing operation") }, any()) }
    }
}
