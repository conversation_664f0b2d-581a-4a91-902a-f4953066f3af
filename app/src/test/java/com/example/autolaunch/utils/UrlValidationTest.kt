package com.example.autolaunch.utils

import org.junit.Test
import org.junit.Assert.*
import java.util.regex.Pattern

/**
 * Unit tests for URL validation logic
 */
class UrlValidationTest {

    companion object {
        private val URL_PATTERN = Pattern.compile(
            "^(https?|ftp)://[^\\s/$.?#].[^\\s]*$",
            Pattern.CASE_INSENSITIVE
        )
    }

    private fun isValidUrl(url: String): <PERSON><PERSON>an {
        return try {
            URL_PATTERN.matcher(url).matches()
        } catch (e: Exception) {
            false
        }
    }

    @Test
    fun `test valid HTTP URLs`() {
        val validUrls = listOf(
            "http://www.google.com",
            "http://google.com",
            "http://example.org/path",
            "http://subdomain.example.com",
            "http://***********",
            "http://localhost:8080"
        )

        validUrls.forEach { url ->
            assertTrue("URL should be valid: $url", isValidUrl(url))
        }
    }

    @Test
    fun `test valid HTTPS URLs`() {
        val validUrls = listOf(
            "https://www.google.com",
            "https://google.com",
            "https://example.org/path/to/resource",
            "https://api.example.com/v1/users",
            "https://secure.example.com:443",
            "https://github.com/user/repo"
        )

        validUrls.forEach { url ->
            assertTrue("URL should be valid: $url", isValidUrl(url))
        }
    }

    @Test
    fun `test valid FTP URLs`() {
        val validUrls = listOf(
            "ftp://ftp.example.com",
            "ftp://files.example.org/path",
            "ftp://***********00"
        )

        validUrls.forEach { url ->
            assertTrue("URL should be valid: $url", isValidUrl(url))
        }
    }

    @Test
    fun `test invalid URLs`() {
        val invalidUrls = listOf(
            "",
            "not-a-url",
            "www.google.com", // Missing protocol
            "google.com", // Missing protocol
            "mailto:<EMAIL>", // Wrong protocol
            "file:///path/to/file", // Wrong protocol
            "javascript:alert('test')", // Wrong protocol
            "http:// invalid url", // Space in URL
            "http://.com", // Invalid domain
            "https://.", // Invalid domain
            "https://example .com" // Space in domain
        )

        invalidUrls.forEach { url ->
            assertFalse("URL should be invalid: '$url'", isValidUrl(url))
        }
    }

    @Test
    fun `test URLs with query parameters`() {
        val validUrls = listOf(
            "https://www.google.com/search?q=test",
            "https://example.com/page?param1=value1&param2=value2",
            "http://api.example.com/endpoint?format=json&limit=10"
        )

        validUrls.forEach { url ->
            assertTrue("URL with query params should be valid: $url", isValidUrl(url))
        }
    }

    @Test
    fun `test URLs with fragments`() {
        val validUrls = listOf(
            "https://example.com/page#section1",
            "https://docs.example.com/guide#getting-started",
            "http://example.org/article#conclusion"
        )

        validUrls.forEach { url ->
            assertTrue("URL with fragment should be valid: $url", isValidUrl(url))
        }
    }

    @Test
    fun `test URLs with ports`() {
        val validUrls = listOf(
            "http://localhost:3000",
            "https://example.com:8443",
            "http://***********:8080"
        )

        validUrls.forEach { url ->
            assertTrue("URL with port should be valid: $url", isValidUrl(url))
        }
    }

    @Test
    fun `test case insensitive protocol validation`() {
        val validUrls = listOf(
            "HTTP://www.google.com",
            "HTTPS://www.google.com",
            "FTP://ftp.example.com",
            "Http://example.com",
            "Https://example.com"
        )

        validUrls.forEach { url ->
            assertTrue("Case insensitive URL should be valid: $url", isValidUrl(url))
        }
    }

    @Test
    fun `test edge cases`() {
        // Test some edge cases that should be valid
        val validUrls = listOf(
            "http://example.com:8080",
            "https://sub.domain.example.com"
        )

        validUrls.forEach { url ->
            assertTrue("Edge case URL should be valid: '$url'", isValidUrl(url))
        }
    }
}
