package com.example.autolaunch.utils

import android.content.Context
import io.mockk.mockk
import kotlinx.coroutines.CancellationException
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import java.io.IOException
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import java.sql.SQLException

/**
 * ExceptionHandler 單元測試
 */
class ExceptionHandlerTest {
    
    private lateinit var mockContext: Context
    
    @Before
    fun setUp() {
        mockContext = mockk(relaxed = true)
        
        // Mock SystemLogManager
        mockkObject(SystemLogManager)
        every { SystemLogManager.logSystemError(any(), any(), any(), any()) } just Runs
    }
    
    @After
    fun tearDown() {
        unmockkAll()
    }
    
    @Test
    fun `handleException should return correct type for CancellationException`() {
        // Given
        val exception = CancellationException("Operation cancelled")
        
        // When
        val result = ExceptionHandler.handleException(exception, mockContext)
        
        // Then
        assertEquals(ExceptionHandler.ExceptionType.SYSTEM_ERROR, result.type)
        assertEquals("Operation cancelled", result.message)
        assertFalse(result.shouldRetry)
    }
    
    @Test
    fun `handleException should return correct type for SocketTimeoutException`() {
        // Given
        val exception = SocketTimeoutException("Connection timeout")
        
        // When
        val result = ExceptionHandler.handleException(exception, mockContext)
        
        // Then
        assertEquals(ExceptionHandler.ExceptionType.NETWORK_ERROR, result.type)
        assertEquals("Network timeout", result.message)
        assertTrue(result.shouldRetry)
        assertEquals("網路連線逾時，請檢查網路狀態", result.userMessage)
    }
    
    @Test
    fun `handleException should return correct type for UnknownHostException`() {
        // Given
        val exception = UnknownHostException("Unknown host")
        
        // When
        val result = ExceptionHandler.handleException(exception, mockContext)
        
        // Then
        assertEquals(ExceptionHandler.ExceptionType.NETWORK_ERROR, result.type)
        assertEquals("Network unavailable", result.message)
        assertTrue(result.shouldRetry)
        assertEquals("無法連接到網路，請檢查網路設定", result.userMessage)
    }
    
    @Test
    fun `handleException should return correct type for IOException`() {
        // Given
        val exception = IOException("File not found")
        
        // When
        val result = ExceptionHandler.handleException(exception, mockContext)
        
        // Then
        assertEquals(ExceptionHandler.ExceptionType.IO_ERROR, result.type)
        assertTrue(result.message.contains("IO operation failed"))
        assertFalse(result.shouldRetry)
        assertEquals("檔案操作失敗", result.userMessage)
    }
    
    @Test
    fun `handleException should return correct type for SQLException`() {
        // Given
        val exception = SQLException("Database error")
        
        // When
        val result = ExceptionHandler.handleException(exception, mockContext)
        
        // Then
        assertEquals(ExceptionHandler.ExceptionType.DATABASE_ERROR, result.type)
        assertTrue(result.message.contains("Database operation failed"))
        assertFalse(result.shouldRetry)
        assertEquals("資料庫操作失敗", result.userMessage)
    }
    
    @Test
    fun `handleException should return correct type for SecurityException`() {
        // Given
        val exception = SecurityException("Permission denied")
        
        // When
        val result = ExceptionHandler.handleException(exception, mockContext)
        
        // Then
        assertEquals(ExceptionHandler.ExceptionType.PERMISSION_ERROR, result.type)
        assertTrue(result.message.contains("Permission denied"))
        assertFalse(result.shouldRetry)
        assertEquals("權限不足，請檢查應用程式權限設定", result.userMessage)
    }
    
    @Test
    fun `handleException should return correct type for IllegalArgumentException`() {
        // Given
        val exception = IllegalArgumentException("Invalid argument")
        
        // When
        val result = ExceptionHandler.handleException(exception, mockContext)
        
        // Then
        assertEquals(ExceptionHandler.ExceptionType.VALIDATION_ERROR, result.type)
        assertTrue(result.message.contains("Validation error"))
        assertFalse(result.shouldRetry)
        assertEquals("輸入資料有誤", result.userMessage)
    }
    
    @Test
    fun `handleException should return correct type for unknown exception`() {
        // Given
        val exception = RuntimeException("Unknown error")
        
        // When
        val result = ExceptionHandler.handleException(exception, mockContext)
        
        // Then
        assertEquals(ExceptionHandler.ExceptionType.UNKNOWN_ERROR, result.type)
        assertTrue(result.message.contains("Unknown error"))
        assertFalse(result.shouldRetry)
        assertEquals("發生未知錯誤", result.userMessage)
    }
    
    @Test
    fun `safeExecute should return result when operation succeeds`() {
        // Given
        val expectedResult = "success"
        
        // When
        val result = ExceptionHandler.safeExecute(mockContext) {
            expectedResult
        }
        
        // Then
        assertEquals(expectedResult, result)
    }
    
    @Test
    fun `safeExecute should return null when operation throws exception`() {
        // When
        val result = ExceptionHandler.safeExecute(mockContext) {
            throw RuntimeException("Test exception")
        }
        
        // Then
        assertNull(result)
    }
    
    @Test
    fun `safeExecuteWithDefault should return result when operation succeeds`() {
        // Given
        val expectedResult = "success"
        val defaultValue = "default"
        
        // When
        val result = ExceptionHandler.safeExecuteWithDefault(defaultValue, mockContext) {
            expectedResult
        }
        
        // Then
        assertEquals(expectedResult, result)
    }
    
    @Test
    fun `safeExecuteWithDefault should return default when operation throws exception`() {
        // Given
        val defaultValue = "default"
        
        // When
        val result = ExceptionHandler.safeExecuteWithDefault(defaultValue, mockContext) {
            throw RuntimeException("Test exception")
        }
        
        // Then
        assertEquals(defaultValue, result)
    }
    
    @Test
    fun `safeExecuteWithRetry should retry on retryable exception`() {
        // Given
        var attemptCount = 0
        val maxRetries = 3
        
        // When
        val result = ExceptionHandler.safeExecuteWithRetry(
            maxRetries = maxRetries,
            retryDelayMs = 10, // Short delay for testing
            context = mockContext
        ) {
            attemptCount++
            if (attemptCount < maxRetries) {
                throw SocketTimeoutException("Timeout")
            }
            "success"
        }
        
        // Then
        assertEquals("success", result)
        assertEquals(maxRetries, attemptCount)
    }
    
    @Test
    fun `safeExecuteWithRetry should not retry on non-retryable exception`() {
        // Given
        var attemptCount = 0
        
        // When
        val result = ExceptionHandler.safeExecuteWithRetry(
            maxRetries = 3,
            retryDelayMs = 10,
            context = mockContext
        ) {
            attemptCount++
            throw IllegalArgumentException("Invalid argument")
        }
        
        // Then
        assertNull(result)
        assertEquals(1, attemptCount) // Should only attempt once
    }
    
    @Test
    fun `isRetryable should return true for retryable exceptions`() {
        assertTrue(ExceptionHandler.isRetryable(SocketTimeoutException()))
        assertTrue(ExceptionHandler.isRetryable(UnknownHostException()))
        assertTrue(ExceptionHandler.isRetryable(IOException()))
    }
    
    @Test
    fun `isRetryable should return false for non-retryable exceptions`() {
        assertFalse(ExceptionHandler.isRetryable(IllegalArgumentException()))
        assertFalse(ExceptionHandler.isRetryable(SecurityException()))
        assertFalse(ExceptionHandler.isRetryable(RuntimeException()))
    }
    
    @Test
    fun `isFatalError should return true for fatal errors`() {
        assertTrue(ExceptionHandler.isFatalError(OutOfMemoryError()))
        assertTrue(ExceptionHandler.isFatalError(StackOverflowError()))
    }
    
    @Test
    fun `isFatalError should return false for non-fatal errors`() {
        assertFalse(ExceptionHandler.isFatalError(RuntimeException()))
        assertFalse(ExceptionHandler.isFatalError(IOException()))
    }
    
    @Test
    fun `getUserFriendlyMessage should return appropriate message`() {
        val networkException = SocketTimeoutException()
        val message = ExceptionHandler.getUserFriendlyMessage(networkException)
        assertEquals("網路連線逾時，請檢查網路狀態", message)
    }
    
    @Test
    fun `handleException should log to SystemLogManager when context is provided`() {
        // Given
        val exception = RuntimeException("Test exception")
        
        // When
        ExceptionHandler.handleException(exception, mockContext)
        
        // Then
        verify { SystemLogManager.logSystemError(mockContext, any(), exception, any()) }
    }
}
