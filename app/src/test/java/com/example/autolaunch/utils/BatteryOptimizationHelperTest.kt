package com.example.autolaunch.utils

import android.content.Context
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.Before
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [30])
class BatteryOptimizationHelperTest {

    private lateinit var context: Context

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
    }

    @Test
    fun `isIgnoringBatteryOptimizations should not throw`() {
        assertDoesNotThrow("isIgnoringBatteryOptimizations should not throw exception") {
            BatteryOptimizationHelper.isIgnoringBatteryOptimizations(context)
        }
    }

    @Test
    fun `createBatteryOptimizationIntent should not throw`() {
        assertDoesNotThrow("createBatteryOptimizationIntent should not throw exception") {
            BatteryOptimizationHelper.createBatteryOptimizationIntent(context)
        }
    }

    @Test
    fun `requestIgnoreBatteryOptimizations should not throw`() {
        assertDoesNotThrow("requestIgnoreBatteryOptimizations should not throw exception") {
            BatteryOptimizationHelper.requestIgnoreBatteryOptimizations(context)
        }
    }
}
