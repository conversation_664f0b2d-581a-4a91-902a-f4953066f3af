package com.example.autolaunch.utils

import com.example.autolaunch.model.*
import java.io.File
import java.io.FileWriter

/**
 * 備份功能測試工具類
 * 提供測試用的輔助方法和數據
 */
object BackupTestUtils {
    
    /**
     * 創建測試排程數據
     */
    fun createTestSchedules(): List<Schedule> {
        return listOf(
            // APP 排程 - 每日
            Schedule(
                id = 1,
                scheduleType = ScheduleType.APP.value,
                appName = "Chrome",
                packageName = "com.android.chrome",
                taskName = "每日瀏覽器",
                hour = 9,
                minute = 0,
                repeatMode = RepeatMode.DAILY.value,
                isEnabled = true,
                createdTime = System.currentTimeMillis() - 86400000, // 昨天創建
                updatedTime = System.currentTimeMillis() - 3600000   // 1小時前更新
            ),
            
            // URL 排程 - 每週
            Schedule(
                id = 2,
                scheduleType = ScheduleType.URL.value,
                url = "https://www.github.com",
                urlTitle = "GitHub",
                taskName = "檢查 GitHub",
                hour = 18,
                minute = 30,
                repeatMode = RepeatMode.WEEKLY.value,
                daysOfWeek = 31, // 週一到週五 (1+2+4+8+16)
                isEnabled = true,
                createdTime = System.currentTimeMillis() - 172800000, // 2天前創建
                updatedTime = System.currentTimeMillis() - 7200000    // 2小時前更新
            ),
            
            // APP 排程 - 單次執行
            Schedule(
                id = 3,
                scheduleType = ScheduleType.APP.value,
                appName = "設定",
                packageName = "com.android.settings",
                taskName = "檢查設定",
                hour = 20,
                minute = 0,
                repeatMode = RepeatMode.ONCE.value,
                singleExecuteDate = System.currentTimeMillis() + 86400000, // 明天執行
                isEnabled = false, // 測試禁用狀態
                createdTime = System.currentTimeMillis() - 259200000, // 3天前創建
                updatedTime = System.currentTimeMillis() - 10800000   // 3小時前更新
            ),
            
            // URL 排程 - 每日（測試不同URL）
            Schedule(
                id = 4,
                scheduleType = ScheduleType.URL.value,
                url = "https://stackoverflow.com",
                urlTitle = "Stack Overflow",
                taskName = "技術問答",
                hour = 12,
                minute = 15,
                repeatMode = RepeatMode.DAILY.value,
                isEnabled = true,
                createdTime = System.currentTimeMillis() - 345600000, // 4天前創建
                updatedTime = System.currentTimeMillis() - 14400000   // 4小時前更新
            ),
            
            // APP 排程 - 每週（週末）
            Schedule(
                id = 5,
                scheduleType = ScheduleType.APP.value,
                appName = "相機",
                packageName = "com.android.camera2",
                taskName = "週末拍照",
                hour = 10,
                minute = 30,
                repeatMode = RepeatMode.WEEKLY.value,
                daysOfWeek = 96, // 週六和週日 (32+64)
                isEnabled = true,
                createdTime = System.currentTimeMillis() - 432000000, // 5天前創建
                updatedTime = System.currentTimeMillis() - 18000000   // 5小時前更新
            )
        )
    }
    
    /**
     * 創建無效的測試排程數據
     */
    fun createInvalidTestSchedules(): List<Schedule> {
        return listOf(
            // 無效 APP 排程 - 缺少包名
            Schedule(
                id = 101,
                scheduleType = ScheduleType.APP.value,
                appName = "無效應用",
                packageName = null, // 無效：缺少包名
                hour = 9,
                minute = 0,
                repeatMode = RepeatMode.DAILY.value,
                isEnabled = true
            ),
            
            // 無效 URL 排程 - 無效 URL
            Schedule(
                id = 102,
                scheduleType = ScheduleType.URL.value,
                url = "invalid-url", // 無效：不是有效的URL
                hour = 10,
                minute = 0,
                repeatMode = RepeatMode.DAILY.value,
                isEnabled = true
            ),
            
            // 無效時間 - 小時超出範圍
            Schedule(
                id = 103,
                scheduleType = ScheduleType.APP.value,
                appName = "測試應用",
                packageName = "com.test.app",
                hour = 25, // 無效：小時超出範圍
                minute = 0,
                repeatMode = RepeatMode.DAILY.value,
                isEnabled = true
            ),
            
            // 無效時間 - 分鐘超出範圍
            Schedule(
                id = 104,
                scheduleType = ScheduleType.APP.value,
                appName = "測試應用",
                packageName = "com.test.app",
                hour = 9,
                minute = 60, // 無效：分鐘超出範圍
                repeatMode = RepeatMode.DAILY.value,
                isEnabled = true
            )
        )
    }
    
    /**
     * 創建測試備份數據
     */
    fun createTestBackupData(schedules: List<Schedule> = createTestSchedules()): BackupData {
        val deviceInfo = DeviceInfo(
            appVersion = "1.0.0-test",
            androidVersion = "14",
            deviceModel = "Test Device",
            deviceManufacturer = "Test Manufacturer"
        )
        
        val scheduleBackups = schedules.map { ScheduleBackup.fromSchedule(it) }
        
        return BackupData(
            version = "1.0",
            timestamp = System.currentTimeMillis(),
            deviceInfo = deviceInfo,
            schedules = scheduleBackups
        )
    }
    
    /**
     * 創建測試備份JSON字符串
     */
    fun createTestBackupJson(schedules: List<Schedule> = createTestSchedules()): String {
        val backupData = createTestBackupData(schedules)
        val gson = com.google.gson.GsonBuilder().setPrettyPrinting().create()
        return gson.toJson(backupData)
    }
    
    /**
     * 創建無效的備份JSON字符串
     */
    fun createInvalidBackupJson(): String {
        return """
        {
            "invalid": "json",
            "format": true,
            "missing": "required_fields"
        }
        """.trimIndent()
    }
    
    /**
     * 創建損壞的備份JSON字符串
     */
    fun createCorruptedBackupJson(): String {
        return """
        {
            "version": "1.0",
            "timestamp": "invalid_timestamp",
            "device_info": null,
            "schedules": [
                {
                    "invalid_schedule": true
                }
            ],
            "total_schedules": "not_a_number"
        }
        """.trimIndent()
    }
    
    /**
     * 創建測試備份文件
     */
    fun createTestBackupFile(
        directory: File,
        fileName: String = "test_backup_${System.currentTimeMillis()}.json",
        schedules: List<Schedule> = createTestSchedules()
    ): File {
        val file = File(directory, fileName)
        val json = createTestBackupJson(schedules)
        
        FileWriter(file).use { writer ->
            writer.write(json)
        }
        
        return file
    }
    
    /**
     * 驗證備份數據完整性
     */
    fun validateBackupData(backupData: BackupData, expectedScheduleCount: Int): List<String> {
        val errors = mutableListOf<String>()
        
        // 檢查基本字段
        if (backupData.version.isBlank()) {
            errors.add("版本號不能為空")
        }
        
        if (backupData.timestamp <= 0) {
            errors.add("時間戳必須大於0")
        }
        
        if (backupData.schedules.size != expectedScheduleCount) {
            errors.add("排程數量不匹配：期望 $expectedScheduleCount，實際 ${backupData.schedules.size}")
        }
        
        if (backupData.totalSchedules != backupData.schedules.size) {
            errors.add("總排程數與實際排程數不匹配")
        }
        
        // 檢查設備信息
        val deviceInfo = backupData.deviceInfo
        if (deviceInfo.appVersion.isBlank()) {
            errors.add("應用版本不能為空")
        }
        
        if (deviceInfo.androidVersion.isBlank()) {
            errors.add("Android版本不能為空")
        }
        
        if (deviceInfo.deviceModel.isBlank()) {
            errors.add("設備型號不能為空")
        }
        
        // 檢查每個排程
        backupData.schedules.forEachIndexed { index, schedule ->
            if (!schedule.isValid()) {
                errors.add("排程 $index 無效")
            }
        }
        
        return errors
    }
    
    /**
     * 比較兩個排程是否相等（忽略ID和時間戳）
     */
    fun compareSchedulesIgnoringIdAndTime(schedule1: Schedule, schedule2: Schedule): Boolean {
        return schedule1.scheduleType == schedule2.scheduleType &&
                schedule1.appName == schedule2.appName &&
                schedule1.packageName == schedule2.packageName &&
                schedule1.taskName == schedule2.taskName &&
                schedule1.url == schedule2.url &&
                schedule1.urlTitle == schedule2.urlTitle &&
                schedule1.hour == schedule2.hour &&
                schedule1.minute == schedule2.minute &&
                schedule1.repeatMode == schedule2.repeatMode &&
                schedule1.daysOfWeek == schedule2.daysOfWeek &&
                schedule1.singleExecuteDate == schedule2.singleExecuteDate &&
                schedule1.isEnabled == schedule2.isEnabled
    }
    
    /**
     * 清理測試文件
     */
    fun cleanupTestFiles(directory: File) {
        directory.listFiles()?.forEach { file ->
            if (file.name.contains("test") || file.name.contains("Test")) {
                file.delete()
            }
        }
    }
    
    /**
     * 生成測試文件名
     */
    fun generateTestFileName(prefix: String = "test_backup"): String {
        val timestamp = System.currentTimeMillis()
        return "${prefix}_${timestamp}.json"
    }
    
    /**
     * 驗證文件是否為有效的JSON格式
     */
    fun isValidJsonFile(file: File): Boolean {
        return try {
            val content = file.readText()
            val gson = com.google.gson.Gson()
            gson.fromJson(content, Any::class.java)
            true
        } catch (e: Exception) {
            false
        }
    }
}
