package com.example.autolaunch.model

import android.content.Context
import androidx.room.Room
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.example.autolaunch.utils.MainCoroutineRule
import com.example.autolaunch.utils.assertDoesNotThrow
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import org.junit.After
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.Assert.*

/**
 * ScheduleDao 的單元測試
 * 測試數據庫訪問對象的 CRUD 操作
 */
@ExperimentalCoroutinesApi
@RunWith(AndroidJUnit4::class)
class ScheduleDaoTest {

    @get:Rule
    val mainCoroutineRule = MainCoroutineRule()

    private lateinit var database: AppDatabase
    private lateinit var scheduleDao: ScheduleDao

    @Before
    fun setUp() {
        val context = ApplicationProvider.getApplicationContext<Context>()
        
        database = Room.inMemoryDatabaseBuilder(context, AppDatabase::class.java)
            .allowMainThreadQueries()
            .build()
        
        scheduleDao = database.scheduleDao()
    }

    @After
    fun tearDown() {
        database.close()
    }

    @Test
    fun `insert and get schedule`() = runTest {
        val schedule = Schedule(
            scheduleType = ScheduleType.APP.value,
            appName = "Test App",
            packageName = "com.test.app",
            hour = 9,
            minute = 30,
            repeatMode = RepeatMode.DAILY.value,
            isEnabled = true
        )

        val scheduleId = scheduleDao.insert(schedule)
        assertTrue("insert should return a valid ID", scheduleId > 0)

        val retrievedSchedule = scheduleDao.getScheduleById(scheduleId).first()
        assertNotNull("Should find the inserted schedule", retrievedSchedule)
        assertEquals("App name should match", "Test App", retrievedSchedule?.appName)
    }

    @Test
    fun `update schedule`() = runTest {
        val schedule = Schedule(appName = "Original App", packageName = "com.original.app", hour = 9, minute = 30, repeatMode = RepeatMode.DAILY.value)
        val scheduleId = scheduleDao.insert(schedule)
        
        val updatedSchedule = schedule.copy(id = scheduleId, appName = "Updated App", hour = 10)
        val updateCount = scheduleDao.update(updatedSchedule)
        assertEquals("Should update one record", 1, updateCount)
        
        val retrievedSchedule = scheduleDao.getScheduleById(scheduleId).first()
        assertEquals("App name should be updated", "Updated App", retrievedSchedule?.appName)
        assertEquals("Hour should be updated", 10, retrievedSchedule?.hour)
    }

    @Test
    fun `delete schedule by id`() = runTest {
        val schedule = Schedule(appName = "To Delete App", packageName = "com.delete.app", hour = 9, minute = 0, repeatMode = RepeatMode.ONCE.value)
        val scheduleId = scheduleDao.insert(schedule)
        
        val deleteCount = scheduleDao.deleteById(scheduleId)
        assertEquals("Should delete one record", 1, deleteCount)
        
        val retrievedSchedule = scheduleDao.getScheduleById(scheduleId).first()
        assertNull("Schedule should be deleted", retrievedSchedule)
    }

    @Test
    fun `getAllSchedules retrieves all schedules`() = runTest {
        scheduleDao.insert(Schedule(appName = "App 1", packageName = "com.app1", hour = 9, minute = 0, repeatMode = RepeatMode.DAILY.value))
        scheduleDao.insert(Schedule(url = "https://a.com", urlTitle = "A", scheduleType = ScheduleType.URL.value, hour = 10, minute = 0, repeatMode = RepeatMode.ONCE.value))
        
        val allSchedules = scheduleDao.getAllSchedules().first()
        assertEquals("Should retrieve 2 schedules", 2, allSchedules.size)
    }

    @Test
    fun `getEnabledSchedules retrieves only enabled schedules`() = runTest {
        scheduleDao.insert(Schedule(appName = "Enabled", packageName = "com.enabled", isEnabled = true, hour = 9, minute = 0, repeatMode = RepeatMode.DAILY.value))
        scheduleDao.insert(Schedule(appName = "Disabled", packageName = "com.disabled", isEnabled = false, hour = 10, minute = 0, repeatMode = RepeatMode.ONCE.value))
        
        val enabledSchedules = scheduleDao.getEnabledSchedules().first()
        assertEquals("Should retrieve 1 enabled schedule", 1, enabledSchedules.size)
        assertTrue("The retrieved schedule should be enabled", enabledSchedules.all { it.isEnabled })
    }

    @Test
    fun `getAppSchedules and getUrlSchedules filter by type`() = runTest {
        scheduleDao.insert(Schedule(appName = "Test App", packageName = "com.test.app", scheduleType = ScheduleType.APP.value, hour = 9, minute = 0, repeatMode = RepeatMode.DAILY.value))
        scheduleDao.insert(Schedule(url = "https://example.com", urlTitle = "Example", scheduleType = ScheduleType.URL.value, hour = 10, minute = 0, repeatMode = RepeatMode.ONCE.value))
        
        val appSchedules = scheduleDao.getAppSchedules().first()
        val urlSchedules = scheduleDao.getUrlSchedules().first()
        
        assertEquals("Should be 1 app schedule", 1, appSchedules.size)
        assertEquals("Should be 1 url schedule", 1, urlSchedules.size)
        assertTrue("All app schedules should be of APP type", appSchedules.all { it.isAppSchedule() })
        assertTrue("All url schedules should be of URL type", urlSchedules.all { it.isUrlSchedule() })
    }

    @Test
    fun `insertAll should perform batch insert`() = runTest {
        val schedules = listOf(
            Schedule(appName = "Batch 1", packageName = "com.b1", hour = 9, minute = 0, repeatMode = RepeatMode.DAILY.value),
            Schedule(appName = "Batch 2", packageName = "com.b2", hour = 10, minute = 0, repeatMode = RepeatMode.ONCE.value)
        )
        
        assertDoesNotThrow("Batch insert should not throw an exception") {
            scheduleDao.insertAll(schedules)
        }
        
        val allSchedules = scheduleDao.getAllSchedules().first()
        assertTrue("Should contain at least the batch inserted schedules", allSchedules.size >= 2)
    }
}
