package com.example.autolaunch.model

import org.junit.Test
import org.junit.Assert.*
import android.content.Context
import androidx.test.core.app.ApplicationProvider

/**
 * 測試排程顯示邏輯，特別是星期顯示的省略功能
 */
class ScheduleDisplayTest {

    @Test
    fun testDaysOfWeekDisplayName_singleDay() {
        val schedule = Schedule(
            id = 1,
            taskName = "測試",
            scheduleType = ScheduleType.APP.value,
            hour = 8,
            minute = 0,
            repeatMode = RepeatMode.WEEKLY.value,
            daysOfWeek = 1 // 只有週一 (二進位: 0000001)
        )

        assertEquals("週一", schedule.getDaysOfWeekDisplayName())
    }

    @Test
    fun testDaysOfWeekDisplayName_twoDays() {
        val schedule = Schedule(
            id = 1,
            taskName = "測試",
            scheduleType = ScheduleType.APP.value,
            hour = 8,
            minute = 0,
            repeatMode = RepeatMode.WEEKLY.value,
            daysOfWeek = 5 // 週一、週三 (二進位: 0000101)
        )

        assertEquals("週一、週三", schedule.getDaysOfWeekDisplayName())
    }

    @Test
    fun testDaysOfWeekDisplayName_moreThanTwoDays_shouldShowEllipsis() {
        val schedule = Schedule(
            id = 1,
            taskName = "測試",
            scheduleType = ScheduleType.APP.value,
            hour = 8,
            minute = 0,
            repeatMode = RepeatMode.WEEKLY.value,
            daysOfWeek = 107 // 週一、週三、週四、週六、週日 (二進位: 1101011)
        )

        val result = schedule.getDaysOfWeekDisplayName()
        assertTrue("應該包含省略號", result.endsWith("..."))
        assertTrue("應該只顯示一個星期", result.startsWith("週"))
        assertTrue("長度應該較短", result.length <= 6) // 例如 "週一..."
    }

    @Test
    fun testDaysOfWeekDisplayName_allDays() {
        val schedule = Schedule(
            id = 1,
            taskName = "測試",
            scheduleType = ScheduleType.APP.value,
            hour = 8,
            minute = 0,
            repeatMode = RepeatMode.WEEKLY.value,
            daysOfWeek = 127 // 全部七天 (二進位: 1111111)
        )

        assertEquals("每日", schedule.getDaysOfWeekDisplayName())
    }

    @Test
    fun testDaysOfWeekDisplayName_weekdays() {
        val schedule = Schedule(
            id = 1,
            taskName = "測試",
            scheduleType = ScheduleType.APP.value,
            hour = 8,
            minute = 0,
            repeatMode = RepeatMode.WEEKLY.value,
            daysOfWeek = 31 // 週一到週五 (二進位: 0011111)
        )

        assertEquals("工作日", schedule.getDaysOfWeekDisplayName())
    }

    @Test
    fun testDaysOfWeekDisplayName_weekend() {
        val schedule = Schedule(
            id = 1,
            taskName = "測試",
            scheduleType = ScheduleType.APP.value,
            hour = 8,
            minute = 0,
            repeatMode = RepeatMode.WEEKLY.value,
            daysOfWeek = 96 // 週六、週日 (二進位: 1100000)
        )

        assertEquals("週末", schedule.getDaysOfWeekDisplayName())
    }

    @Test
    fun testDaysOfWeekDisplayName_nonWeekly() {
        val schedule = Schedule(
            id = 1,
            taskName = "測試",
            scheduleType = ScheduleType.APP.value,
            hour = 8,
            minute = 0,
            repeatMode = RepeatMode.DAILY.value,
            daysOfWeek = 127
        )

        assertEquals("", schedule.getDaysOfWeekDisplayName())
    }

    @Test
    fun testGetDisplayTime_futureTime() {
        val futureTime = System.currentTimeMillis() + 1000 * 60 * 60 * 24
        val schedule = Schedule(
            id = 1,
            lastRunTime = System.currentTimeMillis(),
            nextRunTime = futureTime
        )
        val display = ScheduleDisplay(schedule)
        val context = ApplicationProvider.getApplicationContext<Context>()
        val displayTime = display.getDisplayTime(context)
        assertTrue(displayTime.contains("預計"))
    }

    @Test
    fun testGetDisplayTime_pastTime() {
        val pastTime = System.currentTimeMillis() - 1000 * 60 * 60 * 24
        val schedule = Schedule(
            id = 1,
            lastRunTime = pastTime,
            nextRunTime = 0
        )
        val display = ScheduleDisplay(schedule)
        val context = ApplicationProvider.getApplicationContext<Context>()
        val displayTime = display.getDisplayTime(context)
        assertTrue(displayTime.contains("上次"))
    }

    @Test
    fun testGetDisplayTime_noRunTime() {
        val schedule = Schedule(id = 1, lastRunTime = 0, nextRunTime = 0)
        val display = ScheduleDisplay(schedule)
        val context = ApplicationProvider.getApplicationContext<Context>()
        assertEquals("尚未執行", display.getDisplayTime(context))
    }

    @Test
    fun testGetStatus_enabled() {
        val schedule = Schedule(id = 1, isEnabled = true)
        val display = ScheduleDisplay(schedule)
        val context = ApplicationProvider.getApplicationContext<Context>()
        assertEquals("已啟用", display.getStatus(context))
    }

    @Test
    fun testGetStatus_disabled() {
        val schedule = Schedule(id = 1, isEnabled = false)
        val display = ScheduleDisplay(schedule)
        val context = ApplicationProvider.getApplicationContext<Context>()
        assertEquals("已停用", display.getStatus(context))
    }

    @Test
    fun testGetRecurringText_daily() {
        val schedule = Schedule(id = 1, recurringDays = "1,2,3,4,5,6,7")
        val display = ScheduleDisplay(schedule)
        val context = ApplicationProvider.getApplicationContext<Context>()
        assertEquals("每天", display.getRecurringText(context))
    }

    @Test
    fun testGetRecurringText_once() {
        val schedule = Schedule(id = 1, recurringDays = "")
        val display = ScheduleDisplay(schedule)
        val context = ApplicationProvider.getApplicationContext<Context>()
        assertEquals("單次", display.getRecurringText(context))
    }
}
