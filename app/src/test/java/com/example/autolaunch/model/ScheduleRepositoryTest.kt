package com.example.autolaunch.model

import android.content.Context
import androidx.room.Room
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.example.autolaunch.utils.MainCoroutineRule
import com.example.autolaunch.utils.assertDoesNotThrow
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import org.junit.After
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.Assert.*

/**
 * ScheduleRepository 的單元測試
 * 測試排程倉庫的業務邏輯
 */
@ExperimentalCoroutinesApi
@RunWith(AndroidJUnit4::class)
class ScheduleRepositoryTest {

    @get:Rule
    val mainCoroutineRule = MainCoroutineRule()

    private lateinit var database: AppDatabase
    private lateinit var scheduleDao: ScheduleDao
    private lateinit var repository: ScheduleRepository
    private lateinit var context: Context

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
        database = Room.inMemoryDatabaseBuilder(context, AppDatabase::class.java)
            .allowMainThreadQueries()
            .build()
        scheduleDao = database.scheduleDao()
        repository = ScheduleRepository(context)
    }

    @After
    fun tearDown() {
        database.close()
    }

    @Test
    fun `insertSchedule should not throw exception`() = runTest {
        val schedule = Schedule(
            scheduleType = ScheduleType.APP.value,
            appName = "Test App",
            packageName = "com.test.app",
            hour = 9,
            minute = 30,
            repeatMode = RepeatMode.DAILY.value,
            isEnabled = true
        )
        assertDoesNotThrow("Inserting a valid schedule should not throw") {
            repository.insertSchedule(schedule)
        }
    }

    @Test
    fun `updateSchedule should not throw exception`() = runTest {
        val schedule = Schedule(
            id = 1,
            scheduleType = ScheduleType.APP.value,
            appName = "Test App",
            packageName = "com.test.app",
            hour = 9,
            minute = 30,
            repeatMode = RepeatMode.DAILY.value,
            isEnabled = true
        )
        scheduleDao.insert(schedule)
        val updatedSchedule = schedule.copy(id = 1, appName = "Updated App")
        assertDoesNotThrow("Updating a schedule should not throw") {
            repository.updateSchedule(updatedSchedule)
        }
    }

    @Test
    fun `deleteScheduleById should not throw exception`() = runTest {
        val schedule = Schedule(
            id = 1,
            scheduleType = ScheduleType.APP.value,
            appName = "Test App",
            packageName = "com.test.app",
            hour = 9,
            minute = 30,
            repeatMode = RepeatMode.DAILY.value,
            isEnabled = true
        )
        scheduleDao.insert(schedule)
        assertDoesNotThrow("Deleting a schedule should not throw") {
            repository.deleteScheduleById(schedule.id)
        }
    }

    @Test
    fun `getAllSchedules should return a flow of lists`() = runTest {
        assertDoesNotThrow("Getting all schedules should not throw") {
            val schedulesFlow = repository.getAllSchedules()
            assertNotNull(schedulesFlow)
            schedulesFlow.first()
        }
    }

    @Test
    fun `getScheduleById should return a flow of schedule`() = runTest {
        val scheduleId = 123L
        assertDoesNotThrow("Getting schedule by ID should not throw") {
            val scheduleFlow = repository.getScheduleById(scheduleId)
            assertNotNull(scheduleFlow)
            scheduleFlow.first()
        }
    }

    @Test
    fun `getEnabledSchedules should return a flow of enabled schedules`() = runTest {
        assertDoesNotThrow("Getting enabled schedules should not throw") {
            val enabledSchedulesFlow = repository.getEnabledSchedules()
            assertNotNull(enabledSchedulesFlow)
            enabledSchedulesFlow.first()
        }
    }

    @Test
    fun `test schedule change detection logic`() = runTest {
        // 测试时间变更检测
        val oldSchedule = Schedule(
            id = 1L,
            hour = 9,
            minute = 0,
            repeatMode = RepeatMode.DAILY.value,
            taskName = "测试排程"
        )

        val newScheduleTimeChanged = oldSchedule.copy(
            hour = 10,
            minute = 30
        )

        // 验证时间变更能被检测到
        assertTrue("时间变更应该被检测到",
            oldSchedule.hour != newScheduleTimeChanged.hour ||
            oldSchedule.minute != newScheduleTimeChanged.minute)

        // 测试任务名称变更检测
        val newScheduleNameChanged = oldSchedule.copy(
            taskName = "新标题"
        )

        assertTrue("任务名称变更应该被检测到",
            oldSchedule.taskName != newScheduleNameChanged.taskName)

        // 测试启用状态变更检测
        val newScheduleStatusChanged = oldSchedule.copy(
            isEnabled = false
        )

        assertTrue("启用状态变更应该被检测到",
            oldSchedule.isEnabled != newScheduleStatusChanged.isEnabled)

        // 测试重复模式变更检测
        val newScheduleRepeatChanged = oldSchedule.copy(
            repeatMode = RepeatMode.WEEKLY.value
        )

        assertTrue("重复模式变更应该被检测到",
            oldSchedule.repeatMode != newScheduleRepeatChanged.repeatMode)
    }
}
