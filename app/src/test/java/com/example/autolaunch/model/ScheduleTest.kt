package com.example.autolaunch.model

import org.junit.Test
import org.junit.Assert.*

/**
 * Unit tests for Schedule model with URL support
 */
class ScheduleTest {

    @Test
    fun `test APP schedule creation`() {
        val schedule = Schedule(
            id = 1,
            scheduleType = ScheduleType.APP.value,
            appName = "Test App",
            packageName = "com.test.app",
            hour = 9,
            minute = 30,
            repeatMode = RepeatMode.DAILY.value
        )

        assertTrue(schedule.isAppSchedule())
        assertFalse(schedule.isUrlSchedule())
        assertEquals(ScheduleType.APP, schedule.getScheduleTypeEnum())
        assertEquals("Test App", schedule.getDisplayName())
        assertTrue(schedule.isValid())
    }

    @Test
    fun `test URL schedule creation`() {
        val schedule = Schedule(
            id = 1,
            scheduleType = ScheduleType.URL.value,
            url = "https://www.google.com",
            urlTitle = "Google",
            hour = 9,
            minute = 30,
            repeatMode = RepeatMode.DAILY.value
        )

        assertFalse(schedule.isAppSchedule())
        assertTrue(schedule.isUrlSchedule())
        assertEquals(ScheduleType.URL, schedule.getScheduleTypeEnum())
        assertEquals("Google", schedule.getDisplayName())
        assertTrue(schedule.isValid())
    }

    @Test
    fun `test URL schedule without title uses URL as display name`() {
        val schedule = Schedule(
            id = 1,
            scheduleType = ScheduleType.URL.value,
            url = "https://www.example.com",
            hour = 9,
            minute = 30,
            repeatMode = RepeatMode.DAILY.value
        )

        assertEquals("https://www.example.com", schedule.getDisplayName())
    }

    @Test
    fun `test custom task name takes priority in display name`() {
        val schedule = Schedule(
            id = 1,
            scheduleType = ScheduleType.URL.value,
            url = "https://www.google.com",
            urlTitle = "Google",
            taskName = "Morning Browse",
            hour = 9,
            minute = 30,
            repeatMode = RepeatMode.DAILY.value
        )

        assertEquals("Morning Browse", schedule.getDisplayName())
    }

    @Test
    fun `test APP schedule validation - valid`() {
        val schedule = Schedule(
            id = 1,
            scheduleType = ScheduleType.APP.value,
            appName = "Test App",
            packageName = "com.test.app",
            hour = 9,
            minute = 30,
            repeatMode = RepeatMode.DAILY.value
        )

        assertTrue(schedule.isValid())
    }

    @Test
    fun `test APP schedule validation - invalid missing package name`() {
        val schedule = Schedule(
            id = 1,
            scheduleType = ScheduleType.APP.value,
            appName = "Test App",
            packageName = null,
            hour = 9,
            minute = 30,
            repeatMode = RepeatMode.DAILY.value
        )

        assertFalse(schedule.isValid())
    }

    @Test
    fun `test URL schedule validation - valid`() {
        val schedule = Schedule(
            id = 1,
            scheduleType = ScheduleType.URL.value,
            url = "https://www.google.com",
            hour = 9,
            minute = 30,
            repeatMode = RepeatMode.DAILY.value
        )

        assertTrue(schedule.isValid())
    }

    @Test
    fun `test URL schedule validation - invalid URL format`() {
        val schedule = Schedule(
            id = 1,
            scheduleType = ScheduleType.URL.value,
            url = "not-a-valid-url",
            hour = 9,
            minute = 30,
            repeatMode = RepeatMode.DAILY.value
        )

        assertFalse(schedule.isValid())
    }

    @Test
    fun `test URL schedule validation - missing URL`() {
        val schedule = Schedule(
            id = 1,
            scheduleType = ScheduleType.URL.value,
            url = null,
            hour = 9,
            minute = 30,
            repeatMode = RepeatMode.DAILY.value
        )

        assertFalse(schedule.isValid())
    }

    @Test
    fun `test schedule type enum conversion`() {
        assertEquals(ScheduleType.APP, ScheduleType.fromValue(0))
        assertEquals(ScheduleType.URL, ScheduleType.fromValue(1))
        assertEquals(ScheduleType.APP, ScheduleType.fromValue(999)) // Default fallback
    }

    @Test
    fun `test schedule copy with URL fields`() {
        val original = Schedule(
            id = 1,
            scheduleType = ScheduleType.URL.value,
            url = "https://www.google.com",
            urlTitle = "Google",
            hour = 9,
            minute = 30,
            repeatMode = RepeatMode.DAILY.value
        )

        val copied = original.copy(
            url = "https://www.example.com",
            urlTitle = "Example"
        )

        assertEquals("https://www.example.com", copied.url)
        assertEquals("Example", copied.urlTitle)
        assertEquals(original.hour, copied.hour)
        assertEquals(original.minute, copied.minute)
    }
}
