package com.example.autolaunch.service

import android.app.NotificationManager
import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.example.autolaunch.utils.assertDoesNotThrow
import io.mockk.MockKAnnotations
import io.mockk.impl.annotations.MockK
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.Before
import org.junit.Assert.*
import org.robolectric.annotation.Config
import org.robolectric.Robolectric
import org.robolectric.Shadows.shadowOf

/**
 * ScheduleService 的單元測試
 * 測試前台服務的生命週期和核心功能
 */
@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.O])
class ScheduleServiceTest {

    private lateinit var context: Context
    private lateinit var service: ScheduleService
    
    @MockK
    private lateinit var mockNotificationManager: NotificationManager

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        context = ApplicationProvider.getApplicationContext()
        service = Robolectric.buildService(ScheduleService::class.java).create().get()
    }

    @Test
    fun `service should be created and onBind should return null`() {
        assertNotNull(service)
        val binder = service.onBind(Intent())
        assertNull("onBind should return null for a started service", binder)
    }

    @Test
    fun `onStartCommand with START_SERVICE should return START_STICKY`() {
        val intent = Intent(context, ScheduleService::class.java).apply {
            action = ScheduleService.ACTION_START_SERVICE
        }
        val result = service.onStartCommand(intent, 0, 1)
        assertEquals(android.app.Service.START_STICKY, result)
    }

    @Test
    fun `onStartCommand with STOP_SERVICE should return START_NOT_STICKY`() {
        val intent = Intent(context, ScheduleService::class.java).apply {
            action = ScheduleService.ACTION_STOP_SERVICE
        }
        val result = service.onStartCommand(intent, 0, 1)
        assertEquals(android.app.Service.START_NOT_STICKY, result)
    }

    @Test
    fun `onStartCommand with null intent should return START_STICKY`() {
        val result = service.onStartCommand(null, 0, 1)
        assertEquals(android.app.Service.START_STICKY, result)
    }

    @Test
    fun `startService static method should not throw`() {
        assertDoesNotThrow("Calling static startService should not throw") {
            ScheduleService.startService(context)
        }
    }

    @Test
    fun `stopService static method should not throw`() {
        assertDoesNotThrow("Calling static stopService should not throw") {
            ScheduleService.stopService(context)
        }
    }

    @Test
    fun `service constants should be correct`() {
        assertEquals("com.example.autolaunch.action.START_SERVICE", ScheduleService.ACTION_START_SERVICE)
        assertEquals("com.example.autolaunch.action.STOP_SERVICE", ScheduleService.ACTION_STOP_SERVICE)
    }

    @Test
    @Config(sdk = [Build.VERSION_CODES.O])
    fun `service should create notification channel on start`() {
        val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        val intent = Intent(context, ScheduleService::class.java).apply {
            action = ScheduleService.ACTION_START_SERVICE
        }
        service.onStartCommand(intent, 0, 1)
        
        val channel = notificationManager.getNotificationChannel("schedule_service_channel")
        assertNotNull("Notification channel should be created on API 26+", channel)
    }
}
