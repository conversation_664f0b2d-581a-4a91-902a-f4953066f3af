package com.example.autolaunch

import android.content.Context
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.example.autolaunch.model.*
import com.example.autolaunch.utils.*
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.flow.first
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import java.io.File

/**
 * 備份功能綜合測試
 * 驗證整個備份與匯入系統的完整性和正確性
 */
@RunWith(AndroidJUnit4::class)
class BackupFunctionalityComprehensiveTest {
    
    private lateinit var context: Context
    private lateinit var repository: ScheduleRepository
    private lateinit var backupManager: BackupManager
    private lateinit var cloudBackupManager: CloudBackupManager
    private lateinit var testSchedules: List<Schedule>
    
    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
        repository = ScheduleRepository(context)
        backupManager = BackupManager(context)
        cloudBackupManager = CloudBackupManager(context)
        
        // 清理現有數據
        runBlocking {
            repository.deleteAllSchedules()
        }
        
        // 創建全面的測試數據
        testSchedules = BackupTestUtils.createTestSchedules()
    }
    
    @After
    fun tearDown() {
        runBlocking {
            repository.deleteAllSchedules()
            BackupTestUtils.cleanupTestFiles(FileUtils.getAutoLaunchDownloadsDir())
        }
    }
    
    @Test
    fun testCompleteBackupRestoreWorkflow() = runBlocking {
        // === 階段1：數據準備 ===
        println("=== 階段1：數據準備 ===")
        
        // 插入測試排程
        val insertedIds = mutableListOf<Long>()
        testSchedules.forEach { schedule ->
            val id = repository.insertSchedule(schedule)
            assertTrue("排程插入應該成功", id > 0)
            insertedIds.add(id)
            println("插入排程：${schedule.taskName} (ID: $id)")
        }
        
        // 驗證數據插入
        val originalSchedules = repository.getAllSchedules().first()
        assertEquals("應該插入${testSchedules.size}個排程", testSchedules.size, originalSchedules.size)
        println("成功插入 ${originalSchedules.size} 個排程")
        
        // === 階段2：本地備份創建 ===
        println("\n=== 階段2：本地備份創建 ===")
        
        val backupResult = backupManager.createLocalBackup()
        assertTrue("本地備份創建應該成功", backupResult.success)
        assertNotNull("備份文件路徑不應為空", backupResult.filePath)
        assertEquals("備份排程數量應該匹配", testSchedules.size, backupResult.scheduleCount)
        assertTrue("備份文件大小應該大於0", backupResult.fileSize > 0)
        
        val backupFile = File(backupResult.filePath!!)
        assertTrue("備份文件應該存在", backupFile.exists())
        println("本地備份創建成功：${backupFile.name} (${backupResult.fileSize} bytes)")
        
        // === 階段3：備份文件驗證 ===
        println("\n=== 階段3：備份文件驗證 ===")
        
        val backupFileInfo = backupManager.getBackupFileInfo(backupFile)
        assertNotNull("備份文件信息不應為空", backupFileInfo)
        assertTrue("備份文件應該有效", backupFileInfo!!.isValid)
        assertEquals("文件中排程數量應該匹配", testSchedules.size, backupFileInfo.scheduleCount)
        
        // 驗證JSON內容
        val jsonContent = backupFile.readText()
        val backupData = backupManager.gson.fromJson(jsonContent, BackupData::class.java)
        assertTrue("備份數據應該有效", backupData.isValid())
        
        val validationErrors = BackupTestUtils.validateBackupData(backupData, testSchedules.size)
        assertTrue("備份數據驗證應該通過：$validationErrors", validationErrors.isEmpty())
        println("備份文件驗證通過")
        
        // === 階段4：數據清除 ===
        println("\n=== 階段4：數據清除 ===")
        
        val deleteCount = repository.deleteAllSchedules()
        assertEquals("應該刪除${testSchedules.size}個排程", testSchedules.size, deleteCount)
        
        val emptySchedules = repository.getAllSchedules().first()
        assertTrue("數據庫應該為空", emptySchedules.isEmpty())
        println("數據庫已清空")
        
        // === 階段5：數據恢復 ===
        println("\n=== 階段5：數據恢復 ===")
        
        val restoreResult = backupManager.restoreFromLocalFile(backupResult.filePath!!)
        assertTrue("數據恢復應該成功", restoreResult.success)
        assertEquals("應該匯入${testSchedules.size}個排程", testSchedules.size, restoreResult.importedCount)
        assertEquals("不應該跳過任何排程", 0, restoreResult.skippedCount)
        assertEquals("不應該有錯誤", 0, restoreResult.errorCount)
        println("數據恢復成功：匯入 ${restoreResult.importedCount} 個排程")
        
        // === 階段6：恢復數據驗證 ===
        println("\n=== 階段6：恢復數據驗證 ===")
        
        val restoredSchedules = repository.getAllSchedules().first()
        assertEquals("應該恢復${testSchedules.size}個排程", testSchedules.size, restoredSchedules.size)
        
        // 詳細比較每個排程
        val originalSorted = testSchedules.sortedBy { schedule -> schedule.taskName }
        val restoredSorted = restoredSchedules.sortedBy { schedule -> schedule.taskName }

        for (i in originalSorted.indices) {
            val original = originalSorted[i]
            val restored = restoredSorted[i]

            assertTrue(
                "排程 ${original.taskName} 應該匹配",
                BackupTestUtils.compareSchedulesIgnoringIdAndTime(original, restored)
            )
            println("✓ 排程驗證通過：${original.taskName}")
        }
        
        // === 階段7：文件管理測試 ===
        println("\n=== 階段7：文件管理測試 ===")
        
        val backupFiles = backupManager.getLocalBackupFiles()
        assertTrue("應該至少有1個備份文件", backupFiles.isNotEmpty())
        
        val firstFile = backupFiles.first()
        val deleteSuccess = backupManager.deleteLocalBackupFile(firstFile)
        assertTrue("文件刪除應該成功", deleteSuccess)
        assertFalse("文件應該不存在", firstFile.exists())
        println("備份文件清理成功")
        
        println("\n=== 綜合測試完成 ===")
        println("✅ 所有測試階段都成功通過")
    }
    
    @Test
    fun testErrorHandlingAndEdgeCases() = runBlocking {
        println("=== 錯誤處理和邊緣情況測試 ===")
        
        // 測試空數據庫備份
        val emptyBackupResult = backupManager.createLocalBackup()
        assertFalse("空數據庫備份應該失敗", emptyBackupResult.success)
        println("✓ 空數據庫備份正確失敗")
        
        // 測試無效JSON恢復
        val invalidJson = BackupTestUtils.createInvalidBackupJson()
        val invalidRestoreResult = backupManager.restoreFromJson(invalidJson)
        assertFalse("無效JSON恢復應該失敗", invalidRestoreResult.success)
        println("✓ 無效JSON恢復正確失敗")
        
        // 測試損壞JSON恢復
        val corruptedJson = BackupTestUtils.createCorruptedBackupJson()
        val corruptedRestoreResult = backupManager.restoreFromJson(corruptedJson)
        assertFalse("損壞JSON恢復應該失敗", corruptedRestoreResult.success)
        println("✓ 損壞JSON恢復正確失敗")
        
        // 測試不存在文件恢復
        val nonExistentResult = backupManager.restoreFromLocalFile("/non/existent/file.json")
        assertFalse("不存在文件恢復應該失敗", nonExistentResult.success)
        println("✓ 不存在文件恢復正確失敗")
        
        println("✅ 錯誤處理測試完成")
    }
    
    @Test
    fun testDataIntegrityAndValidation() = runBlocking {
        println("=== 數據完整性和驗證測試 ===")
        
        // 插入測試數據
        testSchedules.forEach { schedule ->
            repository.insertSchedule(schedule)
        }
        
        // 創建備份
        val backupResult = backupManager.createLocalBackup()
        assertTrue("備份創建應該成功", backupResult.success)
        
        // 讀取並驗證備份內容
        val backupFile = File(backupResult.filePath!!)
        val jsonContent = backupFile.readText()
        val backupData = backupManager.gson.fromJson(jsonContent, BackupData::class.java)
        
        // 驗證版本信息
        assertEquals("版本應該為1.0", "1.0", backupData.version)
        assertTrue("時間戳應該合理", backupData.timestamp > 0)
        
        // 驗證設備信息
        val deviceInfo = backupData.deviceInfo
        assertFalse("應用版本不應為空", deviceInfo.appVersion.isBlank())
        assertFalse("Android版本不應為空", deviceInfo.androidVersion.isBlank())
        assertFalse("設備型號不應為空", deviceInfo.deviceModel.isBlank())
        
        // 驗證排程數據
        assertEquals("排程數量應該匹配", testSchedules.size, backupData.schedules.size)
        assertEquals("總排程數應該匹配", testSchedules.size, backupData.totalSchedules)
        
        // 驗證每個排程的有效性
        backupData.schedules.forEach { scheduleBackup ->
            assertTrue("排程應該有效：${scheduleBackup.taskName}", scheduleBackup.isValid())
        }
        
        println("✓ 數據完整性驗證通過")
        
        // 測試部分無效數據的處理
        val mixedSchedules = testSchedules.take(2) + BackupTestUtils.createInvalidTestSchedules().take(1)
        
        // 清空數據庫並插入混合數據
        repository.deleteAllSchedules()
        val validSchedules = mixedSchedules.filter { it.isValid() }
        validSchedules.forEach { schedule ->
            repository.insertSchedule(schedule)
        }
        
        val mixedBackupResult = backupManager.createLocalBackup()
        assertTrue("混合數據備份應該成功", mixedBackupResult.success)
        assertEquals("應該只備份有效排程", validSchedules.size, mixedBackupResult.scheduleCount)
        
        println("✓ 混合數據處理驗證通過")
        println("✅ 數據完整性測試完成")
    }
    
    @Test
    fun testPerformanceAndScalability() = runBlocking {
        println("=== 性能和可擴展性測試 ===")
        
        // 創建大量測試數據
        val largeDataSet = mutableListOf<Schedule>()
        repeat(100) { index ->
            largeDataSet.add(
                Schedule(
                    scheduleType = if (index % 2 == 0) ScheduleType.APP.value else ScheduleType.URL.value,
                    appName = if (index % 2 == 0) "測試應用$index" else null,
                    packageName = if (index % 2 == 0) "com.test.app$index" else null,
                    url = if (index % 2 == 1) "https://example$index.com" else null,
                    urlTitle = if (index % 2 == 1) "測試網站$index" else null,
                    taskName = "大量測試任務$index",
                    hour = index % 24,
                    minute = index % 60,
                    repeatMode = RepeatMode.values()[index % RepeatMode.values().size].value,
                    daysOfWeek = if (index % 3 == 0) 127 else 31, // 全週或工作日
                    isEnabled = index % 3 != 0 // 大部分啟用
                )
            )
        }
        
        // 插入大量數據
        val startInsert = System.currentTimeMillis()
        largeDataSet.forEach { schedule ->
            repository.insertSchedule(schedule)
        }
        val insertTime = System.currentTimeMillis() - startInsert
        println("插入100個排程耗時：${insertTime}ms")
        
        // 測試大量數據備份
        val startBackup = System.currentTimeMillis()
        val largeBackupResult = backupManager.createLocalBackup()
        val backupTime = System.currentTimeMillis() - startBackup
        
        assertTrue("大量數據備份應該成功", largeBackupResult.success)
        assertEquals("應該備份100個排程", 100, largeBackupResult.scheduleCount)
        println("備份100個排程耗時：${backupTime}ms")
        println("備份文件大小：${largeBackupResult.fileSize} bytes")
        
        // 測試大量數據恢復
        repository.deleteAllSchedules()
        
        val startRestore = System.currentTimeMillis()
        val largeRestoreResult = backupManager.restoreFromLocalFile(largeBackupResult.filePath!!)
        val restoreTime = System.currentTimeMillis() - startRestore
        
        assertTrue("大量數據恢復應該成功", largeRestoreResult.success)
        assertEquals("應該恢復100個排程", 100, largeRestoreResult.importedCount)
        println("恢復100個排程耗時：${restoreTime}ms")
        
        // 驗證性能指標
        assertTrue("插入性能應該合理（<5秒）", insertTime < 5000)
        assertTrue("備份性能應該合理（<3秒）", backupTime < 3000)
        assertTrue("恢復性能應該合理（<5秒）", restoreTime < 5000)
        
        println("✅ 性能測試完成")
    }
    
    @Test
    fun testCloudBackupManagerIntegration() {
        println("=== 雲端備份管理器集成測試 ===")
        
        // 測試雲端備份管理器初始化
        assertNotNull("雲端備份管理器不應為空", cloudBackupManager)
        
        // 測試Google Sign-In客戶端獲取
        val signInClient = cloudBackupManager.getGoogleSignInClient()
        assertNotNull("Google Sign-In客戶端不應為空", signInClient)
        
        // 測試登錄狀態檢查（在測試環境中通常未登錄）
        val isSignedIn = cloudBackupManager.isGoogleSignedIn()
        println("Google登錄狀態：$isSignedIn")
        
        // 測試當前帳戶獲取
        val currentAccount = cloudBackupManager.getCurrentGoogleAccount()
        println("當前Google帳戶：${currentAccount?.email ?: "未登錄"}")
        
        println("✅ 雲端備份管理器集成測試完成")
    }
}
