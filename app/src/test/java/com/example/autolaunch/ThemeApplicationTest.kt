package com.example.autolaunch

import android.content.Context
import androidx.appcompat.app.AppCompatDelegate
import androidx.test.core.app.ApplicationProvider
import com.example.autolaunch.utils.ThemeManager
import com.example.autolaunch.utils.ThemeType
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * 主題應用測試
 * 測試主題切換和應用功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [28])
class ThemeApplicationTest {

    private lateinit var context: Context
    private lateinit var themeManager: ThemeManager

    @Before
    fun setup() {
        context = ApplicationProvider.getApplicationContext()
        themeManager = ThemeManager.getInstance(context)
    }

    @Test
    fun testThemeManagerSingleton() {
        val themeManager1 = ThemeManager.getInstance(context)
        val themeManager2 = ThemeManager.getInstance(context)
        
        assertSame("ThemeManager 應該是單例", themeManager1, themeManager2)
    }

    @Test
    fun testDefaultTheme() {
        // 清除之前的設定
        val prefs = context.getSharedPreferences("theme_preferences", Context.MODE_PRIVATE)
        prefs.edit().clear().apply()
        
        val newThemeManager = ThemeManager.getInstance(context)
        val currentTheme = newThemeManager.getCurrentTheme()
        
        // 預設應該是跟隨系統，返回對應的經典主題
        assertTrue("預設主題應該是經典主題", 
            currentTheme == ThemeType.LIGHT_CLASSIC || currentTheme == ThemeType.DARK_CLASSIC)
    }

    @Test
    fun testSetTheme() {
        // 設定深藍主題
        themeManager.setTheme(ThemeType.DARK_BLUE)
        
        val currentTheme = themeManager.getCurrentTheme()
        assertEquals("設定的主題應該生效", ThemeType.DARK_BLUE, currentTheme)
        
        // 設定後應該不再跟隨系統
        assertFalse("設定主題後不應該跟隨系統", themeManager.isFollowingSystem())
    }

    @Test
    fun testFollowSystemTheme() {
        // 設定跟隨系統
        themeManager.setFollowSystem(true)
        
        assertTrue("應該跟隨系統主題", themeManager.isFollowingSystem())
        
        val currentTheme = themeManager.getCurrentTheme()
        // 跟隨系統時應該返回對應的經典主題
        assertTrue("跟隨系統時應該是經典主題", 
            currentTheme == ThemeType.LIGHT_CLASSIC || currentTheme == ThemeType.DARK_CLASSIC)
    }

    @Test
    fun testThemePersistence() {
        // 設定一個主題
        themeManager.setTheme(ThemeType.LIGHT_WARM)
        
        // 創建新的 ThemeManager 實例來模擬應用重啟
        val newThemeManager = ThemeManager.getInstance(context)
        val persistedTheme = newThemeManager.getCurrentTheme()
        
        assertEquals("主題設定應該持久化", ThemeType.LIGHT_WARM, persistedTheme)
    }

    @Test
    fun testSystemThemeDetection() {
        // 設定跟隨系統
        themeManager.setFollowSystem(true)
        
        // 測試系統主題檢測
        val isLightClassicSystemTheme = themeManager.isSystemTheme(ThemeType.LIGHT_CLASSIC)
        val isDarkClassicSystemTheme = themeManager.isSystemTheme(ThemeType.DARK_CLASSIC)
        
        // 其中一個應該是系統主題（取決於當前系統設定）
        assertTrue("經典主題中應該有一個是系統主題", 
            isLightClassicSystemTheme || isDarkClassicSystemTheme)
        
        // 非經典主題不應該是系統主題
        assertFalse("非經典主題不應該是系統主題", themeManager.isSystemTheme(ThemeType.DARK_BLUE))
    }

    @Test
    fun testThemeResourceMapping() {
        // 測試所有主題都有對應的資源ID（通過應用主題來間接測試）
        ThemeType.values().forEach { themeType ->
            try {
                themeManager.setTheme(themeType)
                val currentTheme = themeManager.getCurrentTheme()
                assertEquals("設定的主題應該生效", themeType, currentTheme)
            } catch (e: Exception) {
                fail("主題 ${themeType.displayName} 設定失敗: ${e.message}")
            }
        }
    }

    @Test
    fun testThemePreviewColors() {
        // 測試所有主題都有預覽顏色
        ThemeType.values().forEach { themeType ->
            try {
                val previewColors = themeManager.getThemePreviewColors(themeType)
                assertNotNull("主題 ${themeType.displayName} 應該有預覽顏色", previewColors)
                assertNotNull("主題 ${themeType.displayName} 應該有主色調", previewColors.primary)
                assertNotNull("主題 ${themeType.displayName} 應該有表面顏色", previewColors.surface)
                assertNotNull("主題 ${themeType.displayName} 應該有背景顏色", previewColors.background)
            } catch (e: Exception) {
                fail("主題 ${themeType.displayName} 沒有預覽顏色: ${e.message}")
            }
        }
    }
}
