package com.example.autolaunch

import com.example.autolaunch.adapter.UnifiedScheduleAdapter
import com.example.autolaunch.model.Schedule
import com.example.autolaunch.model.ScheduleType
import org.junit.Test
import org.junit.Assert.*

/**
 * 統一排程適配器的單元測試
 * 驗證多視圖類型的正確性
 */
class UnifiedScheduleAdapterTest {

    @Test
    fun testScheduleItemTypes() {
        // 測試標題項目
        val headerItem = UnifiedScheduleAdapter.ScheduleItem.Header(
            title = "應用程式",
            count = 2,
            icon = R.drawable.ic_apps_24
        )
        
        // 測試排程項目
        val scheduleItem = UnifiedScheduleAdapter.ScheduleItem.ScheduleData(
            Schedule(
                id = 1,
                taskName = "測試排程",
                scheduleType = ScheduleType.APP.value,
                packageName = "com.example.test",
                appName = "測試應用",
                hour = 9,
                minute = 0,
                repeatMode = 1,
                isEnabled = true
            )
        )
        
        // 驗證項目類型
        assertTrue("標題項目應該是 Header 類型", headerItem is UnifiedScheduleAdapter.ScheduleItem.Header)
        assertTrue("排程項目應該是 ScheduleData 類型", scheduleItem is UnifiedScheduleAdapter.ScheduleItem.ScheduleData)
        
        // 驗證標題項目屬性
        assertEquals("標題應該正確", "應用程式", headerItem.title)
        assertEquals("數量應該正確", 2, headerItem.count)
        assertEquals("圖標應該正確", R.drawable.ic_apps_24, headerItem.icon)
        
        // 驗證排程項目屬性
        assertEquals("排程 ID 應該正確", 1, scheduleItem.schedule.id)
        assertEquals("任務名稱應該正確", "測試排程", scheduleItem.schedule.taskName)
    }

    @Test
    fun testUnifiedListCreation() {
        // 創建測試排程
        val appSchedule = Schedule(
            id = 1,
            taskName = "應用排程",
            scheduleType = ScheduleType.APP.value,
            packageName = "com.example.app",
            appName = "測試應用",
            hour = 9,
            minute = 0,
            repeatMode = 1,
            isEnabled = true
        )
        
        val urlSchedule = Schedule(
            id = 2,
            taskName = "網頁排程",
            scheduleType = ScheduleType.URL.value,
            url = "https://example.com",
            hour = 10,
            minute = 30,
            repeatMode = 1,
            isEnabled = true
        )
        
        // 模擬創建統一列表的邏輯
        val unifiedItems = mutableListOf<UnifiedScheduleAdapter.ScheduleItem>()
        
        // 添加應用程式區塊
        unifiedItems.add(
            UnifiedScheduleAdapter.ScheduleItem.Header(
                title = "應用程式",
                count = 1,
                icon = R.drawable.ic_apps_24
            )
        )
        unifiedItems.add(UnifiedScheduleAdapter.ScheduleItem.ScheduleData(appSchedule))
        
        // 添加網頁區塊
        unifiedItems.add(
            UnifiedScheduleAdapter.ScheduleItem.Header(
                title = "網頁",
                count = 1,
                icon = R.drawable.ic_web_24
            )
        )
        unifiedItems.add(UnifiedScheduleAdapter.ScheduleItem.ScheduleData(urlSchedule))
        
        // 驗證列表結構
        assertEquals("統一列表應該有 4 個項目", 4, unifiedItems.size)
        
        // 驗證項目順序和類型
        assertTrue("第一個項目應該是應用程式標題", 
            unifiedItems[0] is UnifiedScheduleAdapter.ScheduleItem.Header)
        assertTrue("第二個項目應該是應用排程", 
            unifiedItems[1] is UnifiedScheduleAdapter.ScheduleItem.ScheduleData)
        assertTrue("第三個項目應該是網頁標題", 
            unifiedItems[2] is UnifiedScheduleAdapter.ScheduleItem.Header)
        assertTrue("第四個項目應該是網頁排程", 
            unifiedItems[3] is UnifiedScheduleAdapter.ScheduleItem.ScheduleData)
        
        // 驗證標題內容
        val appHeader = unifiedItems[0] as UnifiedScheduleAdapter.ScheduleItem.Header
        val webHeader = unifiedItems[2] as UnifiedScheduleAdapter.ScheduleItem.Header
        
        assertEquals("應用程式標題正確", "應用程式", appHeader.title)
        assertEquals("網頁標題正確", "網頁", webHeader.title)
        assertEquals("應用程式數量正確", 1, appHeader.count)
        assertEquals("網頁數量正確", 1, webHeader.count)
    }
}
