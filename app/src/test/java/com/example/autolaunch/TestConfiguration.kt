package com.example.autolaunch

import android.util.Log
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import org.junit.rules.TestRule
import org.junit.runner.Description
import org.junit.runners.model.Statement

/**
 * 測試配置類
 * 提供通用的測試設置和工具
 */
object TestConfiguration {
    
    /**
     * 初始化測試環境
     */
    fun initializeTestEnvironment() {
        // Mock Android Log for all tests
        mockkStatic(Log::class)
        every { Log.v(any(), any(), any()) } returns 0
        every { Log.d(any(), any(), any()) } returns 0
        every { Log.i(any(), any(), any()) } returns 0
        every { Log.w(any(), any(), any()) } returns 0
        every { Log.e(any(), any(), any()) } returns 0
        every { Log.v(any(), any()) } returns 0
        every { Log.d(any(), any()) } returns 0
        every { Log.i(any(), any()) } returns 0
        every { Log.w(any(), any()) } returns 0
        every { Log.e(any(), any()) } returns 0
        every { Log.getStackTraceString(any()) } returns "Mock stack trace"
    }
    
    /**
     * 清理測試環境
     */
    fun cleanupTestEnvironment() {
        // Any cleanup needed after tests
    }
}

/**
 * Android Log Mock Rule
 * 自動為測試設置 Android Log mock
 */
class AndroidLogRule : TestRule {
    override fun apply(base: Statement, description: Description): Statement {
        return object : Statement() {
            override fun evaluate() {
                TestConfiguration.initializeTestEnvironment()
                try {
                    base.evaluate()
                } finally {
                    TestConfiguration.cleanupTestEnvironment()
                }
            }
        }
    }
}

/**
 * 測試工具類
 */
object TestUtils {
    
    /**
     * 創建測試用的異常
     */
    fun createTestException(message: String = "Test exception"): Exception {
        return RuntimeException(message)
    }
    
    /**
     * 等待異步操作完成
     */
    fun waitForAsyncOperation(timeoutMs: Long = 1000) {
        Thread.sleep(timeoutMs)
    }
    
    /**
     * 驗證字符串包含預期內容
     */
    fun assertContains(actual: String, expected: String, message: String = "") {
        if (!actual.contains(expected)) {
            throw AssertionError("$message: Expected '$actual' to contain '$expected'")
        }
    }
    
    /**
     * 驗證字符串不包含內容
     */
    fun assertNotContains(actual: String, unexpected: String, message: String = "") {
        if (actual.contains(unexpected)) {
            throw AssertionError("$message: Expected '$actual' to not contain '$unexpected'")
        }
    }
    
    /**
     * 驗證列表包含預期元素
     */
    fun <T> assertContains(actual: List<T>, expected: T, message: String = "") {
        if (!actual.contains(expected)) {
            throw AssertionError("$message: Expected list to contain '$expected'")
        }
    }
    
    /**
     * 驗證列表大小
     */
    fun <T> assertSize(actual: List<T>, expectedSize: Int, message: String = "") {
        if (actual.size != expectedSize) {
            throw AssertionError("$message: Expected list size $expectedSize, but was ${actual.size}")
        }
    }
    
    /**
     * 驗證值在範圍內
     */
    fun assertInRange(actual: Number, min: Number, max: Number, message: String = "") {
        val actualDouble = actual.toDouble()
        val minDouble = min.toDouble()
        val maxDouble = max.toDouble()
        
        if (actualDouble < minDouble || actualDouble > maxDouble) {
            throw AssertionError("$message: Expected $actual to be between $min and $max")
        }
    }
    
    /**
     * 驗證操作在指定時間內完成
     */
    fun assertExecutionTime(maxTimeMs: Long, operation: () -> Unit) {
        val startTime = System.currentTimeMillis()
        operation()
        val executionTime = System.currentTimeMillis() - startTime
        
        if (executionTime > maxTimeMs) {
            throw AssertionError("Operation took ${executionTime}ms, expected less than ${maxTimeMs}ms")
        }
    }
    
    /**
     * 創建測試用的回調函數
     */
    fun <T> createTestCallback(): TestCallback<T> {
        return TestCallback()
    }
}

/**
 * 測試回調類
 */
class TestCallback<T> {
    private var _result: T? = null
    private var _exception: Throwable? = null
    private var _called = false
    
    val result: T?
        get() = _result
    
    val exception: Throwable?
        get() = _exception
    
    val wasCalled: Boolean
        get() = _called
    
    fun onSuccess(result: T) {
        _result = result
        _called = true
    }
    
    fun onError(exception: Throwable) {
        _exception = exception
        _called = true
    }
    
    fun reset() {
        _result = null
        _exception = null
        _called = false
    }
    
    fun waitForCallback(timeoutMs: Long = 1000): Boolean {
        val startTime = System.currentTimeMillis()
        while (!_called && (System.currentTimeMillis() - startTime) < timeoutMs) {
            Thread.sleep(10)
        }
        return _called
    }
}

/**
 * 測試數據生成器
 */
object TestDataGenerator {
    
    /**
     * 生成測試字符串
     */
    fun generateString(length: Int = 10, prefix: String = "test"): String {
        return "$prefix${(1..length).map { ('a'..'z').random() }.joinToString("")}"
    }
    
    /**
     * 生成測試數字
     */
    fun generateInt(min: Int = 0, max: Int = 100): Int {
        return (min..max).random()
    }
    
    /**
     * 生成測試長整數
     */
    fun generateLong(min: Long = 0L, max: Long = 1000L): Long {
        return (min..max).random()
    }
    
    /**
     * 生成測試布爾值
     */
    fun generateBoolean(): Boolean {
        return listOf(true, false).random()
    }
    
    /**
     * 生成測試列表
     */
    fun <T> generateList(size: Int, generator: () -> T): List<T> {
        return (1..size).map { generator() }
    }
    
    /**
     * 生成測試時間戳
     */
    fun generateTimestamp(): Long {
        return System.currentTimeMillis() - generateLong(0, 86400000) // Within last 24 hours
    }
}

/**
 * 性能測試工具
 */
object PerformanceTestUtils {
    
    /**
     * 測量操作執行時間
     */
    fun measureExecutionTime(operation: () -> Unit): Long {
        val startTime = System.currentTimeMillis()
        operation()
        return System.currentTimeMillis() - startTime
    }
    
    /**
     * 測量多次執行的平均時間
     */
    fun measureAverageExecutionTime(iterations: Int, operation: () -> Unit): Double {
        val times = mutableListOf<Long>()
        repeat(iterations) {
            times.add(measureExecutionTime(operation))
        }
        return times.average()
    }
    
    /**
     * 驗證操作性能
     */
    fun assertPerformance(
        maxTimeMs: Long,
        iterations: Int = 1,
        operation: () -> Unit
    ) {
        val averageTime = measureAverageExecutionTime(iterations, operation)
        if (averageTime > maxTimeMs) {
            throw AssertionError("Average execution time ${averageTime}ms exceeded limit ${maxTimeMs}ms")
        }
    }
    
    /**
     * 記憶體使用測試
     */
    fun measureMemoryUsage(operation: () -> Unit): Long {
        System.gc() // Force garbage collection
        val beforeMemory = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory()
        
        operation()
        
        System.gc() // Force garbage collection again
        val afterMemory = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory()
        
        return afterMemory - beforeMemory
    }
}
