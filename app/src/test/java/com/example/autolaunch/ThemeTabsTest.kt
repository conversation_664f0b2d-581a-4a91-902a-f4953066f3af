package com.example.autolaunch

import com.example.autolaunch.utils.ThemeType
import org.junit.Test
import org.junit.Assert.*

/**
 * 主題 Tabs 功能測試
 * 驗證新增的主題和 tabs 功能
 */
class ThemeTabsTest {

    @Test
    fun testThemeCount() {
        val allThemes = ThemeType.values()
        assertEquals("應該有16個主題", 16, allThemes.size)
    }

    @Test
    fun testLightThemeCount() {
        val lightThemes = ThemeType.getLightThemes()
        assertEquals("淺色主題應該有7個", 7, lightThemes.size)
    }

    @Test
    fun testDarkThemeCount() {
        val darkThemes = ThemeType.getDarkThemes()
        assertEquals("深色主題應該有9個", 9, darkThemes.size)
    }

    @Test
    fun testNewLightThemes() {
        val lightThemes = ThemeType.getLightThemes()
        
        // 檢查新增的淺色主題
        assertTrue("應該包含粉紅淺色主題", lightThemes.contains(ThemeType.LIGHT_PINK))
        assertTrue("應該包含陽光淺色主題", lightThemes.contains(ThemeType.LIGHT_YELLOW))
        assertTrue("應該包含薄荷淺色主題", lightThemes.contains(ThemeType.LIGHT_MINT))
        assertTrue("應該包含薰衣草淺色主題", lightThemes.contains(ThemeType.LIGHT_LAVENDER))
    }

    @Test
    fun testNewDarkThemes() {
        val darkThemes = ThemeType.getDarkThemes()
        
        // 檢查新增的深色主題
        assertTrue("應該包含玫瑰深色主題", darkThemes.contains(ThemeType.DARK_ROSE))
        assertTrue("應該包含金色深色主題", darkThemes.contains(ThemeType.DARK_GOLD))
        assertTrue("應該包含青綠深色主題", darkThemes.contains(ThemeType.DARK_TEAL))
        assertTrue("應該包含深紅主題", darkThemes.contains(ThemeType.DARK_CRIMSON))
    }

    @Test
    fun testThemeProperties() {
        // 測試粉紅主題
        assertEquals("粉紅淺色", ThemeType.LIGHT_PINK.displayName)
        assertEquals("少女心爆棚的夢幻粉 💕", ThemeType.LIGHT_PINK.description)
        assertFalse("粉紅淺色主題不是深色模式", ThemeType.LIGHT_PINK.isDarkMode)

        // 測試金色主題
        assertEquals("金色深色", ThemeType.DARK_GOLD.displayName)
        assertEquals("土豪金的奢華質感 ✨", ThemeType.DARK_GOLD.description)
        assertTrue("金色深色主題是深色模式", ThemeType.DARK_GOLD.isDarkMode)
    }

    @Test
    fun testThemeIdUniqueness() {
        val allThemes = ThemeType.values()
        val ids = allThemes.map { it.id }
        val uniqueIds = ids.toSet()
        
        assertEquals("所有主題ID應該是唯一的", ids.size, uniqueIds.size)
    }

    @Test
    fun testThemeByIdLookup() {
        // 測試新主題的ID查找
        assertEquals(ThemeType.LIGHT_PINK, ThemeType.fromId("light_pink"))
        assertEquals(ThemeType.LIGHT_YELLOW, ThemeType.fromId("light_yellow"))
        assertEquals(ThemeType.DARK_ROSE, ThemeType.fromId("dark_rose"))
        assertEquals(ThemeType.DARK_GOLD, ThemeType.fromId("dark_gold"))
    }

    @Test
    fun testDefaultThemes() {
        val lightDefault = ThemeType.getDefaultTheme(false)
        val darkDefault = ThemeType.getDefaultTheme(true)
        
        assertEquals("淺色模式預設主題", ThemeType.LIGHT_CLASSIC, lightDefault)
        assertEquals("深色模式預設主題", ThemeType.DARK_CLASSIC, darkDefault)
    }
}
