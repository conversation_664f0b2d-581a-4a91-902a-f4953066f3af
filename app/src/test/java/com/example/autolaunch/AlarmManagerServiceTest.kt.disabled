package com.example.autolaunch

import android.app.AlarmManager
import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.example.autolaunch.model.Schedule
import com.example.autolaunch.model.ScheduleType
import com.example.autolaunch.model.RepeatMode
import com.example.autolaunch.utils.assertDoesNotThrow
import io.mockk.MockKAnnotations
import io.mockk.impl.annotations.MockK
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.Before
import org.junit.Assert.*
import org.robolectric.annotation.Config
import org.robolectric.Shadows.shadowOf

/**
 * AlarmManagerService 的單元測試
 * 測試鬧鐘管理服務的核心功能
 */
@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S])
class AlarmManagerServiceTest {

    private lateinit var context: Context
    private lateinit var alarmManagerService: AlarmManagerService
    
    @MockK
    private lateinit var mockAlarmManager: AlarmManager

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        context = ApplicationProvider.getApplicationContext()
        alarmManagerService = AlarmManagerService(context)
        // In a test environment, we might not get a real AlarmManager.
        // If needed, we could use Robolectric's ShadowAlarmManager.
    }

    @Test
    fun `service creation should be successful`() {
        assertNotNull(alarmManagerService)
    }

    @Test
    fun `canScheduleExactAlarms should not throw`() {
        assertDoesNotThrow("Checking for exact alarm permission should not throw") {
            AlarmManagerService.canScheduleExactAlarms(context)
        }
    }

    @Test
    fun `setAlarm with APP schedule should not throw`() {
        val schedule = Schedule(
            id = 1, scheduleType = ScheduleType.APP.value, appName = "Test App", packageName = "com.test.app",
            hour = 9, minute = 30, repeatMode = RepeatMode.DAILY.value, isEnabled = true
        )
        assertDoesNotThrow("Setting alarm for an APP schedule should not throw") {
            alarmManagerService.setAlarm(schedule)
        }
    }

    @Test
    fun `setAlarm with URL schedule should not throw`() {
        val schedule = Schedule(
            id = 2, scheduleType = ScheduleType.URL.value, url = "https://www.google.com", urlTitle = "Google",
            hour = 14, minute = 0, repeatMode = RepeatMode.ONCE.value, isEnabled = true
        )
        assertDoesNotThrow("Setting alarm for a URL schedule should not throw") {
            alarmManagerService.setAlarm(schedule)
        }
    }

    @Test
    fun `setAlarm with disabled schedule should not throw`() {
        val schedule = Schedule(
            id = 3, scheduleType = ScheduleType.APP.value, appName = "Disabled App", packageName = "com.disabled.app",
            hour = 10, minute = 0, repeatMode = RepeatMode.DAILY.value, isEnabled = false
        )
        assertDoesNotThrow("Setting alarm for a disabled schedule should not throw") {
            alarmManagerService.setAlarm(schedule)
        }
    }

    @Test
    fun `cancelAlarm should not throw`() {
        val scheduleId = 123L
        assertDoesNotThrow("Cancelling an alarm should not throw") {
            alarmManagerService.cancelAlarm(scheduleId)
        }
    }

    @Test
    fun `setAlarm with invalid schedule should not throw`() {
        val schedule = Schedule(
            id = 4, scheduleType = ScheduleType.APP.value, appName = null, packageName = null,
            hour = 9, minute = 30, repeatMode = RepeatMode.DAILY.value, isEnabled = true
        )
        assertDoesNotThrow("Setting alarm for an invalid schedule should not throw") {
            alarmManagerService.setAlarm(schedule)
        }
    }

    @Test
    fun `setAlarm with past time for ONCE schedule should not throw`() {
        val schedule = Schedule(
            id = 5, scheduleType = ScheduleType.APP.value, appName = "Past Time App", packageName = "com.past.app",
            hour = 1, minute = 0, repeatMode = RepeatMode.ONCE.value, isEnabled = true
        )
        assertDoesNotThrow("Setting alarm for a past ONCE schedule should not throw") {
            alarmManagerService.setAlarm(schedule)
        }
    }

    @Test
    fun `alarm intent creation for APP schedule should not throw`() {
        val schedule = Schedule(
            id = 6, scheduleType = ScheduleType.APP.value, appName = "Intent Test App", packageName = "com.intent.test",
            hour = 12, minute = 0, repeatMode = RepeatMode.DAILY.value, isEnabled = true
        )
        assertDoesNotThrow("Intent creation for APP schedule should not throw") {
            alarmManagerService.setAlarm(schedule)
        }
    }

    @Test
    fun `alarm intent creation for URL schedule should not throw`() {
        val schedule = Schedule(
            id = 7, scheduleType = ScheduleType.URL.value, url = "https://www.example.com", urlTitle = "Example",
            hour = 16, minute = 30, repeatMode = RepeatMode.WEEKLY.value, isEnabled = true
        )
        assertDoesNotThrow("Intent creation for URL schedule should not throw") {
            alarmManagerService.setAlarm(schedule)
        }
    }
}
