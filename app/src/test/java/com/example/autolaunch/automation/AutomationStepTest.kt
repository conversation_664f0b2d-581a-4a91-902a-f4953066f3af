package com.example.autolaunch.automation

import com.example.autolaunch.model.AutomationStep
import com.example.autolaunch.model.StepType
import com.example.autolaunch.model.TargetType
import org.junit.Test
import org.junit.Assert.*

/**
 * AutomationStep 單元測試
 */
class AutomationStepTest {

    @Test
    fun testClickStepValidation() {
        // 有效的點擊步驟
        val validClickStep = AutomationStep(
            scheduleId = 1L,
            stepOrder = 1,
            stepType = StepType.CLICK.value,
            targetType = TargetType.TEXT.value,
            targetText = "登入"
        )
        assertTrue("有效的點擊步驟應該通過驗證", validClickStep.isValid())

        // 無效的點擊步驟（缺少目標文字）
        val invalidClickStep = AutomationStep(
            scheduleId = 1L,
            stepOrder = 1,
            stepType = StepType.CLICK.value,
            targetType = TargetType.TEXT.value,
            targetText = null
        )
        assertFalse("缺少目標文字的點擊步驟應該驗證失敗", invalidClickStep.isValid())
    }

    @Test
    fun testInputStepValidation() {
        // 有效的輸入步驟
        val validInputStep = AutomationStep(
            scheduleId = 1L,
            stepOrder = 1,
            stepType = StepType.INPUT_TEXT.value,
            targetType = TargetType.ID.value,
            targetId = "username",
            inputText = "testuser"
        )
        assertTrue("有效的輸入步驟應該通過驗證", validInputStep.isValid())

        // 無效的輸入步驟（缺少輸入文字）
        val invalidInputStep = AutomationStep(
            scheduleId = 1L,
            stepOrder = 1,
            stepType = StepType.INPUT_TEXT.value,
            targetType = TargetType.ID.value,
            targetId = "username",
            inputText = null
        )
        assertFalse("缺少輸入文字的輸入步驟應該驗證失敗", invalidInputStep.isValid())
    }

    @Test
    fun testWaitStepValidation() {
        // 有效的等待步驟
        val validWaitStep = AutomationStep(
            scheduleId = 1L,
            stepOrder = 1,
            stepType = StepType.WAIT.value,
            waitDuration = 2000L
        )
        assertTrue("有效的等待步驟應該通過驗證", validWaitStep.isValid())

        // 無效的等待步驟（等待時間為0）
        val invalidWaitStep = AutomationStep(
            scheduleId = 1L,
            stepOrder = 1,
            stepType = StepType.WAIT.value,
            waitDuration = 0L
        )
        assertFalse("等待時間為0的等待步驟應該驗證失敗", invalidWaitStep.isValid())
    }

    @Test
    fun testCoordinateStepValidation() {
        // 有效的座標步驟
        val validCoordinateStep = AutomationStep(
            scheduleId = 1L,
            stepOrder = 1,
            stepType = StepType.CLICK.value,
            targetType = TargetType.COORDINATE.value,
            targetX = 100,
            targetY = 200
        )
        assertTrue("有效的座標步驟應該通過驗證", validCoordinateStep.isValid())

        // 無效的座標步驟（缺少Y座標）
        val invalidCoordinateStep = AutomationStep(
            scheduleId = 1L,
            stepOrder = 1,
            stepType = StepType.CLICK.value,
            targetType = TargetType.COORDINATE.value,
            targetX = 100,
            targetY = null
        )
        assertFalse("缺少Y座標的座標步驟應該驗證失敗", invalidCoordinateStep.isValid())
    }

    @Test
    fun testStepDisplayName() {
        val clickStep = AutomationStep(
            scheduleId = 1L,
            stepOrder = 1,
            stepType = StepType.CLICK.value,
            targetType = TargetType.TEXT.value,
            targetText = "登入"
        )
        assertEquals("點擊步驟顯示名稱不正確", "點擊 「登入」", clickStep.getDisplayName())

        val inputStep = AutomationStep(
            scheduleId = 1L,
            stepOrder = 1,
            stepType = StepType.INPUT_TEXT.value,
            targetType = TargetType.ID.value,
            targetId = "username",
            inputText = "testuser"
        )
        assertEquals("輸入步驟顯示名稱不正確", "輸入「testuser」到 ID:username", inputStep.getDisplayName())

        val waitStep = AutomationStep(
            scheduleId = 1L,
            stepOrder = 1,
            stepType = StepType.WAIT.value,
            waitDuration = 2000L
        )
        assertEquals("等待步驟顯示名稱不正確", "等待 2000ms", waitStep.getDisplayName())
    }

    @Test
    fun testStepTypeCheckers() {
        val clickStep = AutomationStep(
            scheduleId = 1L,
            stepOrder = 1,
            stepType = StepType.CLICK.value,
            targetType = TargetType.TEXT.value,
            targetText = "登入"
        )
        assertTrue("點擊步驟應該被識別為點擊類型", clickStep.isClickStep())
        assertFalse("點擊步驟不應該被識別為輸入類型", clickStep.isInputStep())

        val inputStep = AutomationStep(
            scheduleId = 1L,
            stepOrder = 1,
            stepType = StepType.INPUT_TEXT.value,
            targetType = TargetType.ID.value,
            targetId = "username",
            inputText = "testuser"
        )
        assertTrue("輸入步驟應該被識別為輸入類型", inputStep.isInputStep())
        assertFalse("輸入步驟不應該被識別為點擊類型", inputStep.isClickStep())

        val waitStep = AutomationStep(
            scheduleId = 1L,
            stepOrder = 1,
            stepType = StepType.WAIT.value,
            waitDuration = 2000L
        )
        assertTrue("等待步驟應該被識別為等待類型", waitStep.isWaitStep())
        assertFalse("等待步驟不應該被識別為系統類型", waitStep.isSystemStep())

        val backStep = AutomationStep(
            scheduleId = 1L,
            stepOrder = 1,
            stepType = StepType.BACK.value
        )
        assertTrue("返回步驟應該被識別為系統類型", backStep.isSystemStep())
        assertFalse("返回步驟不應該被識別為滑動類型", backStep.isSwipeStep())
    }

    @Test
    fun testStepCopy() {
        val originalStep = AutomationStep(
            id = 1L,
            scheduleId = 1L,
            stepOrder = 1,
            stepType = StepType.CLICK.value,
            targetType = TargetType.TEXT.value,
            targetText = "登入",
            isEnabled = true,
            isOptional = false,
            createdTime = 1000L
        )

        val copiedStep = originalStep.copy(
            stepOrder = 2,
            targetText = "註冊",
            isOptional = true
        )

        assertEquals("複製的步驟ID應該保持不變", 1L, copiedStep.id)
        assertEquals("複製的步驟順序應該更新", 2, copiedStep.stepOrder)
        assertEquals("複製的目標文字應該更新", "註冊", copiedStep.targetText)
        assertEquals("複製的可選狀態應該更新", true, copiedStep.isOptional)
        assertEquals("複製的創建時間應該保持不變", 1000L, copiedStep.createdTime)
        assertTrue("複製的更新時間應該更新", copiedStep.updatedTime > originalStep.updatedTime)
    }
}
