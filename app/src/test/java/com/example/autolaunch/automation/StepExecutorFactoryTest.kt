package com.example.autolaunch.automation

import com.example.autolaunch.automation.steps.*
import com.example.autolaunch.model.AutomationStep
import com.example.autolaunch.model.StepType
import com.example.autolaunch.model.TargetType
import org.junit.Test
import org.junit.Assert.*

/**
 * StepExecutorFactory 單元測試
 */
class StepExecutorFactoryTest {

    @Test
    fun testGetExecutorForClickStep() {
        val clickStep = AutomationStep(
            scheduleId = 1L,
            stepOrder = 1,
            stepType = StepType.CLICK.value,
            targetType = TargetType.TEXT.value,
            targetText = "登入"
        )
        
        val executor = StepExecutorFactory.getExecutor(clickStep)
        assertTrue("點擊步驟應該返回ClickStepExecutor", executor is ClickStepExecutor)
    }

    @Test
    fun testGetExecutorForInputStep() {
        val inputStep = AutomationStep(
            scheduleId = 1L,
            stepOrder = 1,
            stepType = StepType.INPUT_TEXT.value,
            targetType = TargetType.ID.value,
            targetId = "username",
            inputText = "testuser"
        )
        
        val executor = StepExecutorFactory.getExecutor(inputStep)
        assertTrue("輸入步驟應該返回InputStepExecutor", executor is InputStepExecutor)
    }

    @Test
    fun testGetExecutorForWaitStep() {
        val waitStep = AutomationStep(
            scheduleId = 1L,
            stepOrder = 1,
            stepType = StepType.WAIT.value,
            waitDuration = 2000L
        )
        
        val executor = StepExecutorFactory.getExecutor(waitStep)
        assertTrue("等待步驟應該返回WaitStepExecutor", executor is WaitStepExecutor)
    }

    @Test
    fun testGetExecutorForSwipeStep() {
        val swipeStep = AutomationStep(
            scheduleId = 1L,
            stepOrder = 1,
            stepType = StepType.SWIPE_UP.value
        )
        
        val executor = StepExecutorFactory.getExecutor(swipeStep)
        assertTrue("滑動步驟應該返回SwipeStepExecutor", executor is SwipeStepExecutor)
    }

    @Test
    fun testGetExecutorForSystemStep() {
        val backStep = AutomationStep(
            scheduleId = 1L,
            stepOrder = 1,
            stepType = StepType.BACK.value
        )
        
        val executor = StepExecutorFactory.getExecutor(backStep)
        assertTrue("系統步驟應該返回SystemStepExecutor", executor is SystemStepExecutor)
    }

    @Test
    fun testValidateStep() {
        // 有效步驟
        val validStep = AutomationStep(
            scheduleId = 1L,
            stepOrder = 1,
            stepType = StepType.CLICK.value,
            targetType = TargetType.TEXT.value,
            targetText = "登入"
        )
        assertTrue("有效步驟應該通過驗證", StepExecutorFactory.validateStep(validStep))

        // 無效步驟
        val invalidStep = AutomationStep(
            scheduleId = 1L,
            stepOrder = 1,
            stepType = StepType.CLICK.value,
            targetType = TargetType.TEXT.value,
            targetText = null
        )
        assertFalse("無效步驟應該驗證失敗", StepExecutorFactory.validateStep(invalidStep))
    }

    @Test
    fun testGetStepDescription() {
        val clickStep = AutomationStep(
            scheduleId = 1L,
            stepOrder = 1,
            stepType = StepType.CLICK.value,
            targetType = TargetType.TEXT.value,
            targetText = "登入"
        )
        
        val description = StepExecutorFactory.getStepDescription(clickStep)
        assertEquals("步驟描述不正確", "點擊 文字「登入」", description)
    }

    @Test
    fun testRequiresTarget() {
        assertTrue("點擊步驟應該需要目標", StepExecutorFactory.requiresTarget(StepType.CLICK))
        assertTrue("輸入步驟應該需要目標", StepExecutorFactory.requiresTarget(StepType.INPUT_TEXT))
        assertFalse("等待步驟不應該需要目標", StepExecutorFactory.requiresTarget(StepType.WAIT))
        assertFalse("返回步驟不應該需要目標", StepExecutorFactory.requiresTarget(StepType.BACK))
    }

    @Test
    fun testRequiresInputText() {
        assertTrue("輸入步驟應該需要輸入文字", StepExecutorFactory.requiresInputText(StepType.INPUT_TEXT))
        assertFalse("點擊步驟不應該需要輸入文字", StepExecutorFactory.requiresInputText(StepType.CLICK))
        assertFalse("等待步驟不應該需要輸入文字", StepExecutorFactory.requiresInputText(StepType.WAIT))
    }

    @Test
    fun testRequiresWaitDuration() {
        assertTrue("等待步驟應該需要等待時間", StepExecutorFactory.requiresWaitDuration(StepType.WAIT))
        assertFalse("點擊步驟不應該需要等待時間", StepExecutorFactory.requiresWaitDuration(StepType.CLICK))
        assertFalse("輸入步驟不應該需要等待時間", StepExecutorFactory.requiresWaitDuration(StepType.INPUT_TEXT))
    }

    @Test
    fun testGetDefaultStepConfig() {
        val clickConfig = StepExecutorFactory.getDefaultStepConfig(StepType.CLICK)
        assertEquals("點擊步驟默認重試次數應該是3", 3, clickConfig["retryCount"])
        assertEquals("點擊步驟默認超時時間應該是10000", 10000L, clickConfig["timeoutMs"])
        assertEquals("點擊步驟默認不應該是可選的", false, clickConfig["isOptional"])

        val waitConfig = StepExecutorFactory.getDefaultStepConfig(StepType.WAIT)
        assertEquals("等待步驟默認重試次數應該是1", 1, waitConfig["retryCount"])
        assertEquals("等待步驟默認等待時間應該是1000", 1000L, waitConfig["waitDuration"])

        val inputConfig = StepExecutorFactory.getDefaultStepConfig(StepType.INPUT_TEXT)
        assertEquals("輸入步驟默認輸入文字應該是空字符串", "", inputConfig["inputText"])
    }

    @Test
    fun testGetSupportedStepTypes() {
        val supportedTypes = StepExecutorFactory.getSupportedStepTypes()
        
        assertTrue("應該支持點擊步驟", supportedTypes.contains(StepType.CLICK))
        assertTrue("應該支持長按步驟", supportedTypes.contains(StepType.LONG_CLICK))
        assertTrue("應該支持輸入步驟", supportedTypes.contains(StepType.INPUT_TEXT))
        assertTrue("應該支持等待步驟", supportedTypes.contains(StepType.WAIT))
        assertTrue("應該支持滑動步驟", supportedTypes.contains(StepType.SWIPE))
        assertTrue("應該支持向上滑動步驟", supportedTypes.contains(StepType.SWIPE_UP))
        assertTrue("應該支持向下滑動步驟", supportedTypes.contains(StepType.SWIPE_DOWN))
        assertTrue("應該支持向左滑動步驟", supportedTypes.contains(StepType.SWIPE_LEFT))
        assertTrue("應該支持向右滑動步驟", supportedTypes.contains(StepType.SWIPE_RIGHT))
        assertTrue("應該支持滾動步驟", supportedTypes.contains(StepType.SCROLL))
        assertTrue("應該支持返回步驟", supportedTypes.contains(StepType.BACK))
        assertTrue("應該支持主頁步驟", supportedTypes.contains(StepType.HOME))
        
        assertEquals("支持的步驟類型數量應該是12", 12, supportedTypes.size)
    }
}
