package com.example.autolaunch.automation

import com.example.autolaunch.model.AutomationStep
import com.example.autolaunch.model.StepType
import com.example.autolaunch.model.TargetType
import org.junit.Test
import org.junit.Assert.*

/**
 * AutomationResult 單元測試
 */
class AutomationResultTest {

    private fun createTestStep(id: Long = 1L, stepOrder: Int = 1): AutomationStep {
        return AutomationStep(
            id = id,
            scheduleId = 1L,
            stepOrder = stepOrder,
            stepType = StepType.CLICK.value,
            targetType = TargetType.TEXT.value,
            targetText = "測試按鈕"
        )
    }

    @Test
    fun testSuccessResult() {
        val step1 = createTestStep(1L, 1)
        val step2 = createTestStep(2L, 2)
        
        val stepResults = listOf(
            StepExecutionResult.success(step1, 1000L),
            StepExecutionResult.success(step2, 1500L)
        )
        
        val result = AutomationResult.success("執行成功", stepResults)
        
        assertTrue("成功結果應該標記為成功", result.success)
        assertEquals("成功結果訊息不正確", "執行成功", result.message)
        assertEquals("步驟結果數量不正確", 2, result.stepResults.size)
        assertEquals("總執行時間不正確", 2500L, result.executionTime)
        assertEquals("成功步驟數量不正確", 2, result.getSuccessCount())
        assertEquals("失敗步驟數量不正確", 0, result.getFailureCount())
        assertEquals("成功率不正確", 1.0f, result.getSuccessRate(), 0.01f)
    }

    @Test
    fun testFailureResult() {
        val step1 = createTestStep(1L, 1)
        val step2 = createTestStep(2L, 2)
        
        val stepResults = listOf(
            StepExecutionResult.success(step1, 1000L),
            StepExecutionResult.failure(step2, "執行失敗", 500L)
        )
        
        val result = AutomationResult.failure("執行失敗", stepResults)
        
        assertFalse("失敗結果應該標記為失敗", result.success)
        assertEquals("失敗結果訊息不正確", "執行失敗", result.message)
        assertEquals("步驟結果數量不正確", 2, result.stepResults.size)
        assertEquals("總執行時間不正確", 1500L, result.executionTime)
        assertEquals("成功步驟數量不正確", 1, result.getSuccessCount())
        assertEquals("失敗步驟數量不正確", 1, result.getFailureCount())
        assertEquals("成功率不正確", 0.5f, result.getSuccessRate(), 0.01f)
    }

    @Test
    fun testPartialResult() {
        val step1 = createTestStep(1L, 1)
        val step2 = createTestStep(2L, 2)
        val step3 = createTestStep(3L, 3)
        
        val stepResults = listOf(
            StepExecutionResult.success(step1, 1000L),
            StepExecutionResult.failure(step2, "執行失敗", 500L),
            StepExecutionResult.success(step3, 800L)
        )
        
        val result = AutomationResult.partial("部分成功", stepResults)
        
        assertFalse("部分結果應該標記為失敗", result.success)
        assertEquals("部分結果訊息不正確", "部分成功", result.message)
        assertEquals("步驟結果數量不正確", 3, result.stepResults.size)
        assertEquals("總執行時間不正確", 2300L, result.executionTime)
        assertEquals("成功步驟數量不正確", 2, result.getSuccessCount())
        assertEquals("失敗步驟數量不正確", 1, result.getFailureCount())
        assertEquals("總步驟數量不正確", 3, result.getTotalSteps())
        assertEquals("成功率不正確", 0.667f, result.getSuccessRate(), 0.01f)
    }

    @Test
    fun testEmptyResult() {
        val result = AutomationResult.success("無步驟", emptyList())
        
        assertTrue("空結果應該標記為成功", result.success)
        assertEquals("空結果訊息不正確", "無步驟", result.message)
        assertEquals("空結果步驟數量應該為0", 0, result.stepResults.size)
        assertEquals("空結果執行時間應該為0", 0L, result.executionTime)
        assertEquals("空結果成功步驟數量應該為0", 0, result.getSuccessCount())
        assertEquals("空結果失敗步驟數量應該為0", 0, result.getFailureCount())
        assertEquals("空結果總步驟數量應該為0", 0, result.getTotalSteps())
        assertEquals("空結果成功率應該為0", 0.0f, result.getSuccessRate(), 0.01f)
    }
}

/**
 * StepExecutionResult 單元測試
 */
class StepExecutionResultTest {

    private fun createTestStep(): AutomationStep {
        return AutomationStep(
            id = 1L,
            scheduleId = 1L,
            stepOrder = 1,
            stepType = StepType.CLICK.value,
            targetType = TargetType.TEXT.value,
            targetText = "測試按鈕",
            isOptional = false
        )
    }

    @Test
    fun testSuccessResult() {
        val step = createTestStep()
        val result = StepExecutionResult.success(step, 1000L)
        
        assertTrue("成功結果應該標記為成功", result.success)
        assertNull("成功結果不應該有錯誤訊息", result.errorMessage)
        assertEquals("執行時間不正確", 1000L, result.executionTime)
        assertEquals("步驟顯示名稱不正確", "點擊 「測試按鈕」", result.getStepDisplayName())
        assertEquals("步驟類型不正確", StepType.CLICK, result.getStepType())
        assertFalse("步驟不應該是可選的", result.isOptionalStep())
    }

    @Test
    fun testFailureResult() {
        val step = createTestStep()
        val result = StepExecutionResult.failure(step, "執行失敗", 500L)
        
        assertFalse("失敗結果應該標記為失敗", result.success)
        assertEquals("錯誤訊息不正確", "執行失敗", result.errorMessage)
        assertEquals("執行時間不正確", 500L, result.executionTime)
        assertEquals("步驟顯示名稱不正確", "點擊 「測試按鈕」", result.getStepDisplayName())
        assertEquals("步驟類型不正確", StepType.CLICK, result.getStepType())
        assertFalse("步驟不應該是可選的", result.isOptionalStep())
    }

    @Test
    fun testOptionalStep() {
        val optionalStep = createTestStep().copy(isOptional = true)
        val result = StepExecutionResult.success(optionalStep, 1000L)
        
        assertTrue("可選步驟應該被正確識別", result.isOptionalStep())
    }

    @Test
    fun testTimestamp() {
        val step = createTestStep()
        val beforeTime = System.currentTimeMillis()
        val result = StepExecutionResult.success(step, 1000L)
        val afterTime = System.currentTimeMillis()
        
        assertTrue("時間戳應該在合理範圍內", result.timestamp >= beforeTime && result.timestamp <= afterTime)
    }
}

/**
 * ValidationResult 單元測試
 */
class ValidationResultTest {

    @Test
    fun testValidResult() {
        val result = ValidationResult.valid("配置正確")
        
        assertTrue("有效結果應該標記為有效", result.isValid)
        assertEquals("有效結果訊息不正確", "配置正確", result.message)
        assertEquals("有效結果問題數量應該為0", 0, result.getIssueCount())
        assertEquals("有效結果問題列表應該為空", "", result.getFormattedIssues())
    }

    @Test
    fun testInvalidResult() {
        val issues = listOf("步驟1配置錯誤", "步驟2缺少目標", "步驟3超時設置無效")
        val result = ValidationResult.invalid(issues)
        
        assertFalse("無效結果應該標記為無效", result.isValid)
        assertEquals("無效結果訊息不正確", "發現 3 個問題", result.message)
        assertEquals("無效結果問題數量不正確", 3, result.getIssueCount())
        assertEquals("無效結果問題列表不正確", issues, result.issues)
        
        val formattedIssues = result.getFormattedIssues()
        assertTrue("格式化問題列表應該包含第一個問題", formattedIssues.contains("• 步驟1配置錯誤"))
        assertTrue("格式化問題列表應該包含第二個問題", formattedIssues.contains("• 步驟2缺少目標"))
        assertTrue("格式化問題列表應該包含第三個問題", formattedIssues.contains("• 步驟3超時設置無效"))
    }

    @Test
    fun testEmptyInvalidResult() {
        val result = ValidationResult.invalid(emptyList())
        
        assertFalse("空問題列表的無效結果應該標記為無效", result.isValid)
        assertEquals("空問題列表的無效結果訊息不正確", "發現 0 個問題", result.message)
        assertEquals("空問題列表的無效結果問題數量應該為0", 0, result.getIssueCount())
        assertEquals("空問題列表的無效結果格式化問題應該為空", "", result.getFormattedIssues())
    }
}
