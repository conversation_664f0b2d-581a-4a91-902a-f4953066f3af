package com.example.autolaunch

import android.content.pm.PackageManager
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.example.autolaunch.R
import com.example.autolaunch.databinding.ItemScheduleBinding
import com.example.autolaunch.model.Schedule
import com.example.autolaunch.model.ScheduleType
import com.example.autolaunch.utils.TimeFormatUtils
import com.example.autolaunch.utils.UrlUtils
import com.example.autolaunch.utils.TextIconGenerator

/**
 * 排程列表的RecyclerView Adapter
 */
class ScheduleAdapter(
    private val onItemClick: (Schedule) -> Unit,
    private val onToggleEnabled: (Schedule, Boolean) -> Unit
) : ListAdapter<Schedule, ScheduleAdapter.ScheduleViewHolder>(ScheduleDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ScheduleViewHolder {
        val binding = ItemScheduleBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ScheduleViewHolder(binding, onItemClick, onToggleEnabled)
    }

    override fun onBindViewHolder(holder: ScheduleViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    class ScheduleViewHolder(
        private val binding: ItemScheduleBinding,
        private val onItemClick: (Schedule) -> Unit,
        private val onToggleEnabled: (Schedule, Boolean) -> Unit
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(schedule: Schedule) {
            binding.apply {
                // 設定任務名稱（優先顯示自定義名稱，否則顯示App名稱或URL標題）
                tvAppName.text = schedule.getDisplayName()

                // 設定副標題（根據排程類型顯示不同內容）
                when (schedule.getScheduleTypeEnum()) {
                    ScheduleType.APP -> {
                        if (!schedule.taskName.isNullOrBlank()) {
                            tvAppNameSubtitle.text = schedule.appName
                            tvAppNameSubtitle.visibility = View.VISIBLE
                        } else {
                            tvAppNameSubtitle.visibility = View.GONE
                        }
                    }
                    ScheduleType.URL -> {
                        if (!schedule.taskName.isNullOrBlank()) {
                            // 如果有自定義任務名稱，顯示域名作為副標題
                            tvAppNameSubtitle.text = UrlUtils.extractDomain(schedule.url) ?: schedule.url
                            tvAppNameSubtitle.visibility = View.VISIBLE
                        } else if (!schedule.urlTitle.isNullOrBlank()) {
                            // 如果有 URL 標題但沒有自定義任務名稱，顯示域名作為副標題
                            tvAppNameSubtitle.text = UrlUtils.extractDomain(schedule.url) ?: schedule.url
                            tvAppNameSubtitle.visibility = View.VISIBLE
                        } else {
                            tvAppNameSubtitle.visibility = View.GONE
                        }
                    }
                }
                
                // 設定排程時間
                tvScheduleTime.text = schedule.getFormattedTime()
                
                // 設定重複模式 - 保留簡短顯示
                val repeatModeEnum = schedule.getRepeatModeEnum()
                when (repeatModeEnum) {
                    com.example.autolaunch.model.RepeatMode.ONCE -> {
                        tvRepeatMode.text = "單次"
                    }
                    com.example.autolaunch.model.RepeatMode.DAILY -> {
                        tvRepeatMode.text = "每日"
                    }
                    com.example.autolaunch.model.RepeatMode.WEEKLY -> {
                        tvRepeatMode.text = "每週"
                        val daysDisplay = schedule.getDaysOfWeekDisplayName(binding.root.context)
                        if (daysDisplay.isNotEmpty()) {
                            tvRepeatMode.text = daysDisplay
                        }
                    }
                    com.example.autolaunch.model.RepeatMode.MONTHLY -> {
                        tvRepeatMode.text = "每月"
                    }
                }
                tvRepeatMode.visibility = View.VISIBLE

                // 隱藏重複模式詳細說明
                tvRepeatModeDetail.visibility = View.GONE
                
                // 設定啟用狀態開關
                switchEnabled.isChecked = schedule.isEnabled
                
                // 載入圖標（根據排程類型）
                when (schedule.getScheduleTypeEnum()) {
                    ScheduleType.APP -> {
                        try {
                            val packageManager = itemView.context.packageManager
                            val appIcon = packageManager.getApplicationIcon(schedule.packageName!!)
                            ivAppIcon.setImageDrawable(appIcon)
                        } catch (e: PackageManager.NameNotFoundException) {
                            // 如果找不到應用程式，使用預設圖標
                            ivAppIcon.setImageResource(R.mipmap.ic_launcher)
                        } catch (e: Exception) {
                            // 其他異常也使用預設圖標
                            ivAppIcon.setImageResource(R.mipmap.ic_launcher)
                        }
                    }
                    ScheduleType.URL -> {
                        // URL 排程載入網站圖標
                        val faviconUrl = UrlUtils.generateFaviconUrl(schedule.url)
                        if (!faviconUrl.isNullOrEmpty()) {
                            // 嘗試載入網站 favicon，失敗時使用文字圖標
                            val fallbackIcon = TextIconGenerator.generateWebIcon(itemView.context, schedule.url)
                            Glide.with(itemView.context)
                                .load(faviconUrl)
                                .diskCacheStrategy(DiskCacheStrategy.ALL)
                                .placeholder(fallbackIcon)
                                .error(fallbackIcon)
                                .fallback(fallbackIcon)
                                .into(ivAppIcon)
                        } else {
                            // 如果無法生成圖標 URL，直接使用文字圖標
                            val textIcon = TextIconGenerator.generateWebIcon(itemView.context, schedule.url)
                            ivAppIcon.setImageDrawable(textIcon)
                        }
                    }
                }
                
                // 設定點擊事件
                root.setOnClickListener {
                    onItemClick(schedule)
                }
                
                // 設定開關切換事件
                switchEnabled.setOnCheckedChangeListener { _, isChecked ->
                    if (isChecked != schedule.isEnabled) {
                        onToggleEnabled(schedule, isChecked)
                    }
                }
                
                // 簡化的執行狀態顯示
                if (schedule.isEnabled) {
                    val nextTime = schedule.calculateNextExecuteTime()
                    if (nextTime != null) {
                        // 顯示下次執行時間
                        tvNextExecution.text = itemView.context.getString(R.string.label_next_execution, TimeFormatUtils.formatFutureTime(itemView.context, nextTime))
                        tvNextExecution.visibility = View.VISIBLE
                    } else {
                        if (schedule.getRepeatModeEnum() == com.example.autolaunch.model.RepeatMode.ONCE) {
                            // 單次執行已完成
                            tvNextExecution.text = itemView.context.getString(R.string.status_completed)
                            tvNextExecution.visibility = View.VISIBLE
                        } else {
                            tvNextExecution.visibility = View.GONE
                        }
                    }
                } else {
                    // 顯示已禁用狀態
                    tvNextExecution.text = itemView.context.getString(R.string.status_disabled)
                    tvNextExecution.visibility = View.VISIBLE
                }
                
                // 顯示上次執行時間
                val lastExecutionText = TimeFormatUtils.formatRelativeTime(itemView.context, schedule.lastExecutedTime)
                if (lastExecutionText != "從未執行") {
                    tvLastExecutionTime.text = "上次: $lastExecutionText"
                    tvLastExecutionTime.visibility = View.VISIBLE
                } else {
                    tvLastExecutionTime.text = "尚未執行"
                    tvLastExecutionTime.visibility = View.VISIBLE
                }
                
                // 隱藏舊的布局容器（保持兼容性）
                layoutNextExecution.visibility = View.GONE
                layoutLastExecution.visibility = View.GONE
                tvLastExecution.visibility = View.GONE
            }
        }
    }
}

/**
 * DiffUtil.Callback for Schedule list
 */
class ScheduleDiffCallback : DiffUtil.ItemCallback<Schedule>() {
    override fun areItemsTheSame(oldItem: Schedule, newItem: Schedule): Boolean {
        return oldItem.id == newItem.id
    }

    override fun areContentsTheSame(oldItem: Schedule, newItem: Schedule): Boolean {
        return oldItem == newItem
    }
} 