package com.example.autolaunch.adapters

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.example.autolaunch.fragments.WelcomePageFragment

/**
 * 歡迎導覽頁面適配器
 * 管理多個導覽頁面的顯示
 */
class WelcomePagerAdapter(fragmentActivity: FragmentActivity) : FragmentStateAdapter(fragmentActivity) {

    // 導覽頁面數據
    private val welcomePages = listOf(
        WelcomePageData(
            title = "歡迎使用 AutoLaunch",
            description = "智能排程，自動啟動\n讓您的手機更聰明地工作",
            iconRes = "ic_welcome_rocket",
            backgroundColor = "#4CAF50"
        ),
        WelcomePageData(
            title = "排程應用程式",
            description = "設定時間自動啟動您喜愛的應用程式\n支援單次、每日、每週重複模式",
            iconRes = "ic_welcome_app",
            backgroundColor = "#2196F3"
        ),
        WelcomePageData(
            title = "開啟網頁連結",
            description = "不只是應用程式，還能定時開啟網頁\n新聞、社群媒體、工作平台一鍵直達",
            iconRes = "ic_welcome_web",
            backgroundColor = "#FF9800"
        ),
        WelcomePageData(
            title = "權限設定",
            description = "為了確保排程正常運作\n需要設定一些必要的系統權限",
            iconRes = "ic_welcome_permission",
            backgroundColor = "#9C27B0"
        ),
        WelcomePageData(
            title = "開始使用",
            description = "一切準備就緒！\n現在就開始創建您的第一個排程吧",
            iconRes = "ic_welcome_start",
            backgroundColor = "#4CAF50"
        )
    )

    override fun getItemCount(): Int = welcomePages.size

    override fun createFragment(position: Int): Fragment {
        return WelcomePageFragment.newInstance(welcomePages[position])
    }

    /**
     * 獲取指定位置的頁面數據
     */
    fun getPageData(position: Int): WelcomePageData? {
        return if (position in 0 until welcomePages.size) {
            welcomePages[position]
        } else {
            null
        }
    }
}

/**
 * 歡迎頁面數據類
 */
data class WelcomePageData(
    val title: String,
    val description: String,
    val iconRes: String,
    val backgroundColor: String
) : java.io.Serializable
