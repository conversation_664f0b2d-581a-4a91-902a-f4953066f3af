package com.example.autolaunch.adapters

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.example.autolaunch.R
import com.example.autolaunch.fragment.QAFragment
import com.example.autolaunch.fragment.TutorialFragment

/**
 * 幫助頁面的ViewPager適配器
 * 管理使用教學和Q&A兩個Fragment
 */
class HelpPagerAdapter(private val fragmentActivity: FragmentActivity) : FragmentStateAdapter(fragmentActivity) {
    
    companion object {
        const val TUTORIAL_TAB = 0
        const val QA_TAB = 1
        const val TAB_COUNT = 2
    }
    
    override fun getItemCount(): Int = TAB_COUNT
    
    override fun createFragment(position: Int): Fragment {
        return when (position) {
            TUTORIAL_TAB -> TutorialFragment.newInstance()
            QA_TAB -> QAFragment.newInstance()
            else -> throw IllegalArgumentException("Invalid position: $position")
        }
    }
    
    /**
     * 獲取Tab標題
     */
    fun getTabTitle(position: Int): String {
        return when (position) {
            TUTORIAL_TAB -> fragmentActivity.getString(R.string.tab_tutorial)
            QA_TAB -> fragmentActivity.getString(R.string.tab_qa)
            else -> ""
        }
    }
}
