package com.example.autolaunch.adapters

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.example.autolaunch.databinding.ItemLanguageOptionBinding
import com.example.autolaunch.utils.LanguageManager

/**
 * 語言選項適配器
 */
class LanguageAdapter(
    private val languages: List<LanguageManager.Language>,
    private val currentLanguage: String,
    private val onLanguageSelected: (String) -> Unit
) : RecyclerView.Adapter<LanguageAdapter.LanguageViewHolder>() {

    private var selectedPosition = languages.indexOfFirst { it.code == currentLanguage }

    inner class LanguageViewHolder(private val binding: ItemLanguageOptionBinding) : 
        RecyclerView.ViewHolder(binding.root) {

        fun bind(language: LanguageManager.Language, isSelected: Boolean) {
            binding.apply {
                tvLanguageName.text = language.displayName
                
                // 顯示原生語言名稱（如果與顯示名稱不同）
                if (language.nativeName != language.displayName && 
                    language.code != LanguageManager.LANGUAGE_FOLLOW_SYSTEM) {
                    tvLanguageNative.text = language.nativeName
                    tvLanguageNative.visibility = View.VISIBLE
                } else {
                    tvLanguageNative.visibility = View.GONE
                }
                
                radioButton.isChecked = isSelected
                
                root.setOnClickListener {
                    if (adapterPosition != RecyclerView.NO_POSITION) {
                        val oldPosition = selectedPosition
                        selectedPosition = adapterPosition
                        
                        // 更新選中狀態
                        notifyItemChanged(oldPosition)
                        notifyItemChanged(selectedPosition)
                        
                        // 回調選擇事件
                        onLanguageSelected(language.code)
                    }
                }
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): LanguageViewHolder {
        val binding = ItemLanguageOptionBinding.inflate(
            LayoutInflater.from(parent.context), 
            parent, 
            false
        )
        return LanguageViewHolder(binding)
    }

    override fun onBindViewHolder(holder: LanguageViewHolder, position: Int) {
        holder.bind(languages[position], position == selectedPosition)
    }

    override fun getItemCount(): Int = languages.size
}
