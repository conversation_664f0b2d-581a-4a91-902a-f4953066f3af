package com.example.autolaunch

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.example.autolaunch.model.AppInfo
import com.example.autolaunch.model.AppRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * 應用程式選擇器 ViewModel
 * 管理應用程式列表的載入、搜尋和狀態
 */
class AppSelectorViewModel(application: Application) : AndroidViewModel(application) {
    
    private val appRepository = AppRepository(application)
    
    // 應用程式列表狀態
    private val _apps = MutableStateFlow<List<AppInfo>>(emptyList())
    val apps: StateFlow<List<AppInfo>> = _apps.asStateFlow()
    
    // 載入狀態
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    // 錯誤狀態
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()
    
    // 搜尋關鍵字
    private val _searchQuery = MutableStateFlow("")
    val searchQuery: StateFlow<String> = _searchQuery.asStateFlow()
    
    // 完整的應用程式列表（用於搜尋）
    private var allApps: List<AppInfo> = emptyList()
    
    init {
        loadApps()
    }
    
    /**
     * 載入所有可啟動的應用程式
     */
    private fun loadApps() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null
                
                allApps = appRepository.getAllLaunchableApps()
                _apps.value = allApps
                
            } catch (e: Exception) {
                _error.value = "載入應用程式列表失敗: ${e.message}"
                _apps.value = emptyList()
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 搜尋應用程式
     * @param query 搜尋關鍵字
     */
    fun searchApps(query: String) {
        _searchQuery.value = query
        
        if (query.isBlank()) {
            _apps.value = allApps
        } else {
            val filteredApps = allApps.filter { app ->
                app.appName.contains(query, ignoreCase = true) ||
                app.packageName.contains(query, ignoreCase = true)
            }
            _apps.value = filteredApps
        }
    }
    
    /**
     * 重新載入應用程式列表
     */
    fun refreshApps() {
        loadApps()
    }
    
    /**
     * 清除錯誤狀態
     */
    fun clearError() {
        _error.value = null
    }
    
    /**
     * 根據包名獲取應用程式資訊
     * @param packageName 應用程式包名
     * @return 應用程式資訊，如果找不到則返回 null
     */
    suspend fun getAppByPackageName(packageName: String): AppInfo? {
        return appRepository.getAppInfoByPackageName(packageName)
    }
} 