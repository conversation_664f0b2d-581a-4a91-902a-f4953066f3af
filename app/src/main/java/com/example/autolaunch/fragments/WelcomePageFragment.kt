package com.example.autolaunch.fragments

import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import com.example.autolaunch.R
import com.example.autolaunch.adapters.WelcomePageData
import com.example.autolaunch.databinding.FragmentWelcomePageBinding

/**
 * 歡迎導覽單頁Fragment
 * 顯示單個導覽頁面的內容
 */
class WelcomePageFragment : Fragment() {

    private var _binding: FragmentWelcomePageBinding? = null
    private val binding get() = _binding!!

    companion object {
        private const val ARG_PAGE_DATA = "page_data"

        fun newInstance(pageData: WelcomePageData): WelcomePageFragment {
            val fragment = WelcomePageFragment()
            val args = Bundle().apply {
                putSerializable(ARG_PAGE_DATA, pageData)
            }
            fragment.arguments = args
            return fragment
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentWelcomePageBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        @Suppress("DEPRECATION")
        val pageData = arguments?.getSerializable(ARG_PAGE_DATA) as? WelcomePageData
        pageData?.let { setupPageContent(it) }
    }

    private fun setupPageContent(pageData: WelcomePageData) {
        // 根據圖示資源名稱決定頁面內容
        val (titleRes, descriptionRes) = when (pageData.iconRes) {
            "ic_welcome_rocket" -> Pair(R.string.welcome_page_1_title, R.string.welcome_page_1_description)
            "ic_welcome_app" -> Pair(R.string.welcome_page_2_title, R.string.welcome_page_2_description)
            "ic_welcome_web" -> Pair(R.string.welcome_page_3_title, R.string.welcome_page_3_description)
            "ic_welcome_permission" -> Pair(R.string.welcome_page_4_title, R.string.welcome_page_4_description)
            "ic_welcome_start" -> Pair(R.string.welcome_page_5_title, R.string.welcome_page_5_description)
            else -> Pair(R.string.welcome_page_1_title, R.string.welcome_page_1_description)
        }

        // 設定標題和描述使用字串資源
        binding.tvTitle.text = getString(titleRes)
        binding.tvDescription.text = getString(descriptionRes)

        // 設定背景顏色
        try {
            val backgroundColor = Color.parseColor(pageData.backgroundColor)
            binding.root.setBackgroundColor(backgroundColor)
        } catch (e: IllegalArgumentException) {
            // 如果顏色解析失敗，使用預設顏色
            binding.root.setBackgroundColor(Color.parseColor("#4CAF50"))
        }

        // 設定圖示
        setupIcon(pageData.iconRes)
    }

    private fun setupIcon(iconRes: String) {
        // 根據圖示名稱設定對應的圖示
        val drawableRes = when (iconRes) {
            "ic_welcome_rocket" -> R.drawable.ic_rocket_launch_24
            "ic_welcome_app" -> R.drawable.ic_apps_24
            "ic_welcome_web" -> R.drawable.ic_language_24
            "ic_welcome_permission" -> R.drawable.ic_security_24
            "ic_welcome_start" -> R.drawable.ic_play_arrow_24
            else -> R.drawable.ic_rocket_launch_24 // 預設圖示
        }

        binding.ivIcon.setImageResource(drawableRes)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
