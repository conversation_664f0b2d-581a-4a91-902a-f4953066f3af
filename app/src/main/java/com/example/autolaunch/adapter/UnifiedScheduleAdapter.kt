package com.example.autolaunch.adapter

import android.content.pm.PackageManager
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.example.autolaunch.R
import com.example.autolaunch.databinding.ItemScheduleBinding
import com.example.autolaunch.databinding.ItemScheduleSectionHeaderBinding
import com.example.autolaunch.model.Schedule
import com.example.autolaunch.model.ScheduleType
import com.example.autolaunch.model.RepeatMode
import com.example.autolaunch.utils.TimeFormatUtils
import com.example.autolaunch.utils.UrlUtils
import com.example.autolaunch.utils.TextIconGenerator

/**
 * 統一的排程適配器，支持多種視圖類型
 * 解決多個 RecyclerView 嵌套滾動的問題
 */
class UnifiedScheduleAdapter(
    private val onItemClick: (Schedule) -> Unit,
    private val onToggleEnabled: (Schedule, Boolean) -> Unit,
    private val onStartDrag: ((RecyclerView.ViewHolder) -> Unit)? = null
) : ListAdapter<UnifiedScheduleAdapter.ScheduleItem, RecyclerView.ViewHolder>(ScheduleItemDiffCallback()) {

    companion object {
        internal const val VIEW_TYPE_HEADER = 0
        internal const val VIEW_TYPE_SCHEDULE = 1
    }

    /**
     * 排程項目的封裝類，支持標題和排程兩種類型
     */
    sealed class ScheduleItem {
        data class Header(
            val title: String,
            val count: Int,
            val icon: Int
        ) : ScheduleItem()
        
        data class ScheduleData(val schedule: Schedule) : ScheduleItem()
    }

    override fun getItemViewType(position: Int): Int {
        return when (getItem(position)) {
            is ScheduleItem.Header -> VIEW_TYPE_HEADER
            is ScheduleItem.ScheduleData -> VIEW_TYPE_SCHEDULE
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            VIEW_TYPE_HEADER -> {
                val binding = ItemScheduleSectionHeaderBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
                HeaderViewHolder(binding)
            }
            VIEW_TYPE_SCHEDULE -> {
                val binding = ItemScheduleBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
                ScheduleViewHolder(binding)
            }
            else -> throw IllegalArgumentException("Unknown view type: $viewType")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (val item = getItem(position)) {
            is ScheduleItem.Header -> {
                (holder as HeaderViewHolder).bind(item)
            }
            is ScheduleItem.ScheduleData -> {
                (holder as ScheduleViewHolder).bind(item.schedule)
            }
        }
    }

    /**
     * 標題視圖持有者
     */
    inner class HeaderViewHolder(
        private val binding: ItemScheduleSectionHeaderBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(header: ScheduleItem.Header) {
            binding.apply {
                tvSectionTitle.text = header.title
                tvSectionCount.text = header.count.toString()
                ivSectionIcon.setImageResource(header.icon)
            }
        }
    }

    /**
     * 排程視圖持有者
     */
    inner class ScheduleViewHolder(
        private val binding: ItemScheduleBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(schedule: Schedule) {
            binding.apply {
                // 設定主標題（任務名稱或應用程式名稱）
                when (schedule.getScheduleTypeEnum()) {
                    ScheduleType.APP -> {
                        tvAppName.text = if (!schedule.taskName.isNullOrBlank()) {
                            schedule.taskName
                        } else {
                            schedule.appName ?: binding.root.context.getString(R.string.schedule_unknown_app)
                        }
                    }
                    ScheduleType.URL -> {
                        tvAppName.text = if (!schedule.taskName.isNullOrBlank()) {
                            schedule.taskName
                        } else if (!schedule.urlTitle.isNullOrBlank()) {
                            schedule.urlTitle
                        } else {
                            UrlUtils.extractDisplayName(schedule.url) ?: binding.root.context.getString(R.string.schedule_type_web)
                        }
                    }
                }

                // 設定副標題（根據排程類型顯示不同內容）
                when (schedule.getScheduleTypeEnum()) {
                    ScheduleType.APP -> {
                        if (!schedule.taskName.isNullOrBlank()) {
                            tvAppNameSubtitle.text = schedule.appName
                            tvAppNameSubtitle.visibility = android.view.View.VISIBLE
                        } else {
                            tvAppNameSubtitle.visibility = android.view.View.GONE
                        }
                    }
                    ScheduleType.URL -> {
                        if (!schedule.taskName.isNullOrBlank()) {
                            tvAppNameSubtitle.text = UrlUtils.extractDomain(schedule.url) ?: schedule.url
                            tvAppNameSubtitle.visibility = android.view.View.VISIBLE
                        } else if (!schedule.urlTitle.isNullOrBlank()) {
                            tvAppNameSubtitle.text = UrlUtils.extractDomain(schedule.url) ?: schedule.url
                            tvAppNameSubtitle.visibility = android.view.View.VISIBLE
                        } else {
                            tvAppNameSubtitle.visibility = android.view.View.GONE
                        }
                    }
                }
                
                // 設定排程時間
                tvScheduleTime.text = schedule.getFormattedTime()

                // 設定重複模式 - 保留簡短顯示
                val context = binding.root.context
                val repeatModeEnum = schedule.getRepeatModeEnum()
                when (repeatModeEnum) {
                    RepeatMode.ONCE -> {
                        tvRepeatMode.text = context.getString(R.string.repeat_mode_once_short)
                    }
                    RepeatMode.DAILY -> {
                        tvRepeatMode.text = context.getString(R.string.repeat_mode_daily_short)
                    }
                    RepeatMode.WEEKLY -> {
                        tvRepeatMode.text = context.getString(R.string.repeat_mode_weekly_short)
                        val daysDisplay = schedule.getDaysOfWeekDisplayName(context)
                        if (daysDisplay.isNotEmpty()) {
                            tvRepeatMode.text = daysDisplay
                        }
                    }
                    RepeatMode.MONTHLY -> {
                        tvRepeatMode.text = context.getString(R.string.repeat_mode_monthly_short)
                    }
                }
                tvRepeatMode.visibility = android.view.View.VISIBLE

                // 隱藏重複模式詳細說明
                try {
                    tvRepeatModeDetail.visibility = android.view.View.GONE
                } catch (e: Exception) {
                    // 如果 tvRepeatModeDetail 不存在，忽略錯誤
                }

                // 設定下次執行時間
                if (schedule.isEnabled) {
                    val nextTime = schedule.calculateNextExecuteTime()
                    if (nextTime != null) {
                        val nextExecutionText = itemView.context.getString(R.string.label_next_execution,
                            TimeFormatUtils.formatFutureTime(itemView.context, nextTime))
                        tvNextExecution.text = nextExecutionText
                        tvNextExecution.visibility = android.view.View.VISIBLE
                    } else {
                        if (schedule.getRepeatModeEnum() == RepeatMode.ONCE) {
                            tvNextExecution.text = itemView.context.getString(R.string.status_completed)
                            tvNextExecution.visibility = android.view.View.VISIBLE
                        } else {
                            tvNextExecution.visibility = android.view.View.GONE
                        }
                    }
                } else {
                    tvNextExecution.text = itemView.context.getString(R.string.status_disabled)
                    tvNextExecution.visibility = android.view.View.VISIBLE
                }

                // 設定最後執行時間
                val lastExecutionText = TimeFormatUtils.formatRelativeTime(itemView.context, schedule.lastExecutedTime)
                if (lastExecutionText != "從未執行") {
                    tvLastExecution.text = "上次: $lastExecutionText"
                    tvLastExecution.visibility = android.view.View.VISIBLE
                } else {
                    tvLastExecution.text = "尚未執行"
                    tvLastExecution.visibility = android.view.View.VISIBLE
                }
                
                // 設定啟用狀態開關
                switchEnabled.isChecked = schedule.isEnabled
                
                // 載入圖標（根據排程類型）
                when (schedule.getScheduleTypeEnum()) {
                    ScheduleType.APP -> {
                        try {
                            val packageManager = itemView.context.packageManager
                            val appIcon = packageManager.getApplicationIcon(schedule.packageName!!)
                            ivAppIcon.setImageDrawable(appIcon)
                        } catch (e: PackageManager.NameNotFoundException) {
                            ivAppIcon.setImageResource(R.mipmap.ic_launcher)
                        } catch (e: Exception) {
                            ivAppIcon.setImageResource(R.mipmap.ic_launcher)
                        }
                    }
                    ScheduleType.URL -> {
                        val faviconUrl = UrlUtils.generateFaviconUrl(schedule.url)
                        if (!faviconUrl.isNullOrEmpty()) {
                            // 嘗試載入網站 favicon
                            val fallbackIcon = TextIconGenerator.generateWebIcon(itemView.context, schedule.url)
                            Glide.with(itemView.context)
                                .load(faviconUrl)
                                .diskCacheStrategy(DiskCacheStrategy.ALL)
                                .placeholder(fallbackIcon)
                                .error(fallbackIcon)
                                .fallback(fallbackIcon)
                                .into(ivAppIcon)
                        } else {
                            // 直接使用文字圖標
                            val textIcon = TextIconGenerator.generateWebIcon(itemView.context, schedule.url)
                            ivAppIcon.setImageDrawable(textIcon)
                        }
                    }
                }
                
                // 設定點擊事件
                root.setOnClickListener {
                    onItemClick(schedule)
                }

                // 設定長按事件觸發拖拽排序
                root.setOnLongClickListener {
                    onStartDrag?.invoke(this@ScheduleViewHolder)
                    true
                }

                // 設定開關切換事件
                switchEnabled.setOnCheckedChangeListener { _, isChecked ->
                    onToggleEnabled(schedule, isChecked)
                }
            }
        }
    }

    /**
     * 移動項目位置（用於拖拽排序）
     * @param fromPosition 起始位置
     * @param toPosition 目標位置
     */
    fun moveItem(fromPosition: Int, toPosition: Int) {
        val currentList = currentList.toMutableList()

        // 只允許在同一類型的項目之間移動（排程項目之間）
        val fromItem = currentList[fromPosition]
        val toItem = currentList[toPosition]

        if (fromItem is ScheduleItem.ScheduleData && toItem is ScheduleItem.ScheduleData) {
            val item = currentList.removeAt(fromPosition)
            currentList.add(toPosition, item)
            submitList(currentList)
        }
    }

    /**
     * 獲取當前排程項目的順序（僅排程數據，不包括標題）
     */
    fun getCurrentScheduleOrder(): List<Schedule> {
        return currentList.filterIsInstance<ScheduleItem.ScheduleData>()
            .map { it.schedule }
    }

    /**
     * DiffUtil 回調
     */
    private class ScheduleItemDiffCallback : DiffUtil.ItemCallback<ScheduleItem>() {
        override fun areItemsTheSame(oldItem: ScheduleItem, newItem: ScheduleItem): Boolean {
            return when {
                oldItem is ScheduleItem.Header && newItem is ScheduleItem.Header ->
                    oldItem.title == newItem.title
                oldItem is ScheduleItem.ScheduleData && newItem is ScheduleItem.ScheduleData ->
                    oldItem.schedule.id == newItem.schedule.id
                else -> false
            }
        }

        override fun areContentsTheSame(oldItem: ScheduleItem, newItem: ScheduleItem): Boolean {
            return oldItem == newItem
        }
    }
}
