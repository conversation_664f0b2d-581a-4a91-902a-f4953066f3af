package com.example.autolaunch.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.autolaunch.R
import com.example.autolaunch.databinding.ItemAutomationStepBinding
import com.example.autolaunch.model.AutomationStep

/**
 * 自動化步驟列表適配器
 */
class AutomationStepAdapter(
    private val onStepClick: (AutomationStep) -> Unit,
    private val onStepToggle: (AutomationStep, Boolean) -> Unit,
    private val onStepEdit: (AutomationStep) -> Unit,
    private val onStepDelete: (AutomationStep) -> Unit,
    private val onStepMoreActions: (AutomationStep, View) -> Unit,
    private val onStartDrag: ((androidx.recyclerview.widget.RecyclerView.ViewHolder) -> Unit)? = null
) : ListAdapter<AutomationStep, AutomationStepAdapter.StepViewHolder>(StepDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): StepViewHolder {
        val binding = ItemAutomationStepBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return StepViewHolder(binding)
    }

    override fun onBindViewHolder(holder: StepViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class StepViewHolder(
        private val binding: ItemAutomationStepBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(step: AutomationStep) {
            binding.apply {
                // 設置步驟順序
                tvStepOrder.text = step.stepOrder.toString()
                
                // 設置步驟名稱
                tvStepName.text = if (!step.stepName.isNullOrBlank()) {
                    step.stepName
                } else {
                    step.getDisplayName(binding.root.context)
                }

                // 設置步驟詳情
                tvStepDetails.text = buildStepDetails(step)
                
                // 設置可選標記
                chipOptional.visibility = if (step.isOptional) View.VISIBLE else View.GONE
                
                // 設置啟用狀態
                switchEnabled.isChecked = step.isEnabled
                switchEnabled.setOnCheckedChangeListener { _, isChecked ->
                    onStepToggle(step, isChecked)
                }
                
                // 設置點擊事件（編輯）
                root.setOnClickListener {
                    onStepEdit(step)
                }

                // 移除長按事件，讓ItemTouchHelper處理拖拽
                // 長按拖拽功能由ItemTouchHelper處理

                // 設置更多操作按鈕
                btnMoreActions.setOnClickListener {
                    onStepMoreActions(step, it)
                }

                // 設置拖拽手柄觸摸監聽器
                ivDragHandle.setOnTouchListener { _, event ->
                    if (event.action == android.view.MotionEvent.ACTION_DOWN) {
                        onStartDrag?.invoke(this@StepViewHolder)
                    }
                    false
                }

                // 設置步驟順序背景顏色
                updateStepOrderBackground(step)
            }
        }
        
        private fun buildStepDetails(step: AutomationStep): String {
            val context = binding.root.context
            val details = mutableListOf<String>()

            // 添加步驟類型
            details.add(step.getStepType().getDisplayName(context))

            // 添加目標信息（如果有的話）
            when (step.getStepType()) {
                com.example.autolaunch.model.StepType.CLICK,
                com.example.autolaunch.model.StepType.LONG_CLICK,
                com.example.autolaunch.model.StepType.INPUT_TEXT -> {
                    when (step.getTargetType()) {
                        com.example.autolaunch.model.TargetType.TEXT -> {
                            step.targetText?.let {
                                details.add("目標：「$it」")
                            }
                        }
                        com.example.autolaunch.model.TargetType.ID -> {
                            step.targetId?.let {
                                details.add("ID：$it")
                            }
                        }
                        com.example.autolaunch.model.TargetType.COORDINATE -> {
                            if (step.targetX != null && step.targetY != null) {
                                details.add("座標：(${step.targetX}, ${step.targetY})")
                            }
                        }
                        com.example.autolaunch.model.TargetType.DESCRIPTION -> {
                            step.targetDescription?.let {
                                details.add("描述：$it")
                            }
                        }
                        com.example.autolaunch.model.TargetType.CLASS_NAME -> {
                            step.targetClassName?.let {
                                details.add("類別：$it")
                            }
                        }
                    }
                }
                else -> {
                    // 對於不需要目標的步驟類型，不顯示目標信息
                }
            }

            // 添加輸入文字信息
            if (step.isInputStep() && !step.inputText.isNullOrBlank()) {
                details.add("輸入：「${step.inputText}」")
            }

            // 添加等待時間信息
            if (step.isWaitStep() && step.waitDuration != null) {
                details.add("等待：${step.waitDuration}ms")
            }

            // 添加滑動信息
            if (step.isSwipeStep()) {
                when (step.getStepType()) {
                    com.example.autolaunch.model.StepType.SWIPE -> {
                        if (step.swipeStartX != null && step.swipeStartY != null &&
                            step.swipeEndX != null && step.swipeEndY != null) {
                            details.add("從(${step.swipeStartX},${step.swipeStartY})到(${step.swipeEndX},${step.swipeEndY})")
                        }
                    }
                    else -> {
                        // 預定義滑動方向已經在步驟類型中顯示
                    }
                }
                step.swipeDuration?.let {
                    details.add("持續：${it}ms")
                }
            }

            // 添加滾動信息
            if (step.getStepType() == com.example.autolaunch.model.StepType.SCROLL) {
                step.scrollDirection?.let { direction ->
                    val directionText = when (direction) {
                        1 -> "向上"
                        2 -> "向下"
                        3 -> "向左"
                        4 -> "向右"
                        else -> "未知"
                    }
                    details.add("方向：$directionText")
                }
            }

            // 添加重要配置信息
            val configDetails = mutableListOf<String>()

            // 添加可選步驟標記
            if (step.isOptional) {
                configDetails.add("可選")
            }

            // 添加重試次數（只顯示非默認值）
            if (step.retryCount != 3) {
                configDetails.add("重試：${step.retryCount}")
            }

            // 添加超時時間（只顯示非默認值）
            if (step.timeoutMs != 10000L) {
                configDetails.add("超時：${step.timeoutMs}ms")
            }

            // 添加步驟狀態
            if (!step.isEnabled) {
                configDetails.add("已停用")
            }

            if (configDetails.isNotEmpty()) {
                details.addAll(configDetails)
            }

            return details.joinToString(" • ")
        }
        
        private fun updateStepOrderBackground(step: AutomationStep) {
            val context = binding.root.context
            val backgroundRes = when {
                !step.isEnabled -> R.drawable.circle_background_disabled
                step.isOptional -> R.drawable.circle_background_optional
                else -> R.drawable.circle_background
            }
            
            binding.tvStepOrder.setBackgroundResource(backgroundRes)
        }
    }

    /**
     * 更新步驟順序
     */
    fun updateStepOrders(steps: List<AutomationStep>) {
        val updatedSteps = steps.mapIndexed { index, step ->
            step.copy(stepOrder = index + 1)
        }
        submitList(updatedSteps)
    }

    /**
     * 獲取指定位置的步驟
     */
    fun getStepAt(position: Int): AutomationStep? {
        return if (position in 0 until itemCount) getItem(position) else null
    }

    /**
     * 移動步驟位置
     */
    fun moveStep(fromPosition: Int, toPosition: Int): List<AutomationStep> {
        val currentList = currentList.toMutableList()
        if (fromPosition < currentList.size && toPosition < currentList.size) {
            val step = currentList.removeAt(fromPosition)
            currentList.add(toPosition, step)
            
            // 更新步驟順序
            val updatedSteps = currentList.mapIndexed { index, automationStep ->
                automationStep.copy(stepOrder = index + 1)
            }
            
            submitList(updatedSteps)
            return updatedSteps
        }
        return currentList
    }

    /**
     * 刪除步驟
     */
    fun removeStep(step: AutomationStep): List<AutomationStep> {
        val currentList = currentList.toMutableList()
        currentList.remove(step)
        
        // 更新步驟順序
        val updatedSteps = currentList.mapIndexed { index, automationStep ->
            automationStep.copy(stepOrder = index + 1)
        }
        
        submitList(updatedSteps)
        return updatedSteps
    }

    /**
     * 添加步驟
     */
    fun addStep(step: AutomationStep): List<AutomationStep> {
        val currentList = currentList.toMutableList()
        val newStep = step.copy(stepOrder = currentList.size + 1)
        currentList.add(newStep)
        
        submitList(currentList)
        return currentList
    }

    /**
     * 插入步驟到指定位置
     */
    fun insertStep(step: AutomationStep, position: Int): List<AutomationStep> {
        val currentList = currentList.toMutableList()
        val insertPosition = position.coerceIn(0, currentList.size)
        currentList.add(insertPosition, step)
        
        // 更新步驟順序
        val updatedSteps = currentList.mapIndexed { index, automationStep ->
            automationStep.copy(stepOrder = index + 1)
        }
        
        submitList(updatedSteps)
        return updatedSteps
    }

    /**
     * 更新單個步驟
     */
    fun updateStep(updatedStep: AutomationStep): List<AutomationStep> {
        val currentList = currentList.toMutableList()
        val index = currentList.indexOfFirst { it.id == updatedStep.id }
        
        if (index != -1) {
            currentList[index] = updatedStep
            submitList(currentList)
        }
        
        return currentList
    }
}

/**
 * DiffUtil 回調，用於高效更新列表
 */
class StepDiffCallback : DiffUtil.ItemCallback<AutomationStep>() {
    override fun areItemsTheSame(oldItem: AutomationStep, newItem: AutomationStep): Boolean {
        return oldItem.id == newItem.id
    }

    override fun areContentsTheSame(oldItem: AutomationStep, newItem: AutomationStep): Boolean {
        return oldItem == newItem
    }
}
