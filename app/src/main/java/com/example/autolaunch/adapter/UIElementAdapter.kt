package com.example.autolaunch.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.autolaunch.R
import com.example.autolaunch.automation.UIElementInfo
import com.example.autolaunch.databinding.ItemUiElementBinding

/**
 * UI元素列表適配器
 */
class UIElementAdapter(
    private val onElementClick: (UIElementInfo) -> Unit
) : ListAdapter<UIElementInfo, UIElementAdapter.ElementViewHolder>(ElementDiffCallback()) {

    private var selectedElement: UIElementInfo? = null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ElementViewHolder {
        val binding = ItemUiElementBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ElementViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ElementViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    /**
     * 設置選中的元素
     */
    fun setSelectedElement(element: UIElementInfo) {
        val previousSelected = selectedElement
        selectedElement = element
        
        // 更新之前選中的項目
        previousSelected?.let { prev ->
            val prevIndex = currentList.indexOf(prev)
            if (prevIndex != -1) {
                notifyItemChanged(prevIndex)
            }
        }
        
        // 更新當前選中的項目
        val currentIndex = currentList.indexOf(element)
        if (currentIndex != -1) {
            notifyItemChanged(currentIndex)
        }
    }

    inner class ElementViewHolder(
        private val binding: ItemUiElementBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(element: UIElementInfo) {
            binding.apply {
                // 設置元素名稱
                tvElementName.text = element.getDisplayName()
                
                // 設置元素類型
                tvElementType.text = buildTypeDescription(element)
                
                // 設置元素位置
                tvElementPosition.text = "位置: (${element.bounds.centerX()}, ${element.bounds.centerY()})"
                
                // 設置元素圖標
                ivElementIcon.setImageResource(getElementIcon(element))
                
                // 設置選中狀態
                val isSelected = element == selectedElement
                checkboxSelect.isChecked = isSelected
                root.isChecked = isSelected
                
                // 設置點擊事件
                root.setOnClickListener {
                    onElementClick(element)
                }
            }
        }
        
        private fun buildTypeDescription(element: UIElementInfo): String {
            val types = mutableListOf<String>()
            
            if (element.isClickable) types.add("可點擊")
            if (element.isLongClickable) types.add("可長按")
            if (element.isEditable) types.add("可編輯")
            if (element.isCheckable) types.add("可勾選")
            if (element.isScrollable) types.add("可滾動")
            
            val typeText = if (types.isNotEmpty()) types.joinToString(", ") else "靜態"
            val className = element.className?.substringAfterLast(".") ?: "Unknown"
            
            return "$typeText • $className"
        }
        
        private fun getElementIcon(element: UIElementInfo): Int {
            return when {
                element.isEditable -> R.drawable.ic_edit_24
                element.isCheckable -> R.drawable.ic_check_box_24
                element.isScrollable -> R.drawable.ic_scroll_24
                element.isClickable -> R.drawable.ic_touch_app_24
                else -> R.drawable.ic_crop_free_24
            }
        }
    }
}

/**
 * DiffUtil 回調
 */
class ElementDiffCallback : DiffUtil.ItemCallback<UIElementInfo>() {
    override fun areItemsTheSame(oldItem: UIElementInfo, newItem: UIElementInfo): Boolean {
        return oldItem.viewId == newItem.viewId && 
               oldItem.bounds == newItem.bounds &&
               oldItem.text == newItem.text
    }

    override fun areContentsTheSame(oldItem: UIElementInfo, newItem: UIElementInfo): Boolean {
        return oldItem == newItem
    }
}
