package com.example.autolaunch.adapter

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.example.autolaunch.R
import com.example.autolaunch.fragment.CloudBackupFragment
import com.example.autolaunch.fragment.LocalBackupFragment

/**
 * 備份頁面ViewPager適配器
 */
class BackupPagerAdapter(private val fragmentActivity: FragmentActivity) : FragmentStateAdapter(fragmentActivity) {
    
    companion object {
        const val TAB_LOCAL_BACKUP = 0
        const val TAB_CLOUD_BACKUP = 1
        const val TAB_COUNT = 2
    }
    
    override fun getItemCount(): Int = TAB_COUNT
    
    override fun createFragment(position: Int): Fragment {
        return when (position) {
            TAB_LOCAL_BACKUP -> LocalBackupFragment.newInstance()
            TAB_CLOUD_BACKUP -> CloudBackupFragment.newInstance()
            else -> throw IllegalArgumentException("Invalid position: $position")
        }
    }
    
    fun getTabTitle(position: Int): String {
        return when (position) {
            TAB_LOCAL_BACKUP -> fragmentActivity.getString(R.string.tab_local_backup)
            TAB_CLOUD_BACKUP -> fragmentActivity.getString(R.string.tab_cloud_backup)
            else -> throw IllegalArgumentException("Invalid position: $position")
        }
    }
}
