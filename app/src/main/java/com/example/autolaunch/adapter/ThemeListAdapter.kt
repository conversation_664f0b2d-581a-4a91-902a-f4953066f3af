package com.example.autolaunch.adapter

import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.example.autolaunch.databinding.ItemThemeBinding
import com.example.autolaunch.utils.ThemeManager
import com.example.autolaunch.utils.ThemeType

/**
 * 主題列表適配器
 * 用於顯示單一模式（淺色或深色）的主題列表
 */
class ThemeListAdapter(
    private val themes: List<ThemeType>,
    private var currentTheme: ThemeType,
    private val themeManager: ThemeManager,
    private val onThemeSelected: (ThemeType) -> Unit
) : RecyclerView.Adapter<ThemeListAdapter.ThemeViewHolder>() {

    companion object {
        private const val TAG = "ThemeListAdapter"
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ThemeViewHolder {
        val binding = ItemThemeBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ThemeViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ThemeViewHolder, position: Int) {
        val theme = themes[position]
        holder.bind(theme, currentTheme) { selectedTheme ->
            onThemeSelected(selectedTheme)
        }
    }

    override fun getItemCount(): Int = themes.size

    fun updateCurrentTheme(newTheme: ThemeType) {
        val oldTheme = currentTheme
        currentTheme = newTheme

        // 只更新受影響的項目，而不是整個列表
        themes.forEachIndexed { index, theme ->
            if (theme == oldTheme || theme == newTheme) {
                notifyItemChanged(index)
            }
        }
    }

    /**
     * 主題ViewHolder
     */
    inner class ThemeViewHolder(private val binding: ItemThemeBinding) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(
            theme: ThemeType,
            currentTheme: ThemeType,
            onThemeClick: (ThemeType) -> Unit
        ) {
            binding.tvThemeName.text = theme.displayName
            binding.tvThemeDescription.text = theme.description

            // 檢查是否為系統主題並顯示標籤
            val isSystemTheme = themeManager.isSystemTheme(theme)
            binding.tvSystemThemeLabel.visibility = if (isSystemTheme) View.VISIBLE else View.GONE

            // 設定主題預覽顏色
            setupThemePreview(theme)

            // 設定選中狀態
            val isSelected = theme == currentTheme
            Log.d(TAG, "Binding theme: ${theme.displayName}, current: ${currentTheme.displayName}, selected: $isSelected")
            binding.radioButton.isChecked = isSelected
            binding.cardTheme.strokeWidth = if (isSelected) 4 else 0

            // 設定點擊事件 - 只有在未選中時才觸發
            binding.root.setOnClickListener {
                if (!isSelected) {
                    Log.d(TAG, "Theme card clicked: ${theme.displayName}")
                    onThemeClick(theme)
                }
            }

            // RadioButton 點擊事件 - 防止重複選擇
            binding.radioButton.setOnClickListener {
                if (!isSelected) {
                    Log.d(TAG, "Radio button clicked: ${theme.displayName}")
                    onThemeClick(theme)
                } else {
                    // 如果已經選中，保持選中狀態
                    binding.radioButton.isChecked = true
                }
            }
        }
        
        private fun setupThemePreview(theme: ThemeType) {
            // 為所有主題顯示顏色預覽
            val colors = themeManager.getThemePreviewColors(theme)
            val gradientDrawable = GradientDrawable().apply {
                orientation = GradientDrawable.Orientation.LEFT_RIGHT
                setColors(intArrayOf(
                    Color.parseColor(colors.primary),
                    Color.parseColor(colors.surface)
                ))
                cornerRadius = 12f
            }
            binding.viewThemePreview.background = gradientDrawable
        }
    }
}
