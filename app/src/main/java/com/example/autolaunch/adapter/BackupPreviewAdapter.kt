package com.example.autolaunch.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.example.autolaunch.databinding.ItemBackupPreviewBinding
import com.example.autolaunch.databinding.ItemBackupPreviewHeaderBinding
import com.example.autolaunch.model.Schedule

sealed class PreviewItem {
    data class Header(val title: String) : PreviewItem()
    data class ScheduleItem(val schedule: Schedule) : PreviewItem()
}

class BackupPreviewAdapter(
    private val items: List<PreviewItem>
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        private const val TYPE_HEADER = 0
        private const val TYPE_SCHEDULE = 1
    }

    override fun getItemViewType(position: Int): Int {
        return when (items[position]) {
            is PreviewItem.Header -> TYPE_HEADER
            is PreviewItem.ScheduleItem -> TYPE_SCHEDULE
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        return when (viewType) {
            TYPE_HEADER -> {
                val binding = ItemBackupPreviewHeaderBinding.inflate(inflater, parent, false)
                HeaderViewHolder(binding)
            }
            TYPE_SCHEDULE -> {
                val binding = ItemBackupPreviewBinding.inflate(inflater, parent, false)
                ScheduleViewHolder(binding)
            }
            else -> throw IllegalArgumentException("Invalid view type")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (val item = items[position]) {
            is PreviewItem.Header -> (holder as HeaderViewHolder).bind(item)
            is PreviewItem.ScheduleItem -> (holder as ScheduleViewHolder).bind(item)
        }
    }

    override fun getItemCount() = items.size

    class ScheduleViewHolder(private val binding: ItemBackupPreviewBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: PreviewItem.ScheduleItem) {
            val schedule = item.schedule
            binding.tvScheduleTitle.text = schedule.getDisplayName()

            val details = when {
                schedule.isAppSchedule() -> "啟動應用: ${schedule.appName ?: "未知"}"
                schedule.isUrlSchedule() -> "打開網址: ${schedule.url ?: "未知"}"
                else -> "未知類型的排程"
            }
            binding.tvAppNameToLaunch.text = details
        }
    }

    class HeaderViewHolder(private val binding: ItemBackupPreviewHeaderBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: PreviewItem.Header) {
            binding.tvHeaderTitle.text = item.title
        }
    }
} 