package com.example.autolaunch.adapter

import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.example.autolaunch.R
import com.example.autolaunch.databinding.ItemThemeBinding
import com.example.autolaunch.databinding.ItemThemeSectionHeaderBinding
import com.example.autolaunch.utils.ThemeManager
import com.example.autolaunch.utils.ThemeType

/**
 * 主題選擇適配器
 * 支援分組顯示淺色和深色主題
 */
class ThemeAdapter(
    private val lightThemes: List<ThemeType>,
    private val darkThemes: List<ThemeType>,
    private var currentTheme: ThemeType,
    private val themeManager: ThemeManager,
    private val onThemeSelected: (ThemeType) -> Unit
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        private const val TAG = "ThemeAdapter"
        private const val TYPE_HEADER = 0
        private const val TYPE_THEME = 1
    }
    

    private val items = mutableListOf<ThemeItem>()
    
    init {
        buildItemList()
    }
    
    private fun buildItemList() {
        items.clear()

        // 添加淺色主題區段
        items.add(ThemeItem.Header("淺色主題"))
        lightThemes.forEach { theme ->
            items.add(ThemeItem.Theme(theme))
        }

        // 添加深色主題區段
        items.add(ThemeItem.Header("深色主題"))
        darkThemes.forEach { theme ->
            items.add(ThemeItem.Theme(theme))
        }
    }
    
    override fun getItemViewType(position: Int): Int {
        return when (items[position]) {
            is ThemeItem.Header -> TYPE_HEADER
            is ThemeItem.Theme -> TYPE_THEME
        }
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        
        return when (viewType) {
            TYPE_HEADER -> {
                val binding = ItemThemeSectionHeaderBinding.inflate(inflater, parent, false)
                HeaderViewHolder(binding)
            }
            TYPE_THEME -> {
                val binding = ItemThemeBinding.inflate(inflater, parent, false)
                ThemeViewHolder(binding)
            }
            else -> throw IllegalArgumentException("Unknown view type: $viewType")
        }
    }
    
    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is HeaderViewHolder -> {
                val header = items[position] as ThemeItem.Header
                holder.bind(header.title)
            }
            is ThemeViewHolder -> {
                val themeItem = items[position] as ThemeItem.Theme
                holder.bind(themeItem.theme, currentTheme) { theme ->
                    onThemeSelected(theme)
                }
            }
        }
    }
    
    override fun getItemCount(): Int = items.size
    
    fun updateCurrentTheme(newTheme: ThemeType) {
        currentTheme = newTheme
        notifyDataSetChanged()
    }
    

    
    /**
     * 標題ViewHolder
     */
    class HeaderViewHolder(private val binding: ItemThemeSectionHeaderBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(title: String) {
            binding.tvSectionTitle.text = title
        }
    }
    
    /**
     * 主題ViewHolder
     */
    class ThemeViewHolder(private val binding: ItemThemeBinding) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(
            theme: ThemeType,
            currentTheme: ThemeType,
            onThemeClick: (ThemeType) -> Unit
        ) {
            binding.tvThemeName.text = theme.displayName
            binding.tvThemeDescription.text = theme.description

            // 檢查是否為系統主題並顯示標籤
            val context = binding.root.context
            val themeManager = ThemeManager.getInstance(context)
            val isSystemTheme = themeManager.isSystemTheme(theme)
            binding.tvSystemThemeLabel.visibility = if (isSystemTheme) View.VISIBLE else View.GONE

            // 設定主題預覽顏色
            setupThemePreview(theme)

            // 設定選中狀態
            val isSelected = theme == currentTheme
            binding.radioButton.isChecked = isSelected
            binding.cardTheme.strokeWidth = if (isSelected) 4 else 0

            // 設定點擊事件
            binding.root.setOnClickListener {
                Log.d(TAG, "Theme card clicked: ${theme.displayName}")
                onThemeClick(theme)
            }

            binding.radioButton.setOnClickListener {
                Log.d(TAG, "Radio button clicked: ${theme.displayName}")
                onThemeClick(theme)
            }
        }
        
        private fun setupThemePreview(theme: ThemeType) {
            val context = binding.root.context
            val themeManager = ThemeManager.getInstance(context)

            // 為所有主題顯示顏色預覽
            val colors = themeManager.getThemePreviewColors(theme)
            val gradientDrawable = GradientDrawable().apply {
                orientation = GradientDrawable.Orientation.LEFT_RIGHT
                setColors(intArrayOf(
                    Color.parseColor(colors.primary),
                    Color.parseColor(colors.surface)
                ))
                cornerRadius = 12f
            }
            binding.viewThemePreview.background = gradientDrawable
        }
    }
    
    /**
     * 主題項目密封類
     */
    sealed class ThemeItem {
        data class Header(val title: String) : ThemeItem()
        data class Theme(val theme: ThemeType) : ThemeItem()
    }
}
