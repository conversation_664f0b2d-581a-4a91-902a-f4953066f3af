package com.example.autolaunch.adapter

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.example.autolaunch.R
import com.example.autolaunch.fragment.AppScheduleFragment
import com.example.autolaunch.fragment.WebScheduleFragment

/**
 * 首頁 ViewPager 適配器
 */
class MainPagerAdapter(private val fragmentActivity: FragmentActivity) : FragmentStateAdapter(fragmentActivity) {
    
    companion object {
        const val TAB_APP_SCHEDULE = 0
        const val TAB_WEB_SCHEDULE = 1
        const val TAB_COUNT = 2
    }
    
    override fun getItemCount(): Int = TAB_COUNT
    
    override fun createFragment(position: Int): Fragment {
        return when (position) {
            TAB_APP_SCHEDULE -> AppScheduleFragment.newInstance()
            TAB_WEB_SCHEDULE -> WebScheduleFragment.newInstance()
            else -> throw IllegalArgumentException("Invalid position: $position")
        }
    }
    
    fun getTabTitle(position: Int): String {
        return when (position) {
            TAB_APP_SCHEDULE -> fragmentActivity.getString(R.string.tab_app)
            TAB_WEB_SCHEDULE -> fragmentActivity.getString(R.string.tab_web)
            else -> throw IllegalArgumentException("Invalid position: $position")
        }
    }

    fun getTabTitleWithCount(position: Int, count: Int): String {
        return when (position) {
            TAB_APP_SCHEDULE -> "${fragmentActivity.getString(R.string.tab_app)} ($count)"
            TAB_WEB_SCHEDULE -> "${fragmentActivity.getString(R.string.tab_web)} ($count)"
            else -> throw IllegalArgumentException("Invalid position: $position")
        }
    }
}
