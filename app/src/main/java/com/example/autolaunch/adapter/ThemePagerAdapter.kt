package com.example.autolaunch.adapter

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.example.autolaunch.fragment.ThemeListFragment

/**
 * 主題頁面適配器
 * 管理淺色和深色主題的 Fragment
 */
class ThemePagerAdapter(fragmentActivity: FragmentActivity) : FragmentStateAdapter(fragmentActivity) {

    companion object {
        const val LIGHT_MODE_TAB = 0
        const val DARK_MODE_TAB = 1
        const val TAB_COUNT = 2
    }

    override fun getItemCount(): Int = TAB_COUNT

    override fun createFragment(position: Int): Fragment {
        return when (position) {
            LIGHT_MODE_TAB -> ThemeListFragment.newInstance(isDarkMode = false)
            DARK_MODE_TAB -> ThemeListFragment.newInstance(isDarkMode = true)
            else -> throw IllegalArgumentException("Invalid position: $position")
        }
    }
}
