package com.example.autolaunch.adapter

import android.text.format.Formatter
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.example.autolaunch.databinding.ItemBackupFileBinding
import com.example.autolaunch.utils.DriveFileInfo
import com.example.autolaunch.utils.TimeFormatUtils
import com.example.autolaunch.utils.setDebounceClickListener

/**
 * 雲端備份文件適配器
 */
class CloudBackupFileAdapter(
    private val onFileRestore: (DriveFileInfo) -> Unit,
    private val onFileDelete: (DriveFileInfo) -> Unit,
    private val onGetScheduleCount: (String, (Int) -> Unit) -> Unit
) : RecyclerView.Adapter<CloudBackupFileAdapter.ViewHolder>() {

    private var files: List<DriveFileInfo> = emptyList()
    private val scheduleCountCache = mutableMapOf<String, Int>()

    fun updateFiles(newFiles: List<DriveFileInfo>) {
        files = newFiles.sortedByDescending { it.createdTime }
        scheduleCountCache.clear()
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemBackupFileBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val file = files[position]
        holder.bind(file, scheduleCountCache[file.id], onFileRestore, onFileDelete) {
            // 如果快取中沒有排程數量，則去獲取
            if (scheduleCountCache[file.id] == null) {
                onGetScheduleCount(file.id) { count ->
                    scheduleCountCache[file.id] = count
                    // 在主線程更新UI
                    holder.itemView.post {
                        if (holder.bindingAdapterPosition == position) {
                            holder.updateScheduleCount(count)
                        }
                    }
                }
            }
        }
    }

    override fun getItemCount() = files.size

    class ViewHolder(private val binding: ItemBackupFileBinding) : RecyclerView.ViewHolder(binding.root) {

        fun bind(
            file: DriveFileInfo,
            scheduleCount: Int?,
            onFileRestore: (DriveFileInfo) -> Unit,
            onFileDelete: (DriveFileInfo) -> Unit,
            requestScheduleCount: () -> Unit
        ) {
            val context = binding.root.context
            binding.tvFileName.text = file.name
            binding.tvFileSize.text = Formatter.formatFileSize(context, file.size)
            binding.tvCreatedTime.text = TimeFormatUtils.getRelativeTimeSpanString(context, file.createdTime)

            if (scheduleCount != null) {
                updateScheduleCount(scheduleCount)
            } else {
                binding.tvFileInfo.text = "正在讀取..."
                requestScheduleCount()
            }

            binding.btnRestore.setDebounceClickListener(1500L) {
                onFileRestore(file)
            }

            binding.btnDelete.setDebounceClickListener(1000L) {
                onFileDelete(file)
            }
        }

        fun updateScheduleCount(count: Int) {
            binding.tvFileInfo.text = if (count >= 0) {
                "$count 個排程"
            } else {
                "讀取失敗"
            }
        }
    }
} 