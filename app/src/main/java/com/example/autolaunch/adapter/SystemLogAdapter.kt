package com.example.autolaunch.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.autolaunch.R
import com.example.autolaunch.databinding.ItemSystemLogBinding
import com.example.autolaunch.databinding.ItemSystemLogHeaderBinding
import com.example.autolaunch.model.LogType
import com.example.autolaunch.model.SystemLog
import java.text.SimpleDateFormat
import java.util.*

/**
 * 系統日志適配器
 * 支持按日期分組顯示日志
 */
class SystemLogAdapter(
    private val onItemClick: (SystemLog) -> Unit
) : ListAdapter<SystemLogAdapter.LogItem, RecyclerView.ViewHolder>(LogItemDiffCallback()) {
    
    companion object {
        private const val TYPE_HEADER = 0
        private const val TYPE_LOG = 1
    }
    
    /**
     * 日志項目類型
     */
    sealed class LogItem {
        data class Header(val date: String, val dayOfWeek: String) : LogItem()
        data class Log(val systemLog: SystemLog) : LogItem()
    }
    
    override fun getItemViewType(position: Int): Int {
        return when (getItem(position)) {
            is LogItem.Header -> TYPE_HEADER
            is LogItem.Log -> TYPE_LOG
        }
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            TYPE_HEADER -> {
                val binding = ItemSystemLogHeaderBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
                HeaderViewHolder(binding)
            }
            TYPE_LOG -> {
                val binding = ItemSystemLogBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
                LogViewHolder(binding)
            }
            else -> throw IllegalArgumentException("Unknown view type: $viewType")
        }
    }
    
    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (val item = getItem(position)) {
            is LogItem.Header -> (holder as HeaderViewHolder).bind(item)
            is LogItem.Log -> (holder as LogViewHolder).bind(item.systemLog)
        }
    }
    
    /**
     * 提交新的日志列表並按日期分組
     */
    fun submitLogList(logs: List<SystemLog>) {
        try {
            if (logs.isEmpty()) {
                submitList(emptyList())
                return
            }

            val groupedItems = mutableListOf<LogItem>()
            val dateFormat = SimpleDateFormat("yyyy年MM月dd日", Locale.getDefault())

            // 按日期分組
            val groupedLogs = logs.groupBy { log ->
                try {
                    dateFormat.format(Date(log.timestamp))
                } catch (e: Exception) {
                    android.util.Log.w("SystemLogAdapter", "Failed to format date for log ${log.id}", e)
                    "未知日期"
                }
            }

            // 按日期倒序排列
            val sortedGroups = groupedLogs.toSortedMap(compareByDescending { dateString ->
                try {
                    dateFormat.parse(dateString)?.time ?: 0L
                } catch (e: Exception) {
                    android.util.Log.w("SystemLogAdapter", "Failed to parse date: $dateString", e)
                    0L
                }
            })

            // 構建顯示項目列表
            sortedGroups.forEach { (dateString, logsInDate) ->
                try {
                    // 添加日期標題
                    val dayOfWeek = if (logsInDate.isNotEmpty()) {
                        try {
                            logsInDate.first().getDayOfWeek()
                        } catch (e: Exception) {
                            android.util.Log.w("SystemLogAdapter", "Failed to get day of week", e)
                            ""
                        }
                    } else {
                        ""
                    }
                    groupedItems.add(LogItem.Header(dateString, dayOfWeek))

                    // 添加該日期的日志（按時間倒序）
                    logsInDate.sortedByDescending { it.timestamp }.forEach { log ->
                        groupedItems.add(LogItem.Log(log))
                    }
                } catch (e: Exception) {
                    android.util.Log.e("SystemLogAdapter", "Failed to process logs for date: $dateString", e)
                }
            }

            submitList(groupedItems)
        } catch (e: Exception) {
            android.util.Log.e("SystemLogAdapter", "Failed to submit log list", e)
            // 提交空列表以避免崩潰
            submitList(emptyList())
        }
    }
    
    /**
     * 日期標題ViewHolder
     */
    inner class HeaderViewHolder(
        private val binding: ItemSystemLogHeaderBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(header: LogItem.Header) {
            binding.tvDate.text = header.date
            binding.tvDayOfWeek.text = header.dayOfWeek
        }
    }
    
    /**
     * 日志項目ViewHolder
     */
    inner class LogViewHolder(
        private val binding: ItemSystemLogBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(systemLog: SystemLog) {
            try {
                // 設定時間
                binding.tvTime.text = try {
                    systemLog.getFormattedTime()
                } catch (e: Exception) {
                    android.util.Log.w("SystemLogAdapter", "Failed to format time for log ${systemLog.id}", e)
                    "--:--"
                }

                // 設定消息
                binding.tvMessage.text = systemLog.message.takeIf { it.isNotBlank() } ?: "無訊息"

                // 設定操作類型
                binding.tvActionType.text = try {
                    systemLog.getActionTypeEnum().displayName
                } catch (e: Exception) {
                    android.util.Log.w("SystemLogAdapter", "Failed to get action type for log ${systemLog.id}", e)
                    "未知操作"
                }

                // 設定日志類型圖標和顏色
                try {
                    val logType = systemLog.getLogTypeEnum()
                    val context = binding.root.context

                    when (logType) {
                        LogType.SUCCESS -> {
                            binding.ivLogType.setImageResource(R.drawable.ic_check_circle_24)
                            binding.ivLogType.setColorFilter(
                                ContextCompat.getColor(context, R.color.success_color)
                            )
                            binding.tvActionType.setTextColor(
                                ContextCompat.getColor(context, R.color.success_color)
                            )
                        }
                        LogType.INFO -> {
                            binding.ivLogType.setImageResource(R.drawable.ic_info_24)
                            binding.ivLogType.setColorFilter(
                                ContextCompat.getColor(context, R.color.info_color)
                            )
                            binding.tvActionType.setTextColor(
                                ContextCompat.getColor(context, R.color.info_color)
                            )
                        }
                        LogType.WARNING -> {
                            binding.ivLogType.setImageResource(R.drawable.ic_error_24)
                            binding.ivLogType.setColorFilter(
                                ContextCompat.getColor(context, R.color.warning_color)
                            )
                            binding.tvActionType.setTextColor(
                                ContextCompat.getColor(context, R.color.warning_color)
                            )
                        }
                        LogType.ERROR -> {
                            binding.ivLogType.setImageResource(R.drawable.ic_error_24)
                            binding.ivLogType.setColorFilter(
                                ContextCompat.getColor(context, R.color.error_color)
                            )
                            binding.tvActionType.setTextColor(
                                ContextCompat.getColor(context, R.color.error_color)
                            )
                        }
                    }
                } catch (e: Exception) {
                    android.util.Log.w("SystemLogAdapter", "Failed to set log type styling for log ${systemLog.id}", e)
                    // 設定預設樣式
                    binding.ivLogType.setImageResource(R.drawable.ic_info_24)
                    binding.ivLogType.setColorFilter(
                        ContextCompat.getColor(binding.root.context, R.color.info_color)
                    )
                }

                // 顯示排程名稱（如果有）
                if (!systemLog.scheduleName.isNullOrBlank()) {
                    binding.tvScheduleName.text = systemLog.scheduleName
                    binding.tvScheduleName.visibility = View.VISIBLE
                } else {
                    binding.tvScheduleName.visibility = View.GONE
                }

                // 設定點擊事件
                binding.root.setOnClickListener {
                    try {
                        onItemClick(systemLog)
                    } catch (e: Exception) {
                        android.util.Log.e("SystemLogAdapter", "Failed to handle item click for log ${systemLog.id}", e)
                    }
                }
            } catch (e: Exception) {
                android.util.Log.e("SystemLogAdapter", "Failed to bind log ${systemLog.id}", e)
                // 設定預設值以避免崩潰
                binding.tvTime.text = "--:--"
                binding.tvMessage.text = "載入錯誤"
                binding.tvActionType.text = "未知"
                binding.tvScheduleName.visibility = View.GONE
            }
        }
    }
    
    /**
     * DiffUtil回調
     */
    private class LogItemDiffCallback : DiffUtil.ItemCallback<LogItem>() {
        override fun areItemsTheSame(oldItem: LogItem, newItem: LogItem): Boolean {
            return when {
                oldItem is LogItem.Header && newItem is LogItem.Header -> 
                    oldItem.date == newItem.date
                oldItem is LogItem.Log && newItem is LogItem.Log -> 
                    oldItem.systemLog.id == newItem.systemLog.id
                else -> false
            }
        }
        
        override fun areContentsTheSame(oldItem: LogItem, newItem: LogItem): Boolean {
            return oldItem == newItem
        }
    }
}
