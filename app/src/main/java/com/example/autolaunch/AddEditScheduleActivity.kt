package com.example.autolaunch

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.lifecycle.lifecycleScope
import com.example.autolaunch.databinding.ActivityAddEditScheduleBinding

import com.example.autolaunch.model.AppDatabase
import com.example.autolaunch.model.AppRepository
import com.example.autolaunch.model.AutomationStep
import com.example.autolaunch.model.RepeatMode
import com.example.autolaunch.model.Schedule
import com.example.autolaunch.model.ScheduleRepository
import com.example.autolaunch.model.ScheduleType
import com.example.autolaunch.adapter.AutomationStepAdapter
import com.example.autolaunch.dialog.StepConfigDialog
import androidx.appcompat.app.AlertDialog
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.ItemTouchHelper
import com.example.autolaunch.utils.UrlInputHelper
import com.example.autolaunch.utils.ThemeManager
import com.example.autolaunch.utils.UIPerformanceManager
import com.google.android.material.chip.Chip
import com.example.autolaunch.base.BaseActivity
import com.google.android.material.datepicker.MaterialDatePicker
import com.google.android.material.timepicker.MaterialTimePicker
import com.google.android.material.timepicker.TimeFormat
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import java.util.Calendar

/**
 * 原始排程資料快照，用於比較是否有變更
 */
data class OriginalScheduleData(
    val scheduleType: ScheduleType,
    val hour: Int,
    val minute: Int,
    val repeatMode: RepeatMode,
    val daysOfWeek: Int,
    val singleExecuteDate: Long?,
    val appName: String?,
    val appPackage: String?,
    val url: String?,
    val taskName: String?
)

/**
 * 新增/編輯排程的 Activity
 */
class AddEditScheduleActivity : BaseActivity() {
    
    private lateinit var binding: ActivityAddEditScheduleBinding
    private var scheduleId: Long = -1L // -1 表示新增模式，其他值表示編輯模式
    
    // 當前選擇的時間 - 預設為當前時間加1分鐘
    private var selectedHour: Int = getCurrentHour()
    private var selectedMinute: Int = getCurrentMinute()
    
    // 當前選擇的重複模式
    private var selectedRepeatMode: RepeatMode = RepeatMode.DAILY
    
    // 當前選擇的星期幾 (位元遮罩)
    private var selectedDaysOfWeek: Int = 0
    
    // 當前選擇的單次執行日期 (Unix timestamp)
    private var selectedSingleDate: Long? = null
    
    // 當前選擇的排程類型
    private var selectedScheduleType: ScheduleType = ScheduleType.APP

    // 當前選擇的應用程式
    private var selectedAppName: String? = null
    private var selectedAppPackage: String? = null

    // 當前選擇的 URL
    private var selectedUrl: String? = null

    // 自訂任務名稱
    private var customTaskName: String? = null

    // URL 輸入輔助類
    private var urlInputHelper: UrlInputHelper? = null

    // 自動化步驟相關
    private lateinit var automationStepAdapter: AutomationStepAdapter
    private var automationSteps: MutableList<AutomationStep> = mutableListOf()
    private var isAutomationEnabled: Boolean = false

    // 追蹤內容是否有變更
    private var hasUnsavedChanges: Boolean = false

    // 原始資料快照（用於比較是否有變更）
    private var originalData: OriginalScheduleData? = null

    // Activity Result Launcher for App Selection
    private val appSelectorLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.let { data ->
                selectedAppName = data.getStringExtra(AppSelectorActivity.EXTRA_SELECTED_APP_NAME)
                selectedAppPackage = data.getStringExtra(AppSelectorActivity.EXTRA_SELECTED_APP_PACKAGE)
                updateAppDisplay()
                markAsChanged()
            }
        }
    }
    
    companion object {
        const val EXTRA_SCHEDULE_ID = "extra_schedule_id"
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        try {
            Log.d("AddEditScheduleActivity", "Starting onCreate")

            // 確保隱藏ActionBar標題
            supportActionBar?.hide()

            Log.d("AddEditScheduleActivity", "Inflating binding")
            binding = ActivityAddEditScheduleBinding.inflate(layoutInflater)

            Log.d("AddEditScheduleActivity", "Setting content view")
            setContentView(binding.root)
            
            Log.d("AddEditScheduleActivity", "Setting up UI")
            setupUI()
            
            Log.d("AddEditScheduleActivity", "Setting up window insets")
            setupWindowInsets()
            
            Log.d("AddEditScheduleActivity", "Setting up click listeners")
            setupClickListeners()
            
            Log.d("AddEditScheduleActivity", "Setting up repeat mode toggle")
            setupRepeatModeToggle()

            Log.d("AddEditScheduleActivity", "Initializing schedule type")
            initializeScheduleType()

            Log.d("AddEditScheduleActivity", "Handling intent")
            handleIntent()

            Log.d("AddEditScheduleActivity", "onCreate completed successfully")
        } catch (e: Exception) {
            Log.e("AddEditScheduleActivity", "Error in onCreate", e)
            Toast.makeText(this, "初始化失敗: ${e.message}", Toast.LENGTH_LONG).show()
            finish()
        }
    }
    
    private fun setupUI() {
        // 設定標題
        scheduleId = intent.getLongExtra(EXTRA_SCHEDULE_ID, -1L)
        updateToolbarTitle()

        // 設定返回按鈕
        binding.btnBack.setOnClickListener {
            handleBackPressed()
        }

        // 設定右上角排程儲存按鈕點擊事件
        binding.btnSaveSchedule.setOnClickListener {
            saveSchedule()
        }

        // 設定標題編輯文字變更監聽器
        binding.etToolbarTitle.addTextChangedListener(object : android.text.TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: android.text.Editable?) {
                markAsChanged()
            }
        })

        // 初始化自動化步驟
        setupAutomationSteps()
    }

    /**
     * 更新工具列標題
     */
    private fun updateToolbarTitle() {
        val title = when {
            !customTaskName.isNullOrEmpty() -> customTaskName!!
            scheduleId == -1L -> getString(R.string.title_add_schedule)
            else -> getString(R.string.title_edit_schedule)
        }
        binding.etToolbarTitle.setText(title)
    }


    
    private fun setupWindowInsets() {
        ViewCompat.setOnApplyWindowInsetsListener(binding.root) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }
    }
    
    private fun handleIntent() {
        if (scheduleId != -1L) {
            // 編輯模式：載入現有排程資料
            loadScheduleData(scheduleId)
        } else {
            // 新增模式：使用預設值
            initializeNewSchedule()
        }
    }
    
    private fun loadScheduleData(id: Long) {
        lifecycleScope.launch {
            try {
                Log.d("AddEditScheduleActivity", "Loading schedule data for ID: $id")
                val database = AppDatabase.getDatabase(this@AddEditScheduleActivity)
                val scheduleDao = database.scheduleDao()
                val schedule = scheduleDao.getScheduleById(id).first()
                
                if (schedule != null) {
                    Log.d("AddEditScheduleActivity", "Schedule loaded successfully: ${schedule.appName}")
                    populateUIFromSchedule(schedule)
                } else {
                    Log.w("AddEditScheduleActivity", "Schedule not found for ID: $id")
                    Toast.makeText(this@AddEditScheduleActivity, getString(R.string.error_schedule_not_found), Toast.LENGTH_SHORT).show()
                    finish()
                }
            } catch (e: Exception) {
                Log.e("AddEditScheduleActivity", "Error loading schedule data", e)
                Toast.makeText(this@AddEditScheduleActivity, getString(R.string.error_load_schedule_failed, e.message), Toast.LENGTH_SHORT).show()
                finish()
            }
        }
    }
    
    private fun initializeNewSchedule() {
        // 設定預設值 - 時間已在變數初始化時設為當前時間
        updateTimeDisplay()
        updateRepeatModeSelection()
        updateModeSpecificVisibility()
        updateDateDisplay()
        updateAppDisplay()

        // 建立原始資料快照
        createOriginalDataSnapshot()
    }

    /**
     * 獲取當前時間加1分鐘後的小時
     */
    private fun getCurrentHour(): Int {
        val calendar = Calendar.getInstance()
        calendar.add(Calendar.MINUTE, 1)
        return calendar.get(Calendar.HOUR_OF_DAY)
    }

    /**
     * 獲取當前時間加1分鐘後的分鐘
     */
    private fun getCurrentMinute(): Int {
        val calendar = Calendar.getInstance()
        calendar.add(Calendar.MINUTE, 1)
        return calendar.get(Calendar.MINUTE)
    }
    
    private fun setupClickListeners() {
        // 排程類型選擇事件
        binding.toggleGroupScheduleType.addOnButtonCheckedListener { _, checkedId, isChecked ->
            if (isChecked) {
                val newScheduleType = when (checkedId) {
                    R.id.btnTypeApp -> ScheduleType.APP
                    R.id.btnTypeUrl -> ScheduleType.URL
                    else -> ScheduleType.APP
                }

                // 如果是新增模式且類型發生變化，應用預設範本
                if (scheduleId == -1L && selectedScheduleType != newScheduleType) {
                    selectedScheduleType = newScheduleType
                    updateScheduleTypeUI()
                    setupDefaultTemplates()
                    markAsChanged()
                } else {
                    selectedScheduleType = newScheduleType
                    updateScheduleTypeUI()
                    markAsChanged()
                }
            }
        }

        // 時間選擇點擊事件
        binding.cardTimeSelection.setOnClickListener {
            showTimePickerDialog()
        }

        // App 選擇點擊事件
        binding.cardAppSelection.setOnClickListener {
            openAppSelector()
        }

        // 日期選擇點擊事件
        binding.cardDateSelection.setOnClickListener {
            showDatePickerDialog()
        }

        // 初始化 URL 輸入輔助類
        setupUrlInputHelper()
    }
    
    private fun setupRepeatModeToggle() {
        // 設定重複模式切換事件
        binding.toggleGroupRepeatMode.addOnButtonCheckedListener { _, checkedId, isChecked ->
            if (isChecked) {
                selectedRepeatMode = when (checkedId) {
                    R.id.btnRepeatOnce -> RepeatMode.ONCE
                    R.id.btnRepeatDaily -> RepeatMode.DAILY
                    R.id.btnRepeatWeekly -> RepeatMode.WEEKLY
                    else -> RepeatMode.DAILY
                }
                updateModeSpecificVisibility()
                markAsChanged()
            }
        }
        
        // 設定星期幾選擇事件
        setupDaysOfWeekChips()
        
        // 預設選擇每日執行
        binding.btnRepeatDaily.isChecked = true
    }
    
    private fun setupDaysOfWeekChips() {
        val chips = listOf(
            binding.chipMonday,
            binding.chipTuesday,
            binding.chipWednesday,
            binding.chipThursday,
            binding.chipFriday,
            binding.chipSaturday,
            binding.chipSunday
        )
        
        chips.forEachIndexed { index, chip ->
            chip.setOnCheckedChangeListener { _, isChecked ->
                val dayBit = 1 shl index
                if (isChecked) {
                    selectedDaysOfWeek = selectedDaysOfWeek or dayBit
                } else {
                    selectedDaysOfWeek = selectedDaysOfWeek and dayBit.inv()
                }
            }
        }
    }
    
    private fun showTimePickerDialog() {
        val timePicker = MaterialTimePicker.Builder()
            .setTimeFormat(TimeFormat.CLOCK_24H)
            .setHour(selectedHour)
            .setMinute(selectedMinute)
            .setTitleText(getString(R.string.title_select_execution_time))
            .build()
        
        timePicker.addOnPositiveButtonClickListener {
            selectedHour = timePicker.hour
            selectedMinute = timePicker.minute
            updateTimeDisplay()
            markAsChanged()
        }
        
        timePicker.show(supportFragmentManager, "TIME_PICKER")
    }
    
    private fun updateTimeDisplay() {
        val timeString = String.format("%02d:%02d", selectedHour, selectedMinute)
        binding.tvSelectedTime.text = timeString
    }
    
    private fun updateRepeatModeSelection() {
        when (selectedRepeatMode) {
            RepeatMode.ONCE -> binding.btnRepeatOnce.isChecked = true
            RepeatMode.DAILY -> binding.btnRepeatDaily.isChecked = true
            RepeatMode.WEEKLY -> binding.btnRepeatWeekly.isChecked = true
            RepeatMode.MONTHLY -> {
                // 暫時不支援月重複，預設為每日
                binding.btnRepeatDaily.isChecked = true
                selectedRepeatMode = RepeatMode.DAILY
            }
        }
    }
    
    private fun updateModeSpecificVisibility() {
        when (selectedRepeatMode) {
            RepeatMode.ONCE -> {
                binding.layoutDateSelection.visibility = View.VISIBLE
                binding.layoutDaysOfWeek.visibility = View.GONE
            }
            RepeatMode.DAILY -> {
                binding.layoutDateSelection.visibility = View.GONE
                binding.layoutDaysOfWeek.visibility = View.GONE
            }
            RepeatMode.WEEKLY -> {
                binding.layoutDateSelection.visibility = View.GONE
                binding.layoutDaysOfWeek.visibility = View.VISIBLE
            }
            RepeatMode.MONTHLY -> {
                // 暫時不支援月重複
                binding.layoutDateSelection.visibility = View.VISIBLE
                binding.layoutDaysOfWeek.visibility = View.GONE
            }
        }
    }
    
    private fun showDatePickerDialog() {
        val datePicker = MaterialDatePicker.Builder.datePicker()
            .setTitleText(getString(R.string.title_select_execution_date))
            .setSelection(selectedSingleDate ?: MaterialDatePicker.todayInUtcMilliseconds())
            .build()

        datePicker.addOnPositiveButtonClickListener { selection ->
            selectedSingleDate = selection
            updateDateDisplay()
            markAsChanged()
        }

        datePicker.show(supportFragmentManager, "DATE_PICKER")
    }


    
    private fun updateDateDisplay() {
        selectedSingleDate?.let {
            val calendar = Calendar.getInstance()
            calendar.timeInMillis = it
            val year = calendar.get(Calendar.YEAR)
            val month = calendar.get(Calendar.MONTH) + 1 // Calendar.MONTH is 0-indexed
            val day = calendar.get(Calendar.DAY_OF_MONTH)
            val dateString = getString(R.string.date_format_yyyy_mm_dd, year, month, day)
            binding.tvSelectedDate.text = dateString
        } ?: run {
            binding.tvSelectedDate.text = getString(R.string.hint_select_date)
        }
    }
    
    private fun updateDaysOfWeekChips() {
        val chips = listOf(
            binding.chipMonday,
            binding.chipTuesday,
            binding.chipWednesday,
            binding.chipThursday,
            binding.chipFriday,
            binding.chipSaturday,
            binding.chipSunday
        )
        
        chips.forEachIndexed { index, chip ->
            val dayBit = 1 shl index
            chip.isChecked = (selectedDaysOfWeek and dayBit) != 0
        }
    }
    
    private fun openAppSelector() {
        val intent = Intent(this, AppSelectorActivity::class.java)
        appSelectorLauncher.launch(intent)
    }
    
    private fun updateAppDisplay() {
        if (selectedAppName != null && selectedAppPackage != null) {
            binding.tvSelectedAppName.text = selectedAppName
            binding.tvSelectedAppPackage.text = selectedAppPackage

            // 載入並顯示應用程式圖標
            try {
                val packageManager = packageManager
                val appIcon = packageManager.getApplicationIcon(selectedAppPackage!!)
                binding.ivSelectedAppIcon.setImageDrawable(appIcon)
            } catch (e: PackageManager.NameNotFoundException) {
                // 如果找不到應用程式，使用預設圖標
                binding.ivSelectedAppIcon.setImageResource(R.mipmap.ic_launcher)
            } catch (e: Exception) {
                // 其他異常也使用預設圖標
                binding.ivSelectedAppIcon.setImageResource(R.mipmap.ic_launcher)
            }
        } else {
            binding.tvSelectedAppName.text = getString(R.string.hint_select_app)
            binding.tvSelectedAppPackage.text = getString(R.string.status_no_app_selected)
            // 重置為預設圖標
            binding.ivSelectedAppIcon.setImageResource(R.mipmap.ic_launcher)
        }
    }
    
    private fun populateUIFromSchedule(schedule: Schedule) {
        try {
            Log.d("AddEditScheduleActivity", "Populating UI from schedule: ${schedule.getDisplayName()}")

            // 設置基本資料
            selectedHour = schedule.hour
            selectedMinute = schedule.minute
            selectedRepeatMode = schedule.getRepeatModeEnum()
            selectedDaysOfWeek = schedule.daysOfWeek
            selectedSingleDate = schedule.singleExecuteDate
            customTaskName = schedule.taskName

            // 設置排程類型相關資料
            selectedScheduleType = schedule.getScheduleTypeEnum()
            when (selectedScheduleType) {
                ScheduleType.APP -> {
                    selectedAppName = schedule.appName
                    selectedAppPackage = schedule.packageName
                    binding.btnTypeApp.isChecked = true
                }
                ScheduleType.URL -> {
                    selectedUrl = schedule.url
                    binding.btnTypeUrl.isChecked = true
                    urlInputHelper?.setUrl(schedule.url)
                }
            }

            updateScheduleTypeUI()
            updateTimeDisplay()
            updateRepeatModeSelection()
            updateModeSpecificVisibility()
            updateDateDisplay()
            updateDaysOfWeekChips()
            updateAppDisplay()
            updateToolbarTitle()

            // 建立原始資料快照
            createOriginalDataSnapshot()
            Log.d("AddEditScheduleActivity", "UI populated successfully")
        } catch (e: Exception) {
            Log.e("AddEditScheduleActivity", "Error populating UI from schedule", e)
            Toast.makeText(this, "載入排程資料失敗: ${e.message}", Toast.LENGTH_LONG).show()
            finish()
        }
    }
    
    private fun saveSchedule() {
        if (!validateInput()) {
            return
        }

        // 從 EditText 獲取當前標題
        val currentTitle = binding.etToolbarTitle.text?.toString()?.trim()
        val taskName = if (currentTitle.isNullOrEmpty() ||
                          currentTitle == getString(R.string.title_add_schedule) ||
                          currentTitle == getString(R.string.title_edit_schedule)) {
            null
        } else {
            currentTitle
        }

        val schedule = Schedule(
            id = if (scheduleId == -1L) 0 else scheduleId, // Room handles ID generation for new entities
            scheduleType = selectedScheduleType.value,
            hour = selectedHour,
            minute = selectedMinute,
            repeatMode = selectedRepeatMode.value,
            daysOfWeek = if (selectedRepeatMode == RepeatMode.WEEKLY) selectedDaysOfWeek else 0,
            singleExecuteDate = if (selectedRepeatMode == RepeatMode.ONCE) selectedSingleDate else null,
            appName = if (selectedScheduleType == ScheduleType.APP) selectedAppName else null,
            taskName = if (taskName.isNullOrEmpty()) null else taskName,
            packageName = if (selectedScheduleType == ScheduleType.APP) selectedAppPackage else null,
            url = if (selectedScheduleType == ScheduleType.URL) selectedUrl else null,
            urlTitle = null, // 移除自訂標題功能
            isEnabled = true, // New or edited schedules are enabled by default
            lastExecutedTime = null // Reset last execution time
        )

        lifecycleScope.launch {
            val repository = ScheduleRepository(this@AddEditScheduleActivity)

            try {
                if (scheduleId == -1L) {
                    // 新增排程 - 使用 repository 會自動設定鬧鐘
                    repository.insertSchedule(schedule)
                    Toast.makeText(this@AddEditScheduleActivity, getString(R.string.success_schedule_added), Toast.LENGTH_SHORT).show()
                } else {
                    // 更新排程 - 使用 repository 會自動更新鬧鐘
                    repository.updateSchedule(schedule)
                    Toast.makeText(this@AddEditScheduleActivity, getString(R.string.success_schedule_updated), Toast.LENGTH_SHORT).show()
                }
                hasUnsavedChanges = false // 重置變更標記
                finish()
            } catch (e: Exception) {
                Toast.makeText(this@AddEditScheduleActivity, getString(R.string.error_save_schedule_failed, e.message), Toast.LENGTH_LONG).show()
            }
        }
    }
    
    private fun validateInput(): Boolean {
        // 驗證排程類型相關的輸入
        when (selectedScheduleType) {
            ScheduleType.APP -> {
                if (selectedAppPackage == null) {
                    Toast.makeText(this, getString(R.string.error_select_app_to_launch), Toast.LENGTH_SHORT).show()
                    return false
                }
            }
            ScheduleType.URL -> {
                if (selectedUrl.isNullOrBlank()) {
                    Toast.makeText(this, getString(R.string.error_enter_valid_url), Toast.LENGTH_SHORT).show()
                    return false
                }
                if (urlInputHelper?.isValid() != true) {
                    Toast.makeText(this, getString(R.string.error_invalid_url_format), Toast.LENGTH_SHORT).show()
                    return false
                }
            }
        }

        if (selectedRepeatMode == RepeatMode.ONCE && selectedSingleDate == null) {
            Toast.makeText(this, getString(R.string.error_select_date_for_once_mode), Toast.LENGTH_SHORT).show()
            return false
        }

        if (selectedRepeatMode == RepeatMode.WEEKLY && selectedDaysOfWeek == 0) {
            Toast.makeText(this, getString(R.string.error_select_day_for_weekly_mode), Toast.LENGTH_SHORT).show()
            return false
        }
        return true
    }

    /**
     * 設定 URL 輸入輔助類
     */
    private fun setupUrlInputHelper() {
        urlInputHelper = UrlInputHelper(this, binding) { isValid, url ->
            selectedUrl = if (isValid) url else null
        }
    }

    /**
     * 更新排程類型 UI
     */
    private fun updateScheduleTypeUI() {
        when (selectedScheduleType) {
            ScheduleType.APP -> {
                binding.cardAppSelectionContainer.visibility = View.VISIBLE
                binding.layoutUrlInputContainer.visibility = View.GONE
            }
            ScheduleType.URL -> {
                binding.cardAppSelectionContainer.visibility = View.GONE
                binding.layoutUrlInputContainer.visibility = View.VISIBLE
            }
        }
    }

    /**
     * 初始化排程類型選擇
     */
    private fun initializeScheduleType() {
        // 預設選擇 APP 類型
        binding.btnTypeApp.isChecked = true
        selectedScheduleType = ScheduleType.APP
        updateScheduleTypeUI()

        // 如果是新增模式，設定預設範本
        if (scheduleId == -1L) {
            setupDefaultTemplates()
        }
    }

    /**
     * 設定預設範本
     */
    private fun setupDefaultTemplates() {
        when (selectedScheduleType) {
            ScheduleType.APP -> {
                // APP 排程預設顯示應用本身
                setupDefaultAppTemplate()
            }
            ScheduleType.URL -> {
                // Web 排程預設顯示 google.com
                setupDefaultWebTemplate()
            }
        }
    }

    /**
     * 設定預設 APP 範本 - 不設定預設應用，提醒用戶選擇
     */
    private fun setupDefaultAppTemplate() {
        // 不設定預設應用，保持空狀態讓用戶主動選擇
        selectedAppName = null
        selectedAppPackage = null
        updateAppDisplay()
    }

    /**
     * 設定預設 Web 範本 - 顯示 google.com
     */
    private fun setupDefaultWebTemplate() {
        selectedUrl = "https://www.google.com"
        urlInputHelper?.setUrl(selectedUrl)
    }

    /**
     * 處理返回按鈕點擊
     */
    private fun handleBackPressed() {
        if (hasUnsavedChanges) {
            showUnsavedChangesDialog()
        } else {
            finish()
        }
    }

    /**
     * 顯示未儲存變更的確認對話框
     */
    private fun showUnsavedChangesDialog() {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle(getString(R.string.unsaved_changes_title))
            .setMessage(getString(R.string.unsaved_changes_message))
            .setPositiveButton(getString(R.string.leave)) { _, _ ->
                finish()
            }
            .setNegativeButton(getString(R.string.cancel), null)
            .show()
    }

    /**
     * 建立原始資料快照
     */
    private fun createOriginalDataSnapshot() {
        originalData = OriginalScheduleData(
            scheduleType = selectedScheduleType,
            hour = selectedHour,
            minute = selectedMinute,
            repeatMode = selectedRepeatMode,
            daysOfWeek = selectedDaysOfWeek,
            singleExecuteDate = selectedSingleDate,
            appName = selectedAppName,
            appPackage = selectedAppPackage,
            url = selectedUrl,
            taskName = customTaskName
        )
    }

    /**
     * 檢查是否有未儲存的變更
     */
    private fun checkForUnsavedChanges() {
        val currentData = OriginalScheduleData(
            scheduleType = selectedScheduleType,
            hour = selectedHour,
            minute = selectedMinute,
            repeatMode = selectedRepeatMode,
            daysOfWeek = selectedDaysOfWeek,
            singleExecuteDate = selectedSingleDate,
            appName = selectedAppName,
            appPackage = selectedAppPackage,
            url = selectedUrl,
            taskName = binding.etToolbarTitle.text?.toString()?.trim()?.takeIf {
                it != getString(R.string.title_add_schedule) && it != getString(R.string.title_edit_schedule)
            }
        )

        hasUnsavedChanges = originalData != currentData
    }

    /**
     * 標記內容已變更
     */
    private fun markAsChanged() {
        hasUnsavedChanges = true
    }

    override fun onBackPressed() {
        handleBackPressed()
    }

    /**
     * 設置自動化步驟
     */
    private fun setupAutomationSteps() {
        // 初始化適配器
        automationStepAdapter = AutomationStepAdapter(
            onStepClick = { step ->
                // 點擊步驟項目（預留，目前不使用）
            },
            onStepToggle = { step, isEnabled ->
                // 切換步驟啟用狀態
                updateStepEnabledStatus(step, isEnabled)
            },
            onStepEdit = { step ->
                // 編輯步驟
                showStepConfigDialog(step)
            },
            onStepDelete = { step ->
                // 刪除步驟
                showDeleteStepConfirmDialog(step)
            },
            onStepMoreActions = { step, view ->
                // 顯示更多操作菜單
                showStepMoreActionsMenu(step, view)
            }
        )

        // 設置RecyclerView
        binding.recyclerViewSteps.apply {
            layoutManager = LinearLayoutManager(this@AddEditScheduleActivity)
            adapter = automationStepAdapter
        }

        // 設置拖拽排序
        setupStepDragAndDrop()

        // 設置按鈕點擊事件
        binding.btnAddStep.setOnClickListener {
            showStepConfigDialog(null)
        }

        binding.btnPreviewSteps.setOnClickListener {
            previewAutomationSteps()
        }

        binding.btnToggleAutomation.setOnClickListener {
            toggleAutomationEnabled()
        }

        // 初始化UI狀態
        updateAutomationUI()
    }

    /**
     * 顯示步驟配置對話框
     */
    private fun showStepConfigDialog(step: AutomationStep?) {
        val dialog = StepConfigDialog(this) { configuredStep ->
            if (step == null) {
                // 新增步驟
                addAutomationStep(configuredStep)
            } else {
                // 編輯步驟
                updateAutomationStep(step, configuredStep)
            }
        }

        if (step != null) {
            dialog.setStep(step)
        }

        dialog.show()
    }

    /**
     * 顯示刪除步驟確認對話框
     */
    private fun showDeleteStepConfirmDialog(step: AutomationStep) {
        AlertDialog.Builder(this)
            .setTitle(getString(R.string.confirm_delete_step))
            .setMessage(getString(R.string.confirm_delete_step_message))
            .setPositiveButton(getString(R.string.delete)) { _, _ ->
                deleteAutomationStep(step)
            }
            .setNegativeButton(getString(R.string.cancel), null)
            .show()
    }

    /**
     * 顯示步驟更多操作菜單
     */
    private fun showStepMoreActionsMenu(step: AutomationStep, view: View) {
        // 這裡可以實現彈出菜單，目前簡化為直接編輯
        showStepConfigDialog(step)
    }

    /**
     * 新增自動化步驟
     */
    private fun addAutomationStep(step: AutomationStep) {
        val newStep = step.copy(
            scheduleId = if (scheduleId == -1L) 0 else scheduleId,
            stepOrder = automationSteps.size + 1
        )
        automationSteps.add(newStep)
        automationStepAdapter.submitList(automationSteps.toList())
        updateAutomationUI()
        markAsChanged()
    }

    /**
     * 更新自動化步驟
     */
    private fun updateAutomationStep(oldStep: AutomationStep, newStep: AutomationStep) {
        val index = automationSteps.indexOfFirst { it.id == oldStep.id || it.stepOrder == oldStep.stepOrder }
        if (index != -1) {
            automationSteps[index] = newStep.copy(
                id = oldStep.id,
                scheduleId = oldStep.scheduleId,
                stepOrder = oldStep.stepOrder
            )
            automationStepAdapter.submitList(automationSteps.toList())
            markAsChanged()
        }
    }

    /**
     * 刪除自動化步驟
     */
    private fun deleteAutomationStep(step: AutomationStep) {
        automationSteps.removeAll { it.id == step.id || it.stepOrder == step.stepOrder }
        // 重新排序
        automationSteps.forEachIndexed { index, automationStep ->
            automationSteps[index] = automationStep.copy(stepOrder = index + 1)
        }
        automationStepAdapter.submitList(automationSteps.toList())
        updateAutomationUI()
        markAsChanged()
    }

    /**
     * 更新步驟啟用狀態
     */
    private fun updateStepEnabledStatus(step: AutomationStep, isEnabled: Boolean) {
        val index = automationSteps.indexOfFirst { it.id == step.id || it.stepOrder == step.stepOrder }
        if (index != -1) {
            automationSteps[index] = automationSteps[index].copy(isEnabled = isEnabled)
            automationStepAdapter.submitList(automationSteps.toList())
            markAsChanged()
        }
    }

    /**
     * 切換自動化啟用狀態
     */
    private fun toggleAutomationEnabled() {
        isAutomationEnabled = !isAutomationEnabled
        updateAutomationUI()
        markAsChanged()
    }

    /**
     * 更新自動化UI
     */
    private fun updateAutomationUI() {
        // 更新按鈕文字
        binding.btnToggleAutomation.text = if (isAutomationEnabled) {
            getString(R.string.disable_automation)
        } else {
            getString(R.string.enable_automation)
        }

        // 更新空狀態顯示
        if (automationSteps.isEmpty()) {
            binding.layoutEmptySteps.visibility = View.VISIBLE
            binding.recyclerViewSteps.visibility = View.GONE
        } else {
            binding.layoutEmptySteps.visibility = View.GONE
            binding.recyclerViewSteps.visibility = View.VISIBLE
        }

        // 更新預覽按鈕狀態
        binding.btnPreviewSteps.isEnabled = automationSteps.isNotEmpty() && isAutomationEnabled
    }

    /**
     * 預覽自動化步驟
     */
    private fun previewAutomationSteps() {
        if (automationSteps.isEmpty()) {
            Toast.makeText(this, getString(R.string.no_steps_configured), Toast.LENGTH_SHORT).show()
            return
        }

        // 這裡可以實現預覽功能，目前顯示步驟摘要
        val summary = automationSteps.joinToString("\n") { step ->
            "${step.stepOrder}. ${step.getDisplayName(this)}"
        }

        AlertDialog.Builder(this)
            .setTitle(getString(R.string.preview_execution))
            .setMessage(summary)
            .setPositiveButton(getString(R.string.button_cancel), null)
            .show()
    }

    /**
     * 設置步驟拖拽排序
     */
    private fun setupStepDragAndDrop() {
        val itemTouchHelper = ItemTouchHelper(object : ItemTouchHelper.SimpleCallback(
            ItemTouchHelper.UP or ItemTouchHelper.DOWN,
            0
        ) {
            override fun onMove(
                recyclerView: androidx.recyclerview.widget.RecyclerView,
                viewHolder: androidx.recyclerview.widget.RecyclerView.ViewHolder,
                target: androidx.recyclerview.widget.RecyclerView.ViewHolder
            ): Boolean {
                val fromPosition = viewHolder.adapterPosition
                val toPosition = target.adapterPosition

                if (fromPosition < automationSteps.size && toPosition < automationSteps.size) {
                    // 交換步驟位置
                    val movedStep = automationSteps.removeAt(fromPosition)
                    automationSteps.add(toPosition, movedStep)

                    // 更新步驟順序
                    automationSteps.forEachIndexed { index, step ->
                        automationSteps[index] = step.copy(stepOrder = index + 1)
                    }

                    // 通知適配器
                    automationStepAdapter.notifyItemMoved(fromPosition, toPosition)
                    automationStepAdapter.submitList(automationSteps.toList())

                    markAsChanged()
                    return true
                }
                return false
            }

            override fun onSwiped(
                viewHolder: androidx.recyclerview.widget.RecyclerView.ViewHolder,
                direction: Int
            ) {
                // 不支持滑動刪除
            }

            override fun isLongPressDragEnabled(): Boolean {
                return true
            }

            override fun onSelectedChanged(
                viewHolder: androidx.recyclerview.widget.RecyclerView.ViewHolder?,
                actionState: Int
            ) {
                super.onSelectedChanged(viewHolder, actionState)

                if (actionState == ItemTouchHelper.ACTION_STATE_DRAG) {
                    // 開始拖拽時的視覺反饋
                    viewHolder?.itemView?.alpha = 0.7f
                    viewHolder?.itemView?.scaleX = 1.05f
                    viewHolder?.itemView?.scaleY = 1.05f
                }
            }

            override fun clearView(
                recyclerView: androidx.recyclerview.widget.RecyclerView,
                viewHolder: androidx.recyclerview.widget.RecyclerView.ViewHolder
            ) {
                super.clearView(recyclerView, viewHolder)

                // 結束拖拽時恢復視覺狀態
                viewHolder.itemView.alpha = 1.0f
                viewHolder.itemView.scaleX = 1.0f
                viewHolder.itemView.scaleY = 1.0f
            }
        })

        itemTouchHelper.attachToRecyclerView(binding.recyclerViewSteps)
    }
}