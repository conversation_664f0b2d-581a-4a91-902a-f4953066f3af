package com.example.autolaunch

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.example.autolaunch.model.Schedule
import com.example.autolaunch.model.ScheduleRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * 排程列表的 ViewModel
 * 管理排程數據的載入、更新和狀態
 */
class ScheduleViewModel(application: Application) : AndroidViewModel(application) {
    
    private val repository = ScheduleRepository(application)
    
    // 排程列表狀態
    private val _schedules = MutableStateFlow<List<Schedule>>(emptyList())
    val schedules: StateFlow<List<Schedule>> = _schedules.asStateFlow()
    
    // 載入狀態
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    // 錯誤狀態
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()
    
    // 成功操作狀態
    private val _successMessage = MutableStateFlow<String?>(null)
    val successMessage: StateFlow<String?> = _successMessage.asStateFlow()
    
    init {
        loadSchedules()
        // 初始化測試數據以展示分組功能
        initializeTestData()
    }
    
    /**
     * 載入所有排程
     */
    private fun loadSchedules() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null
                
                repository.getAllSchedules().collect { scheduleList ->
                    _schedules.value = scheduleList
                }
            } catch (e: Exception) {
                _error.value = getApplication<Application>().getString(R.string.error_load_schedule_failed_generic, e.message)
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 刷新排程列表
     */
    fun refreshSchedules() {
        loadSchedules()
    }
    
    /**
     * 更新排程的啟用狀態
     * @param schedule 要更新的排程
     * @param isEnabled 新的啟用狀態
     */
    fun updateScheduleEnabledStatus(schedule: Schedule, isEnabled: Boolean) {
        viewModelScope.launch {
            try {
                val updatedSchedule = schedule.copy(
                    isEnabled = isEnabled,
                    updatedTime = System.currentTimeMillis()
                )
                repository.updateSchedule(updatedSchedule)
                val statusString = if (isEnabled) getApplication<Application>().getString(R.string.status_enabled_true) else getApplication<Application>().getString(R.string.status_enabled_false)
                _successMessage.value = getApplication<Application>().getString(R.string.success_schedule_toggled, schedule.getDisplayName(), statusString)
            } catch (e: Exception) {
                _error.value = getApplication<Application>().getString(R.string.error_update_schedule_status_failed, e.message)
            }
        }
    }
    
    /**
     * 插入排程
     * @param schedule 要插入的排程
     */
    fun insertSchedule(schedule: Schedule) {
        viewModelScope.launch {
            try {
                repository.insertSchedule(schedule)
                _successMessage.value = getApplication<Application>().getString(R.string.success_schedule_restored, schedule.getDisplayName())
            } catch (e: Exception) {
                _error.value = getApplication<Application>().getString(R.string.error_insert_schedule_failed, e.message)
            }
        }
    }
    
    /**
     * 刪除排程
     * @param schedule 要刪除的排程
     */
    fun deleteSchedule(schedule: Schedule) {
        viewModelScope.launch {
            try {
                repository.deleteSchedule(schedule)
                _successMessage.value = getApplication<Application>().getString(R.string.success_schedule_deleted, schedule.getDisplayName())
            } catch (e: Exception) {
                _error.value = getApplication<Application>().getString(R.string.error_delete_schedule_failed, e.message)
            }
        }
    }
    
    /**
     * 清除錯誤狀態
     */
    fun clearError() {
        _error.value = null
    }
    
    /**
     * 清除成功訊息
     */
    fun clearSuccessMessage() {
        _successMessage.value = null
    }
    
    /**
     * 初始化測試數據 (僅用於開發測試)
     */
    fun initializeTestData() {
        viewModelScope.launch {
            try {
                val count = repository.getScheduleCount()
                if (count == 0) {
                    repository.insertTestData()
                }
            } catch (e: Exception) {
                _error.value = getApplication<Application>().getString(R.string.error_init_test_data_failed, e.message)
            }
        }
    }

    /**
     * 重新排序排程列表
     * @param schedules 重新排序後的排程列表
     */
    fun reorderSchedules(schedules: List<Schedule>) {
        viewModelScope.launch {
            try {
                repository.reorderSchedules(schedules)
                _successMessage.value = getApplication<Application>().getString(R.string.success_schedule_reordered)
            } catch (e: Exception) {
                _error.value = getApplication<Application>().getString(R.string.error_reorder_schedule_failed, e.message)
            }
        }
    }

    /**
     * 更新排程的排序順序
     * @param scheduleId 排程 ID
     * @param sortOrder 新的排序順序
     */
    fun updateSortOrder(scheduleId: Long, sortOrder: Int) {
        viewModelScope.launch {
            try {
                repository.updateSortOrder(scheduleId, sortOrder)
            } catch (e: Exception) {
                _error.value = getApplication<Application>().getString(R.string.error_update_sort_order_failed, e.message)
            }
        }
    }
}