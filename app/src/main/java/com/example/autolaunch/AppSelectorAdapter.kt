package com.example.autolaunch

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.autolaunch.databinding.ItemAppSelectorBinding
import com.example.autolaunch.model.AppInfo

/**
 * 應用程式選擇器 RecyclerView 適配器
 * 顯示可選擇的應用程式列表
 */
class AppSelectorAdapter(
    private val onAppClick: (AppInfo) -> Unit
) : ListAdapter<AppInfo, AppSelectorAdapter.AppViewHolder>(AppDiffCallback()) {
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AppViewHolder {
        val binding = ItemAppSelectorBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return AppViewHolder(binding, onAppClick)
    }
    
    override fun onBindViewHolder(holder: AppViewHolder, position: Int) {
        holder.bind(getItem(position))
    }
    
    /**
     * ViewHolder 類別
     */
    class AppViewHolder(
        private val binding: ItemAppSelectorBinding,
        private val onAppClick: (AppInfo) -> Unit
    ) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(appInfo: AppInfo) {
            binding.apply {
                // 設定應用程式名稱
                textAppName.text = appInfo.appName
                
                // 設定包名
                textPackageName.text = appInfo.packageName
                
                // 設定應用程式圖示
                appInfo.icon?.let { icon ->
                    imageAppIcon.setImageDrawable(icon)
                } ?: run {
                    // 如果沒有圖示，使用預設圖示
                    imageAppIcon.setImageResource(R.drawable.ic_android)
                }
                
                // 設定版本資訊
                val versionInfo = appInfo.getVersionInfo()
                if (versionInfo.isNotEmpty()) {
                    textVersionInfo.text = versionInfo
                } else {
                    textVersionInfo.text = ""
                }
                
                // 設定系統應用程式標記
                if (appInfo.isSystemApp) {
                    textSystemApp.text = itemView.context.getString(R.string.label_system_app)
                    textSystemApp.visibility = android.view.View.VISIBLE
                } else {
                    textSystemApp.visibility = android.view.View.GONE
                }
                
                // 設定點擊事件
                root.setOnClickListener {
                    onAppClick(appInfo)
                }
                
                // 設定長按事件（可選，顯示更多資訊）
                root.setOnLongClickListener {
                    // 可以在此處顯示應用程式詳細資訊
                    true
                }
            }
        }
    }
    
    /**
     * DiffUtil 回調，用於高效更新列表
     */
    private class AppDiffCallback : DiffUtil.ItemCallback<AppInfo>() {
        override fun areItemsTheSame(oldItem: AppInfo, newItem: AppInfo): Boolean {
            return oldItem.packageName == newItem.packageName
        }
        
        override fun areContentsTheSame(oldItem: AppInfo, newItem: AppInfo): Boolean {
            return oldItem == newItem
        }
    }
} 