package com.example.autolaunch.base

import android.content.Context
import android.os.Bundle
import android.util.Log
import androidx.appcompat.app.AppCompatActivity
import com.example.autolaunch.utils.LanguageManager
import com.example.autolaunch.utils.ThemeManager

/**
 * 基礎 Activity 類別
 * 提供語言設定和主題應用等通用功能
 */
abstract class BaseActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "BaseActivity"
    }

    protected lateinit var themeManager: ThemeManager
    private var currentThemeId: String? = null

    override fun attachBaseContext(newBase: Context?) {
        super.attachBaseContext(newBase?.let { LanguageManager.applyLanguage(it) })
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        // 在 setContentView 之前應用主題
        themeManager = ThemeManager.getInstance(this)
        themeManager.applyTheme(this)

        // 記錄當前主題ID
        currentThemeId = themeManager.getCurrentTheme().id
        Log.d(TAG, "onCreate: Applied theme ${currentThemeId}")

        super.onCreate(savedInstanceState)
    }

    override fun onResume() {
        super.onResume()

        // 檢查主題是否已變更
        val newThemeId = themeManager.getCurrentTheme().id
        if (currentThemeId != newThemeId) {
            Log.d(TAG, "Theme changed from $currentThemeId to $newThemeId, recreating activity")
            // 主題已變更，重新創建Activity
            recreate()
        }
    }
}
