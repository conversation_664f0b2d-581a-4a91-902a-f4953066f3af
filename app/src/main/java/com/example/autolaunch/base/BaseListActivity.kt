package com.example.autolaunch.base

import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.view.View
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.example.autolaunch.R
import com.example.autolaunch.utils.ExceptionHandler
import com.google.android.material.appbar.MaterialToolbar
import com.google.android.material.button.MaterialButton
import com.google.android.material.textview.MaterialTextView

/**
 * 基礎列表 Activity
 * 提供通用的列表頁面功能和 UI 設置
 */
abstract class BaseListActivity : BaseActivity() {
    
    companion object {
        private const val TAG = "BaseListActivity"
    }
    
    // 抽象屬性，由子類實現
    protected abstract val toolbar: MaterialToolbar
    protected abstract val recyclerView: RecyclerView
    protected abstract val swipeRefreshLayout: SwipeRefreshLayout
    protected abstract val emptyStateView: View?
    protected abstract val loadingView: View?
    protected abstract val errorView: View?
    protected abstract val retryButton: MaterialButton?
    protected abstract val emptyStateText: MaterialTextView?
    
    // 可選的統計信息視圖
    protected open val statisticsView: View? = null
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        ExceptionHandler.safeExecute(this) {
            setupBaseUI()
            setupBaseRecyclerView()
            setupBaseWindowInsets()
            setupBaseSwipeRefresh()
            setupBaseErrorHandling()
            
            // 調用子類的具體設置
            onSetupComplete()
        }
    }
    
    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        val menuRes = getMenuResource()
        if (menuRes != 0) {
            menuInflater.inflate(menuRes, menu)
            return true
        }
        return super.onCreateOptionsMenu(menu)
    }
    
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                true
            }
            R.id.action_refresh -> {
                onRefreshRequested()
                true
            }
            R.id.action_clear -> {
                onClearRequested()
                true
            }
            else -> handleMenuItemSelected(item) || super.onOptionsItemSelected(item)
        }
    }
    
    /**
     * 設置基礎 UI
     */
    private fun setupBaseUI() {
        // 設置工具列
        toolbar.setNavigationIcon(androidx.appcompat.R.drawable.abc_ic_ab_back_material)
        toolbar.setNavigationOnClickListener { onBackPressed() }
        setSupportActionBar(toolbar)
        
        // 設置標題
        toolbar.title = getActivityTitle()
    }
    
    /**
     * 設置基礎 RecyclerView
     */
    private fun setupBaseRecyclerView() {
        recyclerView.layoutManager = createLayoutManager()
        recyclerView.adapter = createAdapter()
        
        // 添加通用的滾動監聽器
        recyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                onRecyclerViewScrolled(dx, dy)
            }
        })
    }
    
    /**
     * 設置基礎窗口插入
     */
    private fun setupBaseWindowInsets() {
        ViewCompat.setOnApplyWindowInsetsListener(getRootView()) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }
    }
    
    /**
     * 設置基礎下拉刷新
     */
    private fun setupBaseSwipeRefresh() {
        swipeRefreshLayout.setOnRefreshListener {
            onRefreshRequested()
        }
    }
    
    /**
     * 設置基礎錯誤處理
     */
    private fun setupBaseErrorHandling() {
        retryButton?.setOnClickListener {
            onRetryRequested()
        }
    }
    
    /**
     * 更新載入狀態
     */
    protected fun updateLoadingState(isLoading: Boolean) {
        ExceptionHandler.safeExecute(this) {
            swipeRefreshLayout.isRefreshing = isLoading
            loadingView?.visibility = if (isLoading && !hasData()) View.VISIBLE else View.GONE
        }
    }
    
    /**
     * 更新空狀態
     */
    protected fun updateEmptyState(isEmpty: Boolean, message: String? = null) {
        ExceptionHandler.safeExecute(this) {
            emptyStateView?.visibility = if (isEmpty && !isLoading()) View.VISIBLE else View.GONE
            message?.let { emptyStateText?.text = it }
        }
    }
    
    /**
     * 更新錯誤狀態
     */
    protected fun updateErrorState(hasError: Boolean, errorMessage: String? = null) {
        ExceptionHandler.safeExecute(this) {
            errorView?.visibility = if (hasError) View.VISIBLE else View.GONE
            // 可以在這裡設置錯誤訊息
        }
    }
    
    /**
     * 顯示錯誤訊息
     */
    protected fun showErrorMessage(message: String) {
        ExceptionHandler.safeExecute(this) {
            // 可以使用 Snackbar 或 Toast 顯示錯誤
            android.widget.Toast.makeText(this, message, android.widget.Toast.LENGTH_LONG).show()
        }
    }
    
    /**
     * 更新統計信息
     */
    protected fun updateStatistics(stats: Map<String, Any>) {
        ExceptionHandler.safeExecute(this) {
            onStatisticsUpdated(stats)
        }
    }
    
    // 抽象方法，由子類實現
    protected abstract fun getActivityTitle(): String
    protected abstract fun getRootView(): View
    protected abstract fun createLayoutManager(): RecyclerView.LayoutManager
    protected abstract fun createAdapter(): RecyclerView.Adapter<*>
    protected abstract fun onSetupComplete()
    protected abstract fun onRefreshRequested()
    protected abstract fun onRetryRequested()
    
    // 可選的方法，子類可以覆蓋
    protected open fun getMenuResource(): Int = 0
    protected open fun handleMenuItemSelected(item: MenuItem): Boolean = false
    protected open fun onClearRequested() {}
    protected open fun onRecyclerViewScrolled(dx: Int, dy: Int) {}
    protected open fun onStatisticsUpdated(stats: Map<String, Any>) {}
    protected open fun hasData(): Boolean = false
    protected open fun isLoading(): Boolean = swipeRefreshLayout.isRefreshing
    
    /**
     * 創建預設的 LinearLayoutManager
     */
    protected fun createLinearLayoutManager(): LinearLayoutManager {
        return LinearLayoutManager(this)
    }
}

/**
 * 基礎列表 Fragment
 * 提供通用的列表 Fragment 功能
 */
abstract class BaseListFragment : androidx.fragment.app.Fragment() {
    
    companion object {
        private const val TAG = "BaseListFragment"
    }
    
    // 抽象屬性，由子類實現
    protected abstract val recyclerView: RecyclerView
    protected abstract val swipeRefreshLayout: SwipeRefreshLayout
    protected abstract val emptyStateView: View?
    protected abstract val loadingView: View?
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        ExceptionHandler.safeExecute(requireContext()) {
            setupFragmentRecyclerView()
            setupFragmentSwipeRefresh()
            onFragmentSetupComplete()
        }
    }
    
    private fun setupFragmentRecyclerView() {
        recyclerView.layoutManager = createFragmentLayoutManager()
        recyclerView.adapter = createFragmentAdapter()
    }
    
    private fun setupFragmentSwipeRefresh() {
        swipeRefreshLayout.setOnRefreshListener {
            onFragmentRefreshRequested()
        }
    }
    
    protected fun updateFragmentLoadingState(isLoading: Boolean) {
        ExceptionHandler.safeExecute(requireContext()) {
            swipeRefreshLayout.isRefreshing = isLoading
            loadingView?.visibility = if (isLoading && !hasFragmentData()) View.VISIBLE else View.GONE
        }
    }
    
    protected fun updateFragmentEmptyState(isEmpty: Boolean) {
        ExceptionHandler.safeExecute(requireContext()) {
            emptyStateView?.visibility = if (isEmpty && !isFragmentLoading()) View.VISIBLE else View.GONE
        }
    }
    
    // 抽象方法，由子類實現
    protected abstract fun createFragmentLayoutManager(): RecyclerView.LayoutManager
    protected abstract fun createFragmentAdapter(): RecyclerView.Adapter<*>
    protected abstract fun onFragmentSetupComplete()
    protected abstract fun onFragmentRefreshRequested()
    
    // 可選的方法，子類可以覆蓋
    protected open fun hasFragmentData(): Boolean = false
    protected open fun isFragmentLoading(): Boolean = swipeRefreshLayout.isRefreshing
}
