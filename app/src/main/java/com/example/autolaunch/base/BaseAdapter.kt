package com.example.autolaunch.base

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import androidx.viewbinding.ViewBinding
import com.example.autolaunch.utils.ExceptionHandler

/**
 * 基礎 Adapter 類別
 * 提供通用的 RecyclerView Adapter 功能
 */
abstract class BaseAdapter<T : Any, VB : ViewBinding>(
    diffCallback: DiffUtil.ItemCallback<T>
) : ListAdapter<T, BaseAdapter.BaseViewHolder<VB>>(diffCallback) {
    
    companion object {
        private const val TAG = "BaseAdapter"
    }
    
    // 點擊事件監聽器
    private var onItemClickListener: ((T, Int) -> Unit)? = null
    private var onItemLongClickListener: ((T, Int) -> Boolean)? = null
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseViewHolder<VB> {
        return ExceptionHandler.safeExecute(parent.context) {
            val binding = createBinding(LayoutInflater.from(parent.context), parent)
            BaseViewHolder(binding)
        } ?: throw RuntimeException("Failed to create ViewHolder")
    }
    
    override fun onBindViewHolder(holder: BaseViewHolder<VB>, position: Int) {
        ExceptionHandler.safeExecute(holder.itemView.context) {
            val item = getItem(position)
            
            // 綁定數據
            bindItem(holder.binding, item, position)
            
            // 設置點擊事件
            holder.itemView.setOnClickListener {
                onItemClickListener?.invoke(item, position)
            }
            
            holder.itemView.setOnLongClickListener {
                onItemLongClickListener?.invoke(item, position) ?: false
            }
        }
    }
    
    /**
     * 設置項目點擊監聽器
     */
    fun setOnItemClickListener(listener: (T, Int) -> Unit) {
        onItemClickListener = listener
    }
    
    /**
     * 設置項目長按監聽器
     */
    fun setOnItemLongClickListener(listener: (T, Int) -> Boolean) {
        onItemLongClickListener = listener
    }
    
    /**
     * 獲取指定位置的項目
     */
    fun getItemAtPosition(position: Int): T? {
        return if (position in 0 until itemCount) {
            getItem(position)
        } else null
    }
    
    /**
     * 安全提交列表
     */
    fun safeSubmitList(list: List<T>?) {
        ExceptionHandler.safeExecute {
            submitList(list)
        }
    }
    
    // 抽象方法，由子類實現
    protected abstract fun createBinding(inflater: LayoutInflater, parent: ViewGroup): VB
    protected abstract fun bindItem(binding: VB, item: T, position: Int)
    
    /**
     * 基礎 ViewHolder
     */
    class BaseViewHolder<VB : ViewBinding>(val binding: VB) : RecyclerView.ViewHolder(binding.root)
}

/**
 * 多類型 Adapter 基類
 * 支持多種視圖類型的 Adapter
 */
abstract class BaseMultiTypeAdapter<T : Any>(
    diffCallback: DiffUtil.ItemCallback<T>
) : ListAdapter<T, RecyclerView.ViewHolder>(diffCallback) {
    
    companion object {
        private const val TAG = "BaseMultiTypeAdapter"
    }
    
    // 點擊事件監聽器
    private var onItemClickListener: ((T, Int) -> Unit)? = null
    private var onItemLongClickListener: ((T, Int) -> Boolean)? = null
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return ExceptionHandler.safeExecute(parent.context) {
            createViewHolderForType(LayoutInflater.from(parent.context), parent, viewType)
        } ?: throw RuntimeException("Failed to create ViewHolder for type $viewType")
    }
    
    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        ExceptionHandler.safeExecute(holder.itemView.context) {
            val item = getItem(position)
            
            // 綁定數據
            bindViewHolderForType(holder, item, position)
            
            // 設置點擊事件
            holder.itemView.setOnClickListener {
                onItemClickListener?.invoke(item, position)
            }
            
            holder.itemView.setOnLongClickListener {
                onItemLongClickListener?.invoke(item, position) ?: false
            }
        }
    }
    
    override fun getItemViewType(position: Int): Int {
        return getViewTypeForPosition(position)
    }
    
    /**
     * 設置項目點擊監聽器
     */
    fun setOnItemClickListener(listener: (T, Int) -> Unit) {
        onItemClickListener = listener
    }
    
    /**
     * 設置項目長按監聽器
     */
    fun setOnItemLongClickListener(listener: (T, Int) -> Boolean) {
        onItemLongClickListener = listener
    }
    
    /**
     * 安全提交列表
     */
    fun safeSubmitList(list: List<T>?) {
        ExceptionHandler.safeExecute {
            submitList(list)
        }
    }
    
    // 抽象方法，由子類實現
    protected abstract fun getViewTypeForPosition(position: Int): Int
    protected abstract fun createViewHolderForType(
        inflater: LayoutInflater,
        parent: ViewGroup,
        viewType: Int
    ): RecyclerView.ViewHolder
    protected abstract fun bindViewHolderForType(
        holder: RecyclerView.ViewHolder,
        item: T,
        position: Int
    )
}

/**
 * 通用的 DiffUtil.ItemCallback
 */
abstract class BaseDiffCallback<T : Any> : DiffUtil.ItemCallback<T>() {
    
    override fun areItemsTheSame(oldItem: T, newItem: T): Boolean {
        return getItemId(oldItem) == getItemId(newItem)
    }
    
    override fun areContentsTheSame(oldItem: T, newItem: T): Boolean {
        return oldItem == newItem
    }
    
    // 抽象方法，由子類實現
    protected abstract fun getItemId(item: T): Any
}

/**
 * 簡單的 DiffUtil.ItemCallback 實現
 * 適用於有唯一 ID 的數據類
 */
class SimpleDiffCallback<T : Any>(
    private val getId: (T) -> Any
) : DiffUtil.ItemCallback<T>() {
    
    override fun areItemsTheSame(oldItem: T, newItem: T): Boolean {
        return getId(oldItem) == getId(newItem)
    }
    
    override fun areContentsTheSame(oldItem: T, newItem: T): Boolean {
        return oldItem == newItem
    }
}

/**
 * 帶有頭部和尾部的 Adapter
 */
abstract class BaseHeaderFooterAdapter<T : Any, H : Any, F : Any>(
    diffCallback: DiffUtil.ItemCallback<Any>
) : BaseMultiTypeAdapter<Any>(diffCallback) {
    
    companion object {
        const val TYPE_HEADER = 0
        const val TYPE_ITEM = 1
        const val TYPE_FOOTER = 2
    }
    
    private var headerItem: H? = null
    private var footerItem: F? = null
    private var items: List<T> = emptyList()
    
    override fun getViewTypeForPosition(position: Int): Int {
        return when {
            hasHeader() && position == 0 -> TYPE_HEADER
            hasFooter() && position == itemCount - 1 -> TYPE_FOOTER
            else -> TYPE_ITEM
        }
    }
    
    override fun getItemCount(): Int {
        var count = items.size
        if (hasHeader()) count++
        if (hasFooter()) count++
        return count
    }
    
    /**
     * 設置頭部項目
     */
    fun setHeader(header: H?) {
        headerItem = header
        notifyDataSetChanged()
    }
    
    /**
     * 設置尾部項目
     */
    fun setFooter(footer: F?) {
        footerItem = footer
        notifyDataSetChanged()
    }
    
    /**
     * 設置主要項目列表
     */
    fun setItems(newItems: List<T>) {
        items = newItems
        notifyDataSetChanged()
    }
    
    /**
     * 獲取實際的項目（不包括頭部和尾部）
     */
    fun getActualItem(position: Int): T? {
        val actualPosition = when {
            hasHeader() -> position - 1
            else -> position
        }
        return if (actualPosition in items.indices) items[actualPosition] else null
    }
    
    private fun hasHeader(): Boolean = headerItem != null
    private fun hasFooter(): Boolean = footerItem != null
    
    // 抽象方法，由子類實現
    protected abstract fun createHeaderViewHolder(
        inflater: LayoutInflater,
        parent: ViewGroup
    ): RecyclerView.ViewHolder
    
    protected abstract fun createItemViewHolder(
        inflater: LayoutInflater,
        parent: ViewGroup
    ): RecyclerView.ViewHolder
    
    protected abstract fun createFooterViewHolder(
        inflater: LayoutInflater,
        parent: ViewGroup
    ): RecyclerView.ViewHolder
    
    protected abstract fun bindHeader(holder: RecyclerView.ViewHolder, header: H)
    protected abstract fun bindItem(holder: RecyclerView.ViewHolder, item: T, position: Int)
    protected abstract fun bindFooter(holder: RecyclerView.ViewHolder, footer: F)
}
