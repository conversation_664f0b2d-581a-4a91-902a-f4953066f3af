package com.example.autolaunch.utils

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.PowerManager
import android.provider.Settings
import android.util.Log
import androidx.appcompat.app.AlertDialog

/**
 * 電池優化檢查和引導工具類
 * 幫助用戶將應用程式加入電池優化白名單
 */
object BatteryOptimizationHelper {
    
    private const val TAG = "BatteryOptimization"
    
    /**
     * 檢查應用程式是否在電池優化白名單中
     * @param context 上下文
     * @return true 如果已在白名單中，false 如果需要用戶手動添加
     */
    fun isIgnoringBatteryOptimizations(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
            powerManager.isIgnoringBatteryOptimizations(context.packageName)
        } else {
            // Android 6.0 以下版本沒有電池優化功能
            true
        }
    }
    
    /**
     * 請求用戶將應用程式加入電池優化白名單
     * @param context 上下文
     * @return Intent 如果成功創建，null 如果不支援
     */
    @SuppressLint("BatteryLife")
    fun createBatteryOptimizationIntent(context: Context): Intent? {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS).apply {
                data = Uri.parse("package:${context.packageName}")
            }
        } else {
            null
        }
    }
    
    /**
     * 請求忽略電池優化
     */
    fun requestIgnoreBatteryOptimizations(context: Context) {
        createBatteryOptimizationIntent(context)?.let {
            it.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            context.startActivity(it)
        }
    }
    
    /**
     * 創建降級的設定 Intent（跳轉到電池設定頁面）
     */
    private fun createFallbackIntent(): Intent? {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                Intent(Settings.ACTION_IGNORE_BATTERY_OPTIMIZATION_SETTINGS)
            } else {
                Intent(Settings.ACTION_BATTERY_SAVER_SETTINGS)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to create fallback intent", e)
            null
        }
    }
    
    /**
     * 獲取電池優化說明文字
     */
    fun getBatteryOptimizationExplanation(): String {
        return """
            為確保排程能準時執行，建議將 AutoLaunch 加入電池優化白名單。
            
            這樣可以防止系統在休眠模式下限制應用程式的後台活動，
            確保您的排程能夠準確觸發。
            
            點擊「前往設定」按鈕，然後選擇「允許」或「不優化」。
        """.trimIndent()
    }
    
    /**
     * 獲取各品牌手機的額外設定提示
     */
    fun getManufacturerSpecificHints(): String {
        val manufacturer = Build.MANUFACTURER.lowercase()
        
        return when {
            manufacturer.contains("xiaomi") || manufacturer.contains("redmi") -> 
                "小米/紅米用戶：請同時在「安全中心」>「應用管理」>「自啟動管理」中允許 AutoLaunch 自啟動。"
            
            manufacturer.contains("huawei") || manufacturer.contains("honor") -> 
                "華為/榮耀用戶：請在「手機管家」>「應用啟動管理」中設定 AutoLaunch 為手動管理，並開啟所有權限。"
            
            manufacturer.contains("oppo") -> 
                "OPPO 用戶：請在「設定」>「電池」>「應用耗電保護」中關閉 AutoLaunch 的耗電保護。"
            
            manufacturer.contains("vivo") -> 
                "vivo 用戶：請在「i管家」>「應用管理」>「自啟動」中允許 AutoLaunch 自啟動。"
            
            manufacturer.contains("oneplus") -> 
                "OnePlus 用戶：請在「設定」>「電池」>「電池優化」中將 AutoLaunch 設為「不優化」。"
            
            manufacturer.contains("samsung") -> 
                "三星用戶：請在「設定」>「裝置管理」>「電池」>「應用程式省電」中將 AutoLaunch 移出省電清單。"
            
            else -> 
                "如果排程無法正常觸發，請檢查是否還有其他電池管理或自啟動相關設定需要調整。"
        }
    }
    
    /**
     * 檢查是否需要顯示電池優化提示
     * @param context 上下文
     * @return true 如果需要顯示提示
     */
    fun shouldShowBatteryOptimizationPrompt(context: Context): Boolean {
        // 檢查是否在模擬器環境
        if (EmulatorHelper.isRunningOnEmulator()) {
            Log.d(TAG, "Running on emulator, skipping battery optimization prompt")
            return false
        }
        
        // 如果已經在白名單中，不需要提示
        if (isIgnoringBatteryOptimizations(context)) {
            return false
        }
        
        // 檢查 SharedPreferences 是否用戶已經拒絕過
        val prefs = context.getSharedPreferences("battery_optimization", Context.MODE_PRIVATE)
        val userDismissed = prefs.getBoolean("user_dismissed", false)
        val lastPromptTime = prefs.getLong("last_prompt_time", 0)
        val currentTime = System.currentTimeMillis()
        
        // 如果用戶拒絕過，且距離上次提示不超過 7 天，不再提示
        if (userDismissed && (currentTime - lastPromptTime) < 7 * 24 * 60 * 60 * 1000L) {
            return false
        }
        
        return true
    }
    
    /**
     * 記錄用戶已查看電池優化提示
     */
    fun markPromptShown(context: Context) {
        val prefs = context.getSharedPreferences("battery_optimization", Context.MODE_PRIVATE)
        prefs.edit()
            .putLong("last_prompt_time", System.currentTimeMillis())
            .apply()
    }
    
    /**
     * 記錄用戶已拒絕電池優化設定
     */
    fun markPromptDismissed(context: Context) {
        val prefs = context.getSharedPreferences("battery_optimization", Context.MODE_PRIVATE)
        prefs.edit()
            .putBoolean("user_dismissed", true)
            .putLong("last_prompt_time", System.currentTimeMillis())
            .apply()
    }
    
    /**
     * 重置提示狀態（用於測試或用戶主動想要重新設定時）
     */
    fun resetPromptState(context: Context) {
        val prefs = context.getSharedPreferences("battery_optimization", Context.MODE_PRIVATE)
        prefs.edit().clear().apply()
    }
} 