package com.example.autolaunch.utils

import android.content.Context
import android.os.Build
import android.util.Log

/**
 * 模擬器環境檢測和適配工具類
 * 用於處理模擬器環境下的特殊情況
 */
object EmulatorHelper {
    
    private const val TAG = "EmulatorHelper"
    
    /**
     * 檢查是否在模擬器環境運行
     */
    fun isRunningOnEmulator(): <PERSON><PERSON><PERSON> {
        val isEmulator = Build.FINGERPRINT.startsWith("generic") ||
                Build.FINGERPRINT.startsWith("unknown") ||
                Build.MODEL.contains("google_sdk") ||
                Build.MODEL.contains("Emulator") ||
                Build.MODEL.contains("Android SDK built for x86") ||
                Build.MANUFACTURER.contains("Genymotion") ||
                Build.BRAND.startsWith("generic") && Build.DEVICE.startsWith("generic") ||
                "google_sdk" == Build.PRODUCT ||
                Build.HARDWARE.contains("goldfish") ||
                Build.HARDWARE.contains("ranchu")
        
        if (isEmulator) {
            Log.d(TAG, "Detected emulator environment: ${Build.MODEL}, ${Build.MANUFACTURER}")
        }
        
        return isEmulator
    }
    
    /**
     * 獲取模擬器適配提示信息
     */
    fun getEmulatorAdaptationInfo(): String {
        return """
            檢測到模擬器環境，已自動調整以下功能：
            
            1. 跳過電池優化權限請求
            2. 禁用部分可能導致崩潰的系統級權限
            3. 使用模擬器友好的排程機制
            
            在真機環境下，這些功能將正常運行。
        """.trimIndent()
    }
    
    /**
     * 檢查模擬器環境下是否支援特定功能
     */
    fun isFeatureSupportedInEmulator(feature: EmulatorFeature): Boolean {
        return when (feature) {
            EmulatorFeature.BATTERY_OPTIMIZATION -> false
            EmulatorFeature.EXACT_ALARM -> true
            EmulatorFeature.BOOT_RECEIVER -> true
            EmulatorFeature.FOREGROUND_SERVICE -> true
            EmulatorFeature.NOTIFICATION -> true
        }
    }
    
    /**
     * 模擬器環境下支援的功能枚舉
     */
    enum class EmulatorFeature {
        BATTERY_OPTIMIZATION,    // 電池優化
        EXACT_ALARM,            // 精確鬧鐘
        BOOT_RECEIVER,          // 開機廣播
        FOREGROUND_SERVICE,     // 前台服務
        NOTIFICATION            // 通知
    }
    
    /**
     * 為模擬器環境調整應用行為
     */
    fun adaptForEmulator(context: Context) {
        Log.i(TAG, "Adapting application for emulator environment")
        
        // 記錄模擬器信息
        Log.d(TAG, "Device Info:")
        Log.d(TAG, "  Model: ${Build.MODEL}")
        Log.d(TAG, "  Manufacturer: ${Build.MANUFACTURER}")
        Log.d(TAG, "  Brand: ${Build.BRAND}")
        Log.d(TAG, "  Device: ${Build.DEVICE}")
        Log.d(TAG, "  Product: ${Build.PRODUCT}")
        Log.d(TAG, "  Hardware: ${Build.HARDWARE}")
        Log.d(TAG, "  Fingerprint: ${Build.FINGERPRINT}")
        
        // 在模擬器環境下設定特殊標記
        val prefs = context.getSharedPreferences("emulator_settings", Context.MODE_PRIVATE)
        prefs.edit()
            .putBoolean("is_emulator", true)
            .putLong("emulator_detected_time", System.currentTimeMillis())
            .apply()
    }
} 