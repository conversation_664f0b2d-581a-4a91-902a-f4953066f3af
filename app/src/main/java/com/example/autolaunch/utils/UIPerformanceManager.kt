package com.example.autolaunch.utils

import android.os.Handler
import android.os.Looper
import android.util.Log
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.*
import java.util.concurrent.Executors
import kotlin.coroutines.CoroutineContext

/**
 * UI 性能管理器
 * 防止主執行緒阻塞，優化 UI 響應性
 */
object UIPerformanceManager {
    
    private const val TAG = "UIPerformanceManager"
    
    // 主執行緒 Handler
    private val mainHandler = Handler(Looper.getMainLooper())
    
    // 背景執行緒池
    private val backgroundExecutor = Executors.newCachedThreadPool { runnable ->
        Thread(runnable, "UIPerformance-Background").apply {
            isDaemon = true
            priority = Thread.NORM_PRIORITY - 1
        }
    }
    
    // 背景 Dispatcher
    private val backgroundDispatcher = backgroundExecutor.asCoroutineDispatcher()
    
    /**
     * 在主執行緒上執行操作
     */
    fun runOnMainThread(action: () -> Unit) {
        if (Looper.myLooper() == Looper.getMainLooper()) {
            action()
        } else {
            mainHandler.post(action)
        }
    }
    
    /**
     * 延遲在主執行緒上執行操作
     */
    fun runOnMainThreadDelayed(delayMs: Long, action: () -> Unit) {
        mainHandler.postDelayed(action, delayMs)
    }
    
    /**
     * 在背景執行緒執行操作
     */
    fun runInBackground(action: () -> Unit) {
        backgroundExecutor.execute {
            try {
                action()
            } catch (e: Exception) {
                Log.e(TAG, "Background operation failed", e)
            }
        }
    }
    
    /**
     * 在背景執行緒執行操作並在主執行緒回調結果
     */
    fun <T> runInBackgroundWithCallback(
        backgroundOperation: () -> T,
        mainThreadCallback: (T?) -> Unit
    ) {
        backgroundExecutor.execute {
            val result = try {
                backgroundOperation()
            } catch (e: Exception) {
                Log.e(TAG, "Background operation with callback failed", e)
                null
            }
            
            mainHandler.post {
                try {
                    mainThreadCallback(result)
                } catch (e: Exception) {
                    Log.e(TAG, "Main thread callback failed", e)
                }
            }
        }
    }
    
    /**
     * 使用 Coroutine 在背景執行操作
     */
    suspend fun <T> runInBackgroundCoroutine(operation: suspend () -> T): T? {
        return try {
            withContext(backgroundDispatcher) {
                operation()
            }
        } catch (e: CancellationException) {
            Log.d(TAG, "Background coroutine cancelled")
            throw e
        } catch (e: Exception) {
            Log.e(TAG, "Background coroutine failed", e)
            null
        }
    }
    
    /**
     * 安全的 UI 更新操作
     * 確保在主執行緒上執行且處理異常
     */
    fun safeUpdateUI(action: () -> Unit) {
        runOnMainThread {
            try {
                action()
            } catch (e: Exception) {
                Log.e(TAG, "UI update failed", e)
            }
        }
    }
    
    /**
     * 批量 UI 更新
     * 將多個 UI 更新操作合併到一個主執行緒任務中
     */
    fun batchUpdateUI(actions: List<() -> Unit>) {
        runOnMainThread {
            actions.forEach { action ->
                try {
                    action()
                } catch (e: Exception) {
                    Log.e(TAG, "Batch UI update item failed", e)
                }
            }
        }
    }
    
    /**
     * 防抖動執行
     * 在指定時間內多次調用只執行最後一次
     */
    class Debouncer(private val delayMs: Long) {
        private var pendingRunnable: Runnable? = null
        
        fun execute(action: () -> Unit) {
            pendingRunnable?.let { mainHandler.removeCallbacks(it) }
            
            val runnable = Runnable {
                try {
                    action()
                } catch (e: Exception) {
                    Log.e(TAG, "Debounced action failed", e)
                }
                pendingRunnable = null
            }
            
            pendingRunnable = runnable
            mainHandler.postDelayed(runnable, delayMs)
        }
        
        fun cancel() {
            pendingRunnable?.let { mainHandler.removeCallbacks(it) }
            pendingRunnable = null
        }
    }
    
    /**
     * 節流執行
     * 在指定時間內最多執行一次
     */
    class Throttler(private val intervalMs: Long) {
        private var lastExecuteTime = 0L
        private var pendingRunnable: Runnable? = null
        
        fun execute(action: () -> Unit) {
            val currentTime = System.currentTimeMillis()
            val timeSinceLastExecute = currentTime - lastExecuteTime
            
            if (timeSinceLastExecute >= intervalMs) {
                // 立即執行
                lastExecuteTime = currentTime
                try {
                    action()
                } catch (e: Exception) {
                    Log.e(TAG, "Throttled action failed", e)
                }
            } else {
                // 延遲執行
                pendingRunnable?.let { mainHandler.removeCallbacks(it) }
                
                val delay = intervalMs - timeSinceLastExecute
                val runnable = Runnable {
                    lastExecuteTime = System.currentTimeMillis()
                    try {
                        action()
                    } catch (e: Exception) {
                        Log.e(TAG, "Throttled delayed action failed", e)
                    }
                    pendingRunnable = null
                }
                
                pendingRunnable = runnable
                mainHandler.postDelayed(runnable, delay)
            }
        }
        
        fun cancel() {
            pendingRunnable?.let { mainHandler.removeCallbacks(it) }
            pendingRunnable = null
        }
    }
    
    /**
     * 生命週期感知的協程執行
     */
    fun LifecycleOwner.launchSafely(
        context: CoroutineContext = Dispatchers.Main,
        start: CoroutineStart = CoroutineStart.DEFAULT,
        block: suspend CoroutineScope.() -> Unit
    ): Job {
        return lifecycleScope.launch(context, start) {
            try {
                block()
            } catch (e: CancellationException) {
                Log.d(TAG, "Lifecycle-aware coroutine cancelled")
                throw e
            } catch (e: Exception) {
                Log.e(TAG, "Lifecycle-aware coroutine failed", e)
            }
        }
    }
    
    /**
     * 檢查是否在主執行緒
     */
    fun isMainThread(): Boolean {
        return Looper.myLooper() == Looper.getMainLooper()
    }
    
    /**
     * 確保在主執行緒上執行，否則拋出異常
     */
    fun ensureMainThread() {
        if (!isMainThread()) {
            throw IllegalStateException("This operation must be performed on the main thread")
        }
    }
    
    /**
     * 確保不在主執行緒上執行，否則拋出異常
     */
    fun ensureBackgroundThread() {
        if (isMainThread()) {
            throw IllegalStateException("This operation must not be performed on the main thread")
        }
    }
    
    /**
     * 清理資源
     */
    fun cleanup() {
        try {
            backgroundExecutor.shutdown()
            mainHandler.removeCallbacksAndMessages(null)
            Log.i(TAG, "UIPerformanceManager cleaned up")
        } catch (e: Exception) {
            Log.e(TAG, "Error during cleanup", e)
        }
    }
}

/**
 * 擴展函數：為任何操作添加主執行緒執行包裝
 */
fun (() -> Unit).runOnMainThread() {
    UIPerformanceManager.runOnMainThread(this)
}

/**
 * 擴展函數：為任何操作添加背景執行緒執行包裝
 */
fun (() -> Unit).runInBackground() {
    UIPerformanceManager.runInBackground(this)
}
