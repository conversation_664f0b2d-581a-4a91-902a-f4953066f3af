package com.example.autolaunch.utils

import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView
import com.example.autolaunch.adapter.UnifiedScheduleAdapter

/**
 * ItemTouchHelper 回調，用於處理排程項目的拖拽排序和滑動刪除
 */
class ScheduleItemTouchHelper(
    private val adapter: UnifiedScheduleAdapter,
    private val onMoveFinished: (List<com.example.autolaunch.model.Schedule>) -> Unit,
    private val onSwipeToDelete: ((Int) -> Unit)? = null,
    private val isSwipeEnabled: ((Int) -> Boolean)? = null
) : ItemTouchHelper.SimpleCallback(
    ItemTouchHelper.UP or ItemTouchHelper.DOWN, // 支持上下拖拽
    ItemTouchHelper.LEFT or ItemTouchHelper.RIGHT // 支持左右滑動刪除
) {

    private var dragFrom = -1
    private var dragTo = -1

    override fun onMove(
        recyclerView: RecyclerView,
        viewHolder: RecyclerView.ViewHolder,
        target: RecyclerView.ViewHolder
    ): Boolean {
        val fromPosition = viewHolder.adapterPosition
        val toPosition = target.adapterPosition

        // 記錄拖拽的起始和結束位置
        if (dragFrom == -1) {
            dragFrom = fromPosition
        }
        dragTo = toPosition

        // 只允許在排程項目之間移動（不允許移動到標題項目）
        val fromItem = adapter.currentList.getOrNull(fromPosition)
        val toItem = adapter.currentList.getOrNull(toPosition)

        if (fromItem is UnifiedScheduleAdapter.ScheduleItem.ScheduleData && 
            toItem is UnifiedScheduleAdapter.ScheduleItem.ScheduleData) {
            adapter.moveItem(fromPosition, toPosition)
            return true
        }

        return false
    }

    override fun onSwiped(viewHolder: RecyclerView.ViewHolder, direction: Int) {
        val position = viewHolder.adapterPosition
        if (position != RecyclerView.NO_POSITION) {
            onSwipeToDelete?.invoke(position)
        }
    }

    override fun clearView(recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder) {
        super.clearView(recyclerView, viewHolder)

        // 拖拽結束時，通知外部更新排序
        if (dragFrom != -1 && dragTo != -1 && dragFrom != dragTo) {
            val reorderedSchedules = adapter.getCurrentScheduleOrder()
            onMoveFinished(reorderedSchedules)
        }

        // 重置拖拽位置
        dragFrom = -1
        dragTo = -1
    }

    override fun isLongPressDragEnabled(): Boolean {
        // 禁用默認的長按拖拽，我們使用自定義的長按事件
        return false
    }

    override fun isItemViewSwipeEnabled(): Boolean {
        // 根據回調決定是否啟用滑動
        return onSwipeToDelete != null
    }

    override fun getSwipeDirs(recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder): Int {
        val position = viewHolder.adapterPosition
        if (position == RecyclerView.NO_POSITION) return 0

        // 檢查是否可以滑動刪除
        val canSwipe = isSwipeEnabled?.invoke(position) ?: true
        return if (canSwipe) super.getSwipeDirs(recyclerView, viewHolder) else 0
    }

    /**
     * 檢查是否可以拖拽指定位置的項目
     */
    override fun canDropOver(
        recyclerView: RecyclerView,
        current: RecyclerView.ViewHolder,
        target: RecyclerView.ViewHolder
    ): Boolean {
        val currentItem = adapter.currentList.getOrNull(current.adapterPosition)
        val targetItem = adapter.currentList.getOrNull(target.adapterPosition)

        // 只允許在排程項目之間拖拽
        return currentItem is UnifiedScheduleAdapter.ScheduleItem.ScheduleData && 
               targetItem is UnifiedScheduleAdapter.ScheduleItem.ScheduleData
    }
}
