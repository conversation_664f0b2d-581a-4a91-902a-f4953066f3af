package com.example.autolaunch.utils

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Environment
import android.provider.DocumentsContract
import android.util.Log
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import java.text.SimpleDateFormat
import java.util.*

/**
 * 文件操作工具類
 * 提供文件選擇、保存、讀取等功能
 */
object FileUtils {
    
    private const val TAG = "FileUtils"
    
    /**
     * 創建文件選擇器Intent
     * @param mimeType MIME類型，例如 "application/json"
     * @return Intent
     */
    fun createFilePickerIntent(mimeType: String = "*/*"): Intent {
        return Intent(Intent.ACTION_OPEN_DOCUMENT).apply {
            addCategory(Intent.CATEGORY_OPENABLE)
            type = mimeType
        }
    }
    
    /**
     * 創建文件保存Intent
     * @param fileName 建議的文件名
     * @param mimeType MIME類型
     * @return Intent
     */
    fun createFileSaveIntent(fileName: String, mimeType: String = "application/json"): Intent {
        return Intent(Intent.ACTION_CREATE_DOCUMENT).apply {
            addCategory(Intent.CATEGORY_OPENABLE)
            type = mimeType
            putExtra(Intent.EXTRA_TITLE, fileName)
        }
    }
    
    /**
     * 從Uri讀取文件內容
     * @param context Context
     * @param uri 文件Uri
     * @return 文件內容字符串，失敗時返回null
     */
    fun readTextFromUri(context: Context, uri: Uri): String? {
        return try {
            context.contentResolver.openInputStream(uri)?.use { inputStream ->
                inputStream.bufferedReader().use { reader ->
                    reader.readText()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to read text from URI: $uri", e)
            null
        }
    }
    
    /**
     * 將文本內容寫入Uri
     * @param context Context
     * @param uri 目標Uri
     * @param content 要寫入的內容
     * @return 是否成功
     */
    fun writeTextToUri(context: Context, uri: Uri, content: String): Boolean {
        return try {
            context.contentResolver.openOutputStream(uri).safeUse { outputStream ->
                outputStream.write(content.toByteArray())
                true
            } ?: false
        } catch (e: Exception) {
            Log.e(TAG, "Failed to write text to URI: $uri", e)
            false
        }
    }
    
    /**
     * 獲取Downloads目錄下的AutoLaunch文件夾
     * @return File對象
     */
    fun getAutoLaunchDownloadsDir(): File {
        val downloadsDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS)
        return File(downloadsDir, "AutoLaunch")
    }
    
    /**
     * 確保目錄存在
     * @param dir 目錄File對象
     * @return 是否成功創建或已存在
     */
    fun ensureDirectoryExists(dir: File): Boolean {
        return if (!dir.exists()) {
            dir.mkdirs()
        } else {
            true
        }
    }
    
    /**
     * 生成帶時間戳的文件名
     * @param prefix 前綴
     * @param extension 擴展名（不包含點）
     * @return 文件名
     */
    fun generateTimestampedFileName(prefix: String, extension: String): String {
        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        return "${prefix}_${timestamp}.${extension}"
    }
    
    /**
     * 獲取文件大小的可讀格式
     * @param bytes 字節數
     * @return 格式化的大小字符串
     */
    fun formatFileSize(bytes: Long): String {
        return when {
            bytes < 1024 -> "$bytes B"
            bytes < 1024 * 1024 -> "${bytes / 1024} KB"
            bytes < 1024 * 1024 * 1024 -> "${bytes / (1024 * 1024)} MB"
            else -> "${bytes / (1024 * 1024 * 1024)} GB"
        }
    }
    
    /**
     * 檢查文件是否為JSON格式
     * @param fileName 文件名
     * @return 是否為JSON文件
     */
    fun isJsonFile(fileName: String): Boolean {
        return fileName.lowercase().endsWith(".json")
    }
    
    /**
     * 從Uri獲取文件名
     * @param context Context
     * @param uri 文件Uri
     * @return 文件名，獲取失敗時返回默認名稱
     */
    fun getFileNameFromUri(context: Context, uri: Uri): String {
        return try {
            context.contentResolver.query(uri, null, null, null, null)?.use { cursor ->
                val nameIndex = cursor.getColumnIndex(DocumentsContract.Document.COLUMN_DISPLAY_NAME)
                if (nameIndex >= 0 && cursor.moveToFirst()) {
                    cursor.getString(nameIndex)
                } else {
                    "unknown_file"
                }
            } ?: "unknown_file"
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get file name from URI: $uri", e)
            "unknown_file"
        }
    }
    
    /**
     * 複製InputStream到文件
     * @param inputStream 輸入流
     * @param targetFile 目標文件
     * @return 是否成功
     */
    fun copyInputStreamToFile(inputStream: InputStream, targetFile: File): Boolean {
        return try {
            FileOutputStream(targetFile).use { outputStream ->
                inputStream.copyTo(outputStream)
            }
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to copy input stream to file: ${targetFile.absolutePath}", e)
            false
        }
    }
    
    /**
     * 刪除文件
     * @param file 要刪除的文件
     * @return 是否成功
     */
    fun deleteFile(file: File): Boolean {
        return try {
            if (file.exists()) {
                file.delete()
            } else {
                true
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to delete file: ${file.absolutePath}", e)
            false
        }
    }
    
    /**
     * 獲取文件的MIME類型
     * @param fileName 文件名
     * @return MIME類型
     */
    fun getMimeType(fileName: String): String {
        return when {
            fileName.lowercase().endsWith(".json") -> "application/json"
            fileName.lowercase().endsWith(".txt") -> "text/plain"
            fileName.lowercase().endsWith(".xml") -> "application/xml"
            else -> "application/octet-stream"
        }
    }
    
    /**
     * 驗證文件是否可讀
     * @param file 文件對象
     * @return 是否可讀
     */
    fun isFileReadable(file: File): Boolean {
        return file.exists() && file.isFile && file.canRead()
    }
    
    /**
     * 驗證目錄是否可寫
     * @param dir 目錄對象
     * @return 是否可寫
     */
    fun isDirectoryWritable(dir: File): Boolean {
        return dir.exists() && dir.isDirectory && dir.canWrite()
    }
}

/**
 * 文件選擇器回調接口
 */
interface FilePickerCallback {
    fun onFileSelected(uri: Uri, fileName: String)
    fun onFileSaved(uri: Uri, fileName: String)
    fun onError(error: String)
    fun onCancelled()
}

/**
 * 文件選擇器助手類
 * 簡化Activity中的文件選擇操作
 */
class FilePickerHelper(private val activity: AppCompatActivity) {
    
    private var callback: FilePickerCallback? = null
    
    private val filePickerLauncher = activity.registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == AppCompatActivity.RESULT_OK) {
            result.data?.data?.let { uri ->
                val fileName = FileUtils.getFileNameFromUri(activity, uri)
                callback?.onFileSelected(uri, fileName)
            } ?: callback?.onError("無法獲取選擇的文件")
        } else {
            callback?.onCancelled()
        }
    }
    
    private val fileSaverLauncher = activity.registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == AppCompatActivity.RESULT_OK) {
            result.data?.data?.let { uri ->
                val fileName = FileUtils.getFileNameFromUri(activity, uri)
                callback?.onFileSaved(uri, fileName)
            } ?: callback?.onError("無法保存文件")
        } else {
            callback?.onCancelled()
        }
    }
    
    /**
     * 選擇文件
     * @param mimeType MIME類型
     * @param callback 回調接口
     */
    fun pickFile(mimeType: String = "application/json", callback: FilePickerCallback) {
        this.callback = callback
        val intent = FileUtils.createFilePickerIntent(mimeType)
        filePickerLauncher.launch(intent)
    }
    
    /**
     * 保存文件
     * @param fileName 建議的文件名
     * @param mimeType MIME類型
     * @param callback 回調接口
     */
    fun saveFile(fileName: String, mimeType: String = "application/json", callback: FilePickerCallback) {
        this.callback = callback
        val intent = FileUtils.createFileSaveIntent(fileName, mimeType)
        fileSaverLauncher.launch(intent)
    }
}
