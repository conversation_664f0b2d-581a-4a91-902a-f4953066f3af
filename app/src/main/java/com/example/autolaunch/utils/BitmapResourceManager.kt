package com.example.autolaunch.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import android.util.Log
import android.util.LruCache
import java.io.ByteArrayOutputStream
import java.io.InputStream
import java.lang.ref.WeakReference

/**
 * Bitmap 資源管理器
 * 提供 Bitmap 的安全創建、緩存和回收機制
 */
object BitmapResourceManager {
    
    private const val TAG = "BitmapResourceManager"
    private const val CACHE_SIZE = 4 * 1024 * 1024 // 4MB
    
    // LRU 緩存
    private val bitmapCache = LruCache<String, Bitmap>(CACHE_SIZE)
    
    // 弱引用集合，用於追蹤所有創建的 Bitmap
    private val activeBitmaps = mutableSetOf<WeakReference<Bitmap>>()
    
    /**
     * 從資源 ID 創建 Bitmap
     */
    fun createBitmapFromResource(context: Context, resourceId: Int, cacheKey: String? = null): Bitmap? {
        return ExceptionHandler.safeExecute(context) {
            val key = cacheKey ?: "res_$resourceId"
            
            // 檢查緩存
            bitmapCache.get(key)?.let { cachedBitmap ->
                if (!cachedBitmap.isRecycled) {
                    Log.d(TAG, "Bitmap loaded from cache: $key")
                    return@safeExecute cachedBitmap
                } else {
                    bitmapCache.remove(key)
                }
            }
            
            // 創建新的 Bitmap
            val bitmap = BitmapFactory.decodeResource(context.resources, resourceId)
            bitmap?.let {
                bitmapCache.put(key, it)
                trackBitmap(it)
                Log.d(TAG, "Bitmap created from resource: $key")
            }
            bitmap
        }
    }
    
    /**
     * 從 InputStream 創建 Bitmap
     */
    fun createBitmapFromStream(inputStream: InputStream, cacheKey: String? = null): Bitmap? {
        return ExceptionHandler.safeExecute {
            inputStream.safeUse { stream ->
                val bitmap = BitmapFactory.decodeStream(stream)
                bitmap?.let {
                    cacheKey?.let { key ->
                        bitmapCache.put(key, bitmap)
                    }
                    trackBitmap(bitmap)
                    Log.d(TAG, "Bitmap created from stream")
                }
                bitmap
            }
        }
    }
    
    /**
     * 從 Drawable 創建 Bitmap
     */
    fun createBitmapFromDrawable(drawable: Drawable): Bitmap? {
        return ExceptionHandler.safeExecute {
            when (drawable) {
                is BitmapDrawable -> {
                    val bitmap = drawable.bitmap
                    trackBitmap(bitmap)
                    bitmap
                }
                else -> {
                    val bitmap = Bitmap.createBitmap(
                        drawable.intrinsicWidth,
                        drawable.intrinsicHeight,
                        Bitmap.Config.ARGB_8888
                    )
                    val canvas = Canvas(bitmap)
                    drawable.setBounds(0, 0, canvas.width, canvas.height)
                    drawable.draw(canvas)
                    trackBitmap(bitmap)
                    Log.d(TAG, "Bitmap created from drawable")
                    bitmap
                }
            }
        }
    }
    
    /**
     * 創建縮放的 Bitmap
     */
    fun createScaledBitmap(source: Bitmap, width: Int, height: Int, filter: Boolean = true): Bitmap? {
        return ExceptionHandler.safeExecute {
            if (source.isRecycled) {
                Log.w(TAG, "Source bitmap is recycled, cannot scale")
                return@safeExecute null
            }
            
            val scaledBitmap = Bitmap.createScaledBitmap(source, width, height, filter)
            trackBitmap(scaledBitmap)
            Log.d(TAG, "Scaled bitmap created: ${width}x${height}")
            scaledBitmap
        }
    }
    
    /**
     * 壓縮 Bitmap 到指定大小
     */
    fun compressBitmap(
        bitmap: Bitmap,
        format: Bitmap.CompressFormat = Bitmap.CompressFormat.PNG,
        quality: Int = 90
    ): ByteArray? {
        return ExceptionHandler.safeExecute {
            if (bitmap.isRecycled) {
                Log.w(TAG, "Bitmap is recycled, cannot compress")
                return@safeExecute null
            }
            
            val outputStream = ByteArrayOutputStream()
            try {
                bitmap.compress(format, quality, outputStream)
                outputStream.toByteArray()
            } catch (e: Exception) {
                Log.e(TAG, "Error compressing bitmap", e)
                null
            } finally {
                try {
                    outputStream.close()
                } catch (e: Exception) {
                    Log.e(TAG, "Error closing output stream", e)
                }
            }
        }
    }
    
    /**
     * 安全回收 Bitmap
     */
    fun recycleBitmap(bitmap: Bitmap?) {
        ExceptionHandler.safeExecute {
            bitmap?.let {
                if (!it.isRecycled) {
                    it.recycle()
                    Log.d(TAG, "Bitmap recycled")
                }
                // 從追蹤集合中移除
                activeBitmaps.removeAll { ref -> ref.get() == bitmap || ref.get() == null }
            }
        }
    }
    
    /**
     * 清理緩存
     */
    fun clearCache() {
        ExceptionHandler.safeExecute {
            bitmapCache.evictAll()
            Log.i(TAG, "Bitmap cache cleared")
        }
    }
    
    /**
     * 清理所有資源
     */
    fun cleanup() {
        ExceptionHandler.safeExecute {
            // 回收所有追蹤的 Bitmap
            activeBitmaps.forEach { ref ->
                ref.get()?.let { bitmap ->
                    if (!bitmap.isRecycled) {
                        bitmap.recycle()
                    }
                }
            }
            activeBitmaps.clear()
            
            // 清理緩存
            clearCache()
            
            Log.i(TAG, "All bitmap resources cleaned up")
        }
    }
    
    /**
     * 獲取緩存統計信息
     */
    fun getCacheStats(): CacheStats {
        return CacheStats(
            cacheSize = bitmapCache.size().toInt(),
            maxCacheSize = bitmapCache.maxSize().toInt(),
            hitCount = bitmapCache.hitCount().toLong(),
            missCount = bitmapCache.missCount().toLong(),
            activeBitmapCount = activeBitmaps.count { it.get() != null }.toInt()
        )
    }
    
    /**
     * 追蹤 Bitmap
     */
    private fun trackBitmap(bitmap: Bitmap) {
        activeBitmaps.add(WeakReference(bitmap))
        // 清理無效的弱引用
        activeBitmaps.removeAll { it.get() == null }
    }
    
    /**
     * 檢查記憶體使用情況
     */
    fun checkMemoryUsage(): MemoryUsage {
        val runtime = Runtime.getRuntime()
        val usedMemory = runtime.totalMemory() - runtime.freeMemory()
        val maxMemory = runtime.maxMemory()
        val cacheMemory = bitmapCache.size()
        
        return MemoryUsage(
            usedMemory = usedMemory,
            maxMemory = maxMemory,
            cacheMemory = cacheMemory,
            memoryUsagePercent = (usedMemory * 100L) / maxMemory,
            cacheUsagePercent = (cacheMemory.toLong() * 100L) / CACHE_SIZE.toLong()
        )
    }
    
    /**
     * 緩存統計數據類
     */
    data class CacheStats(
        val cacheSize: Int,
        val maxCacheSize: Int,
        val hitCount: Long,
        val missCount: Long,
        val activeBitmapCount: Int
    ) {
        val hitRate: Float
            get() = if (hitCount + missCount > 0) {
                hitCount.toFloat() / (hitCount + missCount)
            } else 0f
    }
    
    /**
     * 記憶體使用數據類
     */
    data class MemoryUsage(
        val usedMemory: Long,
        val maxMemory: Long,
        val cacheMemory: Int,
        val memoryUsagePercent: Long,
        val cacheUsagePercent: Long
    )
}

/**
 * Bitmap 擴展函數：安全使用
 */
inline fun <T> Bitmap?.safeUse(block: (Bitmap) -> T): T? {
    return if (this != null && !this.isRecycled) {
        try {
            block(this)
        } catch (e: Exception) {
            Log.e("BitmapResourceManager", "Error using bitmap", e)
            null
        }
    } else {
        null
    }
}

/**
 * Bitmap 擴展函數：安全回收
 */
fun Bitmap?.safeRecycle() {
    BitmapResourceManager.recycleBitmap(this)
}
