package com.example.autolaunch.utils

import android.app.AlarmManager
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.PowerManager
import android.util.Log
import androidx.work.*
import com.example.autolaunch.AlarmManagerService
import com.example.autolaunch.model.AppDatabase
import com.example.autolaunch.service.ScheduleService
import com.example.autolaunch.worker.HealthCheckWorker
import com.example.autolaunch.worker.RecoveryWorker
import com.example.autolaunch.worker.ScheduleMonitorWorker
import com.example.autolaunch.worker.WatchdogWorker
import kotlinx.coroutines.flow.first
import java.util.concurrent.TimeUnit

/**
 * 背景執行可靠性管理器
 * 提供多層保護機制確保排程在各種情況下都能可靠執行
 */
class BackgroundExecutionReliabilityManager private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "BackgroundReliabilityManager"
        private const val HEALTH_CHECK_WORK_NAME = "schedule_health_check"
        private const val RECOVERY_WORK_NAME = "schedule_recovery"
        private const val WATCHDOG_WORK_NAME = "schedule_watchdog"
        
        @Volatile
        private var INSTANCE: BackgroundExecutionReliabilityManager? = null
        
        fun getInstance(context: Context): BackgroundExecutionReliabilityManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: BackgroundExecutionReliabilityManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val workManager = WorkManager.getInstance(context)
    private val alarmService = AlarmManagerService(context)
    private val database = AppDatabase.getDatabase(context)
    
    /**
     * 初始化可靠性保護機制
     */
    fun initialize() {
        Log.i(TAG, "Initializing background execution reliability")
        
        // 啟動多層保護機制
        startHealthCheckMonitoring()
        startWatchdogMonitoring()
        setupRecoveryMechanism()
        
        // 檢查並修復當前狀態
        performImmediateHealthCheck()
    }
    
    /**
     * 啟動健康檢查監控
     * 每30分鐘檢查一次系統狀態
     */
    private fun startHealthCheckMonitoring() {
        val healthCheckRequest = PeriodicWorkRequestBuilder<HealthCheckWorker>(
            30, TimeUnit.MINUTES,
            15, TimeUnit.MINUTES // 靈活間隔
        ).setConstraints(
            Constraints.Builder()
                .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
                .setRequiresBatteryNotLow(false)
                .setRequiresCharging(false)
                .setRequiresDeviceIdle(false)
                .setRequiresStorageNotLow(false)
                .build()
        ).build()
        
        workManager.enqueueUniquePeriodicWork(
            HEALTH_CHECK_WORK_NAME,
            ExistingPeriodicWorkPolicy.KEEP,
            healthCheckRequest
        )
        
        Log.i(TAG, "Health check monitoring started")
    }
    
    /**
     * 啟動看門狗監控
     * 每2小時進行深度檢查
     */
    private fun startWatchdogMonitoring() {
        val watchdogRequest = PeriodicWorkRequestBuilder<WatchdogWorker>(
            2, TimeUnit.HOURS,
            30, TimeUnit.MINUTES
        ).setConstraints(
            Constraints.Builder()
                .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
                .build()
        ).build()
        
        workManager.enqueueUniquePeriodicWork(
            WATCHDOG_WORK_NAME,
            ExistingPeriodicWorkPolicy.KEEP,
            watchdogRequest
        )
        
        Log.i(TAG, "Watchdog monitoring started")
    }
    
    /**
     * 設置恢復機制
     */
    private fun setupRecoveryMechanism() {
        // 註冊系統事件監聽
        registerSystemEventListeners()
        
        // 設置緊急恢復任務
        scheduleEmergencyRecovery()
    }
    
    /**
     * 執行即時健康檢查
     */
    private fun performImmediateHealthCheck() {
        val immediateHealthCheck = OneTimeWorkRequestBuilder<HealthCheckWorker>()
            .setExpedited(OutOfQuotaPolicy.RUN_AS_NON_EXPEDITED_WORK_REQUEST)
            .build()
        
        workManager.enqueue(immediateHealthCheck)
        Log.i(TAG, "Immediate health check scheduled")
    }
    
    /**
     * 註冊系統事件監聽器
     */
    private fun registerSystemEventListeners() {
        // 這些監聽器在 AndroidManifest.xml 中註冊
        // 包括：BOOT_COMPLETED, PACKAGE_REPLACED, BATTERY_LOW, POWER_CONNECTED 等
        Log.i(TAG, "System event listeners registered")
    }
    
    /**
     * 排程緊急恢復任務
     */
    private fun scheduleEmergencyRecovery() {
        val recoveryRequest = OneTimeWorkRequestBuilder<RecoveryWorker>()
            .setInitialDelay(5, TimeUnit.MINUTES)
            .setConstraints(
                Constraints.Builder()
                    .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
                    .build()
            )
            .build()
        
        workManager.enqueueUniqueWork(
            RECOVERY_WORK_NAME,
            ExistingWorkPolicy.REPLACE,
            recoveryRequest
        )
    }
    
    /**
     * 處理系統資源限制
     */
    fun handleSystemResourceConstraints() {
        ExceptionHandler.safeExecute(context) {
            // 檢查電池優化狀態
            if (!BatteryOptimizationHelper.isIgnoringBatteryOptimizations(context)) {
                Log.w(TAG, "Battery optimization is enabled, may affect background execution")
                // 記錄警告但不強制要求用戶關閉
            }
            
            // 檢查精確鬧鐘權限
            if (!AlarmManagerService.canScheduleExactAlarms(context)) {
                Log.w(TAG, "Exact alarm permission not granted, using inexact alarms")
                // 自動切換到非精確鬧鐘模式
            }
            
            // 檢查前台服務狀態
            if (!BackgroundExecutionHelper.isScheduleServiceRunning(context)) {
                Log.w(TAG, "Foreground service not running, attempting to restart")
                ScheduleService.startService(context)
            }
        }
    }
    
    /**
     * 多設備兼容性處理
     */
    fun handleDeviceCompatibility() {
        ExceptionHandler.safeExecute(context) {
            val manufacturer = Build.MANUFACTURER.lowercase()
            val model = Build.MODEL.lowercase()
            
            when {
                manufacturer.contains("xiaomi") -> handleXiaomiOptimizations()
                manufacturer.contains("huawei") -> handleHuaweiOptimizations()
                manufacturer.contains("oppo") -> handleOppoOptimizations()
                manufacturer.contains("vivo") -> handleVivoOptimizations()
                manufacturer.contains("samsung") -> handleSamsungOptimizations()
                manufacturer.contains("oneplus") -> handleOnePlusOptimizations()
                else -> handleGenericOptimizations()
            }
            
            Log.i(TAG, "Device compatibility handled for $manufacturer $model")
        }
    }
    
    private fun handleXiaomiOptimizations() {
        // 小米設備特殊處理
        Log.d(TAG, "Applying Xiaomi-specific optimizations")
        // 可以添加特定的權限檢查和設置引導
    }
    
    private fun handleHuaweiOptimizations() {
        // 華為設備特殊處理
        Log.d(TAG, "Applying Huawei-specific optimizations")
    }
    
    private fun handleOppoOptimizations() {
        // OPPO設備特殊處理
        Log.d(TAG, "Applying OPPO-specific optimizations")
    }
    
    private fun handleVivoOptimizations() {
        // Vivo設備特殊處理
        Log.d(TAG, "Applying Vivo-specific optimizations")
    }
    
    private fun handleSamsungOptimizations() {
        // 三星設備特殊處理
        Log.d(TAG, "Applying Samsung-specific optimizations")
    }
    
    private fun handleOnePlusOptimizations() {
        // OnePlus設備特殊處理
        Log.d(TAG, "Applying OnePlus-specific optimizations")
    }
    
    private fun handleGenericOptimizations() {
        // 通用設備處理
        Log.d(TAG, "Applying generic optimizations")
    }
    
    /**
     * 獲取系統健康狀態報告
     */
    suspend fun getSystemHealthReport(): SystemHealthReport {
        return ExceptionHandler.safeExecute(context) {
            val isServiceRunning = BackgroundExecutionHelper.isScheduleServiceRunning(context)
            val hasBatteryOptimization = BatteryOptimizationHelper.isIgnoringBatteryOptimizations(context)
            val hasExactAlarmPermission = AlarmManagerService.canScheduleExactAlarms(context)
            val hasSystemAlertPermission = SystemPermissionHelper.hasSystemAlertWindowPermission(context)
            
            val enabledSchedules = database.scheduleDao().getEnabledSchedules().first()
            val totalSchedules = database.scheduleDao().getScheduleCount()
            
            SystemHealthReport(
                isServiceRunning = isServiceRunning,
                hasBatteryOptimization = hasBatteryOptimization,
                hasExactAlarmPermission = hasExactAlarmPermission,
                hasSystemAlertPermission = hasSystemAlertPermission,
                enabledScheduleCount = enabledSchedules.size,
                totalScheduleCount = totalSchedules,
                deviceManufacturer = Build.MANUFACTURER,
                deviceModel = Build.MODEL,
                androidVersion = Build.VERSION.SDK_INT,
                overallHealth = calculateOverallHealth(
                    isServiceRunning, hasBatteryOptimization, 
                    hasExactAlarmPermission, enabledSchedules.size
                )
            )
        } ?: SystemHealthReport.createErrorReport()
    }
    
    private fun calculateOverallHealth(
        isServiceRunning: Boolean,
        hasBatteryOptimization: Boolean,
        hasExactAlarmPermission: Boolean,
        enabledScheduleCount: Int
    ): HealthStatus {
        val score = listOf(
            if (isServiceRunning) 25 else 0,
            if (hasBatteryOptimization) 25 else 0,
            if (hasExactAlarmPermission) 25 else 0,
            if (enabledScheduleCount > 0) 25 else 0
        ).sum()
        
        return when {
            score >= 75 -> HealthStatus.EXCELLENT
            score >= 50 -> HealthStatus.GOOD
            score >= 25 -> HealthStatus.FAIR
            else -> HealthStatus.POOR
        }
    }
    
    /**
     * 系統健康報告數據類
     */
    data class SystemHealthReport(
        val isServiceRunning: Boolean,
        val hasBatteryOptimization: Boolean,
        val hasExactAlarmPermission: Boolean,
        val hasSystemAlertPermission: Boolean,
        val enabledScheduleCount: Int,
        val totalScheduleCount: Int,
        val deviceManufacturer: String,
        val deviceModel: String,
        val androidVersion: Int,
        val overallHealth: HealthStatus,
        val timestamp: Long = System.currentTimeMillis()
    ) {
        companion object {
            fun createErrorReport(): SystemHealthReport {
                return SystemHealthReport(
                    isServiceRunning = false,
                    hasBatteryOptimization = false,
                    hasExactAlarmPermission = false,
                    hasSystemAlertPermission = false,
                    enabledScheduleCount = 0,
                    totalScheduleCount = 0,
                    deviceManufacturer = "Unknown",
                    deviceModel = "Unknown",
                    androidVersion = 0,
                    overallHealth = HealthStatus.ERROR
                )
            }
        }
    }
    
    /**
     * 健康狀態枚舉
     */
    enum class HealthStatus {
        EXCELLENT,  // 90-100%
        GOOD,       // 70-89%
        FAIR,       // 50-69%
        POOR,       // 0-49%
        ERROR       // 檢查失敗
    }
}
