package com.example.autolaunch.utils

import android.os.SystemClock
import android.view.View
import android.util.Log

/**
 * 帶防抖動功能的點擊監聽器擴展函數
 *
 * @param debounceTime 防抖動時間，單位為毫秒
 * @param action 點擊後要執行的操作
 */
fun View.setDebounceClickListener(debounceTime: Long = 1000L, action: (View) -> Unit) {
    this.setOnClickListener(object : View.OnClickListener {
        private var lastClickTime: Long = 0

        override fun onClick(v: View) {
            val now = SystemClock.elapsedRealtime()
            if (now - lastClickTime < debounceTime) {
                Log.d("Debounce", "Click blocked for view ${v.id}")
                return
            }
            Log.d("Debounce", "Click processed for view ${v.id}")
            lastClickTime = now
            action(v)
        }
    })
} 