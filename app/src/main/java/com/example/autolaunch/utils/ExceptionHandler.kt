package com.example.autolaunch.utils

import android.content.Context
import android.util.Log
import kotlinx.coroutines.CancellationException
import java.io.IOException
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import java.sql.SQLException

/**
 * 統一異常處理管理器
 * 提供標準化的異常處理和錯誤恢復機制
 */
object ExceptionHandler {
    
    private const val TAG = "ExceptionHandler"
    
    /**
     * 異常類型枚舉
     */
    enum class ExceptionType {
        NETWORK_ERROR,
        DATABASE_ERROR,
        IO_ERROR,
        PERMISSION_ERROR,
        VALIDATION_ERROR,
        SYSTEM_ERROR,
        UNKNOWN_ERROR
    }
    
    /**
     * 異常處理結果
     */
    data class ExceptionResult(
        val type: ExceptionType,
        val message: String,
        val shouldRetry: Boolean = false,
        val userMessage: String? = null
    )
    
    /**
     * 處理異常並返回標準化結果
     */
    fun handleException(exception: Throwable, context: Context? = null): ExceptionResult {
        Log.e(TAG, "Handling exception: ${exception.javaClass.simpleName}", exception)
        
        return when (exception) {
            is CancellationException -> {
                // Coroutine 取消異常，不需要特殊處理
                Log.d(TAG, "Coroutine cancelled")
                ExceptionResult(
                    type = ExceptionType.SYSTEM_ERROR,
                    message = "Operation cancelled",
                    shouldRetry = false
                )
            }
            
            is SocketTimeoutException -> {
                ExceptionResult(
                    type = ExceptionType.NETWORK_ERROR,
                    message = "Network timeout",
                    shouldRetry = true,
                    userMessage = "網路連線逾時，請檢查網路狀態"
                )
            }
            
            is UnknownHostException -> {
                ExceptionResult(
                    type = ExceptionType.NETWORK_ERROR,
                    message = "Network unavailable",
                    shouldRetry = true,
                    userMessage = "無法連接到網路，請檢查網路設定"
                )
            }
            
            is IOException -> {
                ExceptionResult(
                    type = ExceptionType.IO_ERROR,
                    message = "IO operation failed: ${exception.message}",
                    shouldRetry = false,
                    userMessage = "檔案操作失敗"
                )
            }
            
            is SQLException -> {
                ExceptionResult(
                    type = ExceptionType.DATABASE_ERROR,
                    message = "Database operation failed: ${exception.message}",
                    shouldRetry = false,
                    userMessage = "資料庫操作失敗"
                )
            }
            
            is SecurityException -> {
                ExceptionResult(
                    type = ExceptionType.PERMISSION_ERROR,
                    message = "Permission denied: ${exception.message}",
                    shouldRetry = false,
                    userMessage = "權限不足，請檢查應用程式權限設定"
                )
            }
            
            is IllegalArgumentException, is IllegalStateException -> {
                ExceptionResult(
                    type = ExceptionType.VALIDATION_ERROR,
                    message = "Validation error: ${exception.message}",
                    shouldRetry = false,
                    userMessage = "輸入資料有誤"
                )
            }
            
            else -> {
                ExceptionResult(
                    type = ExceptionType.UNKNOWN_ERROR,
                    message = "Unknown error: ${exception.message}",
                    shouldRetry = false,
                    userMessage = "發生未知錯誤"
                )
            }
        }.also { result ->
            // 記錄到系統日誌
            context?.let { ctx ->
                SystemLogManager.logSystemError(
                    context = ctx,
                    errorMessage = result.message,
                    exception = exception,
                    details = "Exception type: ${result.type}"
                )
            }
        }
    }
    
    /**
     * 安全執行操作，自動處理異常
     */
    inline fun <T> safeExecute(
        context: Context? = null,
        operation: () -> T
    ): T? {
        return try {
            operation()
        } catch (e: Exception) {
            handleException(e, context)
            null
        }
    }
    
    /**
     * 安全執行操作，提供預設值
     */
    inline fun <T> safeExecuteWithDefault(
        defaultValue: T,
        context: Context? = null,
        operation: () -> T
    ): T {
        return try {
            operation()
        } catch (e: Exception) {
            handleException(e, context)
            defaultValue
        }
    }
    
    /**
     * 安全執行操作，支援重試機制
     */
    inline fun <T> safeExecuteWithRetry(
        maxRetries: Int = 3,
        retryDelayMs: Long = 1000,
        context: Context? = null,
        operation: () -> T
    ): T? {
        var lastException: Exception? = null
        
        repeat(maxRetries) { attempt ->
            try {
                return operation()
            } catch (e: Exception) {
                lastException = e
                val result = handleException(e, context)
                
                if (!result.shouldRetry || attempt == maxRetries - 1) {
                    return null
                }
                
                // 等待後重試
                try {
                    Thread.sleep(retryDelayMs * (attempt + 1))
                } catch (ie: InterruptedException) {
                    Thread.currentThread().interrupt()
                    return null
                }
            }
        }
        
        return null
    }
    
    /**
     * 檢查異常是否為致命錯誤
     */
    fun isFatalError(exception: Throwable): Boolean {
        return when (exception) {
            is OutOfMemoryError,
            is StackOverflowError,
            is VirtualMachineError -> true
            else -> false
        }
    }
    
    /**
     * 檢查異常是否可以重試
     */
    fun isRetryable(exception: Throwable): Boolean {
        return when (exception) {
            is SocketTimeoutException,
            is UnknownHostException,
            is IOException -> true
            else -> false
        }
    }
    
    /**
     * 獲取用戶友好的錯誤訊息
     */
    fun getUserFriendlyMessage(exception: Throwable): String {
        return handleException(exception).userMessage ?: "發生未知錯誤"
    }
}

/**
 * 擴展函數：為任何函數添加安全執行包裝
 */
inline fun <T> (() -> T).safeExecute(context: Context? = null): T? {
    return ExceptionHandler.safeExecute(context) { this() }
}

/**
 * 擴展函數：為任何函數添加帶預設值的安全執行包裝
 */
inline fun <T> (() -> T).safeExecuteWithDefault(defaultValue: T, context: Context? = null): T {
    return ExceptionHandler.safeExecuteWithDefault(defaultValue, context) { this() }
}
