package com.example.autolaunch.utils

import android.content.Context
import android.content.SharedPreferences
import android.util.Log

/**
 * 設置管理器
 * 負責管理應用程式的各種設置選項
 */
class SettingsManager private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "SettingsManager"
        private const val PREFS_NAME = "app_settings"
        private const val KEY_PERSISTENT_NOTIFICATION = "persistent_notification"
        private const val KEY_WELCOME_CARD_VISIBLE = "welcome_card_visible"
        private const val KEY_FIRST_LAUNCH = "first_launch"
        
        @Volatile
        private var INSTANCE: SettingsManager? = null
        
        fun getInstance(context: Context): SettingsManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: SettingsManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    
    /**
     * 獲取是否啟用常駐通知
     */
    fun isPersistentNotificationEnabled(): Boolean {
        return prefs.getBoolean(KEY_PERSISTENT_NOTIFICATION, true) // 預設為啟用
    }
    
    /**
     * 設定是否啟用常駐通知
     */
    fun setPersistentNotificationEnabled(enabled: Boolean) {
        Log.d(TAG, "Setting persistent notification enabled: $enabled")

        prefs.edit()
            .putBoolean(KEY_PERSISTENT_NOTIFICATION, enabled)
            .apply()
    }

    /**
     * 獲取是否顯示歡迎卡片
     */
    fun isWelcomeCardVisible(): Boolean {
        return prefs.getBoolean(KEY_WELCOME_CARD_VISIBLE, true) // 預設為顯示
    }

    /**
     * 設定是否顯示歡迎卡片
     */
    fun setWelcomeCardVisible(visible: Boolean) {
        Log.d(TAG, "Setting welcome card visible: $visible")

        prefs.edit()
            .putBoolean(KEY_WELCOME_CARD_VISIBLE, visible)
            .apply()
    }

    /**
     * 檢查是否為首次啟動
     */
    fun isFirstLaunch(): Boolean {
        return prefs.getBoolean(KEY_FIRST_LAUNCH, true) // 預設為首次啟動
    }

    /**
     * 設定首次啟動狀態
     */
    fun setFirstLaunch(isFirst: Boolean) {
        Log.d(TAG, "Setting first launch: $isFirst")

        prefs.edit()
            .putBoolean(KEY_FIRST_LAUNCH, isFirst)
            .apply()
    }
}
