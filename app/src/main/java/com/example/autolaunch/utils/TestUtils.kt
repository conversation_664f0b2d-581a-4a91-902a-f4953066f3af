package com.example.autolaunch.utils

import android.content.Context
import android.util.Log
import androidx.lifecycle.lifecycleScope
import com.example.autolaunch.model.AppDatabase
import com.example.autolaunch.model.RepeatMode
import com.example.autolaunch.model.Schedule
import com.example.autolaunch.model.ScheduleRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.Calendar

/**
 * 測試和調試工具類
 * 提供各種測試功能和調試輔助方法
 */
object TestUtils {
    
    private const val TAG = "TestUtils"
    
    /**
     * 創建測試排程
     * @param context 上下文
     * @param testType 測試類型
     */
    fun createTestSchedules(context: Context, testType: TestType = TestType.BASIC) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val repository = ScheduleRepository(context)
                val testSchedules = when (testType) {
                    TestType.BASIC -> createBasicTestSchedules()
                    TestType.EDGE_CASES -> createEdgeCaseSchedules()
                    TestType.STRESS -> createStressTestSchedules()
                }
                
                testSchedules.forEach { schedule ->
                    repository.insertSchedule(schedule)
                    Log.d(TAG, "Created test schedule: ${schedule.appName}")
                }
                
                Log.i(TAG, "Created ${testSchedules.size} test schedules")
            } catch (e: Exception) {
                Log.e(TAG, "Failed to create test schedules", e)
            }
        }
    }
    
    /**
     * 清除所有測試數據
     */
    fun clearAllTestData(context: Context) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val database = AppDatabase.getDatabase(context)
                val scheduleDao = database.scheduleDao()
                val deletedCount = scheduleDao.deleteAllSchedules()
                Log.i(TAG, "Cleared $deletedCount test schedules")
            } catch (e: Exception) {
                Log.e(TAG, "Failed to clear test data", e)
            }
        }
    }
    
    /**
     * 創建基本測試排程
     */
    private fun createBasicTestSchedules(): List<Schedule> {
        val now = System.currentTimeMillis()
        val calendar = Calendar.getInstance()
        
        return listOf(
            // 每日排程 - 明天早上 8:00
            Schedule(
                appName = "測試應用1 (每日)",
                packageName = "com.android.chrome",
                hour = 8,
                minute = 0,
                repeatMode = RepeatMode.DAILY.value,
                isEnabled = true
            ),
            
            // 每週排程 - 工作日 9:00
            Schedule(
                appName = "測試應用2 (工作日)",
                packageName = "com.whatsapp",
                hour = 9,
                minute = 0,
                repeatMode = RepeatMode.WEEKLY.value,
                daysOfWeek = 31, // 週一到週五
                isEnabled = true
            ),
            
            // 單次排程 - 今天晚上 20:00
            Schedule(
                appName = "測試應用3 (單次)",
                packageName = "com.google.android.youtube",
                hour = 20,
                minute = 0,
                repeatMode = RepeatMode.ONCE.value,
                singleExecuteDate = calendar.apply { 
                    set(Calendar.HOUR_OF_DAY, 20)
                    set(Calendar.MINUTE, 0)
                    set(Calendar.SECOND, 0)
                    set(Calendar.MILLISECOND, 0)
                }.timeInMillis,
                isEnabled = true
            ),
            
            // 禁用的排程
            Schedule(
                appName = "測試應用4 (已禁用)",
                packageName = "com.google.android.gm",
                hour = 12,
                minute = 30,
                repeatMode = RepeatMode.DAILY.value,
                isEnabled = false
            )
        )
    }
    
    /**
     * 創建邊緣案例測試排程
     */
    private fun createEdgeCaseSchedules(): List<Schedule> {
        val calendar = Calendar.getInstance()
        
        return listOf(
            // 午夜排程
            Schedule(
                appName = "午夜測試",
                packageName = "com.android.calculator2",
                hour = 0,
                minute = 0,
                repeatMode = RepeatMode.DAILY.value,
                isEnabled = true
            ),
            
            // 深夜排程
            Schedule(
                appName = "深夜測試",
                packageName = "com.android.calendar",
                hour = 23,
                minute = 59,
                repeatMode = RepeatMode.DAILY.value,
                isEnabled = true
            ),
            
            // 只有週日的排程
            Schedule(
                appName = "週日測試",
                packageName = "com.android.contacts",
                hour = 10,
                minute = 0,
                repeatMode = RepeatMode.WEEKLY.value,
                daysOfWeek = 64, // 只有週日
                isEnabled = true
            ),
            
            // 已過期的單次排程
            Schedule(
                appName = "過期測試",
                packageName = "com.android.settings",
                hour = 8,
                minute = 0,
                repeatMode = RepeatMode.ONCE.value,
                singleExecuteDate = System.currentTimeMillis() - 24 * 60 * 60 * 1000, // 昨天
                isEnabled = true
            )
        )
    }
    
    /**
     * 創建壓力測試排程
     */
    private fun createStressTestSchedules(): List<Schedule> {
        val schedules = mutableListOf<Schedule>()
        
        // 創建大量排程用於壓力測試
        for (i in 1..20) {
            schedules.add(
                Schedule(
                    appName = "壓力測試應用 $i",
                    packageName = "com.android.chrome",
                    hour = (8 + i % 12),
                    minute = (i * 3) % 60,
                    repeatMode = RepeatMode.values()[i % 3].value,
                    daysOfWeek = if (i % 3 == 2) (1 shl (i % 7)) else 0,
                    singleExecuteDate = if (i % 3 == 0) System.currentTimeMillis() + i * 60 * 60 * 1000 else null,
                    isEnabled = i % 4 != 0 // 75% 啟用率
                )
            )
        }
        
        return schedules
    }
    
    /**
     * 驗證排程執行時間計算
     */
    fun validateScheduleTimings(context: Context) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val repository = ScheduleRepository(context)
                repository.getAllSchedules().collect { schedules ->
                    val results = mutableListOf<String>()
                    
                    schedules.forEach { schedule ->
                        val nextTime = schedule.calculateNextExecuteTime()
                        val status = when {
                            !schedule.isEnabled -> "已禁用"
                            nextTime == null -> "無下次執行時間"
                            nextTime <= System.currentTimeMillis() -> "時間已過期"
                            else -> "正常"
                        }

                        results.add("${schedule.getDisplayName()}: $status")
                    }
                    
                    Log.i(TAG, "Schedule validation results:")
                    results.forEach { Log.i(TAG, "  $it") }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Failed to validate schedule timings", e)
            }
        }
    }
    
    /**
     * 檢查系統健康狀態
     */
    fun checkSystemHealth(context: Context) {
        Log.i(TAG, "=== System Health Check ===")
        
        // 檢查電池優化狀態
        val batteryOptimized = BatteryOptimizationHelper.isIgnoringBatteryOptimizations(context)
        Log.i(TAG, "Battery optimization: ${if (batteryOptimized) "✓ Excluded" else "✗ Not excluded"}")
        
        // 檢查應用程式權限
        // (這裡可以添加更多權限檢查)
        
        // 檢查 AlarmManager 可用性
        try {
            val alarmManager = context.getSystemService(Context.ALARM_SERVICE)
            Log.i(TAG, "AlarmManager: ${if (alarmManager != null) "✓ Available" else "✗ Not available"}")
        } catch (e: Exception) {
            Log.e(TAG, "AlarmManager check failed", e)
        }
        
        // 檢查數據庫狀態
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val database = AppDatabase.getDatabase(context)
                val scheduleCount = database.scheduleDao().getScheduleCount()
                val enabledCount = database.scheduleDao().getEnabledScheduleCount()
                Log.i(TAG, "Database: ✓ $scheduleCount total schedules, $enabledCount enabled")
            } catch (e: Exception) {
                Log.e(TAG, "Database check failed", e)
            }
        }
        
        Log.i(TAG, "=== Health Check Complete ===")
    }
    
    /**
     * 模擬排程觸發
     */
    fun simulateScheduleTrigger(context: Context, scheduleId: Long) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val database = AppDatabase.getDatabase(context)
                database.scheduleDao().getScheduleById(scheduleId).collect { schedule ->
                    if (schedule != null) {
                        // 這裡可以模擬 LaunchReceiver 的行為
                        Log.i(TAG, "Simulating trigger for schedule: ${schedule.appName}")
                    } else {
                        Log.w(TAG, "Schedule with ID $scheduleId not found")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Failed to simulate schedule trigger", e)
            }
        }
    }
    
    enum class TestType {
        BASIC,      // 基本測試案例
        EDGE_CASES, // 邊緣案例
        STRESS      // 壓力測試
    }
} 