package com.example.autolaunch.utils

import android.app.ActivityManager
import android.content.Context
import android.os.Debug
import android.os.Process
import android.util.Log
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.util.concurrent.ConcurrentLinkedQueue
import java.util.concurrent.atomic.AtomicLong

/**
 * 性能監控器
 * 提供應用程式性能指標的實時監控和分析
 */
class PerformanceMonitor private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "PerformanceMonitor"
        private const val MONITORING_INTERVAL = 5000L // 5 seconds
        private const val MAX_METRICS_HISTORY = 100
        
        @Volatile
        private var INSTANCE: PerformanceMonitor? = null
        
        fun getInstance(context: Context): PerformanceMonitor {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: PerformanceMonitor(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val monitoringScope = CoroutineScope(Dispatchers.Default + SupervisorJob())
    private var monitoringJob: Job? = null
    
    // 性能指標歷史記錄
    private val metricsHistory = ConcurrentLinkedQueue<PerformanceMetrics>()
    
    // 當前性能狀態
    private val _currentMetrics = MutableStateFlow(PerformanceMetrics.empty())
    val currentMetrics: StateFlow<PerformanceMetrics> = _currentMetrics.asStateFlow()
    
    // 性能警告閾值
    private val memoryWarningThreshold = 0.8f // 80%
    private val cpuWarningThreshold = 0.7f // 70%
    private val frameDropWarningThreshold = 10 // 10 dropped frames
    
    // 統計計數器
    private val totalOperations = AtomicLong(0)
    private val failedOperations = AtomicLong(0)
    private val totalResponseTime = AtomicLong(0)
    
    /**
     * 開始性能監控
     */
    fun startMonitoring() {
        if (monitoringJob?.isActive == true) {
            Log.d(TAG, "Performance monitoring already active")
            return
        }
        
        monitoringJob = monitoringScope.launch {
            Log.i(TAG, "Performance monitoring started")
            
            while (isActive) {
                try {
                    val metrics = collectPerformanceMetrics()
                    updateMetrics(metrics)
                    checkPerformanceWarnings(metrics)
                    
                    delay(MONITORING_INTERVAL)
                } catch (e: CancellationException) {
                    Log.d(TAG, "Performance monitoring cancelled")
                    throw e
                } catch (e: Exception) {
                    Log.e(TAG, "Error during performance monitoring", e)
                    delay(MONITORING_INTERVAL)
                }
            }
        }
    }
    
    /**
     * 停止性能監控
     */
    fun stopMonitoring() {
        monitoringJob?.cancel()
        monitoringJob = null
        Log.i(TAG, "Performance monitoring stopped")
    }
    
    /**
     * 收集性能指標
     */
    private fun collectPerformanceMetrics(): PerformanceMetrics {
        val timestamp = System.currentTimeMillis()
        
        // 記憶體指標
        val memoryInfo = getMemoryInfo()
        
        // CPU 指標
        val cpuUsage = getCpuUsage()
        
        // 應用程式指標
        val appInfo = getAppInfo()
        
        // 系統指標
        val systemInfo = getSystemInfo()
        
        return PerformanceMetrics(
            timestamp = timestamp,
            memoryUsedMB = memoryInfo.usedMemoryMB,
            memoryTotalMB = memoryInfo.totalMemoryMB,
            memoryUsagePercent = memoryInfo.usagePercent,
            cpuUsagePercent = cpuUsage,
            heapUsedMB = appInfo.heapUsedMB,
            heapTotalMB = appInfo.heapTotalMB,
            threadCount = appInfo.threadCount,
            gcCount = appInfo.gcCount,
            batteryLevel = systemInfo.batteryLevel,
            networkType = systemInfo.networkType,
            storageAvailableGB = systemInfo.storageAvailableGB
        )
    }
    
    /**
     * 獲取記憶體信息
     */
    private fun getMemoryInfo(): MemoryInfo {
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val memoryInfo = ActivityManager.MemoryInfo()
        activityManager.getMemoryInfo(memoryInfo)
        
        val totalMemoryMB = memoryInfo.totalMem / (1024 * 1024)
        val availableMemoryMB = memoryInfo.availMem / (1024 * 1024)
        val usedMemoryMB = totalMemoryMB - availableMemoryMB
        val usagePercent = (usedMemoryMB.toFloat() / totalMemoryMB) * 100
        
        return MemoryInfo(
            usedMemoryMB = usedMemoryMB,
            totalMemoryMB = totalMemoryMB,
            usagePercent = usagePercent
        )
    }
    
    /**
     * 獲取 CPU 使用率（簡化版本）
     */
    private fun getCpuUsage(): Float {
        return try {
            val pid = Process.myPid()
            val runtime = Runtime.getRuntime()
            val processors = runtime.availableProcessors()
            
            // 簡化的 CPU 使用率計算
            // 實際應用中可能需要更複雜的計算
            val threadCount = Thread.activeCount()
            val estimatedUsage = (threadCount.toFloat() / processors) * 100
            estimatedUsage.coerceAtMost(100f)
        } catch (e: Exception) {
            Log.w(TAG, "Failed to get CPU usage", e)
            0f
        }
    }
    
    /**
     * 獲取應用程式信息
     */
    private fun getAppInfo(): AppInfo {
        val runtime = Runtime.getRuntime()
        val heapUsedMB = (runtime.totalMemory() - runtime.freeMemory()) / (1024 * 1024)
        val heapTotalMB = runtime.maxMemory() / (1024 * 1024)
        val threadCount = Thread.activeCount()
        
        // GC 計數（簡化版本）
        val gcCount = Debug.getGlobalGcInvocationCount()
        
        return AppInfo(
            heapUsedMB = heapUsedMB,
            heapTotalMB = heapTotalMB,
            threadCount = threadCount,
            gcCount = gcCount.toLong()
        )
    }
    
    /**
     * 獲取系統信息
     */
    private fun getSystemInfo(): SystemInfo {
        // 電池電量（簡化版本）
        val batteryLevel = 100 // 實際應用中需要從 BatteryManager 獲取
        
        // 網路類型
        val networkType = NetworkResourceManager.getNetworkType(context).name
        
        // 存儲空間
        val storageAvailableGB = try {
            val internalDir = context.filesDir
            val freeBytes = internalDir.freeSpace
            freeBytes / (1024 * 1024 * 1024)
        } catch (e: Exception) {
            0L
        }
        
        return SystemInfo(
            batteryLevel = batteryLevel,
            networkType = networkType,
            storageAvailableGB = storageAvailableGB
        )
    }
    
    /**
     * 更新性能指標
     */
    private fun updateMetrics(metrics: PerformanceMetrics) {
        _currentMetrics.value = metrics
        
        // 添加到歷史記錄
        metricsHistory.offer(metrics)
        
        // 保持歷史記錄大小限制
        while (metricsHistory.size > MAX_METRICS_HISTORY) {
            metricsHistory.poll()
        }
    }
    
    /**
     * 檢查性能警告
     */
    private fun checkPerformanceWarnings(metrics: PerformanceMetrics) {
        val warnings = mutableListOf<String>()
        
        // 記憶體警告
        if (metrics.memoryUsagePercent > memoryWarningThreshold * 100) {
            warnings.add("High memory usage: ${metrics.memoryUsagePercent.toInt()}%")
        }
        
        // CPU 警告
        if (metrics.cpuUsagePercent > cpuWarningThreshold * 100) {
            warnings.add("High CPU usage: ${metrics.cpuUsagePercent.toInt()}%")
        }
        
        // Heap 警告
        val heapUsagePercent = (metrics.heapUsedMB.toFloat() / metrics.heapTotalMB) * 100
        if (heapUsagePercent > 80) {
            warnings.add("High heap usage: ${heapUsagePercent.toInt()}%")
        }
        
        // 記錄警告
        if (warnings.isNotEmpty()) {
            Log.w(TAG, "Performance warnings: ${warnings.joinToString(", ")}")
            SystemLogManager.logSystemError(
                context = context,
                errorMessage = "Performance warnings detected",
                exception = null,
                details = warnings.joinToString("; ")
            )
        }
    }
    
    /**
     * 記錄操作性能
     */
    fun recordOperation(operationName: String, durationMs: Long, success: Boolean) {
        totalOperations.incrementAndGet()
        totalResponseTime.addAndGet(durationMs)
        
        if (!success) {
            failedOperations.incrementAndGet()
        }
        
        Logger.performance(TAG, operationName, durationMs)
        
        // 如果操作時間過長，記錄警告
        if (durationMs > 1000) { // 超過 1 秒
            Log.w(TAG, "Slow operation detected: $operationName took ${durationMs}ms")
        }
    }
    
    /**
     * 獲取性能統計
     */
    fun getPerformanceStatistics(): PerformanceStatistics {
        val totalOps = totalOperations.get()
        val failedOps = failedOperations.get()
        val totalTime = totalResponseTime.get()
        
        val successRate = if (totalOps > 0) {
            ((totalOps - failedOps).toFloat() / totalOps) * 100
        } else 0f
        
        val averageResponseTime = if (totalOps > 0) {
            totalTime / totalOps
        } else 0L
        
        return PerformanceStatistics(
            totalOperations = totalOps,
            failedOperations = failedOps,
            successRate = successRate,
            averageResponseTime = averageResponseTime,
            metricsHistorySize = metricsHistory.size
        )
    }
    
    /**
     * 獲取歷史指標
     */
    fun getMetricsHistory(): List<PerformanceMetrics> {
        return metricsHistory.toList()
    }
    
    /**
     * 清理資源
     */
    fun cleanup() {
        stopMonitoring()
        monitoringScope.cancel()
        metricsHistory.clear()
        Log.i(TAG, "Performance monitor cleaned up")
    }
    
    // 數據類定義
    data class PerformanceMetrics(
        val timestamp: Long,
        val memoryUsedMB: Long,
        val memoryTotalMB: Long,
        val memoryUsagePercent: Float,
        val cpuUsagePercent: Float,
        val heapUsedMB: Long,
        val heapTotalMB: Long,
        val threadCount: Int,
        val gcCount: Long,
        val batteryLevel: Int,
        val networkType: String,
        val storageAvailableGB: Long
    ) {
        companion object {
            fun empty() = PerformanceMetrics(
                timestamp = 0,
                memoryUsedMB = 0,
                memoryTotalMB = 0,
                memoryUsagePercent = 0f,
                cpuUsagePercent = 0f,
                heapUsedMB = 0,
                heapTotalMB = 0,
                threadCount = 0,
                gcCount = 0,
                batteryLevel = 0,
                networkType = "NONE",
                storageAvailableGB = 0
            )
        }
    }
    
    private data class MemoryInfo(
        val usedMemoryMB: Long,
        val totalMemoryMB: Long,
        val usagePercent: Float
    )
    
    private data class AppInfo(
        val heapUsedMB: Long,
        val heapTotalMB: Long,
        val threadCount: Int,
        val gcCount: Long
    )
    
    private data class SystemInfo(
        val batteryLevel: Int,
        val networkType: String,
        val storageAvailableGB: Long
    )
    
    data class PerformanceStatistics(
        val totalOperations: Long,
        val failedOperations: Long,
        val successRate: Float,
        val averageResponseTime: Long,
        val metricsHistorySize: Int
    )
}
