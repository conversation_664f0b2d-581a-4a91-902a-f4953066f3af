package com.example.autolaunch.utils

import android.content.Context
import android.os.Build
import android.util.Log
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File
import java.io.PrintWriter
import java.io.StringWriter
import java.text.SimpleDateFormat
import java.util.*

/**
 * 崩潰報告器
 * 捕獲和記錄應用程式崩潰信息
 */
class CrashReporter private constructor(private val context: Context) : Thread.UncaughtExceptionHandler {
    
    companion object {
        private const val TAG = "CrashReporter"
        private const val CRASH_DIR = "crashes"
        private const val MAX_CRASH_FILES = 10
        
        @Volatile
        private var INSTANCE: CrashReporter? = null
        
        fun getInstance(context: Context): CrashReporter {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: CrashReporter(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val defaultHandler = Thread.getDefaultUncaughtExceptionHandler()
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd_HH-mm-ss", Locale.getDefault())
    private val crashScope = CoroutineScope(Dispatchers.IO)
    
    /**
     * 初始化崩潰報告器
     */
    fun initialize() {
        Thread.setDefaultUncaughtExceptionHandler(this)
        Log.i(TAG, "Crash reporter initialized")
        
        // 清理舊的崩潰文件
        cleanupOldCrashFiles()
    }
    
    override fun uncaughtException(thread: Thread, exception: Throwable) {
        try {
            Log.e(TAG, "Uncaught exception in thread ${thread.name}", exception)
            
            // 生成崩潰報告
            val crashReport = generateCrashReport(thread, exception)
            
            // 保存崩潰報告
            saveCrashReport(crashReport)
            
            // 記錄到系統日誌
            SystemLogManager.logSystemError(
                context = context,
                errorMessage = "Application crashed: ${exception.message}",
                exception = exception,
                details = "Thread: ${thread.name}"
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "Error handling crash", e)
        } finally {
            // 調用默認處理器
            defaultHandler?.uncaughtException(thread, exception)
        }
    }
    
    /**
     * 生成崩潰報告
     */
    private fun generateCrashReport(thread: Thread, exception: Throwable): CrashReport {
        val timestamp = System.currentTimeMillis()
        val deviceInfo = getDeviceInfo()
        val appInfo = getAppInfo()
        val stackTrace = getStackTrace(exception)
        val systemState = getSystemState()
        
        return CrashReport(
            timestamp = timestamp,
            threadName = thread.name,
            exceptionType = exception.javaClass.simpleName,
            exceptionMessage = exception.message ?: "No message",
            stackTrace = stackTrace,
            deviceInfo = deviceInfo,
            appInfo = appInfo,
            systemState = systemState
        )
    }
    
    /**
     * 獲取設備信息
     */
    private fun getDeviceInfo(): DeviceInfo {
        return DeviceInfo(
            manufacturer = Build.MANUFACTURER,
            model = Build.MODEL,
            androidVersion = Build.VERSION.RELEASE,
            apiLevel = Build.VERSION.SDK_INT,
            brand = Build.BRAND,
            device = Build.DEVICE,
            hardware = Build.HARDWARE,
            board = Build.BOARD
        )
    }
    
    /**
     * 獲取應用程式信息
     */
    private fun getAppInfo(): AppInfo {
        val packageInfo = try {
            context.packageManager.getPackageInfo(context.packageName, 0)
        } catch (e: Exception) {
            null
        }
        
        return AppInfo(
            packageName = context.packageName,
            versionName = packageInfo?.versionName ?: "Unknown",
            versionCode = packageInfo?.longVersionCode ?: 0L,
            targetSdkVersion = context.applicationInfo.targetSdkVersion,
            minSdkVersion = Build.VERSION.SDK_INT // 簡化版本
        )
    }
    
    /**
     * 獲取堆疊追蹤
     */
    private fun getStackTrace(exception: Throwable): String {
        val stringWriter = StringWriter()
        val printWriter = PrintWriter(stringWriter)
        exception.printStackTrace(printWriter)
        return stringWriter.toString()
    }
    
    /**
     * 獲取系統狀態
     */
    private fun getSystemState(): SystemState {
        val runtime = Runtime.getRuntime()
        val totalMemory = runtime.totalMemory()
        val freeMemory = runtime.freeMemory()
        val maxMemory = runtime.maxMemory()
        
        return SystemState(
            totalMemoryMB = totalMemory / (1024 * 1024),
            freeMemoryMB = freeMemory / (1024 * 1024),
            maxMemoryMB = maxMemory / (1024 * 1024),
            availableProcessors = runtime.availableProcessors(),
            activeThreadCount = Thread.activeCount()
        )
    }
    
    /**
     * 保存崩潰報告
     */
    private fun saveCrashReport(crashReport: CrashReport) {
        crashScope.launch {
            try {
                val crashDir = File(context.filesDir, CRASH_DIR)
                if (!crashDir.exists()) {
                    crashDir.mkdirs()
                }
                
                val fileName = "crash_${dateFormat.format(Date(crashReport.timestamp))}.txt"
                val crashFile = File(crashDir, fileName)
                
                val reportContent = formatCrashReport(crashReport)
                crashFile.writeText(reportContent)
                
                Log.i(TAG, "Crash report saved: ${crashFile.absolutePath}")
                
            } catch (e: Exception) {
                Log.e(TAG, "Failed to save crash report", e)
            }
        }
    }
    
    /**
     * 格式化崩潰報告
     */
    private fun formatCrashReport(crashReport: CrashReport): String {
        val dateStr = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
            .format(Date(crashReport.timestamp))
        
        return buildString {
            appendLine("=== CRASH REPORT ===")
            appendLine("Timestamp: $dateStr")
            appendLine("Thread: ${crashReport.threadName}")
            appendLine("Exception: ${crashReport.exceptionType}")
            appendLine("Message: ${crashReport.exceptionMessage}")
            appendLine()
            
            appendLine("=== DEVICE INFO ===")
            appendLine("Manufacturer: ${crashReport.deviceInfo.manufacturer}")
            appendLine("Model: ${crashReport.deviceInfo.model}")
            appendLine("Android Version: ${crashReport.deviceInfo.androidVersion}")
            appendLine("API Level: ${crashReport.deviceInfo.apiLevel}")
            appendLine("Brand: ${crashReport.deviceInfo.brand}")
            appendLine("Device: ${crashReport.deviceInfo.device}")
            appendLine()
            
            appendLine("=== APP INFO ===")
            appendLine("Package: ${crashReport.appInfo.packageName}")
            appendLine("Version: ${crashReport.appInfo.versionName} (${crashReport.appInfo.versionCode})")
            appendLine("Target SDK: ${crashReport.appInfo.targetSdkVersion}")
            appendLine()
            
            appendLine("=== SYSTEM STATE ===")
            appendLine("Total Memory: ${crashReport.systemState.totalMemoryMB} MB")
            appendLine("Free Memory: ${crashReport.systemState.freeMemoryMB} MB")
            appendLine("Max Memory: ${crashReport.systemState.maxMemoryMB} MB")
            appendLine("Processors: ${crashReport.systemState.availableProcessors}")
            appendLine("Active Threads: ${crashReport.systemState.activeThreadCount}")
            appendLine()
            
            appendLine("=== STACK TRACE ===")
            appendLine(crashReport.stackTrace)
        }
    }
    
    /**
     * 清理舊的崩潰文件
     */
    private fun cleanupOldCrashFiles() {
        crashScope.launch {
            try {
                val crashDir = File(context.filesDir, CRASH_DIR)
                if (!crashDir.exists()) return@launch
                
                val crashFiles = crashDir.listFiles { file ->
                    file.name.startsWith("crash_") && file.name.endsWith(".txt")
                }?.sortedByDescending { it.lastModified() }
                
                if (crashFiles != null && crashFiles.size > MAX_CRASH_FILES) {
                    val filesToDelete = crashFiles.drop(MAX_CRASH_FILES)
                    filesToDelete.forEach { file ->
                        if (file.delete()) {
                            Log.d(TAG, "Deleted old crash file: ${file.name}")
                        }
                    }
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "Failed to cleanup old crash files", e)
            }
        }
    }
    
    /**
     * 獲取所有崩潰報告
     */
    fun getAllCrashReports(): List<File> {
        return try {
            val crashDir = File(context.filesDir, CRASH_DIR)
            if (!crashDir.exists()) return emptyList()
            
            crashDir.listFiles { file ->
                file.name.startsWith("crash_") && file.name.endsWith(".txt")
            }?.sortedByDescending { it.lastModified() }?.toList() ?: emptyList()
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get crash reports", e)
            emptyList()
        }
    }
    
    /**
     * 清除所有崩潰報告
     */
    fun clearAllCrashReports() {
        crashScope.launch {
            try {
                val crashDir = File(context.filesDir, CRASH_DIR)
                if (crashDir.exists()) {
                    crashDir.deleteRecursively()
                    Log.i(TAG, "All crash reports cleared")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Failed to clear crash reports", e)
            }
        }
    }
    
    // 數據類定義
    data class CrashReport(
        val timestamp: Long,
        val threadName: String,
        val exceptionType: String,
        val exceptionMessage: String,
        val stackTrace: String,
        val deviceInfo: DeviceInfo,
        val appInfo: AppInfo,
        val systemState: SystemState
    )
    
    data class DeviceInfo(
        val manufacturer: String,
        val model: String,
        val androidVersion: String,
        val apiLevel: Int,
        val brand: String,
        val device: String,
        val hardware: String,
        val board: String
    )
    
    data class AppInfo(
        val packageName: String,
        val versionName: String,
        val versionCode: Long,
        val targetSdkVersion: Int,
        val minSdkVersion: Int
    )
    
    data class SystemState(
        val totalMemoryMB: Long,
        val freeMemoryMB: Long,
        val maxMemoryMB: Long,
        val availableProcessors: Int,
        val activeThreadCount: Int
    )
}
