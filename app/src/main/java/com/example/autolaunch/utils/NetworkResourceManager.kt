package com.example.autolaunch.utils

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Build
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeout
import java.io.IOException
import java.net.HttpURLConnection
import java.net.SocketTimeoutException
import java.net.URL
import java.net.UnknownHostException

/**
 * 網路資源管理器
 * 提供安全的網路連線管理和超時處理
 */
object NetworkResourceManager {
    
    private const val TAG = "NetworkResourceManager"
    private const val DEFAULT_CONNECT_TIMEOUT = 10_000 // 10 seconds
    private const val DEFAULT_READ_TIMEOUT = 15_000 // 15 seconds
    private const val DEFAULT_COROUTINE_TIMEOUT = 30_000L // 30 seconds
    
    /**
     * 網路連線配置
     */
    data class ConnectionConfig(
        val connectTimeout: Int = DEFAULT_CONNECT_TIMEOUT,
        val readTimeout: Int = DEFAULT_READ_TIMEOUT,
        val followRedirects: Boolean = true,
        val userAgent: String = "AutoLaunch/1.0",
        val requestMethod: String = "GET",
        val headers: Map<String, String> = emptyMap()
    )
    
    /**
     * 網路響應結果
     */
    data class NetworkResponse(
        val responseCode: Int,
        val responseMessage: String,
        val data: String?,
        val headers: Map<String, List<String>>,
        val success: Boolean,
        val error: Throwable? = null
    )
    
    /**
     * 檢查網路連線狀態
     */
    fun isNetworkAvailable(context: Context): Boolean {
        return ExceptionHandler.safeExecute(context) {
            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val network = connectivityManager.activeNetwork ?: return@safeExecute false
                val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return@safeExecute false
                
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) ||
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) ||
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET)
            } else {
                @Suppress("DEPRECATION")
                val networkInfo = connectivityManager.activeNetworkInfo
                networkInfo?.isConnected == true
            }
        } ?: false
    }
    
    /**
     * 安全執行 HTTP 請求
     */
    suspend fun executeHttpRequest(
        url: String,
        config: ConnectionConfig = ConnectionConfig(),
        context: Context? = null
    ): NetworkResponse = withContext(Dispatchers.IO) {
        
        return@withContext ExceptionHandler.safeExecute(context) {
            withTimeout(DEFAULT_COROUTINE_TIMEOUT) {
                performHttpRequest(url, config)
            }
        } ?: NetworkResponse(
            responseCode = -1,
            responseMessage = "Request failed",
            data = null,
            headers = emptyMap(),
            success = false,
            error = RuntimeException("Failed to execute HTTP request")
        )
    }
    
    /**
     * 執行 HTTP 請求的核心邏輯
     */
    private fun performHttpRequest(url: String, config: ConnectionConfig): NetworkResponse {
        var connection: HttpURLConnection? = null
        
        return try {
            Log.d(TAG, "Executing HTTP request: $url")
            
            connection = createConnection(url, config)
            
            // 執行請求
            connection.connect()
            
            val responseCode = connection.responseCode
            val responseMessage = connection.responseMessage ?: ""
            val headers = connection.headerFields ?: emptyMap()
            
            // 讀取響應數據
            val data: String? = if (responseCode in 200..299) {
                connection.inputStream?.use { inputStream ->
                    inputStream.bufferedReader().use { reader ->
                        reader.readText()
                    }
                }
            } else {
                connection.errorStream?.use { errorStream ->
                    errorStream.bufferedReader().use { reader ->
                        reader.readText()
                    }
                }
            }
            
            Log.d(TAG, "HTTP request completed: $responseCode $responseMessage")
            
            NetworkResponse(
                responseCode = responseCode,
                responseMessage = responseMessage,
                data = data,
                headers = headers,
                success = responseCode in 200..299
            )
            
        } catch (e: SocketTimeoutException) {
            Log.e(TAG, "HTTP request timeout: $url", e)
            NetworkResponse(
                responseCode = -1,
                responseMessage = "Request timeout",
                data = null,
                headers = emptyMap(),
                success = false,
                error = e
            )
        } catch (e: UnknownHostException) {
            Log.e(TAG, "Unknown host: $url", e)
            NetworkResponse(
                responseCode = -1,
                responseMessage = "Unknown host",
                data = null,
                headers = emptyMap(),
                success = false,
                error = e
            )
        } catch (e: IOException) {
            Log.e(TAG, "IO error during HTTP request: $url", e)
            NetworkResponse(
                responseCode = -1,
                responseMessage = "IO error",
                data = null,
                headers = emptyMap(),
                success = false,
                error = e
            )
        } catch (e: Exception) {
            Log.e(TAG, "Unexpected error during HTTP request: $url", e)
            NetworkResponse(
                responseCode = -1,
                responseMessage = "Unexpected error",
                data = null,
                headers = emptyMap(),
                success = false,
                error = e
            )
        } finally {
            ResourceManager.safeDisconnect(connection)
        }
    }
    
    /**
     * 創建 HTTP 連線
     */
    private fun createConnection(url: String, config: ConnectionConfig): HttpURLConnection {
        val connection = URL(url).openConnection() as HttpURLConnection
        
        // 設定超時
        connection.connectTimeout = config.connectTimeout
        connection.readTimeout = config.readTimeout
        
        // 設定其他屬性
        connection.instanceFollowRedirects = config.followRedirects
        connection.requestMethod = config.requestMethod
        connection.setRequestProperty("User-Agent", config.userAgent)
        
        // 設定自定義 headers
        config.headers.forEach { (key, value) ->
            connection.setRequestProperty(key, value)
        }
        
        return connection
    }
    
    /**
     * 檢查 URL 是否可達
     */
    suspend fun isUrlReachable(
        url: String,
        timeout: Int = 5000,
        context: Context? = null
    ): Boolean = withContext(Dispatchers.IO) {
        
        return@withContext ExceptionHandler.safeExecute(context) {
            val config = ConnectionConfig(
                connectTimeout = timeout,
                readTimeout = timeout,
                requestMethod = "HEAD"
            )
            
            val response = performHttpRequest(url, config)
            response.success
        } ?: false
    }
    
    /**
     * 下載文件到指定路徑
     */
    suspend fun downloadFile(
        url: String,
        outputPath: String,
        config: ConnectionConfig = ConnectionConfig(),
        context: Context? = null,
        progressCallback: ((bytesRead: Long, totalBytes: Long) -> Unit)? = null
    ): Boolean = withContext(Dispatchers.IO) {
        
        return@withContext ExceptionHandler.safeExecute(context) {
            var connection: HttpURLConnection? = null
            
            try {
                connection = createConnection(url, config)
                connection.connect()
                
                if (connection.responseCode !in 200..299) {
                    Log.e(TAG, "Download failed with response code: ${connection.responseCode}")
                    return@safeExecute false
                }
                
                val totalBytes = connection.contentLengthLong
                var bytesRead = 0L
                
                connection.inputStream.safeUse { inputStream ->
                    java.io.FileOutputStream(outputPath).safeUse { outputStream ->
                        val buffer = ByteArray(8192)
                        var bytes: Int
                        
                        while (inputStream.read(buffer).also { bytes = it } != -1) {
                            outputStream.write(buffer, 0, bytes)
                            bytesRead += bytes
                            progressCallback?.invoke(bytesRead, totalBytes)
                        }
                        
                        true
                    }
                } ?: false
                
            } finally {
                ResourceManager.safeDisconnect(connection)
            }
        } ?: false
    }
    
    /**
     * 獲取網路類型
     */
    fun getNetworkType(context: Context): NetworkType {
        return ExceptionHandler.safeExecute(context) {
            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val network = connectivityManager.activeNetwork
                val capabilities = connectivityManager.getNetworkCapabilities(network)
                
                when {
                    capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) == true -> NetworkType.WIFI
                    capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) == true -> NetworkType.CELLULAR
                    capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) == true -> NetworkType.ETHERNET
                    else -> NetworkType.NONE
                }
            } else {
                @Suppress("DEPRECATION")
                val networkInfo = connectivityManager.activeNetworkInfo
                when (networkInfo?.type) {
                    ConnectivityManager.TYPE_WIFI -> NetworkType.WIFI
                    ConnectivityManager.TYPE_MOBILE -> NetworkType.CELLULAR
                    ConnectivityManager.TYPE_ETHERNET -> NetworkType.ETHERNET
                    else -> NetworkType.NONE
                }
            }
        } ?: NetworkType.NONE
    }
    
    /**
     * 網路類型枚舉
     */
    enum class NetworkType {
        WIFI,
        CELLULAR,
        ETHERNET,
        NONE
    }
}

/**
 * URL 擴展函數：安全連線
 */
suspend fun String.safeConnect(
    config: NetworkResourceManager.ConnectionConfig = NetworkResourceManager.ConnectionConfig(),
    context: Context? = null
): NetworkResourceManager.NetworkResponse {
    return NetworkResourceManager.executeHttpRequest(this, config, context)
}
