package com.example.autolaunch.utils

import android.app.ActivityManager
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.provider.Settings
import android.util.Log
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import com.example.autolaunch.service.ScheduleService

/**
 * 後台執行幫助工具類
 * 協助用戶設定應用程式的後台執行權限
 */
object BackgroundExecutionHelper {
    
    private const val TAG = "BackgroundExecutionHelper"
    
    /**
     * 檢查前台服務是否正在運行
     */
    fun isScheduleServiceRunning(context: Context): Boolean {
        return try {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            @Suppress("DEPRECATION")
            val runningServices = activityManager.getRunningServices(Integer.MAX_VALUE)

            runningServices.any { serviceInfo ->
                serviceInfo.service.className == ScheduleService::class.java.name
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking service status", e)
            false
        }
    }
    
    /**
     * 檢查是否需要顯示後台執行提示
     */
    fun shouldShowBackgroundExecutionPrompt(context: Context): Boolean {
        val prefs = context.getSharedPreferences("background_execution", Context.MODE_PRIVATE)
        val lastPromptTime = prefs.getLong("last_prompt_time", 0)
        val currentTime = System.currentTimeMillis()
        
        // 每 24 小時最多提示一次
        return (currentTime - lastPromptTime) > 24 * 60 * 60 * 1000
    }
    
    /**
     * 記錄已顯示後台執行提示
     */
    fun markBackgroundExecutionPromptShown(context: Context) {
        val prefs = context.getSharedPreferences("background_execution", Context.MODE_PRIVATE)
        prefs.edit().putLong("last_prompt_time", System.currentTimeMillis()).apply()
    }
    
    /**
     * 顯示後台執行設定對話框
     */
    fun showBackgroundExecutionDialog(activity: AppCompatActivity) {
        val message = buildString {
            appendLine("為確保排程能在後台正常執行，建議進行以下設定：")
            appendLine()
            appendLine("1. 允許應用程式在後台運行")
            appendLine("2. 關閉應用程式的自動管理")
            appendLine("3. 允許自動啟動")
            appendLine()
            appendLine("這些設定因手機品牌而異，點擊「前往設定」將引導您到相關設定頁面。")
        }
        
        AlertDialog.Builder(activity)
            .setTitle("後台執行設定")
            .setMessage(message)
            .setPositiveButton("前往設定") { _, _ ->
                openBackgroundAppSettings(activity)
                markBackgroundExecutionPromptShown(activity)
            }
            .setNegativeButton("稍後") { dialog, _ ->
                dialog.dismiss()
                markBackgroundExecutionPromptShown(activity)
            }
            .setNeutralButton("不再提示") { dialog, _ ->
                // 設定一個很遠的時間，實際上不再提示
                val prefs = activity.getSharedPreferences("background_execution", Context.MODE_PRIVATE)
                prefs.edit().putLong("last_prompt_time", Long.MAX_VALUE).apply()
                dialog.dismiss()
            }
            .setCancelable(false)
            .show()
    }
    
    /**
     * 開啟後台應用程式設定頁面
     */
    private fun openBackgroundAppSettings(context: Context) {
        try {
            val intent = when {
                // 華為
                isHuawei() -> {
                    Intent().apply {
                        component = android.content.ComponentName(
                            "com.huawei.systemmanager",
                            "com.huawei.systemmanager.startupmgr.ui.StartupNormalAppListActivity"
                        )
                    }
                }
                // 小米
                isXiaomi() -> {
                    Intent().apply {
                        component = android.content.ComponentName(
                            "com.miui.securitycenter",
                            "com.miui.permcenter.autostart.AutoStartManagementActivity"
                        )
                    }
                }
                // OPPO
                isOppo() -> {
                    Intent().apply {
                        component = android.content.ComponentName(
                            "com.coloros.safecenter",
                            "com.coloros.safecenter.permission.startup.FakeActivity"
                        )
                    }
                }
                // Vivo
                isVivo() -> {
                    Intent().apply {
                        component = android.content.ComponentName(
                            "com.iqoo.secure",
                            "com.iqoo.secure.ui.phoneoptimize.AddWhiteListActivity"
                        )
                    }
                }
                // Samsung
                isSamsung() -> {
                    Intent().apply {
                        component = android.content.ComponentName(
                            "com.samsung.android.lool",
                            "com.samsung.android.sm.ui.battery.BatteryActivity"
                        )
                    }
                }
                // 通用設定
                else -> {
                    Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                        data = Uri.parse("package:${context.packageName}")
                    }
                }
            }
            
            context.startActivity(intent)
            Log.i(TAG, "Opened background app settings")
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to open background app settings", e)
            // 降級到通用設定
            try {
                val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                    data = Uri.parse("package:${context.packageName}")
                }
                context.startActivity(intent)
            } catch (e2: Exception) {
                Log.e(TAG, "Failed to open app settings", e2)
            }
        }
    }
    
    private fun isHuawei(): Boolean = Build.MANUFACTURER.equals("HUAWEI", ignoreCase = true)
    private fun isXiaomi(): Boolean = Build.MANUFACTURER.equals("Xiaomi", ignoreCase = true)
    private fun isOppo(): Boolean = Build.MANUFACTURER.equals("OPPO", ignoreCase = true)
    private fun isVivo(): Boolean = Build.MANUFACTURER.equals("vivo", ignoreCase = true)
    private fun isSamsung(): Boolean = Build.MANUFACTURER.equals("samsung", ignoreCase = true)
}
