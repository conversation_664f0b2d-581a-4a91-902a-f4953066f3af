package com.example.autolaunch.utils

import android.content.Context
import android.util.Log
import androidx.room.withTransaction
import com.example.autolaunch.model.AppDatabase
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 資料庫事務管理器
 * 提供安全的事務處理和併發控制
 */
class DatabaseTransactionManager(private val context: Context) {
    
    companion object {
        private const val TAG = "DatabaseTransactionManager"
        
        @Volatile
        private var INSTANCE: DatabaseTransactionManager? = null
        
        fun getInstance(context: Context): DatabaseTransactionManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: DatabaseTransactionManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val database = AppDatabase.getDatabase(context)
    
    /**
     * 執行資料庫事務
     * 確保操作的原子性和一致性
     */
    suspend fun <T> executeTransaction(operation: suspend () -> T): T? {
        return ExceptionHandler.safeExecute(context) {
            withContext(Dispatchers.IO) {
                database.withTransaction {
                    operation()
                }
            }
        }
    }
    
    /**
     * 執行批量操作事務
     * 適用於需要插入/更新多個記錄的場景
     */
    suspend fun <T> executeBatchTransaction(
        operations: List<suspend () -> T>
    ): List<T?> {
        return ExceptionHandler.safeExecute(context) {
            withContext(Dispatchers.IO) {
                database.withTransaction {
                    operations.map { operation ->
                        try {
                            operation()
                        } catch (e: Exception) {
                            Log.e(TAG, "Batch operation failed", e)
                            null
                        }
                    }
                }
            }
        } ?: emptyList()
    }
    
    /**
     * 安全執行資料庫讀取操作
     * 提供重試機制和異常處理
     */
    suspend fun <T> safeRead(operation: suspend () -> T): T? {
        return ExceptionHandler.safeExecuteWithRetry(
            maxRetries = 3,
            retryDelayMs = 500,
            context = context
        ) {
            withContext(Dispatchers.IO) {
                operation()
            }
        }
    }
    
    /**
     * 安全執行資料庫寫入操作
     * 包含事務保護和異常處理
     */
    suspend fun <T> safeWrite(operation: suspend () -> T): T? {
        return executeTransaction {
            operation()
        }
    }
    
    /**
     * 執行資料庫清理操作
     * 用於定期清理過期數據
     */
    suspend fun performMaintenance() {
        ExceptionHandler.safeExecute(context) {
            withContext(Dispatchers.IO) {
                database.withTransaction {
                    // 清理過期的系統日誌（保留最近7天）
                    val sevenDaysAgo = System.currentTimeMillis() - (7 * 24 * 60 * 60 * 1000)
                    database.systemLogDao().deleteOldLogs(sevenDaysAgo)
                    
                    // 清理已禁用且超過30天的排程
                    val thirtyDaysAgo = System.currentTimeMillis() - (30 * 24 * 60 * 60 * 1000)
                    database.scheduleDao().deleteOldDisabledSchedules(thirtyDaysAgo)
                    
                    Log.i(TAG, "Database maintenance completed")
                }
            }
        }
    }
    
    /**
     * 檢查資料庫健康狀態
     */
    suspend fun checkDatabaseHealth(): Boolean {
        return ExceptionHandler.safeExecute(context) {
            withContext(Dispatchers.IO) {
                try {
                    // 嘗試執行簡單查詢來檢查資料庫狀態
                    database.scheduleDao().getScheduleCount()
                    database.systemLogDao().getLogCount()
                    true
                } catch (e: Exception) {
                    Log.e(TAG, "Database health check failed", e)
                    false
                }
            }
        } ?: false
    }
    
    /**
     * 執行資料庫備份前的準備工作
     */
    suspend fun prepareForBackup(): Boolean {
        return ExceptionHandler.safeExecute(context) {
            withContext(Dispatchers.IO) {
                database.withTransaction {
                    // 確保所有待處理的寫入操作完成
                    database.query("PRAGMA wal_checkpoint(FULL)", null)
                    true
                }
            }
        } ?: false
    }
    
    /**
     * 獲取資料庫統計信息
     */
    suspend fun getDatabaseStats(): DatabaseStats? {
        return ExceptionHandler.safeExecute(context) {
            withContext(Dispatchers.IO) {
                val scheduleCount = database.scheduleDao().getScheduleCount()
                val enabledScheduleCount = database.scheduleDao().getEnabledScheduleCount()
                val logCount = database.systemLogDao().getLogCount()
                val dbSize = getDatabaseSize()
                
                DatabaseStats(
                    totalSchedules = scheduleCount,
                    enabledSchedules = enabledScheduleCount,
                    totalLogs = logCount,
                    databaseSizeBytes = dbSize
                )
            }
        }
    }
    
    /**
     * 獲取資料庫檔案大小
     */
    private fun getDatabaseSize(): Long {
        return try {
            val dbFile = context.getDatabasePath(AppDatabase.DATABASE_NAME)
            if (dbFile.exists()) dbFile.length() else 0L
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get database size", e)
            0L
        }
    }
    
    /**
     * 資料庫統計信息數據類
     */
    data class DatabaseStats(
        val totalSchedules: Int,
        val enabledSchedules: Int,
        val totalLogs: Int,
        val databaseSizeBytes: Long
    ) {
        val databaseSizeMB: Double
            get() = databaseSizeBytes / (1024.0 * 1024.0)
    }
}

/**
 * 擴展函數：為 DAO 操作添加事務支持
 */
suspend fun <T> AppDatabase.safeTransaction(operation: suspend () -> T): T? {
    return ExceptionHandler.safeExecute {
        withTransaction {
            operation()
        }
    }
}

/**
 * 擴展函數：為 DAO 操作添加重試機制
 */
suspend fun <T> (() -> T).withRetry(
    maxRetries: Int = 3,
    delayMs: Long = 500
): T? {
    return ExceptionHandler.safeExecuteWithRetry(
        maxRetries = maxRetries,
        retryDelayMs = delayMs
    ) {
        this()
    }
}
