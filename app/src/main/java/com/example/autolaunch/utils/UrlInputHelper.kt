package com.example.autolaunch.utils

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import android.widget.Toast
import androidx.core.content.ContextCompat
import com.example.autolaunch.R
import com.example.autolaunch.databinding.LayoutUrlInputBinding
import com.example.autolaunch.databinding.ActivityAddEditScheduleBinding
import java.util.regex.Pattern

/**
 * URL 輸入輔助類
 * 處理 URL 輸入、驗證和預覽功能
 */
class UrlInputHelper {

    private val context: Context
    private val onUrlValidationChanged: (Boolean, String?) -> Unit

    // URL 輸入相關的 View 引用
    private val etUrl: com.google.android.material.textfield.TextInputEditText
    private val btnPreviewUrl: com.google.android.material.button.MaterialButton
    private val layoutUrlStatus: android.widget.LinearLayout
    private val ivUrlStatus: android.widget.ImageView
    private val tvUrlStatus: com.google.android.material.textview.MaterialTextView

    // 使用 LayoutUrlInputBinding 的構造函數（向後兼容）
    constructor(
        context: Context,
        binding: LayoutUrlInputBinding,
        onUrlValidationChanged: (Boolean, String?) -> Unit
    ) {
        this.context = context
        this.onUrlValidationChanged = onUrlValidationChanged
        this.etUrl = binding.etUrl
        this.btnPreviewUrl = binding.btnPreviewUrl
        this.layoutUrlStatus = binding.layoutUrlStatus
        this.ivUrlStatus = binding.ivUrlStatus
        this.tvUrlStatus = binding.tvUrlStatus

        init()
    }

    // 使用 ActivityAddEditScheduleBinding 的構造函數（新版本）
    constructor(
        context: Context,
        binding: ActivityAddEditScheduleBinding,
        onUrlValidationChanged: (Boolean, String?) -> Unit
    ) {
        this.context = context
        this.onUrlValidationChanged = onUrlValidationChanged
        this.etUrl = binding.etUrl
        this.btnPreviewUrl = binding.btnPreviewUrl
        this.layoutUrlStatus = binding.layoutUrlStatus
        this.ivUrlStatus = binding.ivUrlStatus
        this.tvUrlStatus = binding.tvUrlStatus

        init()
    }
    
    private var isUrlValid = false
    private var currentUrl: String? = null
    
    companion object {
        // URL 驗證正則表達式
        private val URL_PATTERN = Pattern.compile(
            "^(https?|ftp)://[^\\s/$.?#].[^\\s]*$",
            Pattern.CASE_INSENSITIVE
        )
        
        // 常見的 URL 前綴
        private val COMMON_PREFIXES = listOf("http://", "https://", "ftp://")
    }

    private fun init() {
        setupUrlInput()
        setupPreviewButton()
    }
    
    /**
     * 設定 URL 輸入框
     */
    private fun setupUrlInput() {
        etUrl.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}

            override fun afterTextChanged(s: Editable?) {
                val url = s?.toString()?.trim() ?: ""
                currentUrl = if (url.isNotEmpty()) url else null
                validateUrl(url)
            }
        })

        // 設定焦點變化監聽器
        etUrl.setOnFocusChangeListener { _, hasFocus ->
            if (!hasFocus) {
                autoCompleteUrl()
            }
        }
    }

    
    /**
     * 設定預覽按鈕
     */
    private fun setupPreviewButton() {
        btnPreviewUrl.setOnClickListener {
            previewUrl()
        }

        // 初始狀態下禁用預覽按鈕
        btnPreviewUrl.isEnabled = false
    }
    
    /**
     * 驗證 URL
     */
    private fun validateUrl(url: String) {
        when {
            url.isEmpty() -> {
                hideUrlStatus()
                isUrlValid = false
                btnPreviewUrl.isEnabled = false
            }

            isValidUrl(url) -> {
                showUrlStatus(true, context.getString(R.string.url_format_correct))
                isUrlValid = true
                btnPreviewUrl.isEnabled = true
            }

            else -> {
                showUrlStatus(false, context.getString(R.string.url_format_invalid))
                isUrlValid = false
                btnPreviewUrl.isEnabled = false
            }
        }
        
        // 通知驗證結果變化
        onUrlValidationChanged(isUrlValid, currentUrl)
    }
    
    /**
     * 檢查 URL 是否有效
     */
    private fun isValidUrl(url: String): Boolean {
        return URL_PATTERN.matcher(url).matches()
    }
    
    /**
     * 自動補全 URL（添加 http:// 前綴）
     */
    private fun autoCompleteUrl() {
        val url = etUrl.text?.toString()?.trim() ?: ""
        if (url.isNotEmpty() && !hasValidPrefix(url)) {
            val completedUrl = "https://$url"
            if (isValidUrl(completedUrl)) {
                etUrl.setText(completedUrl)
                etUrl.setSelection(completedUrl.length)
            }
        }
    }
    
    /**
     * 檢查 URL 是否有有效的前綴
     */
    private fun hasValidPrefix(url: String): Boolean {
        return COMMON_PREFIXES.any { url.startsWith(it, ignoreCase = true) }
    }
    
    /**
     * 顯示 URL 狀態
     */
    private fun showUrlStatus(isValid: Boolean, message: String) {
        layoutUrlStatus.visibility = View.VISIBLE

        if (isValid) {
            ivUrlStatus.setImageResource(R.drawable.ic_check_circle_24)
            ivUrlStatus.setColorFilter(
                ContextCompat.getColor(context, R.color.success_color)
            )
        } else {
            ivUrlStatus.setImageResource(R.drawable.ic_error_24)
            ivUrlStatus.setColorFilter(
                ContextCompat.getColor(context, R.color.error_color)
            )
        }

        tvUrlStatus.text = message
    }
    
    /**
     * 隱藏 URL 狀態
     */
    private fun hideUrlStatus() {
        layoutUrlStatus.visibility = View.GONE
    }
    
    /**
     * 預覽 URL
     */
    private fun previewUrl() {
        val url = currentUrl
        if (url != null && isValidUrl(url)) {
            try {
                val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
                context.startActivity(intent)
            } catch (e: Exception) {
                Toast.makeText(context, context.getString(R.string.error_cannot_open_url, e.message), Toast.LENGTH_SHORT).show()
            }
        } else {
            Toast.makeText(context, context.getString(R.string.error_enter_valid_url_preview), Toast.LENGTH_SHORT).show()
        }
    }
    
    /**
     * 獲取當前的 URL
     */
    fun getUrl(): String? = currentUrl
    
    /**
     * 設定 URL
     */
    fun setUrl(url: String?) {
        etUrl.setText(url ?: "")
    }
    
    /**
     * 檢查當前輸入是否有效
     */
    fun isValid(): Boolean = isUrlValid && !currentUrl.isNullOrBlank()
    
    /**
     * 清空輸入
     */
    fun clear() {
        etUrl.setText("")
        hideUrlStatus()
    }
}
