package com.example.autolaunch.utils

import android.content.Context
import android.graphics.*
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import androidx.core.content.ContextCompat
import com.example.autolaunch.R
import java.net.URL

/**
 * 文字圖標生成器
 * 用於為網頁排程生成文字圖標
 */
object TextIconGenerator {

    /**
     * 為 URL 生成文字圖標
     * @param context 上下文
     * @param url 網址
     * @param size 圖標大小 (dp)
     * @return 生成的 Drawable
     */
    fun generateWebIcon(context: Context, url: String?, size: Int = 48): Drawable {
        val text = extractDisplayText(url)
        return createTextIcon(context, text, size)
    }

    /**
     * 從 URL 提取顯示文字
     * @param url 網址
     * @return 顯示文字
     */
    private fun extractDisplayText(url: String?): String {
        if (url.isNullOrBlank()) return "WEB"
        
        return try {
            val urlObj = URL(url)
            val domain = urlObj.host
            
            when {
                // 移除 www. 前綴
                domain.startsWith("www.") -> {
                    val cleanDomain = domain.substring(4)
                    extractInitials(cleanDomain)
                }
                // 直接處理域名
                else -> extractInitials(domain)
            }
        } catch (e: Exception) {
            // 如果 URL 解析失敗，嘗試簡單的字串處理
            extractInitialsFromString(url)
        }
    }

    /**
     * 從域名提取首字母
     * @param domain 域名
     * @return 首字母縮寫
     */
    private fun extractInitials(domain: String): String {
        val parts = domain.split(".")
        return when {
            // 如果有多個部分，取前兩個部分的首字母
            parts.size >= 2 -> {
                val firstPart = parts[0].firstOrNull()?.uppercaseChar()
                val secondPart = parts[1].firstOrNull()?.uppercaseChar()
                if (firstPart != null && secondPart != null) {
                    "$firstPart$secondPart"
                } else {
                    firstPart?.toString() ?: "WEB"
                }
            }
            // 如果只有一個部分，取前兩個字母
            parts.size == 1 -> {
                val part = parts[0]
                when {
                    part.length >= 2 -> part.take(2).uppercase()
                    part.length == 1 -> part.uppercase()
                    else -> "WEB"
                }
            }
            else -> "WEB"
        }
    }

    /**
     * 從字串中提取首字母
     * @param text 文字
     * @return 首字母縮寫
     */
    private fun extractInitialsFromString(text: String): String {
        val cleanText = text.replace(Regex("https?://"), "")
            .replace(Regex("www\\."), "")
            .split("/")[0] // 只取域名部分
        
        return when {
            cleanText.length >= 2 -> cleanText.take(2).uppercase()
            cleanText.length == 1 -> cleanText.uppercase()
            else -> "WEB"
        }
    }

    /**
     * 創建文字圖標
     * @param context 上下文
     * @param text 顯示文字
     * @param size 圖標大小 (dp)
     * @return 生成的 Drawable
     */
    private fun createTextIcon(context: Context, text: String, size: Int): Drawable {
        val density = context.resources.displayMetrics.density
        val sizePixels = (size * density).toInt()
        
        // 創建 Bitmap
        val bitmap = Bitmap.createBitmap(sizePixels, sizePixels, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)
        
        // 繪製背景
        val backgroundDrawable = ContextCompat.getDrawable(context, R.drawable.bg_web_icon)
        backgroundDrawable?.setBounds(0, 0, sizePixels, sizePixels)
        backgroundDrawable?.draw(canvas)
        
        // 設定文字畫筆
        val textPaint = Paint().apply {
            color = Color.WHITE
            textAlign = Paint.Align.CENTER
            textSize = sizePixels * 0.35f // 文字大小為圖標的 35%
            typeface = Typeface.DEFAULT_BOLD
            isAntiAlias = true
        }
        
        // 計算文字位置
        val textBounds = Rect()
        textPaint.getTextBounds(text, 0, text.length, textBounds)
        val x = sizePixels / 2f
        val y = sizePixels / 2f + textBounds.height() / 2f
        
        // 繪製文字
        canvas.drawText(text, x, y, textPaint)
        
        return BitmapDrawable(context.resources, bitmap)
    }

    /**
     * 獲取預設的網頁圖標顏色
     * @param text 文字內容
     * @return 顏色值
     */
    fun getIconColor(text: String): Int {
        val colors = arrayOf(
            0xFF5B7C99.toInt(), // Soft Blue
            0xFF6B8E7F.toInt(), // Sage Green
            0xFFB85450.toInt(), // Muted Red
            0xFFD4A574.toInt(), // Warm Gold
            0xFF8B7CA3.toInt(), // Soft Purple
            0xFF5A9B9B.toInt(), // Teal
            0xFFD4956B.toInt(), // Warm Orange
            0xFF8B7355.toInt()  // Warm Brown
        )
        
        val hash = text.hashCode()
        val index = Math.abs(hash) % colors.size
        return colors[index]
    }

    /**
     * 創建帶自定義顏色的文字圖標
     * @param context 上下文
     * @param text 顯示文字
     * @param backgroundColor 背景顏色
     * @param size 圖標大小 (dp)
     * @return 生成的 Drawable
     */
    fun createColoredTextIcon(context: Context, text: String, backgroundColor: Int, size: Int = 48): Drawable {
        val density = context.resources.displayMetrics.density
        val sizePixels = (size * density).toInt()
        
        val bitmap = Bitmap.createBitmap(sizePixels, sizePixels, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)
        
        // 繪製圓形背景
        val backgroundPaint = Paint().apply {
            color = backgroundColor
            isAntiAlias = true
        }
        
        val radius = sizePixels / 2f
        canvas.drawCircle(radius, radius, radius * 0.9f, backgroundPaint)
        
        // 繪製文字
        val textPaint = Paint().apply {
            color = Color.WHITE
            textAlign = Paint.Align.CENTER
            textSize = sizePixels * 0.35f
            typeface = Typeface.DEFAULT_BOLD
            isAntiAlias = true
        }
        
        val textBounds = Rect()
        textPaint.getTextBounds(text, 0, text.length, textBounds)
        val x = sizePixels / 2f
        val y = sizePixels / 2f + textBounds.height() / 2f
        
        canvas.drawText(text, x, y, textPaint)
        
        return BitmapDrawable(context.resources, bitmap)
    }
}
