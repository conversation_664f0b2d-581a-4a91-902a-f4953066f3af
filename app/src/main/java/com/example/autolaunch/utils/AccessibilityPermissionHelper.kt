package com.example.autolaunch.utils

import android.content.Context
import android.content.Intent
import android.provider.Settings
import android.util.Log
import androidx.appcompat.app.AppCompatActivity
import com.example.autolaunch.service.AutoLaunchAccessibilityService

/**
 * 無障礙權限幫助工具類
 * 處理無障礙服務權限的檢查和請求
 */
object AccessibilityPermissionHelper {
    
    private const val TAG = "AccessibilityPermissionHelper"
    
    /**
     * 檢查無障礙服務是否已啟用
     */
    fun isAccessibilityServiceEnabled(context: Context): Boolean {
        return AutoLaunchAccessibilityService.isAccessibilityServiceEnabled(context)
    }
    
    /**
     * 請求無障礙服務權限
     */
    fun requestAccessibilityPermission(activity: AppCompatActivity) {
        try {
            val intent = AutoLaunchAccessibilityService.createAccessibilitySettingsIntent()
            activity.startActivity(intent)
            Log.i(TAG, "Opened accessibility settings")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to open accessibility settings", e)
        }
    }
    
    /**
     * 創建無障礙服務設置Intent
     */
    fun createAccessibilitySettingsIntent(): Intent {
        return Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS)
    }
    
    /**
     * 檢查是否需要請求無障礙權限
     */
    fun shouldRequestAccessibilityPermission(context: Context): Boolean {
        return !isAccessibilityServiceEnabled(context)
    }
    
    /**
     * 獲取無障礙服務實例
     */
    fun getAccessibilityService(): AutoLaunchAccessibilityService? {
        return AutoLaunchAccessibilityService.getInstance()
    }
    
    /**
     * 檢查無障礙服務是否可用
     */
    fun isAccessibilityServiceAvailable(): Boolean {
        return getAccessibilityService() != null
    }
    
    /**
     * 等待無障礙服務連接
     */
    suspend fun waitForAccessibilityService(timeoutMs: Long = 10000): AutoLaunchAccessibilityService? {
        val startTime = System.currentTimeMillis()
        
        while (System.currentTimeMillis() - startTime < timeoutMs) {
            val service = getAccessibilityService()
            if (service != null) {
                return service
            }
            kotlinx.coroutines.delay(500)
        }
        
        return null
    }
    
    /**
     * 獲取無障礙權限狀態描述
     */
    fun getAccessibilityPermissionStatusDescription(context: Context): String {
        return if (isAccessibilityServiceEnabled(context)) {
            "無障礙服務已啟用"
        } else {
            "需要啟用無障礙服務以執行自動化操作"
        }
    }
}
