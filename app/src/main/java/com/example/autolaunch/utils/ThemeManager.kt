package com.example.autolaunch.utils

import android.content.Context
import android.content.SharedPreferences
import android.content.res.Configuration
import android.util.Log
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.app.AppCompatDelegate
import com.example.autolaunch.R

/**
 * 主題管理器
 * 負責管理應用程式的主題切換和儲存
 */
class ThemeManager private constructor(private val context: Context) {

    companion object {
        private const val TAG = "ThemeManager"
        private const val PREFS_NAME = "theme_preferences"
        private const val KEY_THEME_TYPE = "theme_type"
        private const val KEY_FOLLOW_SYSTEM = "follow_system"

        @Volatile
        private var INSTANCE: ThemeManager? = null

        fun getInstance(context: Context): ThemeManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ThemeManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    
    /**
     * 獲取當前主題類型
     */
    fun getCurrentTheme(): ThemeType {
        // 如果設定為跟隨系統，返回對應的經典主題
        if (isFollowingSystem()) {
            val isSystemDarkMode = isSystemInDarkMode()
            return if (isSystemDarkMode) ThemeType.DARK_BLUE else ThemeType.LIGHT_CLASSIC
        }

        val themeId = prefs.getString(KEY_THEME_TYPE, null)
        return if (themeId != null) {
            ThemeType.fromId(themeId)
        } else {
            // 如果沒有儲存的主題，預設跟隨系統
            val isSystemDarkMode = isSystemInDarkMode()
            return if (isSystemDarkMode) ThemeType.DARK_BLUE else ThemeType.LIGHT_CLASSIC
        }
    }
    
    /**
     * 設定主題
     */
    fun setTheme(themeType: ThemeType) {
        Log.d(TAG, "Setting theme to: ${themeType.displayName}")

        prefs.edit()
            .putString(KEY_THEME_TYPE, themeType.id)
            .putBoolean(KEY_FOLLOW_SYSTEM, false)
            .apply()
    }
    
    /**
     * 是否跟隨系統主題
     */
    fun isFollowingSystem(): Boolean {
        return prefs.getBoolean(KEY_FOLLOW_SYSTEM, true)
    }
    
    /**
     * 設定是否跟隨系統主題
     */
    fun setFollowSystem(follow: Boolean) {
        Log.d(TAG, "Setting follow system: $follow")
        
        prefs.edit()
            .putBoolean(KEY_FOLLOW_SYSTEM, follow)
            .apply()
        
        if (follow) {
            // 如果設定跟隨系統，應用系統對應的主題
            val isSystemDarkMode = isSystemInDarkMode()
            val defaultTheme = ThemeType.getDefaultTheme(isSystemDarkMode)
            prefs.edit()
                .putString(KEY_THEME_TYPE, defaultTheme.id)
                .apply()
        }
    }
    
    /**
     * 應用主題到Activity
     */
    fun applyTheme(activity: AppCompatActivity) {
        val currentTheme = getCurrentTheme()
        Log.d(TAG, "Applying theme: ${currentTheme.displayName}")

        // 先設定系統UI模式，確保全局生效
        val nightMode = if (currentTheme.isDarkMode) {
            AppCompatDelegate.MODE_NIGHT_YES
        } else {
            AppCompatDelegate.MODE_NIGHT_NO
        }

        // 只有在模式真的需要改變時才設定，避免不必要的重新創建
        if (AppCompatDelegate.getDefaultNightMode() != nightMode) {
            AppCompatDelegate.setDefaultNightMode(nightMode)
        }

        val themeResId = getThemeResourceId(currentTheme)
        activity.setTheme(themeResId)
    }
    
    /**
     * 獲取主題資源ID
     */
    private fun getThemeResourceId(themeType: ThemeType): Int {
        return when (themeType) {
            ThemeType.LIGHT_CLASSIC -> R.style.Theme_AutoLaunch_Light_Classic
            ThemeType.LIGHT_WARM -> R.style.Theme_AutoLaunch_Light_Warm
            ThemeType.LIGHT_COOL -> R.style.Theme_AutoLaunch_Light_Cool
            ThemeType.LIGHT_PINK -> R.style.Theme_AutoLaunch_Light_Pink
            ThemeType.LIGHT_YELLOW -> R.style.Theme_AutoLaunch_Light_Yellow
            ThemeType.LIGHT_MINT -> R.style.Theme_AutoLaunch_Light_Mint
            ThemeType.LIGHT_LAVENDER -> R.style.Theme_AutoLaunch_Light_Lavender
            ThemeType.DARK_CLASSIC -> R.style.Theme_AutoLaunch_Dark_Classic
            ThemeType.DARK_BLUE -> R.style.Theme_AutoLaunch_Dark_Blue
            ThemeType.DARK_GREEN -> R.style.Theme_AutoLaunch_Dark_Green
            ThemeType.DARK_PURPLE -> R.style.Theme_AutoLaunch_Dark_Purple
            ThemeType.DARK_ORANGE -> R.style.Theme_AutoLaunch_Dark_Orange
            ThemeType.DARK_ROSE -> R.style.Theme_AutoLaunch_Dark_Rose
            ThemeType.DARK_GOLD -> R.style.Theme_AutoLaunch_Dark_Gold
            ThemeType.DARK_TEAL -> R.style.Theme_AutoLaunch_Dark_Teal
            ThemeType.DARK_CRIMSON -> R.style.Theme_AutoLaunch_Dark_Crimson
        }
    }
    
    /**
     * 檢查某個主題是否是當前的系統主題
     */
    fun isSystemTheme(themeType: ThemeType): Boolean {
        if (!isFollowingSystem()) return false

        val isSystemDarkMode = isSystemInDarkMode()
        return if (isSystemDarkMode) {
            themeType == ThemeType.DARK_BLUE
        } else {
            themeType == ThemeType.LIGHT_CLASSIC
        }
    }

    /**
     * 檢查系統是否處於深色模式
     */
    private fun isSystemInDarkMode(): Boolean {
        val nightModeFlags = context.resources.configuration.uiMode and Configuration.UI_MODE_NIGHT_MASK
        return nightModeFlags == Configuration.UI_MODE_NIGHT_YES
    }
    
    /**
     * 重新創建Activity以應用新主題
     */
    fun recreateActivity(activity: AppCompatActivity) {
        // 先應用新主題
        applyTheme(activity)

        // 然後重新創建Activity
        activity.recreate()
    }

    /**
     * 立即應用主題並重新創建Activity
     * 這個方法確保主題立即生效
     */
    fun applyThemeImmediately(activity: AppCompatActivity) {
        val currentTheme = getCurrentTheme()
        Log.d(TAG, "Applying theme immediately: ${currentTheme.displayName}")

        // 設定系統UI模式
        val nightMode = if (currentTheme.isDarkMode) {
            AppCompatDelegate.MODE_NIGHT_YES
        } else {
            AppCompatDelegate.MODE_NIGHT_NO
        }

        // 強制觸發全局主題切換
        // 這個方法會確保所有Activity都重新創建
        Log.d(TAG, "Force triggering global theme change")

        // 使用一個可靠的方法來觸發全局重新創建：
        // 先設定為一個不同的模式，然後立即設定為目標模式
        val tempMode = if (nightMode == AppCompatDelegate.MODE_NIGHT_YES) {
            AppCompatDelegate.MODE_NIGHT_NO
        } else {
            AppCompatDelegate.MODE_NIGHT_YES
        }

        // 設定臨時模式
        AppCompatDelegate.setDefaultNightMode(tempMode)

        // 立即設定目標模式，這會觸發所有Activity重新創建
        AppCompatDelegate.setDefaultNightMode(nightMode)
    }

    /**
     * 應用主題並重新創建所有Activity
     * 這個方法會觸發系統級的主題切換
     */
    fun applyThemeGlobally(activity: AppCompatActivity) {
        val currentTheme = getCurrentTheme()
        Log.d(TAG, "Applying theme globally: ${currentTheme.displayName}")

        // 設定系統UI模式
        val nightMode = if (currentTheme.isDarkMode) {
            AppCompatDelegate.MODE_NIGHT_YES
        } else {
            AppCompatDelegate.MODE_NIGHT_NO
        }

        // 只有在模式真的需要改變時才設定全局夜間模式
        if (AppCompatDelegate.getDefaultNightMode() != nightMode) {
            AppCompatDelegate.setDefaultNightMode(nightMode)
        } else {
            // 如果夜間模式沒有改變，直接重新創建當前Activity以應用新主題
            activity.recreate()
        }
    }
    
    /**
     * 獲取主題預覽顏色
     */
    fun getThemePreviewColors(themeType: ThemeType): ThemePreviewColors {
        return when (themeType) {
            ThemeType.LIGHT_CLASSIC -> ThemePreviewColors(
                primary = "#5B7C99",
                surface = "#FAFBFC",
                background = "#F7F9FA"
            )
            ThemeType.LIGHT_WARM -> ThemePreviewColors(
                primary = "#D2691E",
                surface = "#FFF8F0",
                background = "#FFF5E6"
            )
            ThemeType.LIGHT_COOL -> ThemePreviewColors(
                primary = "#4682B4",
                surface = "#F0F8FF",
                background = "#E6F3FF"
            )
            ThemeType.LIGHT_PINK -> ThemePreviewColors(
                primary = "#E91E63",
                surface = "#FDF2F8",
                background = "#FCE7F3"
            )
            ThemeType.LIGHT_YELLOW -> ThemePreviewColors(
                primary = "#F59E0B",
                surface = "#FFFBEB",
                background = "#FEF3C7"
            )
            ThemeType.LIGHT_MINT -> ThemePreviewColors(
                primary = "#10B981",
                surface = "#F0FDF4",
                background = "#DCFCE7"
            )
            ThemeType.LIGHT_LAVENDER -> ThemePreviewColors(
                primary = "#8B5CF6",
                surface = "#FAF5FF",
                background = "#F3E8FF"
            )
            ThemeType.DARK_CLASSIC -> ThemePreviewColors(
                primary = "#D0BCFF",
                surface = "#1C1B1F",
                background = "#1C1B1F"
            )
            ThemeType.DARK_BLUE -> ThemePreviewColors(
                primary = "#82B1FF",
                surface = "#0D1421",
                background = "#0A1018"
            )
            ThemeType.DARK_GREEN -> ThemePreviewColors(
                primary = "#69F0AE",
                surface = "#0E1F14",
                background = "#0A1A0F"
            )
            ThemeType.DARK_PURPLE -> ThemePreviewColors(
                primary = "#CE93D8",
                surface = "#1A0E1F",
                background = "#150A1A"
            )
            ThemeType.DARK_ORANGE -> ThemePreviewColors(
                primary = "#FFB74D",
                surface = "#1F1408",
                background = "#1A0F05"
            )
            ThemeType.DARK_ROSE -> ThemePreviewColors(
                primary = "#F48FB1",
                surface = "#1F0A14",
                background = "#1A0610"
            )
            ThemeType.DARK_GOLD -> ThemePreviewColors(
                primary = "#FFD54F",
                surface = "#1F1A00",
                background = "#1A1600"
            )
            ThemeType.DARK_TEAL -> ThemePreviewColors(
                primary = "#4DB6AC",
                surface = "#0F1F1C",
                background = "#0A1A17"
            )
            ThemeType.DARK_CRIMSON -> ThemePreviewColors(
                primary = "#EF5350",
                surface = "#1F0A0A",
                background = "#1A0606"
            )
        }
    }
}

/**
 * 主題預覽顏色數據類
 */
data class ThemePreviewColors(
    val primary: String,
    val surface: String,
    val background: String
)
