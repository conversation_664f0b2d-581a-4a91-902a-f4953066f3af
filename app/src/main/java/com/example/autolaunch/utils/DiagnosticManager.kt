package com.example.autolaunch.utils

import android.content.Context
import android.util.Log
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File
import java.text.SimpleDateFormat
import java.util.*

/**
 * 診斷管理器
 * 整合性能監控、崩潰報告和日誌系統，提供全面的診斷功能
 */
class DiagnosticManager private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "DiagnosticManager"
        
        @Volatile
        private var INSTANCE: DiagnosticManager? = null
        
        fun getInstance(context: Context): DiagnosticManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: DiagnosticManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val performanceMonitor = PerformanceMonitor.getInstance(context)
    private val crashReporter = CrashReporter.getInstance(context)
    private val diagnosticScope = CoroutineScope(Dispatchers.IO)
    
    /**
     * 初始化診斷系統
     */
    fun initialize() {
        Log.i(TAG, "Initializing diagnostic manager")
        
        // 初始化各個組件
        performanceMonitor.startMonitoring()
        crashReporter.initialize()
        
        // 初始化 Logger
        Logger.initialize(
            debugMode = true, // 簡化為固定值
            minLevel = Logger.Level.DEBUG,
            systemLog = true,
            memoryLog = true
        )
        
        Log.i(TAG, "Diagnostic manager initialized successfully")
    }
    
    /**
     * 生成診斷報告
     */
    fun generateDiagnosticReport(): DiagnosticReport {
        Log.d(TAG, "Generating diagnostic report")
        
        val timestamp = System.currentTimeMillis()
        
        // 性能統計
        val performanceStats = performanceMonitor.getPerformanceStatistics()
        val currentMetrics = performanceMonitor.currentMetrics.value
        
        // 日誌統計
        val logStats = Logger.getLogStatistics()
        
        // 崩潰報告
        val crashReports = crashReporter.getAllCrashReports()
        
        // 系統信息
        val systemInfo = getSystemDiagnosticInfo()
        
        // 應用程式狀態
        val appState = getAppDiagnosticState()
        
        return DiagnosticReport(
            timestamp = timestamp,
            performanceStats = performanceStats,
            currentMetrics = currentMetrics,
            logStats = logStats,
            crashReportCount = crashReports.size,
            systemInfo = systemInfo,
            appState = appState
        )
    }
    
    /**
     * 獲取系統診斷信息
     */
    private fun getSystemDiagnosticInfo(): SystemDiagnosticInfo {
        val runtime = Runtime.getRuntime()
        
        return SystemDiagnosticInfo(
            totalMemoryMB = runtime.totalMemory() / (1024 * 1024),
            freeMemoryMB = runtime.freeMemory() / (1024 * 1024),
            maxMemoryMB = runtime.maxMemory() / (1024 * 1024),
            availableProcessors = runtime.availableProcessors(),
            activeThreadCount = Thread.activeCount(),
            networkType = NetworkResourceManager.getNetworkType(context).name,
            storageAvailableGB = getAvailableStorage()
        )
    }
    
    /**
     * 獲取應用程式診斷狀態
     */
    private fun getAppDiagnosticState(): AppDiagnosticState {
        val isServiceRunning = BackgroundExecutionHelper.isScheduleServiceRunning(context)
        val hasBatteryOptimization = BatteryOptimizationHelper.isIgnoringBatteryOptimizations(context)
        val hasExactAlarmPermission = try {
            // 簡化權限檢查，避免依賴 AlarmManagerService
            true
        } catch (e: Exception) {
            false
        }
        val hasSystemAlertPermission = SystemPermissionHelper.hasSystemAlertWindowPermission(context)
        
        return AppDiagnosticState(
            isServiceRunning = isServiceRunning,
            hasBatteryOptimization = hasBatteryOptimization,
            hasExactAlarmPermission = hasExactAlarmPermission,
            hasSystemAlertPermission = hasSystemAlertPermission,
            allPermissionsGranted = isServiceRunning && hasBatteryOptimization && 
                                   hasExactAlarmPermission && hasSystemAlertPermission
        )
    }
    
    /**
     * 獲取可用存儲空間
     */
    private fun getAvailableStorage(): Long {
        return try {
            val internalDir = context.filesDir
            internalDir.freeSpace / (1024 * 1024 * 1024) // GB
        } catch (e: Exception) {
            Log.w(TAG, "Failed to get available storage", e)
            0L
        }
    }
    
    /**
     * 導出診斷報告到文件
     */
    fun exportDiagnosticReport(): File? {
        return try {
            val report = generateDiagnosticReport()
            val reportContent = formatDiagnosticReport(report)
            
            val fileName = "diagnostic_${SimpleDateFormat("yyyy-MM-dd_HH-mm-ss", Locale.getDefault()).format(Date())}.txt"
            val reportFile = File(context.getExternalFilesDir(null), fileName)
            
            reportFile.writeText(reportContent)
            Log.i(TAG, "Diagnostic report exported: ${reportFile.absolutePath}")
            
            reportFile
        } catch (e: Exception) {
            Log.e(TAG, "Failed to export diagnostic report", e)
            null
        }
    }
    
    /**
     * 格式化診斷報告
     */
    private fun formatDiagnosticReport(report: DiagnosticReport): String {
        val dateStr = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
            .format(Date(report.timestamp))
        
        return buildString {
            appendLine("=== DIAGNOSTIC REPORT ===")
            appendLine("Generated: $dateStr")
            appendLine()
            
            appendLine("=== PERFORMANCE STATISTICS ===")
            appendLine("Total Operations: ${report.performanceStats.totalOperations}")
            appendLine("Failed Operations: ${report.performanceStats.failedOperations}")
            appendLine("Success Rate: ${String.format("%.2f", report.performanceStats.successRate)}%")
            appendLine("Average Response Time: ${report.performanceStats.averageResponseTime}ms")
            appendLine("Metrics History Size: ${report.performanceStats.metricsHistorySize}")
            appendLine()
            
            appendLine("=== CURRENT PERFORMANCE METRICS ===")
            appendLine("Memory Usage: ${report.currentMetrics.memoryUsedMB}MB / ${report.currentMetrics.memoryTotalMB}MB (${String.format("%.1f", report.currentMetrics.memoryUsagePercent)}%)")
            appendLine("CPU Usage: ${String.format("%.1f", report.currentMetrics.cpuUsagePercent)}%")
            appendLine("Heap Usage: ${report.currentMetrics.heapUsedMB}MB / ${report.currentMetrics.heapTotalMB}MB")
            appendLine("Thread Count: ${report.currentMetrics.threadCount}")
            appendLine("GC Count: ${report.currentMetrics.gcCount}")
            appendLine()
            
            appendLine("=== LOG STATISTICS ===")
            appendLine("Total Logs: ${report.logStats.totalLogs}")
            appendLine("Recent Logs (1h): ${report.logStats.recentLogs}")
            report.logStats.levelCounts.forEach { (level, count) ->
                appendLine("${level.name}: $count")
            }
            appendLine()
            
            appendLine("=== SYSTEM INFO ===")
            appendLine("Total Memory: ${report.systemInfo.totalMemoryMB}MB")
            appendLine("Free Memory: ${report.systemInfo.freeMemoryMB}MB")
            appendLine("Max Memory: ${report.systemInfo.maxMemoryMB}MB")
            appendLine("Processors: ${report.systemInfo.availableProcessors}")
            appendLine("Active Threads: ${report.systemInfo.activeThreadCount}")
            appendLine("Network Type: ${report.systemInfo.networkType}")
            appendLine("Storage Available: ${report.systemInfo.storageAvailableGB}GB")
            appendLine()
            
            appendLine("=== APP STATE ===")
            appendLine("Service Running: ${report.appState.isServiceRunning}")
            appendLine("Battery Optimization: ${report.appState.hasBatteryOptimization}")
            appendLine("Exact Alarm Permission: ${report.appState.hasExactAlarmPermission}")
            appendLine("System Alert Permission: ${report.appState.hasSystemAlertPermission}")
            appendLine("All Permissions Granted: ${report.appState.allPermissionsGranted}")
            appendLine()
            
            appendLine("=== CRASH REPORTS ===")
            appendLine("Total Crash Reports: ${report.crashReportCount}")
            appendLine()
            
            appendLine("=== MEMORY LOGS (Recent) ===")
            val recentLogs = Logger.getMemoryLogs(limit = 50)
            recentLogs.forEach { logEntry ->
                val time = SimpleDateFormat("HH:mm:ss.SSS", Locale.getDefault()).format(Date(logEntry.timestamp))
                appendLine("$time ${logEntry.level.symbol}/${logEntry.tag}: ${logEntry.message}")
            }
        }
    }
    
    /**
     * 檢查系統健康狀態
     */
    fun checkSystemHealth(): SystemHealthStatus {
        val report = generateDiagnosticReport()
        val issues = mutableListOf<String>()
        val warnings = mutableListOf<String>()
        
        // 檢查記憶體使用
        if (report.currentMetrics.memoryUsagePercent > 90) {
            issues.add("Critical memory usage: ${report.currentMetrics.memoryUsagePercent.toInt()}%")
        } else if (report.currentMetrics.memoryUsagePercent > 80) {
            warnings.add("High memory usage: ${report.currentMetrics.memoryUsagePercent.toInt()}%")
        }
        
        // 檢查 CPU 使用
        if (report.currentMetrics.cpuUsagePercent > 80) {
            issues.add("High CPU usage: ${report.currentMetrics.cpuUsagePercent.toInt()}%")
        } else if (report.currentMetrics.cpuUsagePercent > 60) {
            warnings.add("Elevated CPU usage: ${report.currentMetrics.cpuUsagePercent.toInt()}%")
        }
        
        // 檢查權限狀態
        if (!report.appState.allPermissionsGranted) {
            warnings.add("Not all permissions granted")
        }
        
        // 檢查崩潰報告
        if (report.crashReportCount > 0) {
            warnings.add("${report.crashReportCount} crash reports found")
        }
        
        // 檢查操作成功率
        if (report.performanceStats.successRate < 95) {
            warnings.add("Low operation success rate: ${report.performanceStats.successRate.toInt()}%")
        }
        
        val healthLevel = when {
            issues.isNotEmpty() -> HealthLevel.CRITICAL
            warnings.isNotEmpty() -> HealthLevel.WARNING
            else -> HealthLevel.HEALTHY
        }
        
        return SystemHealthStatus(
            level = healthLevel,
            issues = issues,
            warnings = warnings,
            overallScore = calculateHealthScore(report)
        )
    }
    
    /**
     * 計算健康分數
     */
    private fun calculateHealthScore(report: DiagnosticReport): Int {
        var score = 100
        
        // 記憶體使用扣分
        if (report.currentMetrics.memoryUsagePercent > 80) {
            score -= ((report.currentMetrics.memoryUsagePercent - 80) * 2).toInt()
        }
        
        // CPU 使用扣分
        if (report.currentMetrics.cpuUsagePercent > 60) {
            score -= ((report.currentMetrics.cpuUsagePercent - 60) * 1.5).toInt()
        }
        
        // 權限狀態扣分
        if (!report.appState.allPermissionsGranted) {
            score -= 10
        }
        
        // 崩潰報告扣分
        score -= (report.crashReportCount * 5).coerceAtMost(20)
        
        // 操作成功率扣分
        if (report.performanceStats.successRate < 100) {
            score -= ((100 - report.performanceStats.successRate) / 2).toInt()
        }
        
        return score.coerceAtLeast(0)
    }
    
    /**
     * 清理診斷數據
     */
    fun cleanup() {
        performanceMonitor.cleanup()
        Logger.clearMemoryLogs()
        Log.i(TAG, "Diagnostic manager cleaned up")
    }
    
    // 數據類定義
    data class DiagnosticReport(
        val timestamp: Long,
        val performanceStats: PerformanceMonitor.PerformanceStatistics,
        val currentMetrics: PerformanceMonitor.PerformanceMetrics,
        val logStats: Logger.LogStatistics,
        val crashReportCount: Int,
        val systemInfo: SystemDiagnosticInfo,
        val appState: AppDiagnosticState
    )
    
    data class SystemDiagnosticInfo(
        val totalMemoryMB: Long,
        val freeMemoryMB: Long,
        val maxMemoryMB: Long,
        val availableProcessors: Int,
        val activeThreadCount: Int,
        val networkType: String,
        val storageAvailableGB: Long
    )
    
    data class AppDiagnosticState(
        val isServiceRunning: Boolean,
        val hasBatteryOptimization: Boolean,
        val hasExactAlarmPermission: Boolean,
        val hasSystemAlertPermission: Boolean,
        val allPermissionsGranted: Boolean
    )
    
    data class SystemHealthStatus(
        val level: HealthLevel,
        val issues: List<String>,
        val warnings: List<String>,
        val overallScore: Int
    )
    
    enum class HealthLevel {
        HEALTHY,
        WARNING,
        CRITICAL
    }
}
