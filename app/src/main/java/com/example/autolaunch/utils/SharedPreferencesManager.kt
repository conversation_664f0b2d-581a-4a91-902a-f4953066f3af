package com.example.autolaunch.utils

import android.content.Context
import android.content.SharedPreferences
import java.text.SimpleDateFormat
import java.util.*

class SharedPreferencesManager(context: Context) {
    private val sharedPreferences: SharedPreferences =
        context.getSharedPreferences("AutoLaunchPrefs", Context.MODE_PRIVATE)

    companion object {
        const val KEY_THEME = "key_theme"
        const val KEY_LANGUAGE = "key_language"
        const val KEY_WELCOME_COMPLETED = "key_welcome_completed"
    }

    fun setTheme(theme: String) {
        val editor = sharedPreferences.edit()
        editor.putString(KEY_THEME, theme)
        editor.apply()
    }

    fun getTheme(): String {
        return sharedPreferences.getString(KEY_THEME, "system") ?: "system"
    }

    fun setLanguage(language: String) {
        val editor = sharedPreferences.edit()
        editor.putString(KEY_LANGUAGE, language)
        editor.apply()
    }

    fun getLanguage(): String {
        return sharedPreferences.getString(KEY_LANGUAGE, "system") ?: "system"
    }

    fun setWelcomeCompleted(completed: Boolean) {
        sharedPreferences.edit().putBoolean(KEY_WELCOME_COMPLETED, completed).apply()
    }

    fun isWelcomeCompleted(): Boolean {
        return sharedPreferences.getBoolean(KEY_WELCOME_COMPLETED, false)
    }
} 