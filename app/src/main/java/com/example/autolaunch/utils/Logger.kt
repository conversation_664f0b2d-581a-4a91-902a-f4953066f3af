package com.example.autolaunch.utils

import android.content.Context
import android.util.Log
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.ConcurrentLinkedQueue

/**
 * 統一日誌管理器
 * 提供標準化的日誌記錄和管理功能
 */
object Logger {
    
    private const val DEFAULT_TAG = "AutoLaunch"
    private const val MAX_LOG_ENTRIES = 1000
    
    // 日誌級別
    enum class Level(val priority: Int, val symbol: String) {
        VERBOSE(Log.VERBOSE, "V"),
        DEBUG(Log.DEBUG, "D"),
        INFO(Log.INFO, "I"),
        WARN(Log.WARN, "W"),
        ERROR(Log.ERROR, "E")
    }
    
    // 內存中的日誌緩存
    private val logCache = ConcurrentLinkedQueue<LogEntry>()
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault())
    
    // 配置
    private var isDebugMode = true
    private var minLogLevel = Level.DEBUG
    private var enableSystemLog = true
    private var enableMemoryLog = true
    
    /**
     * 初始化日誌系統
     */
    fun initialize(
        debugMode: Boolean = true,
        minLevel: Level = Level.DEBUG,
        systemLog: Boolean = true,
        memoryLog: Boolean = true
    ) {
        isDebugMode = debugMode
        minLogLevel = minLevel
        enableSystemLog = systemLog
        enableMemoryLog = memoryLog
        
        i("Logger", "Logger initialized - Debug: $debugMode, MinLevel: $minLevel")
    }
    
    /**
     * Verbose 日誌
     */
    fun v(tag: String = DEFAULT_TAG, message: String, throwable: Throwable? = null) {
        log(Level.VERBOSE, tag, message, throwable)
    }
    
    /**
     * Debug 日誌
     */
    fun d(tag: String = DEFAULT_TAG, message: String, throwable: Throwable? = null) {
        log(Level.DEBUG, tag, message, throwable)
    }
    
    /**
     * Info 日誌
     */
    fun i(tag: String = DEFAULT_TAG, message: String, throwable: Throwable? = null) {
        log(Level.INFO, tag, message, throwable)
    }
    
    /**
     * Warning 日誌
     */
    fun w(tag: String = DEFAULT_TAG, message: String, throwable: Throwable? = null) {
        log(Level.WARN, tag, message, throwable)
    }
    
    /**
     * Error 日誌
     */
    fun e(tag: String = DEFAULT_TAG, message: String, throwable: Throwable? = null) {
        log(Level.ERROR, tag, message, throwable)
    }
    
    /**
     * 記錄方法進入
     */
    fun enter(tag: String = DEFAULT_TAG, methodName: String, vararg params: Any?) {
        if (isDebugMode) {
            val paramStr = if (params.isNotEmpty()) {
                params.joinToString(", ") { it?.toString() ?: "null" }
            } else ""
            d(tag, "→ $methodName($paramStr)")
        }
    }
    
    /**
     * 記錄方法退出
     */
    fun exit(tag: String = DEFAULT_TAG, methodName: String, result: Any? = null) {
        if (isDebugMode) {
            val resultStr = result?.let { " -> $it" } ?: ""
            d(tag, "← $methodName$resultStr")
        }
    }
    
    /**
     * 記錄性能指標
     */
    fun performance(tag: String = DEFAULT_TAG, operation: String, durationMs: Long) {
        i(tag, "⏱ $operation took ${durationMs}ms")
    }
    
    /**
     * 記錄用戶操作
     */
    fun userAction(tag: String = DEFAULT_TAG, action: String, details: String? = null) {
        val message = if (details != null) "$action - $details" else action
        i(tag, "👤 $message")
    }
    
    /**
     * 記錄系統事件
     */
    fun systemEvent(tag: String = DEFAULT_TAG, event: String, details: String? = null) {
        val message = if (details != null) "$event - $details" else event
        i(tag, "🔧 $message")
    }
    
    /**
     * 記錄網路請求
     */
    fun network(tag: String = DEFAULT_TAG, method: String, url: String, responseCode: Int? = null) {
        val response = responseCode?.let { " [$it]" } ?: ""
        i(tag, "🌐 $method $url$response")
    }
    
    /**
     * 記錄資料庫操作
     */
    fun database(tag: String = DEFAULT_TAG, operation: String, table: String? = null, count: Int? = null) {
        val tableInfo = table?.let { " on $it" } ?: ""
        val countInfo = count?.let { " ($it records)" } ?: ""
        i(tag, "💾 $operation$tableInfo$countInfo")
    }
    
    /**
     * 核心日誌記錄方法
     */
    private fun log(level: Level, tag: String, message: String, throwable: Throwable?) {
        // 檢查日誌級別
        if (level.priority < minLogLevel.priority) return
        
        val timestamp = System.currentTimeMillis()
        val formattedTime = dateFormat.format(Date(timestamp))
        
        // 系統日誌
        if (enableSystemLog) {
            val fullMessage = "[$formattedTime] $message"
            when (level) {
                Level.VERBOSE -> Log.v(tag, fullMessage, throwable)
                Level.DEBUG -> Log.d(tag, fullMessage, throwable)
                Level.INFO -> Log.i(tag, fullMessage, throwable)
                Level.WARN -> Log.w(tag, fullMessage, throwable)
                Level.ERROR -> Log.e(tag, fullMessage, throwable)
            }
        }
        
        // 內存日誌
        if (enableMemoryLog) {
            val logEntry = LogEntry(
                timestamp = timestamp,
                level = level,
                tag = tag,
                message = message,
                throwable = throwable,
                threadName = Thread.currentThread().name
            )
            
            addToCache(logEntry)
        }
    }
    
    /**
     * 添加到內存緩存
     */
    private fun addToCache(entry: LogEntry) {
        logCache.offer(entry)
        
        // 保持緩存大小限制
        while (logCache.size > MAX_LOG_ENTRIES) {
            logCache.poll()
        }
    }
    
    /**
     * 獲取內存中的日誌
     */
    fun getMemoryLogs(
        level: Level? = null,
        tag: String? = null,
        limit: Int = 100
    ): List<LogEntry> {
        return logCache
            .filter { entry ->
                (level == null || entry.level.priority >= level.priority) &&
                (tag == null || entry.tag.contains(tag, ignoreCase = true))
            }
            .takeLast(limit)
            .toList()
    }
    
    /**
     * 清除內存日誌
     */
    fun clearMemoryLogs() {
        logCache.clear()
        i("Logger", "Memory logs cleared")
    }
    
    /**
     * 導出日誌到字符串
     */
    fun exportLogs(
        level: Level? = null,
        tag: String? = null,
        limit: Int = 1000
    ): String {
        val logs = getMemoryLogs(level, tag, limit)
        return logs.joinToString("\n") { entry ->
            val time = dateFormat.format(Date(entry.timestamp))
            val throwableStr = entry.throwable?.let { "\n${Log.getStackTraceString(it)}" } ?: ""
            "$time ${entry.level.symbol}/${entry.tag}: ${entry.message}$throwableStr"
        }
    }
    
    /**
     * 獲取日誌統計信息
     */
    fun getLogStatistics(): LogStatistics {
        val logs = logCache.toList()
        val now = System.currentTimeMillis()
        val oneHourAgo = now - 3600_000 // 1 hour
        
        val recentLogs = logs.filter { it.timestamp > oneHourAgo }
        val levelCounts = Level.values().associateWith { level ->
            recentLogs.count { it.level == level }
        }
        
        return LogStatistics(
            totalLogs = logs.size,
            recentLogs = recentLogs.size,
            levelCounts = levelCounts,
            oldestLogTime = logs.minOfOrNull { it.timestamp },
            newestLogTime = logs.maxOfOrNull { it.timestamp }
        )
    }
    
    /**
     * 日誌條目數據類
     */
    data class LogEntry(
        val timestamp: Long,
        val level: Level,
        val tag: String,
        val message: String,
        val throwable: Throwable?,
        val threadName: String
    )
    
    /**
     * 日誌統計數據類
     */
    data class LogStatistics(
        val totalLogs: Int,
        val recentLogs: Int,
        val levelCounts: Map<Level, Int>,
        val oldestLogTime: Long?,
        val newestLogTime: Long?
    )
}

/**
 * 性能測量工具
 */
inline fun <T> Logger.measureTime(
    tag: String = "Performance",
    operation: String,
    block: () -> T
): T {
    val startTime = System.currentTimeMillis()
    return try {
        block()
    } finally {
        val duration = System.currentTimeMillis() - startTime
        performance(tag, operation, duration)
    }
}

/**
 * 安全執行並記錄
 */
inline fun <T> Logger.safeExecute(
    tag: String = "SafeExecute",
    operation: String,
    block: () -> T
): T? {
    return try {
        enter(tag, operation)
        val result = block()
        exit(tag, operation, result)
        result
    } catch (e: Exception) {
        e(tag, "Failed to execute $operation", e)
        null
    }
}
