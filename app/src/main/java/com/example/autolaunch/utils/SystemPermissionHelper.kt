package com.example.autolaunch.utils

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.provider.Settings
import android.util.Log
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import com.example.autolaunch.AlarmManagerService

/**
 * 系統權限幫助工具類
 * 處理各種系統級權限的檢查和請求
 */
object SystemPermissionHelper {
    
    private const val TAG = "SystemPermissionHelper"
    
    /**
     * 檢查是否有系統警報窗口權限
     */
    fun hasSystemAlertWindowPermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Settings.canDrawOverlays(context)
        } else {
            true // Android 6.0 以下不需要此權限
        }
    }
    
    /**
     * 創建系統警報窗口設置 Intent
     */
    fun createSystemAlertWindowIntent(context: Context): Intent? {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            return Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION).apply {
                data = Uri.parse("package:${context.packageName}")
            }
        }
        return null // 6.0 以下無此設置
    }
    
    /**
     * 請求系統警報窗口權限
     */
    fun requestSystemAlertWindowPermission(activity: AppCompatActivity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (!Settings.canDrawOverlays(activity)) {
                showSystemAlertWindowPermissionDialog(activity)
            }
        }
    }
    
    fun requestSystemAlertWindowPermission(context: Context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (!Settings.canDrawOverlays(context)) {
                val intent = createSystemAlertWindowIntent(context)?.apply {
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                }
                context.startActivity(intent)
            }
        }
    }
    
    /**
     * 顯示系統警報窗口權限對話框
     */
    private fun showSystemAlertWindowPermissionDialog(activity: AppCompatActivity) {
        AlertDialog.Builder(activity)
            .setTitle(activity.getString(com.example.autolaunch.R.string.permission_system_alert_window))
            .setMessage(activity.getString(com.example.autolaunch.R.string.permission_storage_message))
            .setPositiveButton(activity.getString(com.example.autolaunch.R.string.permission_go_to_settings)) { _, _ ->
                try {
                    val intent = Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION).apply {
                        data = Uri.parse("package:${activity.packageName}")
                    }
                    activity.startActivity(intent)
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to open overlay permission settings", e)
                }
            }
            .setNegativeButton(activity.getString(com.example.autolaunch.R.string.battery_optimization_later)) { dialog, _ ->
                dialog.dismiss()
            }
            .setCancelable(false)
            .show()
    }
    
    /**
     * 檢查是否有自動啟動權限（品牌特定）
     */
    fun checkAutoStartPermission(context: Context): Boolean {
        // 這個方法主要用於記錄和提示，實際檢查較困難
        Log.i(TAG, "Auto-start permission check requested")
        return true // 假設有權限，實際需要用戶手動設定
    }
    
    /**
     * 檢查所有必要的系統權限
     */
    fun checkAllSystemPermissions(activity: AppCompatActivity): Boolean {
        var allGranted = true
        
        // 檢查系統警報窗口權限
        if (!hasSystemAlertWindowPermission(activity)) {
            allGranted = false
            Log.w(TAG, "System alert window permission not granted")
        }
        
        // 檢查電池優化
        if (!BatteryOptimizationHelper.isIgnoringBatteryOptimizations(activity)) {
            allGranted = false
            Log.w(TAG, "Battery optimization not disabled")
        }
        
        // 檢查精確鬧鐘權限
        if (!AlarmManagerService.canScheduleExactAlarms(activity)) {
            allGranted = false
            Log.w(TAG, "Exact alarm permission not granted")
        }
        
        return allGranted
    }
    
    /**
     * 顯示權限總結對話框
     */
    fun showPermissionSummaryDialog(activity: AppCompatActivity) {
        val message = buildString {
            appendLine(activity.getString(com.example.autolaunch.R.string.permission_check_message))
            appendLine()

            // 系統警報窗口權限
            if (hasSystemAlertWindowPermission(activity)) {
                appendLine("✅ ${activity.getString(com.example.autolaunch.R.string.permission_system_alert_window)}：${activity.getString(com.example.autolaunch.R.string.permission_granted)}")
            } else {
                appendLine("❌ ${activity.getString(com.example.autolaunch.R.string.permission_system_alert_window)}：${activity.getString(com.example.autolaunch.R.string.permission_not_granted)}")
            }

            // 電池優化
            if (BatteryOptimizationHelper.isIgnoringBatteryOptimizations(activity)) {
                appendLine("✅ ${activity.getString(com.example.autolaunch.R.string.permission_battery_optimization)}：${activity.getString(com.example.autolaunch.R.string.permission_granted)}")
            } else {
                appendLine("❌ ${activity.getString(com.example.autolaunch.R.string.permission_battery_optimization)}：${activity.getString(com.example.autolaunch.R.string.permission_not_granted)}")
            }

            // 精確鬧鐘權限
            if (AlarmManagerService.canScheduleExactAlarms(activity)) {
                appendLine("✅ ${activity.getString(com.example.autolaunch.R.string.permission_exact_alarm)}：${activity.getString(com.example.autolaunch.R.string.permission_granted)}")
            } else {
                appendLine("❌ ${activity.getString(com.example.autolaunch.R.string.permission_exact_alarm)}：${activity.getString(com.example.autolaunch.R.string.permission_not_granted)}")
            }

            appendLine()
            appendLine(activity.getString(com.example.autolaunch.R.string.permission_recommendation))
        }

        AlertDialog.Builder(activity)
            .setTitle(activity.getString(com.example.autolaunch.R.string.permission_check_details))
            .setMessage(message)
            .setPositiveButton(activity.getString(com.example.autolaunch.R.string.confirm)) { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }
}
