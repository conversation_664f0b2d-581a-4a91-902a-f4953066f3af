package com.example.autolaunch.utils

import android.content.Context
import android.content.SharedPreferences
import android.content.res.Configuration
import android.os.Build
import java.util.*

/**
 * 語言管理器
 * 負責管理應用程式的多國語系設定
 */
object LanguageManager {
    
    private const val PREF_NAME = "language_settings"
    private const val KEY_LANGUAGE = "selected_language"
    const val LANGUAGE_FOLLOW_SYSTEM = "follow_system"
    
    // 支援的語言代碼
    const val LANGUAGE_CHINESE = "zh"
    const val LANGUAGE_ENGLISH = "en"
    const val LANGUAGE_JAPANESE = "ja"
    const val LANGUAGE_KOREAN = "ko"
    
    /**
     * 支援的語言列表
     */
    data class Language(
        val code: String,
        val displayName: String,
        val nativeName: String
    )
    
    val supportedLanguages = listOf(
        Language(LANGUAGE_FOLLOW_SYSTEM, "跟隨系統", "Follow System"),
        Language(LANGUAGE_CHINESE, "中文", "中文"),
        Language(LANGUAGE_ENGLISH, "English", "English"),
        Language(LANGUAGE_JAPANESE, "日本語", "日本語"),
        Language(LANGUAGE_KOREAN, "한국어", "한국어")
    )
    
    private fun getPreferences(context: Context): SharedPreferences {
        return context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
    }
    
    /**
     * 獲取當前選擇的語言
     */
    fun getCurrentLanguage(context: Context): String {
        return getPreferences(context).getString(KEY_LANGUAGE, LANGUAGE_FOLLOW_SYSTEM) 
            ?: LANGUAGE_FOLLOW_SYSTEM
    }
    
    /**
     * 設定語言
     */
    fun setLanguage(context: Context, languageCode: String) {
        getPreferences(context).edit()
            .putString(KEY_LANGUAGE, languageCode)
            .apply()
    }
    
    /**
     * 獲取系統語言
     */
    fun getSystemLanguage(): String {
        val systemLocale = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            Locale.getDefault()
        } else {
            @Suppress("DEPRECATION")
            Locale.getDefault()
        }
        
        return when (systemLocale.language) {
            "zh" -> LANGUAGE_CHINESE
            "en" -> LANGUAGE_ENGLISH
            "ja" -> LANGUAGE_JAPANESE
            "ko" -> LANGUAGE_KOREAN
            else -> LANGUAGE_ENGLISH // 預設為英文
        }
    }
    
    /**
     * 獲取實際應該使用的語言代碼
     */
    fun getActualLanguage(context: Context): String {
        val selectedLanguage = getCurrentLanguage(context)
        return if (selectedLanguage == LANGUAGE_FOLLOW_SYSTEM) {
            getSystemLanguage()
        } else {
            selectedLanguage
        }
    }
    
    /**
     * 應用語言設定到 Context
     */
    fun applyLanguage(context: Context): Context {
        val languageCode = getActualLanguage(context)
        val locale = Locale(languageCode)
        
        Locale.setDefault(locale)
        
        val configuration = Configuration(context.resources.configuration)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            configuration.setLocale(locale)
        } else {
            @Suppress("DEPRECATION")
            configuration.locale = locale
        }
        
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            context.createConfigurationContext(configuration)
        } else {
            @Suppress("DEPRECATION")
            context.resources.updateConfiguration(configuration, context.resources.displayMetrics)
            context
        }
    }
    
    /**
     * 檢查是否需要重新啟動應用程式
     */
    fun isLanguageChanged(context: Context, newLanguageCode: String): Boolean {
        val currentLanguage = getCurrentLanguage(context)
        return currentLanguage != newLanguageCode
    }
    
    /**
     * 獲取語言的顯示名稱
     */
    fun getLanguageDisplayName(context: Context, languageCode: String): String {
        return when (languageCode) {
            LANGUAGE_FOLLOW_SYSTEM -> context.getString(com.example.autolaunch.R.string.language_follow_system)
            LANGUAGE_CHINESE -> context.getString(com.example.autolaunch.R.string.language_chinese)
            LANGUAGE_ENGLISH -> context.getString(com.example.autolaunch.R.string.language_english)
            LANGUAGE_JAPANESE -> context.getString(com.example.autolaunch.R.string.language_japanese)
            LANGUAGE_KOREAN -> context.getString(com.example.autolaunch.R.string.language_korean)
            else -> languageCode
        }
    }
    
    /**
     * 獲取語言的原生名稱
     */
    fun getLanguageNativeName(languageCode: String): String {
        return supportedLanguages.find { it.code == languageCode }?.nativeName ?: languageCode
    }
}
