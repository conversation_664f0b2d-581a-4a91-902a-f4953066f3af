package com.example.autolaunch.utils

import android.database.Cursor
import android.util.Log
import java.io.Closeable
import java.io.InputStream
import java.io.OutputStream
import java.net.HttpURLConnection
import java.net.URLConnection

/**
 * 資源管理器
 * 提供安全的資源關閉和記憶體洩漏防護
 */
object ResourceManager {
    
    private const val TAG = "ResourceManager"
    
    /**
     * 安全關閉 Cursor
     */
    fun safeCloseCursor(cursor: Cursor?) {
        try {
            cursor?.close()
            Log.d(TAG, "Cursor closed safely")
        } catch (e: Exception) {
            Log.e(TAG, "Error closing cursor", e)
        }
    }
    
    /**
     * 安全關閉 InputStream
     */
    fun safeCloseInputStream(inputStream: InputStream?) {
        try {
            inputStream?.close()
            Log.d(TAG, "InputStream closed safely")
        } catch (e: Exception) {
            Log.e(TAG, "Error closing InputStream", e)
        }
    }
    
    /**
     * 安全關閉 OutputStream
     */
    fun safeCloseOutputStream(outputStream: OutputStream?) {
        try {
            outputStream?.close()
            Log.d(TAG, "OutputStream closed safely")
        } catch (e: Exception) {
            Log.e(TAG, "Error closing OutputStream", e)
        }
    }
    
    /**
     * 安全關閉任何 Closeable 資源
     */
    fun safeClose(closeable: Closeable?) {
        try {
            closeable?.close()
            Log.d(TAG, "Closeable resource closed safely")
        } catch (e: Exception) {
            Log.e(TAG, "Error closing resource", e)
        }
    }
    
    /**
     * 安全斷開 HTTP 連線
     */
    fun safeDisconnect(connection: HttpURLConnection?) {
        try {
            connection?.disconnect()
            Log.d(TAG, "HTTP connection disconnected safely")
        } catch (e: Exception) {
            Log.e(TAG, "Error disconnecting HTTP connection", e)
        }
    }
    
    /**
     * 安全斷開 URL 連線
     */
    fun safeDisconnect(connection: URLConnection?) {
        try {
            if (connection is HttpURLConnection) {
                connection.disconnect()
                Log.d(TAG, "URL connection disconnected safely")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error disconnecting URL connection", e)
        }
    }
    
    /**
     * 批量關閉多個資源
     */
    fun safeCloseAll(vararg resources: Closeable?) {
        resources.forEach { resource ->
            safeClose(resource)
        }
    }
    
    /**
     * 使用資源並自動關閉（類似 Kotlin 的 use 函數，但有額外的錯誤處理）
     */
    fun <T : Closeable?, R> T.safeUse(block: (T) -> R): R? {
        return try {
            block(this)
        } catch (e: Exception) {
            Log.e(TAG, "Error using resource", e)
            null
        } finally {
            safeClose(this)
        }
    }
    
    /**
     * 檢查資源是否需要關閉
     */
    fun needsClosing(cursor: Cursor?): Boolean {
        return cursor != null && !cursor.isClosed
    }
    
    /**
     * 安全執行資源操作，確保異常時也能正確清理
     */
    fun <T> safeExecute(
        resources: List<Closeable?> = emptyList(),
        operation: () -> T
    ): T? {
        return try {
            operation()
        } catch (e: Exception) {
            Log.e(TAG, "Error executing operation", e)
            null
        } finally {
            resources.forEach { resource ->
                safeClose(resource)
            }
        }
    }
}

/**
 * Cursor 擴展函數，提供安全的使用方式
 */
inline fun <T> Cursor?.safeUse(block: (Cursor) -> T): T? {
    return if (this != null && !this.isClosed) {
        try {
            block(this)
        } catch (e: Exception) {
            Log.e("ResourceManager", "Error using cursor", e)
            null
        } finally {
            ResourceManager.safeCloseCursor(this)
        }
    } else {
        null
    }
}

/**
 * InputStream 擴展函數
 */
inline fun <T> InputStream?.safeUse(block: (InputStream) -> T): T? {
    return this?.let { stream ->
        try {
            block(stream)
        } catch (e: Exception) {
            Log.e("ResourceManager", "Error using InputStream", e)
            null
        } finally {
            ResourceManager.safeCloseInputStream(stream)
        }
    }
}

/**
 * OutputStream 擴展函數
 */
inline fun <T> OutputStream?.safeUse(block: (OutputStream) -> T): T? {
    return this?.let { stream ->
        try {
            block(stream)
        } catch (e: Exception) {
            Log.e("ResourceManager", "Error using OutputStream", e)
            null
        } finally {
            ResourceManager.safeCloseOutputStream(stream)
        }
    }
}
