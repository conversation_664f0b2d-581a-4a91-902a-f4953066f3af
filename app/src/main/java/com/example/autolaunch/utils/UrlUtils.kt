package com.example.autolaunch.utils

import java.net.URI
import java.net.URL

/**
 * URL 工具類
 * 提供 URL 相關的實用功能
 */
object UrlUtils {
    
    /**
     * 從 URL 提取域名
     * @param url 完整的 URL
     * @return 域名，如果提取失敗則返回 null
     */
    fun extractDomain(url: String?): String? {
        if (url.isNullOrBlank()) return null

        return try {
            val uri = URI(url)
            val host = uri.host

            // 如果沒有host，返回null
            if (host.isNullOrBlank()) return null

            // 移除 www. 前綴
            if (host.startsWith("www.")) {
                host.substring(4)
            } else {
                host
            }
        } catch (e: Exception) {
            // 如果 URI 解析失敗，嘗試使用 URL 類
            try {
                val urlObj = URL(url)
                val host = urlObj.host

                // 如果沒有host，返回null
                if (host.isNullOrBlank()) return null

                if (host.startsWith("www.")) {
                    host.substring(4)
                } else {
                    host
                }
            } catch (e2: Exception) {
                null
            }
        }
    }
    
    /**
     * 從 URL 提取顯示名稱（域名的主要部分）
     * @param url 完整的 URL
     * @return 顯示名稱，如果提取失敗則返回 null
     */
    fun extractDisplayName(url: String?): String? {
        val domain = extractDomain(url) ?: return null
        
        return try {
            // 分割域名，取主要部分
            val parts = domain.split(".")
            when {
                parts.size >= 2 -> {
                    // 對於常見的頂級域名，取倒數第二部分
                    val mainPart = parts[parts.size - 2]
                    // 首字母大寫
                    mainPart.replaceFirstChar { if (it.isLowerCase()) it.titlecase() else it.toString() }
                }
                parts.size == 1 -> {
                    // 如果只有一部分，直接使用
                    parts[0].replaceFirstChar { if (it.isLowerCase()) it.titlecase() else it.toString() }
                }
                else -> domain
            }
        } catch (e: Exception) {
            domain
        }
    }
    
    /**
     * 生成網站圖標 URL
     * @param url 完整的 URL
     * @return 圖標 URL，如果生成失敗則返回 null
     */
    fun generateFaviconUrl(url: String?): String? {
        val domain = extractDomain(url) ?: return null

        return "https://www.google.com/s2/favicons?domain=$domain&sz=64"
    }
    
    /**
     * 檢查 URL 是否有效
     * @param url 要檢查的 URL
     * @return 是否有效
     */
    fun isValidUrl(url: String?): Boolean {
        if (url.isNullOrBlank()) return false
        
        return try {
            val uri = URI(url)
            uri.scheme != null && uri.host != null
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 標準化 URL（確保有協議前綴）
     * @param url 原始 URL
     * @return 標準化後的 URL
     */
    fun normalizeUrl(url: String?): String? {
        if (url.isNullOrBlank()) return null
        
        val trimmedUrl = url.trim()
        
        return when {
            trimmedUrl.startsWith("http://", ignoreCase = true) ||
            trimmedUrl.startsWith("https://", ignoreCase = true) ||
            trimmedUrl.startsWith("ftp://", ignoreCase = true) -> trimmedUrl
            
            else -> "https://$trimmedUrl"
        }
    }
}
