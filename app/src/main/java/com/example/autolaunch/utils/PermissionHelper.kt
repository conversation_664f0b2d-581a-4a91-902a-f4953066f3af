package com.example.autolaunch.utils

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat

/**
 * 權限管理助手類
 * 處理應用程式所需的各種權限請求和檢查
 */
object PermissionHelper {
    
    // 權限請求代碼
    const val REQUEST_CODE_STORAGE_PERMISSION = 1001
    const val REQUEST_CODE_ALL_PERMISSIONS = 1002
    
    // 存儲權限（根據 Android 版本不同）
    private val STORAGE_PERMISSIONS_LEGACY = arrayOf(
        Manifest.permission.READ_EXTERNAL_STORAGE,
        Manifest.permission.WRITE_EXTERNAL_STORAGE
    )
    
    private val STORAGE_PERMISSIONS_ANDROID_13 = arrayOf(
        Manifest.permission.READ_MEDIA_IMAGES,
        Manifest.permission.READ_MEDIA_VIDEO,
        Manifest.permission.READ_MEDIA_AUDIO
    )
    
    // Google Drive 相關權限
    private val GOOGLE_DRIVE_PERMISSIONS = arrayOf(
        Manifest.permission.GET_ACCOUNTS,
        Manifest.permission.INTERNET,
        Manifest.permission.ACCESS_NETWORK_STATE
    )
    
    /**
     * 檢查是否擁有存儲權限（針對備份功能優化）
     */
    fun hasStoragePermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ 對於 Downloads 目錄的文件操作，通常不需要特殊權限
            // 因為我們使用的是公共 Downloads 目錄
            true
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            // Android 6-12 檢查傳統存儲權限
            ContextCompat.checkSelfPermission(context, Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED
        } else {
            // Android 6 以下自動擁有權限
            true
        }
    }
    
    /**
     * 檢查是否擁有 Google Drive 相關權限
     */
    fun hasGoogleDrivePermissions(context: Context): Boolean {
        return GOOGLE_DRIVE_PERMISSIONS.all { permission ->
            ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
        }
    }
    
    /**
     * 請求存儲權限（針對備份功能優化）
     */
    fun requestStoragePermission(activity: Activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ 通常不需要請求權限來訪問 Downloads 目錄
            // 但如果需要，可以引導用戶到設置頁面
            return
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            // Android 6-12 請求傳統存儲權限
            ActivityCompat.requestPermissions(
                activity,
                arrayOf(Manifest.permission.WRITE_EXTERNAL_STORAGE),
                REQUEST_CODE_STORAGE_PERMISSION
            )
        }
        // Android 6 以下不需要請求權限
    }
    
    /**
     * 請求 Google Drive 相關權限
     */
    fun requestGoogleDrivePermissions(activity: Activity) {
        ActivityCompat.requestPermissions(
            activity,
            GOOGLE_DRIVE_PERMISSIONS,
            REQUEST_CODE_ALL_PERMISSIONS
        )
    }
    
    /**
     * 請求所有必要權限
     */
    fun requestAllPermissions(activity: Activity) {
        val allPermissions = mutableListOf<String>()
        
        // 添加存儲權限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            allPermissions.addAll(STORAGE_PERMISSIONS_ANDROID_13)
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            allPermissions.addAll(STORAGE_PERMISSIONS_LEGACY)
        }
        
        // 添加 Google Drive 權限
        allPermissions.addAll(GOOGLE_DRIVE_PERMISSIONS)
        
        // 過濾掉已經擁有的權限
        val missingPermissions = allPermissions.filter { permission ->
            ContextCompat.checkSelfPermission(activity, permission) != PackageManager.PERMISSION_GRANTED
        }
        
        if (missingPermissions.isNotEmpty()) {
            ActivityCompat.requestPermissions(
                activity,
                missingPermissions.toTypedArray(),
                REQUEST_CODE_ALL_PERMISSIONS
            )
        }
    }
    
    /**
     * 檢查權限請求結果
     */
    fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray,
        onStoragePermissionGranted: () -> Unit = {},
        onStoragePermissionDenied: () -> Unit = {},
        onGoogleDrivePermissionGranted: () -> Unit = {},
        onGoogleDrivePermissionDenied: () -> Unit = {},
        onAllPermissionsGranted: () -> Unit = {},
        onSomePermissionsDenied: (deniedPermissions: List<String>) -> Unit = {}
    ) {
        when (requestCode) {
            REQUEST_CODE_STORAGE_PERMISSION -> {
                if (grantResults.isNotEmpty() && grantResults.all { it == PackageManager.PERMISSION_GRANTED }) {
                    onStoragePermissionGranted()
                } else {
                    onStoragePermissionDenied()
                }
            }
            
            REQUEST_CODE_ALL_PERMISSIONS -> {
                val deniedPermissions = mutableListOf<String>()
                
                for (i in permissions.indices) {
                    if (grantResults[i] != PackageManager.PERMISSION_GRANTED) {
                        deniedPermissions.add(permissions[i])
                    }
                }
                
                if (deniedPermissions.isEmpty()) {
                    onAllPermissionsGranted()
                } else {
                    onSomePermissionsDenied(deniedPermissions)
                    
                    // 檢查特定權限類型
                    val storagePermissions = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                        STORAGE_PERMISSIONS_ANDROID_13
                    } else {
                        STORAGE_PERMISSIONS_LEGACY
                    }
                    
                    val hasStoragePermissionDenied = deniedPermissions.any { it in storagePermissions }
                    val hasGoogleDrivePermissionDenied = deniedPermissions.any { it in GOOGLE_DRIVE_PERMISSIONS }
                    
                    if (hasStoragePermissionDenied) {
                        onStoragePermissionDenied()
                    }
                    
                    if (hasGoogleDrivePermissionDenied) {
                        onGoogleDrivePermissionDenied()
                    }
                }
            }
        }
    }
    
    /**
     * 檢查是否應該顯示權限說明
     */
    fun shouldShowRequestPermissionRationale(activity: Activity, permission: String): Boolean {
        return ActivityCompat.shouldShowRequestPermissionRationale(activity, permission)
    }
    
    /**
     * 檢查是否應該顯示存儲權限說明
     */
    fun shouldShowStoragePermissionRationale(activity: Activity): Boolean {
        val permissions = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            STORAGE_PERMISSIONS_ANDROID_13
        } else {
            STORAGE_PERMISSIONS_LEGACY
        }
        
        return permissions.any { permission ->
            ActivityCompat.shouldShowRequestPermissionRationale(activity, permission)
        }
    }
    
    /**
     * 獲取權限狀態摘要
     */
    fun getPermissionStatusSummary(context: Context): PermissionStatus {
        return PermissionStatus(
            hasStoragePermission = hasStoragePermission(context),
            hasGoogleDrivePermissions = hasGoogleDrivePermissions(context)
        )
    }
}

/**
 * 權限狀態數據類
 */
data class PermissionStatus(
    val hasStoragePermission: Boolean,
    val hasGoogleDrivePermissions: Boolean
) {
    val hasAllPermissions: Boolean
        get() = hasStoragePermission && hasGoogleDrivePermissions
    
    val missingPermissions: List<String>
        get() {
            val missing = mutableListOf<String>()
            if (!hasStoragePermission) missing.add("存儲權限")
            if (!hasGoogleDrivePermissions) missing.add("網路權限")
            return missing
        }
}
