package com.example.autolaunch.utils

/**
 * 主題類型枚舉
 * 定義應用程式支援的所有主題
 */
enum class ThemeType(
    val id: String,
    val displayName: String,
    val description: String,
    val isDarkMode: Boolean
) {
    // 淺色主題
    LIGHT_CLASSIC("light_classic", "經典淺色", "就像白襯衫一樣永不過時 👔", false),
    LIGHT_WARM("light_warm", "溫暖淺色", "像被陽光擁抱的感覺 ☀️", false),
    LIGHT_COOL("light_cool", "清涼淺色", "海風徐來，清爽怡人 🌊", false),
    LIGHT_PINK("light_pink", "粉紅淺色", "少女心爆棚的夢幻粉 💕", false),
    LIGHT_YELLOW("light_yellow", "陽光淺色", "滿滿維他命C的活力黃 🍋", false),
    LIGHT_MINT("light_mint", "薄荷淺色", "薄荷糖般的清新綠意 🌿", false),
    LIGHT_LAVENDER("light_lavender", "薰衣草淺色", "普羅旺斯的浪漫紫調 💜", false),

    // 深色主題
    DARK_BLUE("dark_blue", "深藍主題", "深海般的神秘藍調 🌊", true),
    DARK_CLASSIC("dark_classic", "經典深色", "夜貓子的最愛，護眼又經典 🌙", true),
    DARK_GREEN("dark_green", "深綠主題", "森林系的自然綠意 🌲", true),
    DARK_PURPLE("dark_purple", "深紫主題", "魔法師的神秘紫色 🔮", true),
    DARK_ORANGE("dark_orange", "深橙主題", "篝火般的溫暖橙光 🔥", true),
    DARK_ROSE("dark_rose", "玫瑰深色", "暗夜玫瑰的浪漫情調 🌹", true),
    DARK_GOLD("dark_gold", "金色深色", "土豪金的奢華質感 ✨", true),
    DARK_TEAL("dark_teal", "青綠深色", "湖水般的沉靜青綠 🏞️", true),
    DARK_CRIMSON("dark_crimson", "深紅主題", "熱血沸騰的激情紅 ❤️", true);
    
    companion object {
        /**
         * 根據ID獲取主題類型
         */
        fun fromId(id: String): ThemeType {
            return values().find { it.id == id } ?: LIGHT_CLASSIC
        }
        
        /**
         * 獲取所有淺色主題
         */
        fun getLightThemes(): List<ThemeType> {
            return values().filter { !it.isDarkMode }
        }

        /**
         * 獲取所有深色主題
         */
        fun getDarkThemes(): List<ThemeType> {
            return values().filter { it.isDarkMode }
        }
        
        /**
         * 根據系統深色模式獲取預設主題
         */
        fun getDefaultTheme(isSystemDarkMode: Boolean): ThemeType {
            return if (isSystemDarkMode) DARK_BLUE else LIGHT_CLASSIC
        }
    }
}
