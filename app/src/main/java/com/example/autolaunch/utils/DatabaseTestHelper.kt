package com.example.autolaunch.utils

import android.content.Context
import android.util.Log
import com.example.autolaunch.model.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch

/**
 * 資料庫測試輔助工具
 * 用於驗證系統日誌功能是否正常運作
 */
object DatabaseTestHelper {

    private const val TAG = "DatabaseTestHelper"

    /**
     * 測試系統日誌功能
     */
    fun testSystemLogFunctionality(context: Context) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                Log.d(TAG, "開始測試系統日誌功能...")

                val database = AppDatabase.getDatabase(context)
                val systemLogDao = database.systemLogDao()

                // 測試插入日誌
                val testLog = SystemLog(
                    logType = LogType.INFO.value,
                    actionType = ActionType.APP_STARTED.value,
                    message = "測試日誌記錄",
                    details = "這是一個測試日誌，用於驗證系統日誌功能是否正常運作"
                )

                val logId = systemLogDao.insert(testLog)
                Log.d(TAG, "插入測試日誌成功，ID: $logId")

                // 測試查詢日誌
                val logs = systemLogDao.getAllLogs().first()
                Log.d(TAG, "查詢到 ${logs.size} 條日誌")

                // 測試統計功能
                val totalCount = systemLogDao.getLogCount()
                Log.d(TAG, "日誌總數: $totalCount")

                // 測試SystemLogManager
                SystemLogManager.logAppStarted(context, "資料庫測試完成")

                Log.d(TAG, "系統日誌功能測試完成！")

            } catch (e: Exception) {
                Log.e(TAG, "系統日誌功能測試失敗", e)
            }
        }
    }
    
    /**
     * 清理测试数据
     */
    fun cleanupTestData(context: Context) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val database = AppDatabase.getDatabase(context)
                val systemLogDao = database.systemLogDao()
                
                // 刪除測試日誌
                val deletedCount = systemLogDao.deleteAllLogs()
                Log.d(TAG, "清理了 $deletedCount 條測試日誌")

            } catch (e: Exception) {
                Log.e(TAG, "清理測試資料失敗", e)
            }
        }
    }
    
    /**
     * 验证数据库迁移是否成功
     */
    fun verifyDatabaseMigration(context: Context) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                Log.d(TAG, "验证数据库迁移...")
                
                val database = AppDatabase.getDatabase(context)
                
                // 测试Schedule表是否正常
                val scheduleDao = database.scheduleDao()
                val scheduleCount = scheduleDao.getScheduleCount()
                Log.d(TAG, "Schedule表正常，当前有 $scheduleCount 条记录")
                
                // 测试SystemLog表是否正常
                val systemLogDao = database.systemLogDao()
                val logCount = systemLogDao.getLogCount()
                Log.d(TAG, "SystemLog表正常，当前有 $logCount 条记录")
                
                Log.d(TAG, "数据库迁移验证成功！")
                
            } catch (e: Exception) {
                Log.e(TAG, "数据库迁移验证失败", e)
            }
        }
    }
}
