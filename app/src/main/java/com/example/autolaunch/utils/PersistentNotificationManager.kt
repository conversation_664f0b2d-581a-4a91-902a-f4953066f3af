package com.example.autolaunch.utils

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.core.content.ContextCompat
import com.example.autolaunch.MainActivity
import com.example.autolaunch.R

/**
 * 常駐通知管理器
 * 負責管理應用程式在通知欄的常駐顯示
 */
class PersistentNotificationManager private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "PersistentNotificationManager"
        private const val CHANNEL_ID = "autolaunch_persistent"
        private const val NOTIFICATION_ID = 1001
        
        @Volatile
        private var INSTANCE: PersistentNotificationManager? = null
        
        fun getInstance(context: Context): PersistentNotificationManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: PersistentNotificationManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
    private val settingsManager = SettingsManager.getInstance(context)
    
    init {
        createNotificationChannel()
    }
    
    /**
     * 創建通知通道
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                context.getString(R.string.notification_channel_persistent_name),
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = context.getString(R.string.notification_channel_persistent_description)
                setShowBadge(false)
                enableLights(false)
                enableVibration(false)
            }
            notificationManager.createNotificationChannel(channel)
            Log.d(TAG, "Notification channel created")
        }
    }
    
    /**
     * 檢查是否有通知權限
     */
    private fun hasNotificationPermission(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            val hasPermission = ContextCompat.checkSelfPermission(
                context,
                android.Manifest.permission.POST_NOTIFICATIONS
            ) == PackageManager.PERMISSION_GRANTED
            Log.d(TAG, "Android ${Build.VERSION.SDK_INT}, checking POST_NOTIFICATIONS permission: $hasPermission")
            hasPermission
        } else {
            Log.d(TAG, "Android ${Build.VERSION.SDK_INT}, notification permission not required")
            true // Android 13 以下不需要運行時權限
        }
    }

    /**
     * 顯示常駐通知
     */
    fun showPersistentNotification() {
        if (!settingsManager.isPersistentNotificationEnabled()) {
            Log.d(TAG, "Persistent notification is disabled, skipping")
            return
        }

        if (!hasNotificationPermission()) {
            Log.w(TAG, "No notification permission, cannot show persistent notification")
            return
        }
        
        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        }
        
        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or 
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) PendingIntent.FLAG_IMMUTABLE else 0
        )
        
        val notification = NotificationCompat.Builder(context, CHANNEL_ID)
            .setContentTitle(context.getString(R.string.app_name))
            .setContentText(context.getString(R.string.persistent_notification_text))
            .setSmallIcon(R.drawable.ic_notification)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setCategory(NotificationCompat.CATEGORY_SERVICE)
            .setShowWhen(false)
            .setAutoCancel(false)
            .build()
        
        notificationManager.notify(NOTIFICATION_ID, notification)
        Log.d(TAG, "Persistent notification shown")
    }
    
    /**
     * 隱藏常駐通知
     */
    fun hidePersistentNotification() {
        notificationManager.cancel(NOTIFICATION_ID)
        Log.d(TAG, "Persistent notification hidden")
    }
    
    /**
     * 更新常駐通知狀態
     */
    fun updatePersistentNotificationState() {
        if (settingsManager.isPersistentNotificationEnabled()) {
            showPersistentNotification()
        } else {
            hidePersistentNotification()
        }
    }

    /**
     * 檢查並恢復常駐通知（用於應用重啟後）
     */
    fun restorePersistentNotificationIfNeeded() {
        if (settingsManager.isPersistentNotificationEnabled()) {
            Log.d(TAG, "Restoring persistent notification after app restart")
            showPersistentNotification()
        }
    }
}
