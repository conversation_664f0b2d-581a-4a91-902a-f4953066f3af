package com.example.autolaunch

import android.os.Bundle
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import com.example.autolaunch.base.BaseActivity
import com.example.autolaunch.databinding.ActivityQaBinding

/**
 * Q&A(常見問題)頁面Activity
 */
class QAActivity : BaseActivity() {
    
    private lateinit var binding: ActivityQaBinding
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        binding = ActivityQaBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupUI()
        setupWindowInsets()
    }
    
    private fun setupUI() {
        // 設定工具列
        binding.toolbar.setNavigationIcon(androidx.appcompat.R.drawable.abc_ic_ab_back_material)
        binding.toolbar.setNavigationOnClickListener {
            finish()
        }
    }
    
    private fun setupWindowInsets() {
        ViewCompat.setOnApplyWindowInsetsListener(binding.root) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }
    }
}
