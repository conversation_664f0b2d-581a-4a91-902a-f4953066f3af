package com.example.autolaunch

import android.content.Intent
import android.os.Bundle
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.viewpager2.widget.ViewPager2
import com.example.autolaunch.adapters.WelcomePagerAdapter
import com.example.autolaunch.base.BaseActivity
import com.example.autolaunch.databinding.ActivityWelcomeBinding
import com.example.autolaunch.utils.SettingsManager
import com.google.android.material.tabs.TabLayoutMediator

/**
 * 歡迎導覽頁面Activity
 * 初次使用時顯示的多頁滑動式導覽
 */
class WelcomeActivity : BaseActivity() {

    private lateinit var binding: ActivityWelcomeBinding
    private lateinit var welcomePagerAdapter: WelcomePagerAdapter
    private lateinit var settingsManager: SettingsManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityWelcomeBinding.inflate(layoutInflater)
        setContentView(binding.root)

        settingsManager = SettingsManager.getInstance(this)

        setupUI()
        setupViewPager()
        setupWindowInsets()
    }

    private fun setupUI() {
        // 設定跳過按鈕
        binding.btnSkip.setOnClickListener {
            finishWelcome()
        }

        // 設定開始使用按鈕
        binding.btnGetStarted.setOnClickListener {
            finishWelcome()
        }

        // 設定上一頁按鈕
        binding.btnPrevious.setOnClickListener {
            val currentItem = binding.viewPager.currentItem
            if (currentItem > 0) {
                binding.viewPager.currentItem = currentItem - 1
            }
        }

        // 設定下一頁按鈕
        binding.btnNext.setOnClickListener {
            val currentItem = binding.viewPager.currentItem
            val totalPages = welcomePagerAdapter.itemCount
            if (currentItem < totalPages - 1) {
                binding.viewPager.currentItem = currentItem + 1
            }
        }
    }

    private fun setupViewPager() {
        // 設定ViewPager適配器
        welcomePagerAdapter = WelcomePagerAdapter(this)
        binding.viewPager.adapter = welcomePagerAdapter

        // 設定TabLayout與ViewPager的連接（用作頁面指示器）
        TabLayoutMediator(binding.tabLayout, binding.viewPager) { _, _ ->
            // 不需要設定文字，只作為指示器
        }.attach()

        // 監聽頁面變化
        binding.viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                updateNavigationButtons(position)
            }
        })

        // 初始化按鈕狀態
        updateNavigationButtons(0)
    }

    private fun updateNavigationButtons(position: Int) {
        val totalPages = welcomePagerAdapter.itemCount
        val isFirstPage = position == 0
        val isLastPage = position == totalPages - 1

        // 更新按鈕可見性
        binding.btnPrevious.visibility = if (isFirstPage) {
            android.view.View.INVISIBLE
        } else {
            android.view.View.VISIBLE
        }

        binding.btnNext.visibility = if (isLastPage) {
            android.view.View.INVISIBLE
        } else {
            android.view.View.VISIBLE
        }

        binding.btnGetStarted.visibility = if (isLastPage) {
            android.view.View.VISIBLE
        } else {
            android.view.View.INVISIBLE
        }
    }

    private fun finishWelcome() {
        // 標記不再是首次啟動
        settingsManager.setFirstLaunch(false)

        // 啟動主頁面
        val intent = Intent(this, MainActivity::class.java)
        startActivity(intent)
        finish()
    }

    private fun setupWindowInsets() {
        ViewCompat.setOnApplyWindowInsetsListener(binding.root) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }
    }

    @Deprecated("Deprecated in Java")
    override fun onBackPressed() {
        // 在歡迎頁面禁用返回鍵，強制用戶完成導覽或跳過
        // 可以選擇顯示退出確認對話框
        super.onBackPressed()
    }
}
