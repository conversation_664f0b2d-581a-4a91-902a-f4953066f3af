package com.example.autolaunch

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.updateLayoutParams
import androidx.drawerlayout.widget.DrawerLayout
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.autolaunch.databinding.ActivityMainBinding
import com.example.autolaunch.databinding.BottomSheetMenuBinding
import com.example.autolaunch.dialog.BatteryOptimizationDialog
import com.example.autolaunch.model.Schedule
import com.example.autolaunch.model.RepeatMode
import com.example.autolaunch.model.ScheduleType
import com.example.autolaunch.service.ScheduleService
import com.example.autolaunch.AlarmManagerService
import com.example.autolaunch.adapter.UnifiedScheduleAdapter
import com.example.autolaunch.adapter.MainPagerAdapter
import com.example.autolaunch.utils.BackgroundExecutionHelper
import com.example.autolaunch.utils.BatteryOptimizationHelper
import com.example.autolaunch.utils.DatabaseTestHelper
import com.example.autolaunch.utils.SystemPermissionHelper
import com.example.autolaunch.utils.SystemLogManager
import com.example.autolaunch.utils.TestUtils
import com.example.autolaunch.utils.EmulatorHelper
import com.example.autolaunch.utils.ScheduleItemTouchHelper
import com.example.autolaunch.base.BaseActivity
import com.example.autolaunch.utils.ThemeManager
import com.example.autolaunch.utils.PersistentNotificationManager
import com.example.autolaunch.utils.SettingsManager
import com.example.autolaunch.utils.UIPerformanceManager
import com.google.android.material.snackbar.Snackbar
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.navigation.NavigationView
import com.google.android.material.tabs.TabLayoutMediator
import kotlinx.coroutines.launch
import android.view.GestureDetector
import android.view.MotionEvent
import android.animation.ObjectAnimator
import android.animation.AnimatorSet
import android.view.animation.AccelerateDecelerateInterpolator

class MainActivity : BaseActivity() {

    companion object {
        private const val TAG = "MainActivity"
        private const val REQUEST_CODE_NOTIFICATION_PERMISSION = 1001
    }

    private lateinit var binding: ActivityMainBinding
    private lateinit var unifiedScheduleAdapter: UnifiedScheduleAdapter
    private lateinit var mainPagerAdapter: MainPagerAdapter
    private lateinit var viewModel: ScheduleViewModel
    private lateinit var notificationManager: PersistentNotificationManager
    private lateinit var settingsManager: SettingsManager
    private lateinit var itemTouchHelper: ItemTouchHelper
    private lateinit var drawerLayout: DrawerLayout
    private lateinit var navigationView: NavigationView
    private lateinit var gestureDetector: GestureDetector
    private var isSortingMode = false

    // 用於管理動畫監聽器，防止記憶體洩漏
    private var welcomeCardAnimator: android.animation.AnimatorSet? = null

    // 防抖動器，防止過度頻繁的 UI 更新
    private val uiUpdateDebouncer = UIPerformanceManager.Debouncer(300L)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 初始化通知管理器
        notificationManager = PersistentNotificationManager.getInstance(this)

        // 初始化設定管理器
        settingsManager = SettingsManager.getInstance(this)

        // 檢查是否為首次啟動，如果是則顯示歡迎導覽
        if (settingsManager.isFirstLaunch()) {
            startActivity(Intent(this, WelcomeActivity::class.java))
            finish()
            return
        }

        // 隱藏ActionBar
        supportActionBar?.hide()

        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // 初始化DrawerLayout和NavigationView
        drawerLayout = binding.drawerLayout
        navigationView = binding.root.findViewById(R.id.navigationView)

        setupViewModel()
        setupWindowInsets()
        setupRecyclerView()
        setupTabs()
        setupUI()
        setupNavigationDrawer()
        observeViewModel()

        // 啟動前台服務確保後台排程正常執行
        startScheduleService()

        // 記錄應用啟動日志
        SystemLogManager.logAppStarted(this, "應用程式正常啟動")

        // 验证数据库迁移
        DatabaseTestHelper.verifyDatabaseMigration(this)

        // 檢查是否在模擬器環境，如果是則跳過可能有問題的權限檢查
        if (EmulatorHelper.isRunningOnEmulator()) {
            // 模擬器環境適配
            EmulatorHelper.adaptForEmulator(this)
            Toast.makeText(this, getString(R.string.emulator_detected), Toast.LENGTH_SHORT).show()
        } else {
            checkBatteryOptimization()
            checkExactAlarmPermission()
            checkSystemPermissions()
            checkBackgroundExecution()
            checkNotificationPermission()
        }

        // 初始化常駐通知
        notificationManager.restorePersistentNotificationIfNeeded()
    }

    override fun onResume() {
        super.onResume()
        // 每次回到應用程式時更新權限提示顯示狀態
        updatePermissionHintVisibility()
    }
    
    private fun setupViewModel() {
        viewModel = ViewModelProvider(this)[ScheduleViewModel::class.java]
    }
    
    private fun setupWindowInsets() {
        ViewCompat.setOnApplyWindowInsetsListener(binding.drawerLayout) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            // 確保 toolbar 不被狀態欄遮擋
            binding.topToolbar.setPadding(
                binding.topToolbar.paddingLeft,
                systemBars.top,
                binding.topToolbar.paddingRight,
                binding.topToolbar.paddingBottom
            )
            insets
        }
    }
    
    private fun setupRecyclerView() {
        // 設定統一排程適配器
        unifiedScheduleAdapter = UnifiedScheduleAdapter(
            onItemClick = { schedule ->
                if (!isSortingMode) {
                    // 處理排程項目點擊事件 - 導航到編輯頁面
                    val intent = Intent(this, AddEditScheduleActivity::class.java)
                    intent.putExtra(AddEditScheduleActivity.EXTRA_SCHEDULE_ID, schedule.id)
                    startActivity(intent)
                }
            },
            onToggleEnabled = { schedule, isEnabled ->
                if (!isSortingMode) {
                    // 處理啟用/禁用開關切換事件
                    viewModel.updateScheduleEnabledStatus(schedule, isEnabled)
                }
            },
            onStartDrag = { viewHolder ->
                // 長按直接啟用排序模式並開始拖拽
                if (!isSortingMode) {
                    isSortingMode = true
                    Toast.makeText(this, getString(R.string.sort_mode_enabled), Toast.LENGTH_SHORT).show()
                }
                // 開始拖拽
                itemTouchHelper.startDrag(viewHolder)
            }
        )

        // 設定統一排程RecyclerView
        binding.recyclerViewSchedules.apply {
            layoutManager = LinearLayoutManager(this@MainActivity)
            adapter = unifiedScheduleAdapter
        }

        // 設定拖拽排序功能
        setupDragToSort()

        // 設定滑動刪除功能
        setupSwipeToDelete()
    }

    private fun setupTabs() {
        // 設定 ViewPager2
        mainPagerAdapter = MainPagerAdapter(this)
        binding.viewPager.adapter = mainPagerAdapter

        // 連接 TabLayout 和 ViewPager2
        TabLayoutMediator(binding.tabLayout, binding.viewPager) { tab, position ->
            tab.text = mainPagerAdapter.getTabTitle(position)
        }.attach()
    }

    private fun setupUI() {
        // 設定新增排程按鈕點擊事件
        binding.fabAddSchedule.setOnClickListener {
            try {
                Log.d("MainActivity", "Add schedule button clicked")
                val intent = Intent(this, AddEditScheduleActivity::class.java)
                startActivity(intent)
                Log.d("MainActivity", "AddEditScheduleActivity started")
            } catch (e: Exception) {
                Log.e("MainActivity", "Error starting AddEditScheduleActivity", e)
                Toast.makeText(this, getString(R.string.error_open_add_schedule, e.message), Toast.LENGTH_LONG).show()
            }
        }
        
        // 設定空狀態按鈕點擊事件
        binding.btnCreateFirstSchedule.setOnClickListener {
            val intent = Intent(this, AddEditScheduleActivity::class.java)
            startActivity(intent)
        }
        
        // 設定歡迎卡片點擊事件 - 進行權限檢查
        binding.welcomeCard.setOnClickListener {
            showPermissionCheckDialog()
        }

        // 設定關閉歡迎卡片按鈕
        binding.btnCloseWelcome.setOnClickListener {
            hideWelcomeCardWithAnimation()
        }

        // 設定歡迎卡片滑動手勢
        setupWelcomeCardSwipeGesture()

        // 初始化權限狀態顯示
        updatePermissionHintVisibility()
        
        // 啟動狀態圖標的脈動動畫
        startStatusIconAnimation()

        // 設定模糊背景根據主題模式
        setupBlurBackground()

        // 設定漢堡選單按鈕點擊事件
        binding.btnMenu.setOnClickListener {
            drawerLayout.openDrawer(navigationView)
        }


    }
    
    private fun observeViewModel() {
        // 觀察排程列表變化
        lifecycleScope.launch {
            viewModel.schedules.collect { schedules ->
                // 使用 UIPerformanceManager 確保 UI 更新在主執行緒執行
                UIPerformanceManager.safeUpdateUI {
                    if (schedules.isEmpty()) {
                        showEmptyState()
                    } else {
                        showTabScheduleList(schedules)
                    }
                }
            }
        }
        
        // 觀察載入狀態
        lifecycleScope.launch {
            viewModel.isLoading.collect { isLoading ->
                // 可以在此處顯示載入指示器
                // binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
            }
        }
        
        // 觀察錯誤狀態
        lifecycleScope.launch {
            viewModel.error.collect { error ->
                error?.let {
                    UIPerformanceManager.safeUpdateUI {
                        Toast.makeText(this@MainActivity, it, Toast.LENGTH_LONG).show()
                        viewModel.clearError()
                    }
                }
            }
        }
        
        // 觀察成功訊息
        lifecycleScope.launch {
            viewModel.successMessage.collect { message ->
                message?.let {
                    Toast.makeText(this@MainActivity, it, Toast.LENGTH_SHORT).show()
                    viewModel.clearSuccessMessage()
                }
            }
        }
    }
    
    private fun showEmptyState() {
        binding.apply {
            emptyStateCard.visibility = View.VISIBLE
            welcomeCard.visibility = View.GONE
            recyclerViewSchedules.visibility = View.GONE
            tabLayout.visibility = View.GONE
            tabDivider.visibility = View.GONE
            viewPager.visibility = View.GONE
        }
    }

    private fun showGroupedScheduleList(schedules: List<Schedule>) {
        binding.apply {
            emptyStateCard.visibility = View.GONE
            welcomeCard.visibility = if (settingsManager.isWelcomeCardVisible()) View.VISIBLE else View.GONE
            recyclerViewSchedules.visibility = View.VISIBLE
        }

        // 按類型分組排程並創建統一列表項目
        val appSchedules = schedules.filter { it.scheduleType == ScheduleType.APP.value }
        val urlSchedules = schedules.filter { it.scheduleType == ScheduleType.URL.value }

        val unifiedItems = mutableListOf<UnifiedScheduleAdapter.ScheduleItem>()

        // 添加應用程式區塊
        if (appSchedules.isNotEmpty()) {
            unifiedItems.add(
                UnifiedScheduleAdapter.ScheduleItem.Header(
                    title = getString(R.string.schedule_category_apps),
                    count = appSchedules.size,
                    icon = R.drawable.ic_apps_24
                )
            )
            appSchedules.forEach { schedule ->
                unifiedItems.add(UnifiedScheduleAdapter.ScheduleItem.ScheduleData(schedule))
            }
        }

        // 添加網頁區塊
        if (urlSchedules.isNotEmpty()) {
            unifiedItems.add(
                UnifiedScheduleAdapter.ScheduleItem.Header(
                    title = getString(R.string.schedule_category_web),
                    count = urlSchedules.size,
                    icon = R.drawable.ic_web_24
                )
            )
            urlSchedules.forEach { schedule ->
                unifiedItems.add(UnifiedScheduleAdapter.ScheduleItem.ScheduleData(schedule))
            }
        }

        // 提交統一列表
        unifiedScheduleAdapter.submitList(unifiedItems)

        // 當顯示排程列表時啟動動畫
        startStatusIconAnimation()
    }

    private fun showTabScheduleList(schedules: List<Schedule>) {
        binding.apply {
            emptyStateCard.visibility = View.GONE
            welcomeCard.visibility = if (settingsManager.isWelcomeCardVisible()) View.VISIBLE else View.GONE
            recyclerViewSchedules.visibility = View.GONE
            tabLayout.visibility = View.VISIBLE
            tabDivider.visibility = View.VISIBLE
            viewPager.visibility = View.VISIBLE
        }

        // 更新 tab 標題以顯示排程數量
        updateTabTitlesWithCount(schedules)

        // 當顯示排程列表時啟動動畫
        startStatusIconAnimation()
    }

    private fun updateTabTitlesWithCount(schedules: List<Schedule>) {
        val appCount = schedules.count { it.scheduleType == ScheduleType.APP.value }
        val webCount = schedules.count { it.scheduleType == ScheduleType.URL.value }

        // 更新 tab 標題
        binding.tabLayout.getTabAt(0)?.text = mainPagerAdapter.getTabTitleWithCount(0, appCount)
        binding.tabLayout.getTabAt(1)?.text = mainPagerAdapter.getTabTitleWithCount(1, webCount)
    }

    /**
     * 啟動狀態圖標的脈動動畫
     */
    private fun startStatusIconAnimation() {
        // 使用防抖動器避免過度頻繁的動畫啟動
        uiUpdateDebouncer.execute {
            try {
                val statusIcon = binding.statusIcon
                if (statusIcon.visibility == View.VISIBLE) {
                    val animation = android.view.animation.AnimationUtils.loadAnimation(this@MainActivity, R.anim.pulse_animation)
                    statusIcon.startAnimation(animation)
                }
            } catch (e: Exception) {
                Log.w("MainActivity", "Failed to start status icon animation", e)
            }
        }
    }

    /**
     * 啟動選單中狀態指示器的脈動動畫
     */
    private fun startMenuStatusIconAnimation(menuBinding: com.example.autolaunch.databinding.BottomSheetMenuBinding) {
        try {
            val statusIndicator = menuBinding.statusIndicator
            val animation = android.view.animation.AnimationUtils.loadAnimation(this, R.anim.pulse_animation)
            statusIndicator.startAnimation(animation)
        } catch (e: Exception) {
            Log.w("MainActivity", "Failed to start menu status icon animation", e)
        }
    }

    private fun startMenuStatusIconAnimation(statusIndicator: android.widget.ImageView, tvRunningStatus: com.google.android.material.textview.MaterialTextView) {
        try {
            val animation = android.view.animation.AnimationUtils.loadAnimation(this, R.anim.pulse_animation)
            statusIndicator.startAnimation(animation)
        } catch (e: Exception) {
            Log.w("MainActivity", "Failed to start menu status icon animation", e)
        }
    }

    private fun setupDragToSort() {
        // 設定拖拽排序和滑動刪除功能
        val dragToSortCallback = ScheduleItemTouchHelper(
            adapter = unifiedScheduleAdapter,
            onMoveFinished = { reorderedSchedules ->
                // 拖拽結束後更新排序
                viewModel.reorderSchedules(reorderedSchedules)

                // 自動退出排序模式
                if (isSortingMode) {
                    isSortingMode = false
                    Toast.makeText(this, getString(R.string.sort_mode_disabled), Toast.LENGTH_SHORT).show()
                }
            },
            onSwipeToDelete = { position ->
                val item = unifiedScheduleAdapter.currentList[position]
                // 只有排程項目可以刪除，標題項目不能刪除
                if (item is UnifiedScheduleAdapter.ScheduleItem.ScheduleData) {
                    showDeleteConfirmationDialog(item.schedule, position)
                }
            },
            isSwipeEnabled = { position ->
                // 檢查項目是否可以滑動刪除（只有排程項目可以刪除，且不在排序模式下）
                val item = unifiedScheduleAdapter.currentList.getOrNull(position)
                item is UnifiedScheduleAdapter.ScheduleItem.ScheduleData && !isSortingMode
            }
        )
        itemTouchHelper = ItemTouchHelper(dragToSortCallback)
        itemTouchHelper.attachToRecyclerView(binding.recyclerViewSchedules)
    }

    private fun setupSwipeToDelete() {
        // 注意：滑動刪除功能已經整合到 setupDragToSort 中的 ScheduleItemTouchHelper
        // 這裡保留方法以避免破壞現有調用，但實際功能在 ScheduleItemTouchHelper 中實現
    }


    
    private fun showDeleteConfirmationDialog(schedule: Schedule, position: Int) {
        val displayName = schedule.getDisplayName()
        AlertDialog.Builder(this)
            .setTitle(getString(R.string.delete_schedule_title))
            .setMessage(getString(R.string.delete_schedule_message, displayName))
            .setPositiveButton(getString(R.string.delete)) { _, _ ->
                deleteScheduleWithUndo(schedule, position)
            }
            .setNegativeButton(getString(R.string.cancel)) { _, _ ->
                // 取消時恢復項目位置
                unifiedScheduleAdapter.notifyItemChanged(position)
            }
            .setOnCancelListener {
                // 取消時恢復項目位置
                unifiedScheduleAdapter.notifyItemChanged(position)
            }
            .show()
    }
    
    private fun deleteScheduleWithUndo(schedule: Schedule, position: Int) {
        // 執行刪除
        viewModel.deleteSchedule(schedule)

        val displayName = schedule.getDisplayName()
        // 顯示撤銷 Snackbar
        Snackbar.make(binding.root, "已刪除排程「${displayName}」", Snackbar.LENGTH_LONG)
            .setAction("撤銷") {
                // 重新插入排程
                viewModel.insertSchedule(schedule)
            }
            .show()
    }
    
    private fun checkBatteryOptimization() {
        // 延遲檢查，避免在啟動時立即彈出對話框
        lifecycleScope.launch {
            kotlinx.coroutines.delay(2000) // 延遲 2 秒
            
            if (BatteryOptimizationHelper.shouldShowBatteryOptimizationPrompt(this@MainActivity)) {
                showBatteryOptimizationDialog()
            }
        }
    }
    
    private fun showBatteryOptimizationDialog() {
        if (isFinishing || isDestroyed) return

        val dialog = BatteryOptimizationDialog.newInstance()
        dialog.show(supportFragmentManager, BatteryOptimizationDialog.TAG)
    }

    private fun checkExactAlarmPermission() {
        // 延遲檢查，避免在啟動時立即彈出對話框
        lifecycleScope.launch {
            kotlinx.coroutines.delay(3000) // 延遲 3 秒，在電池優化檢查之後

            if (AlarmManagerService.shouldRequestExactAlarmPermission(this@MainActivity)) {
                showExactAlarmPermissionDialog()
            }
        }
    }

    private fun showExactAlarmPermissionDialog() {
        if (isFinishing || isDestroyed) return

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle(getString(R.string.exact_alarm_permission_title))
            .setMessage(getString(R.string.exact_alarm_permission_message))
            .setPositiveButton(getString(R.string.permission_go_to_settings)) { _, _ ->
                try {
                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
                        val intent = android.content.Intent(android.provider.Settings.ACTION_REQUEST_SCHEDULE_EXACT_ALARM)
                        startActivity(intent)
                    }
                } catch (e: Exception) {
                    Toast.makeText(this, getString(R.string.battery_optimization_cannot_open), Toast.LENGTH_SHORT).show()
                }
            }
            .setNegativeButton(getString(R.string.battery_optimization_later)) { dialog, _ ->
                dialog.dismiss()
            }
            .setCancelable(false)
            .show()
    }
    
    private fun showDebugMenu() {
        val options = arrayOf(
            "創建基本測試數據",
            "創建邊緣案例測試",
            "創建壓力測試數據",
            "清除所有數據",
            "檢查系統健康狀態",
            "驗證排程時間計算",
            "重置電池優化提示"
        )
        
        AlertDialog.Builder(this)
            .setTitle(getString(R.string.debug_menu_title))
            .setItems(options) { _, which ->
                when (which) {
                    0 -> {
                        TestUtils.createTestSchedules(this, TestUtils.TestType.BASIC)
                        Toast.makeText(this, getString(R.string.debug_basic_data_created), Toast.LENGTH_SHORT).show()
                    }
                    1 -> {
                        TestUtils.createTestSchedules(this, TestUtils.TestType.EDGE_CASES)
                        Toast.makeText(this, getString(R.string.debug_edge_cases_created), Toast.LENGTH_SHORT).show()
                    }
                    2 -> {
                        TestUtils.createTestSchedules(this, TestUtils.TestType.STRESS)
                        Toast.makeText(this, getString(R.string.debug_stress_data_created), Toast.LENGTH_SHORT).show()
                    }
                    3 -> {
                        TestUtils.clearAllTestData(this)
                        Toast.makeText(this, getString(R.string.debug_all_data_cleared), Toast.LENGTH_SHORT).show()
                    }
                    4 -> {
                        TestUtils.checkSystemHealth(this)
                        Toast.makeText(this, getString(R.string.debug_health_check_complete), Toast.LENGTH_SHORT).show()
                    }
                    5 -> {
                        TestUtils.validateScheduleTimings(this)
                        Toast.makeText(this, "排程時間驗證完成，請查看 Logcat", Toast.LENGTH_SHORT).show()
                    }
                    6 -> {
                        BatteryOptimizationHelper.resetPromptState(this)
                        Toast.makeText(this, "已重置電池優化提示狀態", Toast.LENGTH_SHORT).show()
                    }
                }
            }
            .setNegativeButton(getString(R.string.cancel), null)
            .show()
    }
    
    /**
     * 設定導航抽屜
     */
    private fun setupNavigationDrawer() {
        // 設定App版本資訊
        val tvAppVersion = navigationView.findViewById<com.google.android.material.textview.MaterialTextView>(R.id.tvAppVersion)
        try {
            val pInfo = packageManager.getPackageInfo(packageName, 0)
            tvAppVersion.text = getString(R.string.app_version_format, pInfo.versionName)
        } catch (e: Exception) {
            tvAppVersion.text = getString(R.string.app_version_default)
        }

        // 設定版本資訊長按事件（調試功能）
        val isDebugMode = (applicationInfo.flags and android.content.pm.ApplicationInfo.FLAG_DEBUGGABLE) != 0
        if (isDebugMode) {
            tvAppVersion.setOnLongClickListener {
                drawerLayout.closeDrawer(navigationView)
                showDebugMenu()
                true
            }
        }

        // 啟動狀態指示器的脈動動畫
        val statusIndicator = navigationView.findViewById<android.widget.ImageView>(R.id.statusIndicator)
        val tvRunningStatus = navigationView.findViewById<com.google.android.material.textview.MaterialTextView>(R.id.tvRunningStatus)
        startMenuStatusIconAnimation(statusIndicator, tvRunningStatus)

        // 設定選單項目點擊事件
        navigationView.findViewById<View>(R.id.menuTutorial).setOnClickListener {
            drawerLayout.closeDrawer(navigationView)
            openTutorial()
        }

        navigationView.findViewById<View>(R.id.menuInviteFriends).setOnClickListener {
            drawerLayout.closeDrawer(navigationView)
            inviteFriends()
        }

        navigationView.findViewById<View>(R.id.menuThemeSettings).setOnClickListener {
            drawerLayout.closeDrawer(navigationView)
            showThemeSettings()
        }





        navigationView.findViewById<View>(R.id.menuAbout).setOnClickListener {
            drawerLayout.closeDrawer(navigationView)
            showAbout()
        }

        navigationView.findViewById<View>(R.id.menuBackupRestore).setOnClickListener {
            drawerLayout.closeDrawer(navigationView)
            showBackupRestore()
        }

        navigationView.findViewById<View>(R.id.menuSystemLog).setOnClickListener {
            drawerLayout.closeDrawer(navigationView)
            showSystemLog()
        }

        navigationView.findViewById<View>(R.id.menuLanguageSettings).setOnClickListener {
            drawerLayout.closeDrawer(navigationView)
            showLanguageSettings()
        }

        navigationView.findViewById<View>(R.id.menuSettings).setOnClickListener {
            drawerLayout.closeDrawer(navigationView)
            showSettings()
        }

        // 設定抽屜打開和關閉時的背景模糊效果
        drawerLayout.addDrawerListener(object : DrawerLayout.DrawerListener {
            override fun onDrawerSlide(drawerView: View, slideOffset: Float) {}

            override fun onDrawerOpened(drawerView: View) {
                showBlurOverlay()
            }

            override fun onDrawerClosed(drawerView: View) {
                hideBlurOverlay()
            }

            override fun onDrawerStateChanged(newState: Int) {}
        })
    }

    /**
     * 設定模糊背景根據主題模式
     */
    private fun setupBlurBackground() {
        val nightModeFlags = resources.configuration.uiMode and android.content.res.Configuration.UI_MODE_NIGHT_MASK
        val isNightMode = nightModeFlags == android.content.res.Configuration.UI_MODE_NIGHT_YES

        val backgroundResource = if (isNightMode) {
            R.drawable.blur_overlay_background
        } else {
            R.drawable.blur_overlay_background_light
        }

        binding.blurOverlay.setBackgroundResource(backgroundResource)
    }

    /**
     * 顯示背景模糊覆蓋層
     */
    private fun showBlurOverlay() {
        binding.blurOverlay.apply {
            visibility = View.VISIBLE
            alpha = 0f
            scaleX = 1.05f
            scaleY = 1.05f
            animate()
                .alpha(1f)
                .scaleX(1f)
                .scaleY(1f)
                .setDuration(300)
                .setInterpolator(android.view.animation.DecelerateInterpolator())
                .start()
        }
    }

    /**
     * 隱藏背景模糊覆蓋層
     */
    private fun hideBlurOverlay() {
        binding.blurOverlay.animate()
            .alpha(0f)
            .scaleX(1.05f)
            .scaleY(1.05f)
            .setDuration(250)
            .setInterpolator(android.view.animation.AccelerateInterpolator())
            .withEndAction {
                binding.blurOverlay.visibility = View.GONE
            }
            .start()
    }

    /**
     * 開啟教學
     */
    private fun openTutorial() {
        val intent = Intent(this, TutorialActivity::class.java)
        startActivity(intent)
    }
    
    /**
     * 邀請朋友
     */
    private fun inviteFriends() {
        try {
            val shareIntent = Intent(Intent.ACTION_SEND).apply {
                type = "text/plain"
                putExtra(Intent.EXTRA_SUBJECT, getString(R.string.invite_friends_subject))
                putExtra(Intent.EXTRA_TEXT, getString(R.string.invite_friends_content))
            }
            startActivity(Intent.createChooser(shareIntent, getString(R.string.invite_friends_chooser_title)))
        } catch (e: Exception) {
            Toast.makeText(this, getString(R.string.share_function_unavailable), Toast.LENGTH_SHORT).show()
        }
    }
    



    
    /**
     * 顯示關於此APP
     */
    private fun showAbout() {
        val intent = Intent(this, AboutActivity::class.java)
        startActivity(intent)
    }

    /**
     * 顯示備份與匯入
     */
    private fun showBackupRestore() {
        val intent = Intent(this, BackupRestoreActivity::class.java)
        startActivity(intent)
    }

    /**
     * 顯示主題設定
     */
    private fun showThemeSettings() {
        val intent = Intent(this, ThemeSettingsActivity::class.java)
        startActivity(intent)
    }

    /**
     * 顯示系統日志
     */
    private fun showSystemLog() {
        val intent = Intent(this, SystemLogActivity::class.java)
        startActivity(intent)
    }

    /**
     * 顯示語言設定
     */
    private fun showLanguageSettings() {
        val intent = Intent(this, LanguageSettingsActivity::class.java)
        startActivity(intent)
    }

    /**
     * 顯示設定頁面
     */
    private fun showSettings() {
        val intent = Intent(this, SettingsActivity::class.java)
        startActivity(intent)
    }



    private fun startScheduleService() {
        try {
            // 延遲啟動服務，確保應用程式完全初始化
            lifecycleScope.launch {
                kotlinx.coroutines.delay(2000) // 延遲 2 秒確保完全初始化
                try {
                    ScheduleService.startService(this@MainActivity)
                    Log.i("MainActivity", "Schedule service started successfully")
                } catch (e: Exception) {
                    Log.e("MainActivity", "Failed to start schedule service", e)
                    // 顯示錯誤但不崩潰應用程式
                    Toast.makeText(this@MainActivity, "後台服務啟動失敗，排程可能無法在背景執行", Toast.LENGTH_LONG).show()
                }
            }
        } catch (e: Exception) {
            Log.e("MainActivity", "Failed to schedule service start", e)
        }
    }

    private fun checkBackgroundExecution() {
        // 延遲檢查，避免在啟動時立即彈出對話框
        lifecycleScope.launch {
            kotlinx.coroutines.delay(5000) // 延遲 5 秒，在其他檢查之後

            if (BackgroundExecutionHelper.shouldShowBackgroundExecutionPrompt(this@MainActivity)) {
                BackgroundExecutionHelper.showBackgroundExecutionDialog(this@MainActivity)
            }
        }
    }

    private fun testScheduleService() {
        // 延遲測試前台服務，確保應用程式完全啟動
        lifecycleScope.launch {
            try {
                kotlinx.coroutines.delay(3000) // 延遲 3 秒
                Log.i("MainActivity", "Testing schedule service start...")

                // 檢查是否在模擬器環境
                if (EmulatorHelper.isRunningOnEmulator()) {
                    Log.i("MainActivity", "Emulator detected, skipping service start")
                    return@launch
                }

                ScheduleService.startService(this@MainActivity)
                Log.i("MainActivity", "Schedule service test completed successfully")

                // 顯示成功消息
                Toast.makeText(this@MainActivity, "後台服務已啟動", Toast.LENGTH_SHORT).show()

            } catch (e: Exception) {
                Log.e("MainActivity", "Schedule service test failed", e)
                Toast.makeText(this@MainActivity, "後台服務啟動失敗: ${e.message}", Toast.LENGTH_LONG).show()
            }
        }
    }

    private fun checkSystemPermissions() {
        // 延遲檢查系統權限
        lifecycleScope.launch {
            kotlinx.coroutines.delay(4000) // 延遲 4 秒

            // 檢查系統警報窗口權限
            if (!SystemPermissionHelper.hasSystemAlertWindowPermission(this@MainActivity)) {
                SystemPermissionHelper.requestSystemAlertWindowPermission(this@MainActivity)
            }
        }
    }

    private fun checkNotificationPermission() {
        // 延遲檢查通知權限
        lifecycleScope.launch {
            kotlinx.coroutines.delay(5000) // 延遲 5 秒，在其他權限檢查之後

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                if (ContextCompat.checkSelfPermission(
                        this@MainActivity,
                        android.Manifest.permission.POST_NOTIFICATIONS
                    ) != PackageManager.PERMISSION_GRANTED
                ) {
                    // 請求通知權限
                    ActivityCompat.requestPermissions(
                        this@MainActivity,
                        arrayOf(android.Manifest.permission.POST_NOTIFICATIONS),
                        REQUEST_CODE_NOTIFICATION_PERMISSION
                    )
                }
            }
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        when (requestCode) {
            REQUEST_CODE_NOTIFICATION_PERMISSION -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    Log.d(TAG, "Notification permission granted")
                    // 權限獲得後，嘗試顯示常駐通知
                    notificationManager.updatePersistentNotificationState()
                    Toast.makeText(this, getString(R.string.permission_granted), Toast.LENGTH_SHORT).show()
                } else {
                    Log.w(TAG, "Notification permission denied")
                    Toast.makeText(this, getString(R.string.permission_denied), Toast.LENGTH_LONG).show()
                }
            }
        }
    }

    /**
     * 顯示權限檢查對話框
     * 當用戶點擊歡迎卡片時觸發
     */
    private fun showPermissionCheckDialog() {
        val permissionStatus = getPermissionStatus()

        val dialog = androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle(getString(R.string.permission_check_details))
            .setMessage(permissionStatus.message)
            .setPositiveButton(permissionStatus.primaryButtonText) { _, _ ->
                when (permissionStatus.primaryAction) {
                    PermissionAction.OPEN_SETTINGS -> openPermissionSettings()
                    PermissionAction.SHOW_DETAILS -> showDetailedPermissionStatus()
                    PermissionAction.DISMISS -> { /* 關閉對話框 */ }
                }
            }
            .setNegativeButton(getString(R.string.button_cancel)) { dialog, _ ->
                dialog.dismiss()
            }
            .setCancelable(true)
            .create()

        dialog.show()
    }

    /**
     * 獲取當前權限狀態
     */
    private fun getPermissionStatus(): PermissionStatus {
        val hasSystemAlert = SystemPermissionHelper.hasSystemAlertWindowPermission(this)
        val hasBatteryOptimization = BatteryOptimizationHelper.isIgnoringBatteryOptimizations(this)
        val hasExactAlarm = AlarmManagerService.canScheduleExactAlarms(this)
        val isServiceRunning = BackgroundExecutionHelper.isScheduleServiceRunning(this)

        val missingPermissions = mutableListOf<String>()

        if (!hasSystemAlert) {
            missingPermissions.add("• " + getString(R.string.permission_system_alert_window))
        }
        if (!hasBatteryOptimization) {
            missingPermissions.add("• " + getString(R.string.permission_battery_optimization))
        }
        if (!hasExactAlarm) {
            missingPermissions.add("• " + getString(R.string.permission_exact_alarm))
        }

        return when {
            missingPermissions.isEmpty() && isServiceRunning -> {
                PermissionStatus(
                    message = getString(R.string.permission_status_all_good),
                    primaryButtonText = getString(R.string.permission_check_details),
                    primaryAction = PermissionAction.SHOW_DETAILS
                )
            }
            missingPermissions.isEmpty() && !isServiceRunning -> {
                PermissionStatus(
                    message = getString(R.string.permission_status_service_not_running),
                    primaryButtonText = getString(R.string.permission_check_details),
                    primaryAction = PermissionAction.SHOW_DETAILS
                )
            }
            else -> {
                PermissionStatus(
                    message = getString(R.string.permission_status_missing_permissions, missingPermissions.joinToString("\n")),
                    primaryButtonText = getString(R.string.permission_go_to_settings),
                    primaryAction = PermissionAction.OPEN_SETTINGS
                )
            }
        }
    }

    /**
     * 開啟權限設定頁面
     */
    private fun openPermissionSettings() {
        // 優先處理最重要的權限
        when {
            !BatteryOptimizationHelper.isIgnoringBatteryOptimizations(this) -> {
                val intent = BatteryOptimizationHelper.createBatteryOptimizationIntent(this)
                if (intent != null) {
                    try {
                        startActivity(intent)
                    } catch (e: Exception) {
                        Toast.makeText(this, getString(R.string.battery_optimization_cannot_open), Toast.LENGTH_SHORT).show()
                    }
                } else {
                    Toast.makeText(this, getString(R.string.battery_optimization_not_detected), Toast.LENGTH_SHORT).show()
                }
            }
            !AlarmManagerService.canScheduleExactAlarms(this) -> {
                showExactAlarmPermissionDialog()
            }
            !SystemPermissionHelper.hasSystemAlertWindowPermission(this) -> {
                SystemPermissionHelper.requestSystemAlertWindowPermission(this)
            }
            else -> {
                Toast.makeText(this, getString(R.string.permission_status_all_good), Toast.LENGTH_SHORT).show()
            }
        }
    }

    /**
     * 顯示詳細權限狀態
     */
    private fun showDetailedPermissionStatus() {
        SystemPermissionHelper.showPermissionSummaryDialog(this)
    }

    /**
     * 權限狀態數據類
     */
    private data class PermissionStatus(
        val message: String,
        val primaryButtonText: String,
        val primaryAction: PermissionAction
    )

    /**
     * 權限操作枚舉
     */
    private enum class PermissionAction {
        OPEN_SETTINGS,
        SHOW_DETAILS,
        DISMISS
    }

    /**
     * 更新權限提示的顯示狀態
     * 根據權限狀態動態更新歡迎訊息和提示文字
     */
    private fun updatePermissionHintVisibility() {
        // 延遲檢查，確保服務有時間啟動
        lifecycleScope.launch {
            kotlinx.coroutines.delay(1000) // 延遲 1 秒

            val hasSystemAlert = SystemPermissionHelper.hasSystemAlertWindowPermission(this@MainActivity)
            val hasBatteryOptimization = BatteryOptimizationHelper.isIgnoringBatteryOptimizations(this@MainActivity)
            val hasExactAlarm = AlarmManagerService.canScheduleExactAlarms(this@MainActivity)
            val isServiceRunning = BackgroundExecutionHelper.isScheduleServiceRunning(this@MainActivity)

            // 如果所有權限都已設定好且服務正在運行，顯示成功訊息
            val allPermissionsGranted = hasSystemAlert && hasBatteryOptimization && hasExactAlarm && isServiceRunning

            if (allPermissionsGranted) {
                // 所有權限都已設定好，顯示成功訊息和綠燈圖示
                binding.tvWelcomeMessage.text = getString(R.string.welcome_message_ready)
                binding.tvPermissionHint.visibility = View.GONE
                binding.ivPermissionArrow.visibility = View.GONE
                binding.statusIcon.setImageResource(R.drawable.ic_status_running) // 綠燈圖示
                binding.statusIcon.contentDescription = getString(R.string.status_icon_ready)
            } else {
                // 權限未完全設定，提醒用戶設定授權並顯示驚嘆號圖示
                binding.tvWelcomeMessage.text = getString(R.string.welcome_message_permission_needed)
                binding.tvPermissionHint.visibility = View.GONE // 隱藏第二行提示文字
                binding.ivPermissionArrow.visibility = View.VISIBLE
                binding.statusIcon.setImageResource(R.drawable.ic_status_warning) // 驚嘆號圖示
                binding.statusIcon.contentDescription = getString(R.string.status_icon_warning)
            }

            Log.d("MainActivity", "Permission status - SystemAlert: $hasSystemAlert, Battery: $hasBatteryOptimization, ExactAlarm: $hasExactAlarm, Service: $isServiceRunning")
            Log.d("MainActivity", "All permissions granted: $allPermissionsGranted")
        }
    }

    /**
     * 設定歡迎卡片滑動手勢
     */
    private fun setupWelcomeCardSwipeGesture() {
        gestureDetector = GestureDetector(this, object : GestureDetector.SimpleOnGestureListener() {
            override fun onFling(
                e1: MotionEvent?,
                e2: MotionEvent,
                velocityX: Float,
                velocityY: Float
            ): Boolean {
                if (e1 == null) return false

                val diffX = e2.x - e1.x
                val diffY = e2.y - e1.y

                // 檢查是否為水平滑動且速度足夠
                if (kotlin.math.abs(diffX) > kotlin.math.abs(diffY) &&
                    kotlin.math.abs(diffX) > 100 &&
                    kotlin.math.abs(velocityX) > 100) {

                    // 向左或向右滑動都可以關閉
                    hideWelcomeCardWithAnimation()
                    return true
                }
                return false
            }
        })

        binding.welcomeCard.setOnTouchListener { _, event ->
            gestureDetector.onTouchEvent(event)
            false // 讓點擊事件繼續傳遞
        }
    }

    /**
     * 隱藏歡迎卡片並播放動畫
     */
    private fun hideWelcomeCardWithAnimation() {
        // 取消之前的動畫以防止記憶體洩漏
        welcomeCardAnimator?.cancel()

        // 創建滑出動畫
        val slideOut = ObjectAnimator.ofFloat(binding.welcomeCard, "translationX", 0f, binding.welcomeCard.width.toFloat())
        val fadeOut = ObjectAnimator.ofFloat(binding.welcomeCard, "alpha", 1f, 0f)

        welcomeCardAnimator = AnimatorSet().apply {
            playTogether(slideOut, fadeOut)
            duration = 300
            interpolator = AccelerateDecelerateInterpolator()

            addListener(object : android.animation.Animator.AnimatorListener {
                override fun onAnimationStart(animation: android.animation.Animator) {}
                override fun onAnimationRepeat(animation: android.animation.Animator) {}
                override fun onAnimationCancel(animation: android.animation.Animator) {
                    // 動畫被取消時清理引用
                    welcomeCardAnimator = null
                }
                override fun onAnimationEnd(animation: android.animation.Animator) {
                    // 動畫結束後隱藏卡片並保存設定
                    if (!isDestroyed && !isFinishing) {
                        binding.welcomeCard.visibility = View.GONE
                        settingsManager.setWelcomeCardVisible(false)

                        // 重置動畫屬性以備下次顯示
                        binding.welcomeCard.translationX = 0f
                        binding.welcomeCard.alpha = 1f

                        // 顯示提示訊息
                        Snackbar.make(binding.root, "歡迎提示已隱藏，可在設定中重新開啟", Snackbar.LENGTH_LONG)
                            .setAction("復原") {
                                // 復原操作
                                settingsManager.setWelcomeCardVisible(true)
                                binding.welcomeCard.visibility = View.VISIBLE
                            }
                            .show()
                    }
                    // 清理動畫引用
                    welcomeCardAnimator = null
                }
            })
            start()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        // 清理動畫監聽器以防止記憶體洩漏
        welcomeCardAnimator?.cancel()
        welcomeCardAnimator = null

        // 清理防抖動器
        uiUpdateDebouncer.cancel()
    }


}