package com.example.autolaunch

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.Drawable
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView

/**
 * 滑動刪除功能的 ItemTouchHelper.Callback
 * 支持檢查項目是否可以滑動刪除
 */
class SwipeToDeleteCallback(
    private val context: Context,
    private val onSwipeToDelete: (Int) -> Unit,
    private val isSwipeEnabled: ((Int) -> Boolean)? = null
) : ItemTouchHelper.SimpleCallback(0, ItemTouchHelper.LEFT or ItemTouchHelper.RIGHT) {

    private val deleteIcon: Drawable? = ContextCompat.getDrawable(context, R.drawable.ic_delete_24)
    private val intrinsicWidth = deleteIcon?.intrinsicWidth ?: 0
    private val intrinsicHeight = deleteIcon?.intrinsicHeight ?: 0
    private val background = ColorDrawable()
    private val backgroundColor = Color.parseColor("#f44336")
    private val clearPaint = Paint().apply { color = Color.TRANSPARENT }

    // 最小滑動距離（像素），避免意外觸發
    private val minSwipeDistance = 200f
    
    override fun onMove(
        recyclerView: RecyclerView,
        viewHolder: RecyclerView.ViewHolder,
        target: RecyclerView.ViewHolder
    ): Boolean {
        return false
    }
    
    override fun getSwipeThreshold(viewHolder: RecyclerView.ViewHolder): Float {
        // 檢查是否允許滑動
        val position = viewHolder.bindingAdapterPosition
        return if (isSwipeEnabled?.invoke(position) == false) {
            1.0f // 設置為 1.0 表示需要完全滑動才能觸發，實際上禁用滑動
        } else {
            0.7f // 提高滑動閾值，需要滑動 70% 才能觸發刪除，避免意外觸發
        }
    }

    override fun getSwipeDirs(recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder): Int {
        val position = viewHolder.bindingAdapterPosition
        return if (isSwipeEnabled?.invoke(position) == false) {
            0 // 返回 0 表示不允許任何方向的滑動
        } else {
            super.getSwipeDirs(recyclerView, viewHolder)
        }
    }

    override fun onSwiped(viewHolder: RecyclerView.ViewHolder, direction: Int) {
        val position = viewHolder.bindingAdapterPosition
        onSwipeToDelete(position)
    }

    override fun getSwipeEscapeVelocity(defaultValue: Float): Float {
        // 提高逃脫速度，需要更快的滑動才能觸發刪除
        return defaultValue * 2.0f
    }

    override fun getSwipeVelocityThreshold(defaultValue: Float): Float {
        // 提高速度閾值，避免慢速滑動意外觸發
        return defaultValue * 1.5f
    }
    
    override fun onChildDraw(
        c: Canvas,
        recyclerView: RecyclerView,
        viewHolder: RecyclerView.ViewHolder,
        dX: Float,
        dY: Float,
        actionState: Int,
        isCurrentlyActive: Boolean
    ) {
        val itemView = viewHolder.itemView
        val itemHeight = itemView.bottom - itemView.top
        val isCanceled = dX == 0f && !isCurrentlyActive
        
        if (isCanceled) {
            clearCanvas(c, itemView.right + dX, itemView.top.toFloat(), itemView.right.toFloat(), itemView.bottom.toFloat())
            super.onChildDraw(c, recyclerView, viewHolder, dX, dY, actionState, isCurrentlyActive)
            return
        }
        
        // 繪製背景
        background.color = backgroundColor
        if (dX > 0) {
            // 向右滑動
            background.setBounds(itemView.left, itemView.top, itemView.left + dX.toInt(), itemView.bottom)
        } else {
            // 向左滑動
            background.setBounds(itemView.right + dX.toInt(), itemView.top, itemView.right, itemView.bottom)
        }
        background.draw(c)
        
        // 繪製刪除圖標
        deleteIcon?.let { icon ->
            val deleteIconTop = itemView.top + (itemHeight - intrinsicHeight) / 2
            val deleteIconMargin = (itemHeight - intrinsicHeight) / 2
            val deleteIconBottom = deleteIconTop + intrinsicHeight
            
            if (dX > 0) {
                // 向右滑動，圖標在左側
                val deleteIconLeft = itemView.left + deleteIconMargin
                val deleteIconRight = itemView.left + deleteIconMargin + intrinsicWidth
                icon.setBounds(deleteIconLeft, deleteIconTop, deleteIconRight, deleteIconBottom)
            } else {
                // 向左滑動，圖標在右側
                val deleteIconLeft = itemView.right - deleteIconMargin - intrinsicWidth
                val deleteIconRight = itemView.right - deleteIconMargin
                icon.setBounds(deleteIconLeft, deleteIconTop, deleteIconRight, deleteIconBottom)
            }
            
            icon.draw(c)
        }
        
        super.onChildDraw(c, recyclerView, viewHolder, dX, dY, actionState, isCurrentlyActive)
    }
    
    private fun clearCanvas(c: Canvas?, left: Float, top: Float, right: Float, bottom: Float) {
        c?.drawRect(left, top, right, bottom, clearPaint)
    }
} 