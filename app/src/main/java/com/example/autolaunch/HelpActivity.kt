package com.example.autolaunch

import android.content.Intent
import android.os.Bundle
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import com.example.autolaunch.base.BaseActivity
import com.example.autolaunch.databinding.ActivityHelpBinding

/**
 * 幫助頁面
 * 整合教學和Q&A功能
 */
class HelpActivity : BaseActivity() {

    companion object {
        private const val TAG = "HelpActivity"
    }

    private lateinit var binding: ActivityHelpBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        binding = ActivityHelpBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupUI()
        setupMenuItems()
        setupWindowInsets()
    }
    
    private fun setupUI() {
        // 設定工具列
        binding.toolbar.setNavigationIcon(androidx.appcompat.R.drawable.abc_ic_ab_back_material)
        binding.toolbar.setNavigationOnClickListener {
            finish()
        }
    }
    
    private fun setupMenuItems() {
        // 教學
        binding.menuTutorial.setOnClickListener {
            startActivity(Intent(this, TutorialActivity::class.java))
        }
        
        // Q&A(常見問題)
        binding.menuQA.setOnClickListener {
            startActivity(Intent(this, QAActivity::class.java))
        }
    }
    
    private fun setupWindowInsets() {
        ViewCompat.setOnApplyWindowInsetsListener(binding.root) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }
    }
}
