package com.example.autolaunch

import android.app.AlarmManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import com.example.autolaunch.model.RepeatMode
import com.example.autolaunch.model.Schedule
import com.example.autolaunch.model.ScheduleType
import com.example.autolaunch.receiver.LaunchReceiver
import com.example.autolaunch.utils.ExceptionHandler
import java.util.*

/**
 * AlarmManager 核心調度服務
 * 負責管理應用程式排程的鬧鐘設定、取消和更新
 */
class AlarmManagerService(private val context: Context) {
    
    private val alarmManager: AlarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
    
    companion object {
        private const val TAG = "AlarmManagerService"

        /**
         * 檢查是否可以設定精確鬧鐘
         * @param context 上下文
         * @return true 如果可以設定精確鬧鐘
         */
        fun canScheduleExactAlarms(context: Context): Boolean {
            return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
                alarmManager.canScheduleExactAlarms()
            } else {
                // Android 12 以下版本不需要特殊權限
                true
            }
        }

        /**
         * 檢查是否需要請求精確鬧鐘權限
         * @param context 上下文
         * @return true 如果需要請求權限
         */
        fun shouldRequestExactAlarmPermission(context: Context): Boolean {
            return Build.VERSION.SDK_INT >= Build.VERSION_CODES.S && !canScheduleExactAlarms(context)
        }
    }
    
    /**
     * 設定排程鬧鐘
     * @param schedule 排程物件
     */
    fun setAlarm(schedule: Schedule) {
        ExceptionHandler.safeExecute(context) {
            Log.d(TAG, "Setting alarm for schedule ID: ${schedule.id}")

            if (!schedule.isEnabled) {
                Log.w(TAG, "Schedule ${schedule.id} is disabled, skipping alarm setup")
                return@safeExecute
            }

            val nextExecuteTime = calculateNextExecutionTime(schedule)
            if (nextExecuteTime == null) {
                Log.e(TAG, "Cannot calculate next execute time for schedule ${schedule.id}")
                return@safeExecute
            }

            val pendingIntent = createPendingIntent(schedule)

            // 檢查 Android 12+ 的精確鬧鐘權限
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                if (!alarmManager.canScheduleExactAlarms()) {
                    Log.w(TAG, "SCHEDULE_EXACT_ALARM permission not granted, using inexact alarm")
                    // 使用非精確鬧鐘
                    alarmManager.setAndAllowWhileIdle(
                        AlarmManager.RTC_WAKEUP,
                        nextExecuteTime,
                        pendingIntent
                    )
                    Log.i(TAG, "Inexact alarm set for schedule ${schedule.id} at ${Date(nextExecuteTime)}")
                    return@safeExecute
                }
            }

            // 使用最適合的鬧鐘設定方法
            when {
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.M -> {
                    // Android 6.0+ 使用 setExactAndAllowWhileIdle 確保在休眠模式下也能觸發
                    alarmManager.setExactAndAllowWhileIdle(
                        AlarmManager.RTC_WAKEUP,
                        nextExecuteTime,
                        pendingIntent
                    )
                }
                else -> {
                    // Android 6.0 以下使用 setExact
                    alarmManager.setExact(
                        AlarmManager.RTC_WAKEUP,
                        nextExecuteTime,
                        pendingIntent
                    )
                }
            }

            Log.i(TAG, "Exact alarm set successfully for schedule ${schedule.id} at ${Date(nextExecuteTime)}")
        }
    }
    
    /**
     * 取消排程鬧鐘
     * @param scheduleId 排程 ID
     */
    fun cancelAlarm(scheduleId: Long) {
        ExceptionHandler.safeExecute(context) {
            Log.d(TAG, "Cancelling alarm for schedule ID: $scheduleId")

            val pendingIntent = createPendingIntent(scheduleId, null, null)
            alarmManager.cancel(pendingIntent)
            pendingIntent.cancel()

            Log.i(TAG, "Alarm cancelled for schedule $scheduleId")
        }
    }

    /**
     * 取消排程鬧鐘（使用 Schedule 物件）
     * @param schedule 排程物件
     */
    fun cancelAlarm(schedule: Schedule) {
        cancelAlarm(schedule.id)
    }
    
    /**
     * 更新排程鬧鐘
     * @param schedule 更新後的排程物件
     */
    fun updateAlarm(schedule: Schedule) {
        Log.d(TAG, "Updating alarm for schedule ID: ${schedule.id}")
        
        // 先取消舊鬧鐘
        cancelAlarm(schedule.id)
        
        // 設定新鬧鐘
        setAlarm(schedule)
    }
    
    /**
     * 計算下次執行時間
     * @param schedule 排程物件
     * @return Unix timestamp 或 null
     */
    fun calculateNextExecutionTime(schedule: Schedule): Long? {
        return calculateNextExecuteTime(schedule)
    }

    private fun calculateNextExecuteTime(schedule: Schedule): Long? {
        val calendar = Calendar.getInstance()
        val now = System.currentTimeMillis()
        
        return when (schedule.getRepeatModeEnum()) {
            RepeatMode.ONCE -> {
                schedule.singleExecuteDate?.let { date ->
                    calendar.timeInMillis = date
                    calendar.set(Calendar.HOUR_OF_DAY, schedule.hour)
                    calendar.set(Calendar.MINUTE, schedule.minute)
                    calendar.set(Calendar.SECOND, 0)
                    calendar.set(Calendar.MILLISECOND, 0)
                    
                    val executeTime = calendar.timeInMillis
                    if (executeTime > now) executeTime else null
                }
            }
            
            RepeatMode.DAILY -> {
                calendar.set(Calendar.HOUR_OF_DAY, schedule.hour)
                calendar.set(Calendar.MINUTE, schedule.minute)
                calendar.set(Calendar.SECOND, 0)
                calendar.set(Calendar.MILLISECOND, 0)
                
                var executeTime = calendar.timeInMillis
                if (executeTime <= now) {
                    // 如果今天的時間已過，設定為明天
                    calendar.add(Calendar.DAY_OF_MONTH, 1)
                    executeTime = calendar.timeInMillis
                }
                executeTime
            }
            
            RepeatMode.WEEKLY -> {
                val selectedDays = schedule.getSelectedDaysOfWeek()
                if (selectedDays.isEmpty()) return null
                
                calendar.set(Calendar.HOUR_OF_DAY, schedule.hour)
                calendar.set(Calendar.MINUTE, schedule.minute)
                calendar.set(Calendar.SECOND, 0)
                calendar.set(Calendar.MILLISECOND, 0)
                
                val currentDayOfWeek = calendar.get(Calendar.DAY_OF_WEEK)
                val currentDayIndex = if (currentDayOfWeek == Calendar.SUNDAY) 7 else currentDayOfWeek - 1
                
                // 找到最近的執行日
                var nextDay: Int? = null
                val todayTime = calendar.timeInMillis
                
                // 檢查今天是否為執行日且時間未過
                if (selectedDays.contains(currentDayIndex) && todayTime > now) {
                    nextDay = currentDayIndex
                } else {
                    // 找下一個執行日
                    for (i in 1..7) {
                        val checkDay = (currentDayIndex + i - 1) % 7 + 1
                        if (selectedDays.contains(checkDay)) {
                            nextDay = checkDay
                            calendar.add(Calendar.DAY_OF_MONTH, i)
                            break
                        }
                    }
                }
                
                nextDay?.let { calendar.timeInMillis }
            }
            
            RepeatMode.MONTHLY -> {
                // 暫時不支援月重複
                Log.w(TAG, "Monthly repeat mode not supported yet")
                null
            }
        }
    }
    
    /**
     * 創建 PendingIntent
     * @param schedule 排程物件
     * @return PendingIntent
     */
    private fun createPendingIntent(schedule: Schedule): PendingIntent {
        return when (schedule.getScheduleTypeEnum()) {
            ScheduleType.APP -> createPendingIntent(schedule.id, schedule.packageName, null)
            ScheduleType.URL -> createPendingIntent(schedule.id, null, schedule.url)
        }
    }
    
    /**
     * 創建 PendingIntent (用於取消鬧鐘)
     * @param scheduleId 排程 ID
     * @param packageName 應用程式包名 (可選)
     * @param url URL 地址 (可選)
     * @return PendingIntent
     */
    private fun createPendingIntent(scheduleId: Long, packageName: String? = null, url: String? = null): PendingIntent {
        val intent = Intent(context, LaunchReceiver::class.java).apply {
            when {
                packageName != null -> {
                    action = LaunchReceiver.ACTION_LAUNCH_APP
                    putExtra(LaunchReceiver.EXTRA_PACKAGE_NAME, packageName)
                }
                url != null -> {
                    action = LaunchReceiver.ACTION_LAUNCH_URL
                    putExtra(LaunchReceiver.EXTRA_URL, url)
                }
                else -> {
                    // 預設為 APP 類型（用於取消鬧鐘）
                    action = LaunchReceiver.ACTION_LAUNCH_APP
                }
            }
            putExtra(LaunchReceiver.EXTRA_SCHEDULE_ID, scheduleId)
            // 添加額外標識確保廣播能被正確接收
            addFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES)
            addFlags(Intent.FLAG_RECEIVER_FOREGROUND)
        }

        val flags = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        } else {
            PendingIntent.FLAG_UPDATE_CURRENT
        }

        return PendingIntent.getBroadcast(
            context,
            scheduleId.toInt(), // 使用排程 ID 作為 request code 確保唯一性
            intent,
            flags
        )
    }
} 