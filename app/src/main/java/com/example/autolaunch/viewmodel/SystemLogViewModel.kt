package com.example.autolaunch.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import com.example.autolaunch.model.ActionType
import com.example.autolaunch.model.AppDatabase
import com.example.autolaunch.model.LogType
import com.example.autolaunch.model.SystemLog
import com.example.autolaunch.model.SystemLogDao
import com.example.autolaunch.utils.LogStatistics
import com.example.autolaunch.utils.SystemLogManager
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import java.util.*

/**
 * 系統日志ViewModel
 * 管理系統日志的數據和業務邏輯
 */
@OptIn(ExperimentalCoroutinesApi::class)
class SystemLogViewModel(application: Application) : AndroidViewModel(application) {
    
    private val systemLogDao: SystemLogDao = AppDatabase.getDatabase(application).systemLogDao()
    
    // 當前篩選狀態
    private val _filterState = MutableStateFlow(FilterState.ALL)
    
    // 日志列表
    val logs: LiveData<List<SystemLog>> = _filterState.flatMapLatest { filterState ->
        when (filterState) {
            FilterState.ALL -> systemLogDao.getAllLogs()
            FilterState.SUCCESS -> systemLogDao.getLogsByType(LogType.SUCCESS.value)
            FilterState.INFO -> systemLogDao.getLogsByType(LogType.INFO.value)
            FilterState.WARNING -> systemLogDao.getLogsByType(LogType.WARNING.value)
            FilterState.ERROR -> systemLogDao.getLogsByType(LogType.ERROR.value)
            FilterState.SCHEDULE_RELATED -> getScheduleRelatedLogs()
            FilterState.TODAY -> getTodayLogs()
        }
    }.asLiveData()
    
    // 載入狀態
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    // 錯誤信息
    private val _error = MutableLiveData<String?>()
    val error: LiveData<String?> = _error
    
    // 統計信息
    private val _statistics = MutableLiveData<LogStatistics>()
    val statistics: LiveData<LogStatistics> = _statistics
    
    init {
        loadStatistics()
    }
    
    /**
     * 刷新日志
     */
    fun refreshLogs() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                loadStatistics()
                // 由於使用Flow，數據會自動更新
            } catch (e: Exception) {
                _error.value = "刷新日誌時發生錯誤：${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 顯示所有日志
     */
    fun showAllLogs() {
        try {
            _filterState.value = FilterState.ALL
        } catch (e: Exception) {
            _error.value = "顯示所有日誌時發生錯誤：${e.message}"
        }
    }

    /**
     * 根據類型篩選日志
     */
    fun filterByType(logType: LogType) {
        try {
            _filterState.value = when (logType) {
                LogType.SUCCESS -> FilterState.SUCCESS
                LogType.INFO -> FilterState.INFO
                LogType.WARNING -> FilterState.WARNING
                LogType.ERROR -> FilterState.ERROR
            }
        } catch (e: Exception) {
            _error.value = "篩選日誌類型時發生錯誤：${e.message}"
        }
    }

    /**
     * 篩選排程相關日志
     */
    fun filterScheduleRelated() {
        try {
            _filterState.value = FilterState.SCHEDULE_RELATED
        } catch (e: Exception) {
            _error.value = "篩選排程相關日誌時發生錯誤：${e.message}"
        }
    }

    /**
     * 篩選今天的日志
     */
    fun filterTodayLogs() {
        try {
            _filterState.value = FilterState.TODAY
        } catch (e: Exception) {
            _error.value = "篩選今天日誌時發生錯誤：${e.message}"
        }
    }
    
    /**
     * 清除錯誤信息
     */
    fun clearError() {
        _error.value = null
    }
    
    /**
     * 載入統計信息
     */
    private fun loadStatistics() {
        viewModelScope.launch {
            try {
                val stats = SystemLogManager.getLogStatistics(getApplication())
                _statistics.value = stats
            } catch (e: Exception) {
                _error.value = "載入統計信息失敗：${e.message}"
            }
        }
    }
    
    /**
     * 獲取排程相關日志
     */
    private fun getScheduleRelatedLogs() = systemLogDao.getAllLogs().map { logs ->
        logs.filter { log ->
            val actionType = log.getActionTypeEnum()
            actionType == ActionType.SCHEDULE_CREATED ||
            actionType == ActionType.SCHEDULE_UPDATED ||
            actionType == ActionType.SCHEDULE_DELETED ||
            actionType == ActionType.SCHEDULE_EXECUTED ||
            actionType == ActionType.SCHEDULE_ENABLED ||
            actionType == ActionType.SCHEDULE_DISABLED
        }
    }
    
    /**
     * 獲取今天的日誌
     */
    private fun getTodayLogs() = run {
        val calendar = Calendar.getInstance()
        
        // 今天開始時間（00:00:00）
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        val startOfDay = calendar.timeInMillis
        
        // 今天結束時間（23:59:59）
        calendar.set(Calendar.HOUR_OF_DAY, 23)
        calendar.set(Calendar.MINUTE, 59)
        calendar.set(Calendar.SECOND, 59)
        calendar.set(Calendar.MILLISECOND, 999)
        val endOfDay = calendar.timeInMillis
        
        systemLogDao.getTodayLogs(startOfDay, endOfDay)
    }
    
    /**
     * 搜尋日誌
     */
    fun searchLogs(query: String): LiveData<List<SystemLog>> {
        return try {
            if (query.isBlank()) {
                logs
            } else {
                // 驗證搜尋關鍵字
                val sanitizedQuery = query.trim().take(100) // 限制搜尋關鍵字長度
                if (sanitizedQuery.length < 2) {
                    _error.value = "搜尋關鍵字至少需要2個字元"
                    logs
                } else {
                    systemLogDao.searchLogs(sanitizedQuery).asLiveData()
                }
            }
        } catch (e: Exception) {
            _error.value = "搜尋日誌時發生錯誤：${e.message}"
            logs
        }
    }
    
    /**
     * 獲取最近的日志
     */
    fun getRecentLogs(limit: Int = 100): LiveData<List<SystemLog>> {
        return systemLogDao.getRecentLogs(limit).asLiveData()
    }
    
    /**
     * 獲取指定日期範圍的日志
     */
    fun getLogsByDateRange(startTime: Long, endTime: Long): LiveData<List<SystemLog>> {
        return try {
            if (startTime > endTime) {
                _error.value = "開始時間不能晚於結束時間"
                logs
            } else {
                systemLogDao.getLogsByDateRange(startTime, endTime).asLiveData()
            }
        } catch (e: Exception) {
            _error.value = "獲取日期範圍日誌時發生錯誤：${e.message}"
            logs
        }
    }

    /**
     * 獲取儲存空間資訊
     */
    fun getStorageInfo(): LiveData<com.example.autolaunch.utils.StorageInfo> {
        val result = MutableLiveData<com.example.autolaunch.utils.StorageInfo>()
        viewModelScope.launch {
            try {
                val storageInfo = SystemLogManager.checkStorageUsage(getApplication())
                result.postValue(storageInfo)
            } catch (e: Exception) {
                _error.value = "獲取儲存資訊時發生錯誤：${e.message}"
                result.postValue(com.example.autolaunch.utils.StorageInfo())
            }
        }
        return result
    }

    /**
     * 執行手動清理
     */
    fun performManualCleanup(beforeDays: Int = 7): LiveData<com.example.autolaunch.utils.CleanupResult> {
        val result = MutableLiveData<com.example.autolaunch.utils.CleanupResult>()
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val cleanupResult = SystemLogManager.manualCleanup(getApplication(), beforeDays)
                result.postValue(cleanupResult)

                if (cleanupResult.success) {
                    // 刷新統計資訊
                    loadStatistics()
                }
            } catch (e: Exception) {
                _error.value = "執行清理時發生錯誤：${e.message}"
                result.postValue(com.example.autolaunch.utils.CleanupResult(
                    success = false,
                    message = "清理失敗：${e.message}"
                ))
            } finally {
                _isLoading.value = false
            }
        }
        return result
    }
    
    /**
     * 篩選狀態枚舉
     */
    private enum class FilterState {
        ALL,
        SUCCESS,
        INFO,
        WARNING,
        ERROR,
        SCHEDULE_RELATED,
        TODAY
    }
}
