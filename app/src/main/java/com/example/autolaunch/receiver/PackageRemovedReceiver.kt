package com.example.autolaunch.receiver

import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.WorkManager
import androidx.work.workDataOf
import com.example.autolaunch.R
import com.example.autolaunch.worker.PackageRemovedWorker

/**
 * 應用程式卸載廣播接收器
 * 監聽應用程式卸載事件並處理相關排程
 */
class PackageRemovedReceiver : BroadcastReceiver() {
    
    companion object {
        private const val TAG = "PackageRemovedReceiver"
        private const val NOTIFICATION_CHANNEL_ID = "package_removed"
        private const val NOTIFICATION_ID = 1002
    }
    
    override fun onReceive(context: Context, intent: Intent) {
        Log.d(TAG, "Received broadcast: ${intent.action}")
        
        if (intent.action == Intent.ACTION_PACKAGE_REMOVED) {
            val packageName = intent.data?.schemeSpecificPart
            val isReplacing = intent.getBooleanExtra(Intent.EXTRA_REPLACING, false)
            
            Log.i(TAG, "Package removed: $packageName, isReplacing: $isReplacing")
            
            // 如果只是應用程式更新（替換），不需要處理
            if (isReplacing) {
                Log.d(TAG, "Package is being replaced, ignoring")
                return
            }
            
            if (!packageName.isNullOrEmpty()) {
                handlePackageRemoved(context, packageName)
            }
        }
    }
    
    private fun handlePackageRemoved(context: Context, packageName: String) {
        Log.i(TAG, "Handling package removal: $packageName")
        
        // 使用 WorkManager 在後台處理排程清理
        val workRequest = OneTimeWorkRequestBuilder<PackageRemovedWorker>()
            .setInputData(workDataOf("package_name" to packageName))
            .build()
        
        WorkManager.getInstance(context).enqueue(workRequest)
        
        Log.d(TAG, "Enqueued package removal work for: $packageName")
    }
} 