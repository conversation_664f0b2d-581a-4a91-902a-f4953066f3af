package com.example.autolaunch.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.WorkManager
import com.example.autolaunch.service.ScheduleService
import com.example.autolaunch.worker.ScheduleReregistrationWorker
import com.example.autolaunch.utils.PersistentNotificationManager

/**
 * 開機完成廣播接收器
 * 用於在設備重啟後重新註冊所有排程
 */
class BootReceiver : BroadcastReceiver() {
    
    companion object {
        private const val TAG = "BootReceiver"
    }
    
    override fun onReceive(context: Context, intent: Intent) {
        Log.d(TAG, "Received broadcast: ${intent.action}")
        
        when (intent.action) {
            Intent.ACTION_BOOT_COMPLETED -> {
                Log.i(TAG, "Device boot completed, starting service and re-registering schedules")
                // 啟動前台服務
                try {
                    ScheduleService.startService(context)
                    Log.i(TAG, "Foreground service started on boot")
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to start service on boot", e)
                }

                // 恢復常駐通知
                try {
                    val notificationManager = PersistentNotificationManager.getInstance(context)
                    notificationManager.restorePersistentNotificationIfNeeded()
                    Log.i(TAG, "Persistent notification restored on boot")
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to restore persistent notification on boot", e)
                }

                reregisterSchedules(context)
            }
            Intent.ACTION_MY_PACKAGE_REPLACED,
            Intent.ACTION_PACKAGE_REPLACED -> {
                Log.i(TAG, "App updated, starting service and re-registering schedules")
                // 啟動前台服務
                try {
                    ScheduleService.startService(context)
                    Log.i(TAG, "Foreground service started on package replaced")
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to start service on package replaced", e)
                }

                // 恢復常駐通知
                try {
                    val notificationManager = PersistentNotificationManager.getInstance(context)
                    notificationManager.restorePersistentNotificationIfNeeded()
                    Log.i(TAG, "Persistent notification restored on package replaced")
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to restore persistent notification on package replaced", e)
                }

                reregisterSchedules(context)
            }
        }
    }
    
    private fun reregisterSchedules(context: Context) {
        Log.d(TAG, "Starting schedule re-registration work")
        
        // 使用 WorkManager 在後台執行重新註冊任務
        val workRequest = OneTimeWorkRequestBuilder<ScheduleReregistrationWorker>()
            .build()
        
        WorkManager.getInstance(context).enqueue(workRequest)
        
        Log.i(TAG, "Schedule re-registration work enqueued")
    }
} 