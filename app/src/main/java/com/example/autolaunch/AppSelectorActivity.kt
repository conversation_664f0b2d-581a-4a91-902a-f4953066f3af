package com.example.autolaunch

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.appcompat.widget.SearchView
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.autolaunch.base.BaseActivity
import com.example.autolaunch.databinding.ActivityAppSelectorBinding
import com.example.autolaunch.model.AppInfo
import kotlinx.coroutines.launch

/**
 * 應用程式選擇器 Activity
 * 顯示可啟動的應用程式列表供用戶選擇
 */
class AppSelectorActivity : BaseActivity() {
    
    private lateinit var binding: ActivityAppSelectorBinding
    private lateinit var viewModel: AppSelectorViewModel
    private lateinit var appAdapter: AppSelectorAdapter
    
    companion object {
        const val EXTRA_SELECTED_APP_NAME = "extra_selected_app_name"
        const val EXTRA_SELECTED_APP_PACKAGE = "extra_selected_app_package"
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        binding = ActivityAppSelectorBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupViewModel()
        setupUI()
        setupWindowInsets()
        setupRecyclerView()
        setupSearchView()
        observeViewModel()
    }
    
    private fun setupViewModel() {
        viewModel = ViewModelProvider(this)[AppSelectorViewModel::class.java]
    }
    
    private fun setupUI() {
        // 設定工具列
        binding.toolbar.setNavigationIcon(androidx.appcompat.R.drawable.abc_ic_ab_back_material)
        binding.toolbar.setNavigationOnClickListener {
            finish()
        }
        binding.toolbar.title = "選擇應用程式"
    }
    
    private fun setupWindowInsets() {
        ViewCompat.setOnApplyWindowInsetsListener(binding.root) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }
    }
    
    private fun setupRecyclerView() {
        appAdapter = AppSelectorAdapter { appInfo ->
            onAppSelected(appInfo)
        }
        
        binding.recyclerViewApps.apply {
            layoutManager = LinearLayoutManager(this@AppSelectorActivity)
            adapter = appAdapter
        }
    }
    
    private fun setupSearchView() {
        binding.searchView.setOnQueryTextListener(object : SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(query: String?): Boolean {
                return false
            }
            
            override fun onQueryTextChange(newText: String?): Boolean {
                viewModel.searchApps(newText ?: "")
                return true
            }
        })
    }
    
    private fun observeViewModel() {
        // 觀察應用程式列表變化
        lifecycleScope.launch {
            viewModel.apps.collect { apps ->
                appAdapter.submitList(apps)
                updateEmptyState(apps.isEmpty())
            }
        }
        
        // 觀察載入狀態
        lifecycleScope.launch {
            viewModel.isLoading.collect { isLoading ->
                binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
            }
        }
        
        // 觀察錯誤狀態
        lifecycleScope.launch {
            viewModel.error.collect { error ->
                error?.let {
                    // 可以在此處顯示錯誤訊息
                    viewModel.clearError()
                }
            }
        }
    }
    
    private fun updateEmptyState(isEmpty: Boolean) {
        binding.apply {
            if (isEmpty) {
                emptyStateLayout.visibility = View.VISIBLE
                recyclerViewApps.visibility = View.GONE
            } else {
                emptyStateLayout.visibility = View.GONE
                recyclerViewApps.visibility = View.VISIBLE
            }
        }
    }
    
    private fun onAppSelected(appInfo: AppInfo) {
        val resultIntent = Intent().apply {
            putExtra(EXTRA_SELECTED_APP_NAME, appInfo.appName)
            putExtra(EXTRA_SELECTED_APP_PACKAGE, appInfo.packageName)
        }
        setResult(Activity.RESULT_OK, resultIntent)
        finish()
    }
} 