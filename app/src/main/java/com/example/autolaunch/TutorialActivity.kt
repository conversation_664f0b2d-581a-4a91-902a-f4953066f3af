package com.example.autolaunch

import android.os.Bundle
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import com.example.autolaunch.adapters.HelpPagerAdapter
import com.example.autolaunch.base.BaseActivity
import com.example.autolaunch.databinding.ActivityTutorialBinding
import com.google.android.material.tabs.TabLayoutMediator

/**
 * 教學頁面Activity
 * 整合使用教學和Q&A功能
 */
class TutorialActivity : BaseActivity() {

    private lateinit var binding: ActivityTutorialBinding
    private lateinit var helpPagerAdapter: HelpPagerAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityTutorialBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupUI()
        setupViewPager()
        setupWindowInsets()
    }

    private fun setupUI() {
        // 設定工具列
        binding.toolbar.setNavigationIcon(androidx.appcompat.R.drawable.abc_ic_ab_back_material)
        binding.toolbar.setNavigationOnClickListener {
            finish()
        }
    }

    private fun setupViewPager() {
        // 設定ViewPager適配器
        helpPagerAdapter = HelpPagerAdapter(this)
        binding.viewPager.adapter = helpPagerAdapter

        // 設定TabLayout與ViewPager的連接
        TabLayoutMediator(binding.tabLayout, binding.viewPager) { tab, position ->
            tab.text = helpPagerAdapter.getTabTitle(position)
        }.attach()
    }
    
    private fun setupWindowInsets() {
        ViewCompat.setOnApplyWindowInsetsListener(binding.root) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }
    }
} 