package com.example.autolaunch.service

import android.accessibilityservice.AccessibilityService
import android.accessibilityservice.GestureDescription
import android.content.Context
import android.content.Intent
import android.graphics.Path
import android.graphics.Rect
import android.os.Bundle
import android.provider.Settings
import android.util.Log
import android.view.accessibility.AccessibilityEvent
import android.view.accessibility.AccessibilityNodeInfo
import androidx.core.view.accessibility.AccessibilityNodeInfoCompat
import com.example.autolaunch.model.AutomationStep
import com.example.autolaunch.model.StepType
import com.example.autolaunch.model.TargetType
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow

/**
 * AutoLaunch 無障礙服務
 * 負責執行自動化步驟和UI元素操作
 */
class AutoLaunchAccessibilityService : AccessibilityService() {
    
    companion object {
        private const val TAG = "AutoLaunchAccessibilityService"
        
        // 服務實例（用於外部調用）
        @Volatile
        private var instance: AutoLaunchAccessibilityService? = null
        
        /**
         * 獲取服務實例
         */
        fun getInstance(): AutoLaunchAccessibilityService? = instance
        
        /**
         * 檢查無障礙服務是否已啟用
         */
        fun isAccessibilityServiceEnabled(context: Context): Boolean {
            val serviceName = "${context.packageName}/${AutoLaunchAccessibilityService::class.java.name}"
            val enabledServices = Settings.Secure.getString(
                context.contentResolver,
                Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES
            )
            return enabledServices?.contains(serviceName) == true
        }
        
        /**
         * 創建無障礙服務設置Intent
         */
        fun createAccessibilitySettingsIntent(): Intent {
            return Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS)
        }
    }
    
    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    
    // 事件流
    private val _accessibilityEvents = MutableSharedFlow<AccessibilityEvent>()
    val accessibilityEvents: SharedFlow<AccessibilityEvent> = _accessibilityEvents
    
    // 窗口變化流
    private val _windowChanges = MutableSharedFlow<String>()
    val windowChanges: SharedFlow<String> = _windowChanges
    
    override fun onServiceConnected() {
        super.onServiceConnected()
        instance = this
        Log.i(TAG, "AutoLaunch Accessibility Service connected")
        
        // 初始化服務配置
        serviceScope.launch {
            initializeService()
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        instance = null
        serviceScope.cancel()
        Log.i(TAG, "AutoLaunch Accessibility Service destroyed")
    }
    
    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        event?.let { accessibilityEvent ->
            serviceScope.launch {
                try {
                    handleAccessibilityEvent(accessibilityEvent)
                    _accessibilityEvents.emit(accessibilityEvent)
                } catch (e: Exception) {
                    Log.e(TAG, "Error handling accessibility event", e)
                }
            }
        }
    }
    
    override fun onInterrupt() {
        Log.w(TAG, "Accessibility service interrupted")
    }
    
    /**
     * 初始化服務
     */
    private suspend fun initializeService() {
        Log.d(TAG, "Initializing accessibility service")
        // 這裡可以添加初始化邏輯
    }
    
    /**
     * 處理無障礙事件
     */
    private suspend fun handleAccessibilityEvent(event: AccessibilityEvent) {
        when (event.eventType) {
            AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED -> {
                val packageName = event.packageName?.toString()
                val className = event.className?.toString()
                Log.d(TAG, "Window changed: $packageName/$className")
                
                packageName?.let { pkg ->
                    _windowChanges.emit(pkg)
                }
            }
            AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED -> {
                // 窗口內容變化，可用於檢測UI更新
                Log.v(TAG, "Window content changed")
            }
        }
    }
    
    /**
     * 執行自動化步驟
     */
    suspend fun executeStep(step: AutomationStep): Boolean {
        return withContext(Dispatchers.Main) {
            try {
                Log.d(TAG, "Executing step: ${step.getDisplayName()}")
                
                when (step.getStepType()) {
                    StepType.CLICK -> performClick(step)
                    StepType.LONG_CLICK -> performLongClick(step)
                    StepType.INPUT_TEXT -> performInputText(step)
                    StepType.SWIPE -> performSwipe(step)
                    StepType.WAIT -> performWait(step)
                    StepType.SCROLL -> performScroll(step)
                    StepType.BACK -> performBack()
                    StepType.HOME -> performHome()
                    StepType.SWIPE_UP -> performSwipeUp()
                    StepType.SWIPE_DOWN -> performSwipeDown()
                    StepType.SWIPE_LEFT -> performSwipeLeft()
                    StepType.SWIPE_RIGHT -> performSwipeRight()
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error executing step: ${step.getDisplayName()}", e)
                false
            }
        }
    }
    
    /**
     * 執行點擊操作
     */
    private suspend fun performClick(step: AutomationStep): Boolean {
        val node = findTargetNode(step) ?: return false
        
        return if (node.isClickable) {
            node.performAction(AccessibilityNodeInfo.ACTION_CLICK)
        } else {
            // 如果節點不可點擊，嘗試點擊其父節點
            val clickableParent = findClickableParent(node)
            clickableParent?.performAction(AccessibilityNodeInfo.ACTION_CLICK) ?: false
        }
    }
    
    /**
     * 執行長按操作
     */
    private suspend fun performLongClick(step: AutomationStep): Boolean {
        val node = findTargetNode(step) ?: return false
        return node.performAction(AccessibilityNodeInfo.ACTION_LONG_CLICK)
    }
    
    /**
     * 執行文字輸入操作
     */
    private suspend fun performInputText(step: AutomationStep): Boolean {
        val node = findTargetNode(step) ?: return false
        val inputText = step.inputText ?: return false
        
        // 先點擊輸入框獲得焦點
        if (!node.performAction(AccessibilityNodeInfo.ACTION_CLICK)) {
            return false
        }
        
        // 清空現有文字
        node.performAction(AccessibilityNodeInfo.ACTION_SELECT_ALL)
        
        // 輸入新文字
        val arguments = Bundle().apply {
            putCharSequence(AccessibilityNodeInfo.ACTION_ARGUMENT_SET_TEXT_CHARSEQUENCE, inputText)
        }
        
        return node.performAction(AccessibilityNodeInfo.ACTION_SET_TEXT, arguments)
    }
    
    /**
     * 執行滑動操作
     */
    private suspend fun performSwipe(step: AutomationStep): Boolean {
        val startX = step.swipeStartX ?: return false
        val startY = step.swipeStartY ?: return false
        val endX = step.swipeEndX ?: return false
        val endY = step.swipeEndY ?: return false
        val duration = step.swipeDuration ?: 500L
        
        return performGestureSwipe(startX, startY, endX, endY, duration)
    }
    
    /**
     * 執行等待操作
     */
    private suspend fun performWait(step: AutomationStep): Boolean {
        val duration = step.waitDuration ?: return false
        delay(duration)
        return true
    }
    
    /**
     * 執行滾動操作
     */
    private suspend fun performScroll(step: AutomationStep): Boolean {
        val rootNode = rootInActiveWindow ?: return false
        
        return when (step.scrollDirection) {
            1 -> rootNode.performAction(AccessibilityNodeInfo.ACTION_SCROLL_BACKWARD) // 向上
            2 -> rootNode.performAction(AccessibilityNodeInfo.ACTION_SCROLL_FORWARD)  // 向下
            else -> false
        }
    }
    
    /**
     * 執行返回操作
     */
    private suspend fun performBack(): Boolean {
        return performGlobalAction(GLOBAL_ACTION_BACK)
    }
    
    /**
     * 執行回到主頁操作
     */
    private suspend fun performHome(): Boolean {
        return performGlobalAction(GLOBAL_ACTION_HOME)
    }
    
    /**
     * 執行向上滑動
     */
    private suspend fun performSwipeUp(): Boolean {
        val displayMetrics = resources.displayMetrics
        val centerX = displayMetrics.widthPixels / 2
        val startY = (displayMetrics.heightPixels * 0.8).toInt()
        val endY = (displayMetrics.heightPixels * 0.2).toInt()
        
        return performGestureSwipe(centerX, startY, centerX, endY, 500L)
    }
    
    /**
     * 執行向下滑動
     */
    private suspend fun performSwipeDown(): Boolean {
        val displayMetrics = resources.displayMetrics
        val centerX = displayMetrics.widthPixels / 2
        val startY = (displayMetrics.heightPixels * 0.2).toInt()
        val endY = (displayMetrics.heightPixels * 0.8).toInt()
        
        return performGestureSwipe(centerX, startY, centerX, endY, 500L)
    }
    
    /**
     * 執行向左滑動
     */
    private suspend fun performSwipeLeft(): Boolean {
        val displayMetrics = resources.displayMetrics
        val centerY = displayMetrics.heightPixels / 2
        val startX = (displayMetrics.widthPixels * 0.8).toInt()
        val endX = (displayMetrics.widthPixels * 0.2).toInt()
        
        return performGestureSwipe(startX, centerY, endX, centerY, 500L)
    }
    
    /**
     * 執行向右滑動
     */
    private suspend fun performSwipeRight(): Boolean {
        val displayMetrics = resources.displayMetrics
        val centerY = displayMetrics.heightPixels / 2
        val startX = (displayMetrics.widthPixels * 0.2).toInt()
        val endX = (displayMetrics.widthPixels * 0.8).toInt()
        
        return performGestureSwipe(startX, centerY, endX, centerY, 500L)
    }
    
    /**
     * 執行手勢滑動
     */
    private suspend fun performGestureSwipe(
        startX: Int, startY: Int, 
        endX: Int, endY: Int, 
        duration: Long
    ): Boolean {
        return withContext(Dispatchers.Main) {
            val path = Path().apply {
                moveTo(startX.toFloat(), startY.toFloat())
                lineTo(endX.toFloat(), endY.toFloat())
            }
            
            val gesture = GestureDescription.Builder()
                .addStroke(GestureDescription.StrokeDescription(path, 0, duration))
                .build()
            
            val result = CompletableDeferred<Boolean>()
            
            dispatchGesture(gesture, object : GestureResultCallback() {
                override fun onCompleted(gestureDescription: GestureDescription?) {
                    result.complete(true)
                }
                
                override fun onCancelled(gestureDescription: GestureDescription?) {
                    result.complete(false)
                }
            }, null)
            
            result.await()
        }
    }

    /**
     * 根據步驟配置查找目標節點
     */
    private suspend fun findTargetNode(step: AutomationStep): AccessibilityNodeInfo? {
        val rootNode = rootInActiveWindow ?: return null

        return when (step.getTargetType()) {
            TargetType.TEXT -> findNodeByText(rootNode, step.targetText)
            TargetType.ID -> findNodeById(rootNode, step.targetId)
            TargetType.COORDINATE -> findNodeByCoordinate(step.targetX, step.targetY)
            TargetType.DESCRIPTION -> findNodeByDescription(rootNode, step.targetDescription)
            TargetType.CLASS_NAME -> findNodeByClassName(rootNode, step.targetClassName)
        }
    }

    /**
     * 根據文字查找節點
     */
    private fun findNodeByText(rootNode: AccessibilityNodeInfo, text: String?): AccessibilityNodeInfo? {
        if (text.isNullOrBlank()) return null

        val nodes = rootNode.findAccessibilityNodeInfosByText(text)
        return nodes?.firstOrNull()
    }

    /**
     * 根據ID查找節點
     */
    private fun findNodeById(rootNode: AccessibilityNodeInfo, viewId: String?): AccessibilityNodeInfo? {
        if (viewId.isNullOrBlank()) return null

        val nodes = rootNode.findAccessibilityNodeInfosByViewId(viewId)
        return nodes?.firstOrNull()
    }

    /**
     * 根據座標查找節點
     */
    private fun findNodeByCoordinate(x: Int?, y: Int?): AccessibilityNodeInfo? {
        if (x == null || y == null) return null

        val rootNode = rootInActiveWindow ?: return null
        return findNodeAtPosition(rootNode, x, y)
    }

    /**
     * 根據描述查找節點
     */
    private fun findNodeByDescription(rootNode: AccessibilityNodeInfo, description: String?): AccessibilityNodeInfo? {
        if (description.isNullOrBlank()) return null

        return findNodeRecursively(rootNode) { node ->
            node.contentDescription?.toString()?.contains(description, ignoreCase = true) == true
        }
    }

    /**
     * 根據類別名稱查找節點
     */
    private fun findNodeByClassName(rootNode: AccessibilityNodeInfo, className: String?): AccessibilityNodeInfo? {
        if (className.isNullOrBlank()) return null

        return findNodeRecursively(rootNode) { node ->
            node.className?.toString()?.contains(className, ignoreCase = true) == true
        }
    }

    /**
     * 在指定位置查找節點
     */
    private fun findNodeAtPosition(rootNode: AccessibilityNodeInfo, x: Int, y: Int): AccessibilityNodeInfo? {
        return findNodeRecursively(rootNode) { node ->
            val rect = Rect()
            node.getBoundsInScreen(rect)
            rect.contains(x, y)
        }
    }

    /**
     * 遞歸查找節點
     */
    private fun findNodeRecursively(
        node: AccessibilityNodeInfo,
        predicate: (AccessibilityNodeInfo) -> Boolean
    ): AccessibilityNodeInfo? {
        if (predicate(node)) {
            return node
        }

        for (i in 0 until node.childCount) {
            val child = node.getChild(i) ?: continue
            val result = findNodeRecursively(child, predicate)
            if (result != null) {
                return result
            }
        }

        return null
    }

    /**
     * 查找可點擊的父節點
     */
    private fun findClickableParent(node: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        var parent = node.parent
        while (parent != null) {
            if (parent.isClickable) {
                return parent
            }
            parent = parent.parent
        }
        return null
    }

    /**
     * 獲取當前活動窗口的包名
     */
    fun getCurrentPackageName(): String? {
        return rootInActiveWindow?.packageName?.toString()
    }

    /**
     * 獲取當前活動窗口的類名
     */
    fun getCurrentClassName(): String? {
        return rootInActiveWindow?.className?.toString()
    }

    /**
     * 檢查指定包名的應用是否在前台
     */
    fun isAppInForeground(packageName: String): Boolean {
        return getCurrentPackageName() == packageName
    }

    /**
     * 等待指定應用進入前台
     */
    suspend fun waitForApp(packageName: String, timeoutMs: Long = 30000): Boolean {
        val startTime = System.currentTimeMillis()

        while (System.currentTimeMillis() - startTime < timeoutMs) {
            if (isAppInForeground(packageName)) {
                return true
            }
            delay(500)
        }

        return false
    }

    /**
     * 等待UI元素出現
     */
    suspend fun waitForElement(step: AutomationStep, timeoutMs: Long = 10000): AccessibilityNodeInfo? {
        val startTime = System.currentTimeMillis()

        while (System.currentTimeMillis() - startTime < timeoutMs) {
            val node = findTargetNode(step)
            if (node != null) {
                return node
            }
            delay(500)
        }

        return null
    }

    /**
     * 獲取所有可交互的節點信息
     */
    fun getInteractiveNodes(): List<AccessibilityNodeInfo> {
        val rootNode = rootInActiveWindow ?: return emptyList()
        val interactiveNodes = mutableListOf<AccessibilityNodeInfo>()

        collectInteractiveNodes(rootNode, interactiveNodes)
        return interactiveNodes
    }

    /**
     * 收集可交互的節點
     */
    private fun collectInteractiveNodes(node: AccessibilityNodeInfo, result: MutableList<AccessibilityNodeInfo>) {
        if (node.isClickable || node.isLongClickable || node.isEditable || node.isScrollable) {
            result.add(node)
        }

        for (i in 0 until node.childCount) {
            val child = node.getChild(i) ?: continue
            collectInteractiveNodes(child, result)
        }
    }
}
