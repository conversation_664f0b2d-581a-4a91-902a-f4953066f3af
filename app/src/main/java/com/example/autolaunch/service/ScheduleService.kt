package com.example.autolaunch.service

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.IBinder
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.work.ExistingPeriodicWorkPolicy
import androidx.work.PeriodicWorkRequestBuilder
import androidx.work.WorkManager
import com.example.autolaunch.MainActivity
import com.example.autolaunch.R
import com.example.autolaunch.worker.ScheduleMonitorWorker
import com.example.autolaunch.model.AppDatabase
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import java.util.concurrent.TimeUnit

/**
 * 排程監控前台服務
 * 確保應用程式在後台時排程仍能正常執行
 */
class ScheduleService : Service() {
    
    companion object {
        private const val TAG = "ScheduleService"
        private const val NOTIFICATION_ID = 1000
        private const val CHANNEL_ID = "schedule_service_channel"
        private const val CHANNEL_NAME = "排程監控服務"
        private const val MONITOR_WORK_NAME = "schedule_monitor_work"

        const val ACTION_START_SERVICE = "com.example.autolaunch.action.START_SERVICE"
        const val ACTION_STOP_SERVICE = "com.example.autolaunch.action.STOP_SERVICE"
        
        /**
         * 啟動排程服務
         */
        fun startService(context: Context) {
            try {
                val intent = Intent(context, ScheduleService::class.java).apply {
                    action = ACTION_START_SERVICE
                }
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    context.startForegroundService(intent)
                } else {
                    context.startService(intent)
                }
                Log.d(TAG, "Service start requested")
            } catch (e: Exception) {
                Log.e(TAG, "Failed to start service", e)
                throw e
            }
        }
        
        /**
         * 停止排程服務
         */
        fun stopService(context: Context) {
            val intent = Intent(context, ScheduleService::class.java).apply {
                action = ACTION_STOP_SERVICE
            }
            context.stopService(intent)
        }
    }
    
    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    private var monitoringJob: Job? = null
    
    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "ScheduleService created")
        createNotificationChannel()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "ScheduleService onStartCommand: ${intent?.action}")

        try {
            when (intent?.action) {
                ACTION_START_SERVICE -> {
                    startForeground(1, createNotification())
                    Log.i(TAG, "ScheduleService started.")
                    startMonitoring()
                    schedulePeriodicMonitoring()
                }
                ACTION_STOP_SERVICE -> {
                    Log.i(TAG, "Stopping ScheduleService.")
                    stopForeground(true)
                    stopSelf()
                    return START_NOT_STICKY
                }
                null -> {
                    // 處理 null intent 的情況（系統重啟服務時可能發生）
                    Log.i(TAG, "Service restarted by system, starting foreground service")
                    startForeground(1, createNotification())
                    startMonitoring()
                    schedulePeriodicMonitoring()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in onStartCommand", e)
            // 即使出錯也要嘗試啟動前台服務
            try {
                startForeground(1, createNotification())
            } catch (e2: Exception) {
                Log.e(TAG, "Failed to start foreground service", e2)
            }
        }

        // 返回 START_STICKY 確保服務被系統殺死後會重新啟動
        return START_STICKY
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    override fun onDestroy() {
        Log.d(TAG, "ScheduleService destroyed")

        // 安全取消所有 Coroutine 任務
        try {
            monitoringJob?.cancel()
            monitoringJob = null

            // 取消 WorkManager 的週期性監控
            WorkManager.getInstance(this).cancelUniqueWork(MONITOR_WORK_NAME)

            // 取消服務範圍內的所有協程
            serviceScope.cancel()

            Log.i(TAG, "All monitoring tasks cancelled successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error during service cleanup", e)
        }

        super.onDestroy()
    }
    
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "用於保持排程監控服務在後台運行"
                setShowBadge(false)
            }
            
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    private fun createNotification(): Notification {
        val intent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or 
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) PendingIntent.FLAG_IMMUTABLE else 0
        )
        
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("AutoLaunch 正在運行")
            .setContentText("排程監控服務已啟動，確保排程正常執行")
            .setSmallIcon(R.drawable.ic_notification)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setCategory(NotificationCompat.CATEGORY_SERVICE)
            .build()
    }
    
    private fun startMonitoring() {
        // 安全取消之前的監控任務
        monitoringJob?.cancel()

        monitoringJob = serviceScope.launch {
            try {
                Log.i(TAG, "Started monitoring active schedules")
                monitorActiveSchedules()
            } catch (e: CancellationException) {
                Log.d(TAG, "Monitoring cancelled")
                throw e // 重新拋出 CancellationException
            } catch (e: Exception) {
                Log.e(TAG, "Error in monitoring", e)
            }
        }
    }
    
    private suspend fun monitorActiveSchedules() {
        val database = AppDatabase.getDatabase(this)
        val scheduleDao = database.scheduleDao()

        try {
            // 獲取啟用的排程（一次性檢查）
            val schedules = scheduleDao.getEnabledSchedules().first()
            val activeCount = schedules.size
            Log.d(TAG, "Monitoring $activeCount active schedules")

            // 更新通知內容
            updateNotification(activeCount)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting enabled schedules", e)
            // 使用默認值
            updateNotification(0)
        }
    }
    
    private fun updateNotification(activeScheduleCount: Int) {
        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("AutoLaunch 正在運行")
            .setContentText("正在監控 $activeScheduleCount 個活躍排程")
            .setSmallIcon(R.drawable.ic_notification)
            .setOngoing(true)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setCategory(NotificationCompat.CATEGORY_SERVICE)
            .build()
        
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(NOTIFICATION_ID, notification)
    }

    private fun schedulePeriodicMonitoring() {
        val monitorWorkRequest = PeriodicWorkRequestBuilder<ScheduleMonitorWorker>(
            15, TimeUnit.MINUTES // 每 15 分鐘檢查一次
        ).build()

        WorkManager.getInstance(this).enqueueUniquePeriodicWork(
            MONITOR_WORK_NAME,
            ExistingPeriodicWorkPolicy.KEEP,
            monitorWorkRequest
        )

        Log.i(TAG, "Scheduled periodic monitoring every 15 minutes")
    }
}
