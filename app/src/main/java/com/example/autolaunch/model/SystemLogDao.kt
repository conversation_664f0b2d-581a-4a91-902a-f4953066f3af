package com.example.autolaunch.model

import androidx.room.*
import kotlinx.coroutines.flow.Flow

/**
 * 系統日志數據存取物件 (DAO)
 * 定義對 SystemLog 實體進行資料庫操作的方法
 */
@Dao
interface SystemLogDao {
    
    /**
     * 插入一個新的系統日志
     * @param log 要插入的日志物件
     * @return 插入的日志 ID
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insert(log: SystemLog): Long
    
    /**
     * 批量插入系統日志
     * @param logs 要插入的日志列表
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAll(logs: List<SystemLog>)
    
    /**
     * 刪除一個系統日志
     * @param log 要刪除的日志物件
     * @return 受影響的列數
     */
    @Delete
    suspend fun delete(log: SystemLog): Int
    
    /**
     * 根據 ID 刪除系統日志
     * @param id 日志 ID
     * @return 受影響的列數
     */
    @Query("DELETE FROM system_logs WHERE id = :id")
    suspend fun deleteById(id: Long): Int
    
    /**
     * 獲取所有系統日誌，按時間倒序排列
     * @return Flow<List<SystemLog>> 所有日誌的 Flow
     */
    @Query("SELECT * FROM system_logs ORDER BY timestamp DESC")
    fun getAllLogs(): Flow<List<SystemLog>>

    /**
     * 獲取指定日期範圍內的系統日誌
     * @param startTime 開始時間戳
     * @param endTime 結束時間戳
     * @return Flow<List<SystemLog>> 指定範圍內日誌的 Flow
     */
    @Query("SELECT * FROM system_logs WHERE timestamp BETWEEN :startTime AND :endTime ORDER BY timestamp DESC")
    fun getLogsByDateRange(startTime: Long, endTime: Long): Flow<List<SystemLog>>

    /**
     * 根據日誌類型獲取系統日誌
     * @param logType 日誌類型
     * @return Flow<List<SystemLog>> 指定類型日誌的 Flow
     */
    @Query("SELECT * FROM system_logs WHERE log_type = :logType ORDER BY timestamp DESC")
    fun getLogsByType(logType: Int): Flow<List<SystemLog>>

    /**
     * 根據操作類型獲取系統日誌
     * @param actionType 操作類型
     * @return Flow<List<SystemLog>> 指定操作類型日誌的 Flow
     */
    @Query("SELECT * FROM system_logs WHERE action_type = :actionType ORDER BY timestamp DESC")
    fun getLogsByAction(actionType: Int): Flow<List<SystemLog>>

    /**
     * 根據排程ID獲取相關日誌
     * @param scheduleId 排程ID
     * @return Flow<List<SystemLog>> 相關日誌的 Flow
     */
    @Query("SELECT * FROM system_logs WHERE schedule_id = :scheduleId ORDER BY timestamp DESC")
    fun getLogsByScheduleId(scheduleId: Long): Flow<List<SystemLog>>
    
    /**
     * 獲取今天的系統日志
     * @param startOfDay 今天開始的時間戳
     * @param endOfDay 今天結束的時間戳
     * @return Flow<List<SystemLog>> 今天日志的 Flow
     */
    @Query("SELECT * FROM system_logs WHERE timestamp BETWEEN :startOfDay AND :endOfDay ORDER BY timestamp DESC")
    fun getTodayLogs(startOfDay: Long, endOfDay: Long): Flow<List<SystemLog>>
    
    /**
     * 獲取最近N條日志
     * @param limit 限制數量
     * @return Flow<List<SystemLog>> 最近日志的 Flow
     */
    @Query("SELECT * FROM system_logs ORDER BY timestamp DESC LIMIT :limit")
    fun getRecentLogs(limit: Int): Flow<List<SystemLog>>
    
    /**
     * 搜尋日誌（根據訊息內容）
     * @param query 搜尋關鍵字
     * @return Flow<List<SystemLog>> 搜尋結果的 Flow
     */
    @Query("SELECT * FROM system_logs WHERE message LIKE '%' || :query || '%' OR schedule_name LIKE '%' || :query || '%' ORDER BY timestamp DESC")
    fun searchLogs(query: String): Flow<List<SystemLog>>

    /**
     * 獲取日誌總數
     * @return 日誌總數
     */
    @Query("SELECT COUNT(*) FROM system_logs")
    suspend fun getLogCount(): Int

    /**
     * 獲取指定類型的日誌數量
     * @param logType 日誌類型
     * @return 指定類型的日誌數量
     */
    @Query("SELECT COUNT(*) FROM system_logs WHERE log_type = :logType")
    suspend fun getLogCountByType(logType: Int): Int
    
    /**
     * 刪除指定時間之前的日志（用於日志輪轉）
     * @param beforeTime 時間戳，刪除此時間之前的日志
     * @return 受影響的列數
     */
    @Query("DELETE FROM system_logs WHERE timestamp < :beforeTime")
    suspend fun deleteLogsBefore(beforeTime: Long): Int

    /**
     * 刪除舊日志（別名方法，用於DatabaseTransactionManager）
     * @param beforeTime 時間戳，刪除此時間之前的日志
     * @return 受影響的列數
     */
    suspend fun deleteOldLogs(beforeTime: Long): Int = deleteLogsBefore(beforeTime)
    
    /**
     * 清空所有日志
     * @return 受影響的列數
     */
    @Query("DELETE FROM system_logs")
    suspend fun deleteAllLogs(): Int
    
    /**
     * 獲取最舊的日志時間戳
     * @return 最舊日志的時間戳，如果沒有日志則返回null
     */
    @Query("SELECT MIN(timestamp) FROM system_logs")
    suspend fun getOldestLogTimestamp(): Long?
    
    /**
     * 獲取最新的日志時間戳
     * @return 最新日志的時間戳，如果沒有日志則返回null
     */
    @Query("SELECT MAX(timestamp) FROM system_logs")
    suspend fun getLatestLogTimestamp(): Long?
    
    /**
     * 根據日期分組獲取日志數量統計
     * @return Flow<List<LogDateCount>> 每日日志數量統計
     */
    @Query("""
        SELECT DATE(timestamp/1000, 'unixepoch', 'localtime') as date, 
               COUNT(*) as count 
        FROM system_logs 
        GROUP BY DATE(timestamp/1000, 'unixepoch', 'localtime') 
        ORDER BY date DESC
    """)
    fun getLogCountByDate(): Flow<List<LogDateCount>>
}

/**
 * 日志日期統計數據類
 */
data class LogDateCount(
    val date: String,
    val count: Int
)
