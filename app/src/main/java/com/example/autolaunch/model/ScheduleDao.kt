package com.example.autolaunch.model

import androidx.room.*
import kotlinx.coroutines.flow.Flow

/**
 * 排程數據存取物件 (DAO)
 * 定義對 Schedule 實體進行資料庫操作的方法
 */
@Dao
interface ScheduleDao {
    
    /**
     * 插入一個新的排程
     * @param schedule 要插入的排程物件
     * @return 插入的排程 ID
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insert(schedule: Schedule): Long
    
    /**
     * 批量插入排程
     * @param schedules 要插入的排程列表
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAll(schedules: List<Schedule>)
    
    /**
     * 更新現有排程
     * @param schedule 要更新的排程物件
     * @return 受影響的列數
     */
    @Update
    suspend fun update(schedule: Schedule): Int
    
    /**
     * 刪除一個排程
     * @param schedule 要刪除的排程物件
     * @return 受影響的列數
     */
    @Delete
    suspend fun delete(schedule: Schedule): Int
    
    /**
     * 根據 ID 刪除排程
     * @param id 排程 ID
     * @return 受影響的列數
     */
    @Query("DELETE FROM schedules WHERE id = :id")
    suspend fun deleteById(id: Long): Int
    
    /**
     * 獲取所有排程，支援數據變動時自動更新
     * @return Flow<List<Schedule>> 所有排程的 Flow
     */
    @Query("SELECT * FROM schedules ORDER BY sort_order ASC, created_time DESC")
    fun getAllSchedules(): Flow<List<Schedule>>
    
    /**
     * 根據 ID 獲取特定排程
     * @param id 排程 ID
     * @return Flow<Schedule?> 指定排程的 Flow
     */
    @Query("SELECT * FROM schedules WHERE id = :id")
    fun getScheduleById(id: Long): Flow<Schedule?>
    
    /**
     * 獲取所有啟用的排程
     * @return Flow<List<Schedule>> 啟用排程的 Flow
     */
    @Query("SELECT * FROM schedules WHERE is_enabled = 1 ORDER BY hour, minute")
    fun getEnabledSchedules(): Flow<List<Schedule>>
    
    /**
     * 根據應用程式包名獲取排程
     * @param packageName 應用程式包名
     * @return Flow<List<Schedule>> 指定應用程式的排程 Flow
     */
    @Query("SELECT * FROM schedules WHERE package_name = :packageName ORDER BY hour, minute")
    fun getSchedulesByPackageName(packageName: String): Flow<List<Schedule>>
    
    /**
     * 獲取特定重複模式的排程
     * @param repeatMode 重複模式值
     * @return Flow<List<Schedule>> 指定重複模式的排程 Flow
     */
    @Query("SELECT * FROM schedules WHERE repeat_mode = :repeatMode ORDER BY hour, minute")
    fun getSchedulesByRepeatMode(repeatMode: Int): Flow<List<Schedule>>
    
    /**
     * 更新排程的啟用狀態
     * @param id 排程 ID
     * @param isEnabled 是否啟用
     * @return 受影響的列數
     */
    @Query("UPDATE schedules SET is_enabled = :isEnabled, updated_time = :updatedTime WHERE id = :id")
    suspend fun updateEnabledStatus(id: Long, isEnabled: Boolean, updatedTime: Long = System.currentTimeMillis()): Int
    
    /**
     * 更新排程的最後執行時間
     * @param id 排程 ID
     * @param lastExecutedTime 最後執行時間
     * @return 受影響的列數
     */
    @Query("UPDATE schedules SET last_executed_time = :lastExecutedTime, updated_time = :updatedTime WHERE id = :id")
    suspend fun updateLastExecutedTime(id: Long, lastExecutedTime: Long, updatedTime: Long = System.currentTimeMillis()): Int
    
    /**
     * 更新排程的下次執行時間
     * @param id 排程 ID
     * @param nextExecutedTime 下次執行時間
     * @return 受影響的列數
     */
    @Query("UPDATE schedules SET next_executed_time = :nextExecutedTime, updated_time = :updatedTime WHERE id = :id")
    suspend fun updateNextExecutedTime(id: Long, nextExecutedTime: Long, updatedTime: Long = System.currentTimeMillis()): Int
    
    /**
     * 清空所有排程
     * @return 受影響的列數
     */
    @Query("DELETE FROM schedules")
    suspend fun deleteAllSchedules(): Int
    
    /**
     * 獲取排程總數
     * @return 排程總數
     */
    @Query("SELECT COUNT(*) FROM schedules")
    suspend fun getScheduleCount(): Int
    
    /**
     * 獲取啟用的排程總數
     * @return 啟用的排程總數
     */
    @Query("SELECT COUNT(*) FROM schedules WHERE is_enabled = 1")
    suspend fun getEnabledScheduleCount(): Int

    /**
     * 根據排程類型獲取排程
     * @param scheduleType 排程類型（0=APP, 1=URL）
     * @return Flow<List<Schedule>> 指定類型的排程 Flow
     */
    @Query("SELECT * FROM schedules WHERE schedule_type = :scheduleType ORDER BY hour, minute")
    fun getSchedulesByType(scheduleType: Int): Flow<List<Schedule>>

    /**
     * 根據URL獲取排程
     * @param url URL地址
     * @return Flow<List<Schedule>> 指定URL的排程 Flow
     */
    @Query("SELECT * FROM schedules WHERE url = :url ORDER BY hour, minute")
    fun getSchedulesByUrl(url: String): Flow<List<Schedule>>

    /**
     * 獲取所有APP類型的排程
     * @return Flow<List<Schedule>> APP類型排程的 Flow
     */
    @Query("SELECT * FROM schedules WHERE schedule_type = 0 ORDER BY hour, minute")
    fun getAppSchedules(): Flow<List<Schedule>>

    /**
     * 獲取所有URL類型的排程
     * @return Flow<List<Schedule>> URL類型排程的 Flow
     */
    @Query("SELECT * FROM schedules WHERE schedule_type = 1 ORDER BY hour, minute")
    fun getUrlSchedules(): Flow<List<Schedule>>

    /**
     * 更新排程的排序順序
     * @param id 排程 ID
     * @param sortOrder 新的排序順序
     * @return 受影響的列數
     */
    @Query("UPDATE schedules SET sort_order = :sortOrder, updated_time = :updatedTime WHERE id = :id")
    suspend fun updateSortOrder(id: Long, sortOrder: Int, updatedTime: Long = System.currentTimeMillis()): Int

    /**
     * 批量更新排程的排序順序
     * @param scheduleIds 排程 ID 列表
     * @param sortOrders 對應的排序順序列表
     */
    @Transaction
    suspend fun updateSortOrders(scheduleIds: List<Long>, sortOrders: List<Int>) {
        val currentTime = System.currentTimeMillis()
        scheduleIds.forEachIndexed { index, scheduleId ->
            updateSortOrder(scheduleId, sortOrders[index], currentTime)
        }
    }

    /**
     * 刪除舊的已禁用排程
     * @param beforeTime 時間戳，刪除此時間之前的已禁用排程
     * @return 受影響的列數
     */
    @Query("DELETE FROM schedules WHERE is_enabled = 0 AND updated_time < :beforeTime")
    suspend fun deleteOldDisabledSchedules(beforeTime: Long): Int
}