package com.example.autolaunch.model

import android.content.Context
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.first

/**
 * 資料庫使用範例
 * 展示如何使用 ScheduleRepository 進行各種數據操作
 * 
 * 注意：這個文件僅作為使用參考，不應在實際應用中直接使用
 */
class DatabaseUsageExample(private val context: Context) {
    
    private val repository = ScheduleRepository(context)
    
    /**
     * 範例：插入排程
     */
    suspend fun insertExampleSchedule(): Long {
        val schedule = Schedule(
            appName = "範例應用",
            packageName = "com.example.app",
            hour = 9,
            minute = 30,
            repeatMode = RepeatMode.DAILY.value,
            daysOfWeek = 0, // 每日模式不需要設定星期幾
            isEnabled = true
        )
        
        return repository.insertSchedule(schedule)
    }
    
    /**
     * 範例：獲取所有排程
     */
    suspend fun getAllSchedulesExample(): List<Schedule> {
        return repository.getAllSchedules().first()
    }
    
    /**
     * 範例：更新排程
     */
    suspend fun updateScheduleExample(scheduleId: Long) {
        // 先獲取現有排程
        val existingSchedule = repository.getScheduleById(scheduleId).first()
        
        existingSchedule?.let { schedule ->
            // 更新排程時間
            val updatedSchedule = schedule.copy(
                hour = 10,
                minute = 0,
                updatedTime = System.currentTimeMillis()
            )
            
            repository.updateSchedule(updatedSchedule)
        }
    }
    
    /**
     * 範例：刪除排程
     */
    suspend fun deleteScheduleExample(scheduleId: Long) {
        repository.deleteScheduleById(scheduleId)
    }
    
    /**
     * 範例：獲取啟用的排程
     */
    suspend fun getEnabledSchedulesExample(): List<Schedule> {
        return repository.getEnabledSchedules().first()
    }
    
    /**
     * 範例：切換排程啟用狀態
     */
    suspend fun toggleScheduleExample(scheduleId: Long) {
        val schedule = repository.getScheduleById(scheduleId).first()
        
        schedule?.let {
            repository.updateScheduleEnabledStatus(scheduleId, !it.isEnabled)
        }
    }
    
    /**
     * 範例：批量插入排程
     */
    suspend fun insertMultipleSchedulesExample() {
        val schedules = listOf(
            Schedule(
                appName = "音樂應用",
                packageName = "com.example.music",
                hour = 7,
                minute = 0,
                repeatMode = RepeatMode.WEEKLY.value,
                daysOfWeek = DaysOfWeek.MONDAY or DaysOfWeek.WEDNESDAY or DaysOfWeek.FRIDAY
            ),
            Schedule(
                appName = "新聞應用",
                packageName = "com.example.news",
                hour = 8,
                minute = 30,
                repeatMode = RepeatMode.DAILY.value
            ),
            Schedule(
                appName = "健身應用",
                packageName = "com.example.fitness",
                hour = 18,
                minute = 0,
                repeatMode = RepeatMode.WEEKLY.value,
                daysOfWeek = DaysOfWeek.TUESDAY or DaysOfWeek.THURSDAY or DaysOfWeek.SATURDAY
            )
        )
        
        repository.insertSchedules(schedules)
    }
    
    /**
     * 範例：查詢特定應用的排程
     */
    suspend fun getSchedulesForAppExample(packageName: String): List<Schedule> {
        return repository.getSchedulesByPackageName(packageName).first()
    }
    
    /**
     * 範例：獲取統計資訊
     */
    suspend fun getStatisticsExample(): Pair<Int, Int> {
        val totalCount = repository.getScheduleCount()
        val enabledCount = repository.getEnabledScheduleCount()
        
        return Pair(totalCount, enabledCount)
    }
    
    /**
     * 範例：更新執行時間記錄
     */
    suspend fun updateExecutionTimeExample(scheduleId: Long) {
        val currentTime = System.currentTimeMillis()
        
        // 更新最後執行時間
        repository.updateLastExecutedTime(scheduleId, currentTime)
        
        // 計算下次執行時間 (這裡只是簡單的範例，實際應用需要更複雜的邏輯)
        val nextExecutionTime = currentTime + 24 * 60 * 60 * 1000 // 24小時後
        repository.updateNextExecutedTime(scheduleId, nextExecutionTime)
    }
    
    /**
     * 示範在 Activity 或 Fragment 中如何使用
     * 
     * 在實際應用中，您應該：
     * 1. 在 ViewModel 中使用 Repository
     * 2. 使用 lifecycleScope 或 viewModelScope 來啟動協程
     * 3. 使用 LiveData 或 StateFlow 來觀察數據變化
     */
    fun demonstrateUsageInActivity() {
        // 在 Activity 中的使用範例：
        /*
        class MainActivity : AppCompatActivity() {
            private lateinit var repository: ScheduleRepository
            
            override fun onCreate(savedInstanceState: Bundle?) {
                super.onCreate(savedInstanceState)
                
                repository = ScheduleRepository(this)
                
                // 觀察所有排程的變化
                lifecycleScope.launch {
                    repository.getAllSchedules().collect { schedules ->
                        // 更新 UI
                        updateUI(schedules)
                    }
                }
                
                // 插入新排程
                lifecycleScope.launch {
                    val newScheduleId = repository.insertSchedule(...)
                    // 處理插入結果
                }
            }
        }
        */
    }
} 