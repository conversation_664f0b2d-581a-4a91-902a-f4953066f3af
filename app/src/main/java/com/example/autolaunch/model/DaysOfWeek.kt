package com.example.autolaunch.model

import android.content.Context
import com.example.autolaunch.R

/**
 * 星期幾位元遮罩輔助類別
 * 提供方便的方法來操作星期幾的位元遮罩
 */
object DaysOfWeek {

    // 位元遮罩常數
    const val MONDAY = 1      // 0000001
    const val TUESDAY = 2     // 0000010
    const val WEDNESDAY = 4   // 0000100
    const val THURSDAY = 8    // 0001000
    const val FRIDAY = 16     // 0010000
    const val SATURDAY = 32   // 0100000
    const val SUNDAY = 64     // 1000000

    // 常用組合
    const val WEEKDAYS = MONDAY or TUESDAY or WEDNESDAY or THURSDAY or FRIDAY
    const val WEEKEND = SATURDAY or SUNDAY
    const val ALL_DAYS = WEEKDAYS or WEEKEND

    /**
     * 獲取星期幾顯示名稱陣列
     */
    fun getDayNames(context: Context): Array<String> {
        return arrayOf(
            context.getString(R.string.day_name_monday),
            context.getString(R.string.day_name_tuesday),
            context.getString(R.string.day_name_wednesday),
            context.getString(R.string.day_name_thursday),
            context.getString(R.string.day_name_friday),
            context.getString(R.string.day_name_saturday),
            context.getString(R.string.day_name_sunday)
        )
    }

    /**
     * 獲取星期幾短名稱陣列
     */
    fun getShortDayNames(context: Context): Array<String> {
        return arrayOf(
            context.getString(R.string.day_short_monday),
            context.getString(R.string.day_short_tuesday),
            context.getString(R.string.day_short_wednesday),
            context.getString(R.string.day_short_thursday),
            context.getString(R.string.day_short_friday),
            context.getString(R.string.day_short_saturday),
            context.getString(R.string.day_short_sunday)
        )
    }
    
    /**
     * 根據星期幾編號取得位元遮罩
     * @param dayOfWeek 1=週一, 2=週二, ..., 7=週日
     */
    fun getDayMask(dayOfWeek: Int): Int {
        return when (dayOfWeek) {
            1 -> MONDAY
            2 -> TUESDAY
            3 -> WEDNESDAY
            4 -> THURSDAY
            5 -> FRIDAY
            6 -> SATURDAY
            7 -> SUNDAY
            else -> 0
        }
    }
    
    /**
     * 設定指定星期幾
     * @param current 目前的位元遮罩
     * @param dayOfWeek 要設定的星期幾 (1-7)
     * @param selected 是否選中
     */
    fun setDay(current: Int, dayOfWeek: Int, selected: Boolean): Int {
        val mask = getDayMask(dayOfWeek)
        return if (selected) {
            current or mask
        } else {
            current and mask.inv()
        }
    }
    
    /**
     * 檢查指定星期幾是否被選中
     * @param daysOfWeek 位元遮罩
     * @param dayOfWeek 要檢查的星期幾 (1-7)
     */
    fun isSelected(daysOfWeek: Int, dayOfWeek: Int): Boolean {
        val mask = getDayMask(dayOfWeek)
        return (daysOfWeek and mask) != 0
    }
    
    /**
     * 取得選中的星期幾列表
     * @param daysOfWeek 位元遮罩
     * @return 選中的星期幾列表 (1-7)
     */
    fun getSelectedDays(daysOfWeek: Int): List<Int> {
        val selectedDays = mutableListOf<Int>()
        for (day in 1..7) {
            if (isSelected(daysOfWeek, day)) {
                selectedDays.add(day)
            }
        }
        return selectedDays
    }
    
    /**
     * 根據星期幾列表建立位元遮罩
     * @param selectedDays 選中的星期幾列表 (1-7)
     */
    fun createMask(selectedDays: List<Int>): Int {
        var mask = 0
        selectedDays.forEach { day ->
            mask = mask or getDayMask(day)
        }
        return mask
    }
    
    /**
     * 取得顯示名稱
     * @param context 上下文
     * @param daysOfWeek 位元遮罩
     * @param useShortName 是否使用短名稱
     */
    fun getDisplayName(context: Context, daysOfWeek: Int, useShortName: Boolean = false): String {
        val selectedDays = getSelectedDays(daysOfWeek)
        val names = if (useShortName) getShortDayNames(context) else getDayNames(context)

        return when {
            selectedDays.isEmpty() -> context.getString(R.string.days_none)
            selectedDays.size == 7 -> context.getString(R.string.days_daily)
            selectedDays.size == 5 && selectedDays.all { it <= 5 } -> context.getString(R.string.days_weekdays)
            selectedDays.size == 2 && selectedDays.contains(6) && selectedDays.contains(7) -> context.getString(R.string.days_weekend)
            else -> selectedDays.joinToString("、") { names[it - 1] }
        }
    }
} 