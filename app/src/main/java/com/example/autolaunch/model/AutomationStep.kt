package com.example.autolaunch.model

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey

/**
 * 自動化步驟類型枚舉
 */
enum class StepType(val value: Int, val displayName: String) {
    CLICK(1, "點擊"),
    LONG_CLICK(2, "長按"),
    INPUT_TEXT(3, "輸入文字"),
    SWIPE(4, "滑動"),
    WAIT(5, "等待"),
    SCROLL(6, "滾動"),
    BACK(7, "返回"),
    HOME(8, "回到主頁"),
    SWIPE_UP(9, "向上滑動"),
    SWIPE_DOWN(10, "向下滑動"),
    SWIPE_LEFT(11, "向左滑動"),
    SWIPE_RIGHT(12, "向右滑動");

    companion object {
        fun fromValue(value: Int): StepType {
            return values().find { it.value == value } ?: CLICK
        }
    }
}

/**
 * UI元素定位方式枚舉
 */
enum class TargetType(val value: Int, val displayName: String) {
    TEXT(1, "文字"),
    ID(2, "元素ID"),
    COORDINATE(3, "座標"),
    DESCRIPTION(4, "描述"),
    CLASS_NAME(5, "類別名稱");

    companion object {
        fun fromValue(value: Int): TargetType {
            return values().find { it.value == value } ?: TEXT
        }
    }
}

/**
 * 自動化步驟實體類
 * 用於儲存排程的自動化操作步驟
 */
@Entity(
    tableName = "automation_steps",
    foreignKeys = [
        ForeignKey(
            entity = Schedule::class,
            parentColumns = ["id"],
            childColumns = ["schedule_id"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [
        Index(value = ["schedule_id"]),
        Index(value = ["schedule_id", "step_order"])
    ]
)
data class AutomationStep(
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "id")
    val id: Long = 0,
    
    @ColumnInfo(name = "schedule_id")
    val scheduleId: Long, // 關聯的排程ID
    
    @ColumnInfo(name = "step_order")
    val stepOrder: Int, // 執行順序 (從1開始)
    
    @ColumnInfo(name = "step_type")
    val stepType: Int, // 步驟類型
    
    @ColumnInfo(name = "step_name")
    val stepName: String? = null, // 步驟名稱/描述
    
    @ColumnInfo(name = "target_type")
    val targetType: Int = TargetType.TEXT.value, // 目標定位方式
    
    @ColumnInfo(name = "target_text")
    val targetText: String? = null, // 目標文字
    
    @ColumnInfo(name = "target_id")
    val targetId: String? = null, // 目標元素ID
    
    @ColumnInfo(name = "target_description")
    val targetDescription: String? = null, // 目標元素描述
    
    @ColumnInfo(name = "target_class_name")
    val targetClassName: String? = null, // 目標元素類別名稱
    
    @ColumnInfo(name = "target_x")
    val targetX: Int? = null, // 目標X座標
    
    @ColumnInfo(name = "target_y")
    val targetY: Int? = null, // 目標Y座標
    
    @ColumnInfo(name = "input_text")
    val inputText: String? = null, // 輸入文字內容
    
    @ColumnInfo(name = "wait_duration")
    val waitDuration: Long? = null, // 等待時間(毫秒)
    
    @ColumnInfo(name = "swipe_start_x")
    val swipeStartX: Int? = null, // 滑動起始X座標
    
    @ColumnInfo(name = "swipe_start_y")
    val swipeStartY: Int? = null, // 滑動起始Y座標
    
    @ColumnInfo(name = "swipe_end_x")
    val swipeEndX: Int? = null, // 滑動結束X座標
    
    @ColumnInfo(name = "swipe_end_y")
    val swipeEndY: Int? = null, // 滑動結束Y座標
    
    @ColumnInfo(name = "swipe_duration")
    val swipeDuration: Long? = null, // 滑動持續時間(毫秒)
    
    @ColumnInfo(name = "scroll_direction")
    val scrollDirection: Int? = null, // 滾動方向 (1=上, 2=下, 3=左, 4=右)
    
    @ColumnInfo(name = "scroll_amount")
    val scrollAmount: Int? = null, // 滾動量
    
    @ColumnInfo(name = "retry_count")
    val retryCount: Int = 3, // 重試次數
    
    @ColumnInfo(name = "timeout_ms")
    val timeoutMs: Long = 10000, // 超時時間(毫秒)
    
    @ColumnInfo(name = "is_enabled")
    val isEnabled: Boolean = true,
    
    @ColumnInfo(name = "is_optional")
    val isOptional: Boolean = false, // 是否為可選步驟（失敗時可跳過）
    
    @ColumnInfo(name = "created_time")
    val createdTime: Long = System.currentTimeMillis(),
    
    @ColumnInfo(name = "updated_time")
    val updatedTime: Long = System.currentTimeMillis()
) {
    
    /**
     * 取得步驟類型枚舉
     */
    fun getStepType(): StepType {
        return StepType.fromValue(stepType)
    }
    
    /**
     * 取得目標定位方式枚舉
     */
    fun getTargetType(): TargetType {
        return TargetType.fromValue(targetType)
    }
    
    /**
     * 檢查是否為點擊類型步驟
     */
    fun isClickStep(): Boolean {
        return getStepType() in listOf(StepType.CLICK, StepType.LONG_CLICK)
    }
    
    /**
     * 檢查是否為輸入類型步驟
     */
    fun isInputStep(): Boolean {
        return getStepType() == StepType.INPUT_TEXT
    }
    
    /**
     * 檢查是否為滑動類型步驟
     */
    fun isSwipeStep(): Boolean {
        return getStepType() in listOf(
            StepType.SWIPE, StepType.SWIPE_UP, StepType.SWIPE_DOWN,
            StepType.SWIPE_LEFT, StepType.SWIPE_RIGHT
        )
    }
    
    /**
     * 檢查是否為等待類型步驟
     */
    fun isWaitStep(): Boolean {
        return getStepType() == StepType.WAIT
    }
    
    /**
     * 檢查是否為系統操作步驟
     */
    fun isSystemStep(): Boolean {
        return getStepType() in listOf(StepType.BACK, StepType.HOME)
    }
    
    /**
     * 檢查步驟配置是否有效
     */
    fun isValid(): Boolean {
        return when (getStepType()) {
            StepType.CLICK, StepType.LONG_CLICK -> {
                when (getTargetType()) {
                    TargetType.TEXT -> !targetText.isNullOrBlank()
                    TargetType.ID -> !targetId.isNullOrBlank()
                    TargetType.COORDINATE -> targetX != null && targetY != null
                    TargetType.DESCRIPTION -> !targetDescription.isNullOrBlank()
                    TargetType.CLASS_NAME -> !targetClassName.isNullOrBlank()
                }
            }
            StepType.INPUT_TEXT -> {
                !inputText.isNullOrBlank() && when (getTargetType()) {
                    TargetType.TEXT -> !targetText.isNullOrBlank()
                    TargetType.ID -> !targetId.isNullOrBlank()
                    TargetType.COORDINATE -> targetX != null && targetY != null
                    TargetType.DESCRIPTION -> !targetDescription.isNullOrBlank()
                    TargetType.CLASS_NAME -> !targetClassName.isNullOrBlank()
                }
            }
            StepType.SWIPE -> {
                swipeStartX != null && swipeStartY != null &&
                swipeEndX != null && swipeEndY != null
            }
            StepType.WAIT -> waitDuration != null && waitDuration > 0
            StepType.SCROLL -> scrollDirection != null && scrollDirection in 1..4
            StepType.BACK, StepType.HOME -> true // 系統操作不需要額外參數
            StepType.SWIPE_UP, StepType.SWIPE_DOWN, 
            StepType.SWIPE_LEFT, StepType.SWIPE_RIGHT -> true // 預定義滑動方向
        }
    }
    
    /**
     * 取得步驟的顯示名稱
     */
    fun getDisplayName(): String {
        return stepName?.takeIf { it.isNotBlank() } ?: run {
            when (getStepType()) {
                StepType.CLICK -> "點擊 ${getTargetDisplayText()}"
                StepType.LONG_CLICK -> "長按 ${getTargetDisplayText()}"
                StepType.INPUT_TEXT -> "輸入「${inputText}」到 ${getTargetDisplayText()}"
                StepType.SWIPE -> "滑動 (${swipeStartX},${swipeStartY}) → (${swipeEndX},${swipeEndY})"
                StepType.WAIT -> "等待 ${waitDuration}ms"
                StepType.SCROLL -> "滾動 ${getScrollDirectionText()}"
                StepType.BACK -> "返回"
                StepType.HOME -> "回到主頁"
                StepType.SWIPE_UP -> "向上滑動"
                StepType.SWIPE_DOWN -> "向下滑動"
                StepType.SWIPE_LEFT -> "向左滑動"
                StepType.SWIPE_RIGHT -> "向右滑動"
            }
        }
    }
    
    /**
     * 取得目標元素的顯示文字
     */
    private fun getTargetDisplayText(): String {
        return when (getTargetType()) {
            TargetType.TEXT -> "「${targetText}」"
            TargetType.ID -> "ID:${targetId}"
            TargetType.COORDINATE -> "(${targetX},${targetY})"
            TargetType.DESCRIPTION -> "描述:${targetDescription}"
            TargetType.CLASS_NAME -> "類別:${targetClassName}"
        }
    }
    
    /**
     * 取得滾動方向文字
     */
    private fun getScrollDirectionText(): String {
        return when (scrollDirection) {
            1 -> "向上"
            2 -> "向下"
            3 -> "向左"
            4 -> "向右"
            else -> "未知方向"
        }
    }

    /**
     * 複製步驟並更新指定欄位
     */
    fun copy(
        id: Long = this.id,
        scheduleId: Long = this.scheduleId,
        stepOrder: Int = this.stepOrder,
        stepType: Int = this.stepType,
        stepName: String? = this.stepName,
        targetType: Int = this.targetType,
        targetText: String? = this.targetText,
        targetId: String? = this.targetId,
        targetDescription: String? = this.targetDescription,
        targetClassName: String? = this.targetClassName,
        targetX: Int? = this.targetX,
        targetY: Int? = this.targetY,
        inputText: String? = this.inputText,
        waitDuration: Long? = this.waitDuration,
        swipeStartX: Int? = this.swipeStartX,
        swipeStartY: Int? = this.swipeStartY,
        swipeEndX: Int? = this.swipeEndX,
        swipeEndY: Int? = this.swipeEndY,
        swipeDuration: Long? = this.swipeDuration,
        scrollDirection: Int? = this.scrollDirection,
        scrollAmount: Int? = this.scrollAmount,
        retryCount: Int = this.retryCount,
        timeoutMs: Long = this.timeoutMs,
        isEnabled: Boolean = this.isEnabled,
        isOptional: Boolean = this.isOptional,
        updatedTime: Long = System.currentTimeMillis()
    ): AutomationStep {
        return AutomationStep(
            id = id,
            scheduleId = scheduleId,
            stepOrder = stepOrder,
            stepType = stepType,
            stepName = stepName,
            targetType = targetType,
            targetText = targetText,
            targetId = targetId,
            targetDescription = targetDescription,
            targetClassName = targetClassName,
            targetX = targetX,
            targetY = targetY,
            inputText = inputText,
            waitDuration = waitDuration,
            swipeStartX = swipeStartX,
            swipeStartY = swipeStartY,
            swipeEndX = swipeEndX,
            swipeEndY = swipeEndY,
            swipeDuration = swipeDuration,
            scrollDirection = scrollDirection,
            scrollAmount = scrollAmount,
            retryCount = retryCount,
            timeoutMs = timeoutMs,
            isEnabled = isEnabled,
            isOptional = isOptional,
            createdTime = this.createdTime,
            updatedTime = updatedTime
        )
    }
}
