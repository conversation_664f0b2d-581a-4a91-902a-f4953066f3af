package com.example.autolaunch.model

import androidx.room.*
import kotlinx.coroutines.flow.Flow

/**
 * 自動化步驟數據存取物件 (DAO)
 * 定義對 AutomationStep 實體進行資料庫操作的方法
 */
@Dao
interface AutomationStepDao {
    
    /**
     * 插入一個新的自動化步驟
     * @param step 要插入的步驟物件
     * @return 插入的步驟 ID
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insert(step: AutomationStep): Long
    
    /**
     * 批量插入自動化步驟
     * @param steps 要插入的步驟列表
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAll(steps: List<AutomationStep>)
    
    /**
     * 更新現有自動化步驟
     * @param step 要更新的步驟物件
     * @return 受影響的列數
     */
    @Update
    suspend fun update(step: AutomationStep): Int
    
    /**
     * 刪除一個自動化步驟
     * @param step 要刪除的步驟物件
     * @return 受影響的列數
     */
    @Delete
    suspend fun delete(step: AutomationStep): Int
    
    /**
     * 根據 ID 刪除自動化步驟
     * @param id 步驟 ID
     * @return 受影響的列數
     */
    @Query("DELETE FROM automation_steps WHERE id = :id")
    suspend fun deleteById(id: Long): Int
    
    /**
     * 根據排程 ID 刪除所有相關的自動化步驟
     * @param scheduleId 排程 ID
     * @return 受影響的列數
     */
    @Query("DELETE FROM automation_steps WHERE schedule_id = :scheduleId")
    suspend fun deleteByScheduleId(scheduleId: Long): Int
    
    /**
     * 根據排程 ID 獲取所有自動化步驟，按執行順序排序
     * @param scheduleId 排程 ID
     * @return Flow<List<AutomationStep>> 步驟列表的 Flow
     */
    @Query("SELECT * FROM automation_steps WHERE schedule_id = :scheduleId ORDER BY step_order ASC")
    fun getStepsByScheduleId(scheduleId: Long): Flow<List<AutomationStep>>
    
    /**
     * 根據排程 ID 獲取所有啟用的自動化步驟，按執行順序排序
     * @param scheduleId 排程 ID
     * @return Flow<List<AutomationStep>> 啟用步驟列表的 Flow
     */
    @Query("SELECT * FROM automation_steps WHERE schedule_id = :scheduleId AND is_enabled = 1 ORDER BY step_order ASC")
    fun getEnabledStepsByScheduleId(scheduleId: Long): Flow<List<AutomationStep>>
    
    /**
     * 根據 ID 獲取特定自動化步驟
     * @param id 步驟 ID
     * @return Flow<AutomationStep?> 指定步驟的 Flow
     */
    @Query("SELECT * FROM automation_steps WHERE id = :id")
    fun getStepById(id: Long): Flow<AutomationStep?>
    
    /**
     * 獲取指定排程的步驟總數
     * @param scheduleId 排程 ID
     * @return 步驟總數
     */
    @Query("SELECT COUNT(*) FROM automation_steps WHERE schedule_id = :scheduleId")
    suspend fun getStepCountByScheduleId(scheduleId: Long): Int
    
    /**
     * 獲取指定排程的啟用步驟總數
     * @param scheduleId 排程 ID
     * @return 啟用步驟總數
     */
    @Query("SELECT COUNT(*) FROM automation_steps WHERE schedule_id = :scheduleId AND is_enabled = 1")
    suspend fun getEnabledStepCountByScheduleId(scheduleId: Long): Int
    
    /**
     * 更新步驟的啟用狀態
     * @param id 步驟 ID
     * @param isEnabled 是否啟用
     * @param updatedTime 更新時間
     * @return 受影響的列數
     */
    @Query("UPDATE automation_steps SET is_enabled = :isEnabled, updated_time = :updatedTime WHERE id = :id")
    suspend fun updateEnabledStatus(id: Long, isEnabled: Boolean, updatedTime: Long = System.currentTimeMillis()): Int
    
    /**
     * 更新步驟的執行順序
     * @param id 步驟 ID
     * @param stepOrder 新的執行順序
     * @param updatedTime 更新時間
     * @return 受影響的列數
     */
    @Query("UPDATE automation_steps SET step_order = :stepOrder, updated_time = :updatedTime WHERE id = :id")
    suspend fun updateStepOrder(id: Long, stepOrder: Int, updatedTime: Long = System.currentTimeMillis()): Int
    
    /**
     * 批量更新步驟的執行順序
     * @param stepIds 步驟 ID 列表
     * @param stepOrders 對應的執行順序列表
     */
    @Transaction
    suspend fun updateStepOrders(stepIds: List<Long>, stepOrders: List<Int>) {
        val currentTime = System.currentTimeMillis()
        stepIds.forEachIndexed { index, stepId ->
            updateStepOrder(stepId, stepOrders[index], currentTime)
        }
    }
    
    /**
     * 獲取指定排程中最大的執行順序
     * @param scheduleId 排程 ID
     * @return 最大執行順序，如果沒有步驟則返回 0
     */
    @Query("SELECT COALESCE(MAX(step_order), 0) FROM automation_steps WHERE schedule_id = :scheduleId")
    suspend fun getMaxStepOrder(scheduleId: Long): Int
    
    /**
     * 根據步驟類型獲取步驟
     * @param scheduleId 排程 ID
     * @param stepType 步驟類型
     * @return Flow<List<AutomationStep>> 指定類型的步驟列表 Flow
     */
    @Query("SELECT * FROM automation_steps WHERE schedule_id = :scheduleId AND step_type = :stepType ORDER BY step_order ASC")
    fun getStepsByType(scheduleId: Long, stepType: Int): Flow<List<AutomationStep>>
    
    /**
     * 檢查指定排程是否有自動化步驟
     * @param scheduleId 排程 ID
     * @return 是否有步驟
     */
    @Query("SELECT EXISTS(SELECT 1 FROM automation_steps WHERE schedule_id = :scheduleId)")
    suspend fun hasSteps(scheduleId: Long): Boolean
    
    /**
     * 檢查指定排程是否有啟用的自動化步驟
     * @param scheduleId 排程 ID
     * @return 是否有啟用的步驟
     */
    @Query("SELECT EXISTS(SELECT 1 FROM automation_steps WHERE schedule_id = :scheduleId AND is_enabled = 1)")
    suspend fun hasEnabledSteps(scheduleId: Long): Boolean
    
    /**
     * 清空指定排程的所有自動化步驟
     * @param scheduleId 排程 ID
     * @return 受影響的列數
     */
    @Query("DELETE FROM automation_steps WHERE schedule_id = :scheduleId")
    suspend fun clearStepsByScheduleId(scheduleId: Long): Int
    
    /**
     * 清空所有自動化步驟
     * @return 受影響的列數
     */
    @Query("DELETE FROM automation_steps")
    suspend fun deleteAllSteps(): Int
    
    /**
     * 複製指定排程的所有步驟到新排程
     * @param sourceScheduleId 源排程 ID
     * @param targetScheduleId 目標排程 ID
     */
    @Transaction
    suspend fun copyStepsToSchedule(sourceScheduleId: Long, targetScheduleId: Long) {
        val sourceSteps = getStepsByScheduleId(sourceScheduleId)
        // 注意：這裡需要在實際使用時轉換為 suspend 函數或在 Repository 層處理
        // 因為 Flow 需要收集才能獲取數據
    }
    
    /**
     * 獲取所有包含自動化步驟的排程 ID
     * @return Flow<List<Long>> 包含步驟的排程 ID 列表
     */
    @Query("SELECT DISTINCT schedule_id FROM automation_steps")
    fun getScheduleIdsWithSteps(): Flow<List<Long>>
    
    /**
     * 根據目標文字搜索步驟
     * @param searchText 搜索文字
     * @return Flow<List<AutomationStep>> 匹配的步驟列表
     */
    @Query("""
        SELECT * FROM automation_steps 
        WHERE target_text LIKE '%' || :searchText || '%' 
           OR input_text LIKE '%' || :searchText || '%'
           OR step_name LIKE '%' || :searchText || '%'
        ORDER BY schedule_id, step_order ASC
    """)
    fun searchSteps(searchText: String): Flow<List<AutomationStep>>
    
    /**
     * 獲取指定時間範圍內創建的步驟
     * @param startTime 開始時間
     * @param endTime 結束時間
     * @return Flow<List<AutomationStep>> 時間範圍內的步驟列表
     */
    @Query("SELECT * FROM automation_steps WHERE created_time BETWEEN :startTime AND :endTime ORDER BY created_time DESC")
    fun getStepsByTimeRange(startTime: Long, endTime: Long): Flow<List<AutomationStep>>
    
    /**
     * 獲取最近創建的步驟
     * @param limit 限制數量
     * @return Flow<List<AutomationStep>> 最近創建的步驟列表
     */
    @Query("SELECT * FROM automation_steps ORDER BY created_time DESC LIMIT :limit")
    fun getRecentSteps(limit: Int = 10): Flow<List<AutomationStep>>
}
