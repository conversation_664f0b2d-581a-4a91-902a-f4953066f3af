package com.example.autolaunch.model

import android.graphics.drawable.Drawable

/**
 * 應用程式資訊數據類別
 * 用於表示裝置上安裝的應用程式資訊
 */
data class AppInfo(
    val appName: String,      // 應用程式顯示名稱
    val packageName: String,  // 應用程式包名
    val icon: Drawable? = null, // 應用程式圖示 (動態載入，不儲存在資料庫)
    val isSystemApp: Boolean = false, // 是否為系統應用程式
    val versionName: String = "", // 版本名稱
    val versionCode: Long = 0L, // 版本代碼
    val installTime: Long = 0L, // 安裝時間
    val lastUpdateTime: Long = 0L // 最後更新時間
) {
    
    /**
     * 檢查應用程式是否可啟動
     */
    fun isLaunchable(): Boolean {
        return packageName.isNotEmpty() && !isSystemApp
    }
    
    /**
     * 取得簡短的版本資訊
     */
    fun getVersionInfo(): String {
        return if (versionName.isNotEmpty()) {
            versionName
        } else {
            "v$versionCode"
        }
    }
    
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        
        other as AppInfo
        
        return packageName == other.packageName
    }
    
    override fun hashCode(): Int {
        return packageName.hashCode()
    }
} 