package com.example.autolaunch.model

import com.google.gson.annotations.SerializedName
import java.util.*

/**
 * 備份數據模型
 * 用於序列化和反序列化排程備份數據
 */
data class BackupData(
    @SerializedName("version")
    val version: String = "1.0",
    
    @SerializedName("timestamp")
    val timestamp: Long = System.currentTimeMillis(),
    
    @SerializedName("device_info")
    val deviceInfo: DeviceInfo,
    
    @SerializedName("schedules")
    val schedules: List<ScheduleBackup>,
    
    @SerializedName("total_schedules")
    val totalSchedules: Int = schedules.size
) {
    /**
     * 驗證備份數據是否有效
     */
    fun isValid(): Boolean {
        return version.isNotBlank() && 
               timestamp > 0 && 
               schedules.isNotEmpty() && 
               totalSchedules == schedules.size &&
               schedules.all { it.isValid() }
    }
    
    /**
     * 獲取備份創建時間的格式化字符串
     */
    fun getFormattedTimestamp(): String {
        val date = Date(timestamp)
        return android.text.format.DateFormat.format("yyyy-MM-dd HH:mm:ss", date).toString()
    }
    
    /**
     * 獲取備份文件建議名稱
     */
    fun getSuggestedFileName(): String {
        val date = Date(timestamp)
        val dateStr = android.text.format.DateFormat.format("yyyyMMdd_HHmmss", date).toString()
        return "AutoLaunch_Backup_${dateStr}.json"
    }
}

/**
 * 設備信息
 */
data class DeviceInfo(
    @SerializedName("app_version")
    val appVersion: String,
    
    @SerializedName("android_version")
    val androidVersion: String,
    
    @SerializedName("device_model")
    val deviceModel: String,
    
    @SerializedName("device_manufacturer")
    val deviceManufacturer: String = android.os.Build.MANUFACTURER,
    
    @SerializedName("backup_created_time")
    val backupCreatedTime: String = Date().toString()
)

/**
 * 排程備份數據
 * 對應 Schedule 實體的備份格式
 */
data class ScheduleBackup(
    @SerializedName("id")
    val id: Long = 0,
    
    @SerializedName("schedule_type")
    val scheduleType: Int,
    
    @SerializedName("app_name")
    val appName: String? = null,
    
    @SerializedName("task_name")
    val taskName: String? = null,
    
    @SerializedName("package_name")
    val packageName: String? = null,
    
    @SerializedName("url")
    val url: String? = null,
    
    @SerializedName("url_title")
    val urlTitle: String? = null,
    
    @SerializedName("hour")
    val hour: Int,
    
    @SerializedName("minute")
    val minute: Int,
    
    @SerializedName("repeat_mode")
    val repeatMode: Int,
    
    @SerializedName("days_of_week")
    val daysOfWeek: Int = 0,
    
    @SerializedName("single_execute_date")
    val singleExecuteDate: Long? = null,
    
    @SerializedName("is_enabled")
    val isEnabled: Boolean = true,
    
    @SerializedName("created_time")
    val createdTime: Long,
    
    @SerializedName("updated_time")
    val updatedTime: Long
) {
    /**
     * 驗證排程備份數據是否有效
     */
    fun isValid(): Boolean {
        return when (ScheduleType.fromValue(scheduleType)) {
            ScheduleType.APP -> {
                !appName.isNullOrBlank() && !packageName.isNullOrBlank()
            }
            ScheduleType.URL -> {
                !url.isNullOrBlank() && isValidUrl(url)
            }
        } && hour in 0..23 && minute in 0..59
    }
    
    /**
     * 檢查 URL 是否有效
     */
    private fun isValidUrl(url: String): Boolean {
        return try {
            val urlPattern = Regex("^(https?|ftp)://[^\\s/$.?#].[^\\s]*$", RegexOption.IGNORE_CASE)
            urlPattern.matches(url)
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 轉換為 Schedule 實體
     */
    fun toSchedule(): Schedule {
        return Schedule(
            id = 0, // 匯入時重新生成ID
            scheduleType = scheduleType,
            appName = appName,
            taskName = taskName,
            packageName = packageName,
            url = url,
            urlTitle = urlTitle,
            hour = hour,
            minute = minute,
            repeatMode = repeatMode,
            daysOfWeek = daysOfWeek,
            singleExecuteDate = singleExecuteDate,
            isEnabled = isEnabled,
            createdTime = System.currentTimeMillis(), // 使用當前時間作為匯入時間
            updatedTime = System.currentTimeMillis()
        )
    }
    
    companion object {
        /**
         * 從 Schedule 實體創建備份數據
         */
        fun fromSchedule(schedule: Schedule): ScheduleBackup {
            return ScheduleBackup(
                id = schedule.id,
                scheduleType = schedule.scheduleType,
                appName = schedule.appName,
                taskName = schedule.taskName,
                packageName = schedule.packageName,
                url = schedule.url,
                urlTitle = schedule.urlTitle,
                hour = schedule.hour,
                minute = schedule.minute,
                repeatMode = schedule.repeatMode,
                daysOfWeek = schedule.daysOfWeek,
                singleExecuteDate = schedule.singleExecuteDate,
                isEnabled = schedule.isEnabled,
                createdTime = schedule.createdTime,
                updatedTime = schedule.updatedTime
            )
        }
    }
}

/**
 * 備份結果
 */
data class BackupResult(
    val success: Boolean,
    val message: String,
    val filePath: String? = null,
    val fileSize: Long = 0,
    val scheduleCount: Int = 0,
    val error: Throwable? = null
)

/**
 * 匯入結果
 */
data class RestoreResult(
    val success: Boolean,
    val message: String,
    val importedCount: Int = 0,
    val skippedCount: Int = 0,
    val errorCount: Int = 0,
    val errors: List<String> = emptyList()
)

/**
 * 備份文件信息
 */
data class BackupFileInfo(
    val fileName: String,
    val filePath: String,
    val fileSize: Long,
    val createdTime: Long,
    val scheduleCount: Int,
    val appVersion: String,
    val deviceModel: String,
    val isValid: Boolean
) {
    /**
     * 獲取格式化的文件大小
     */
    fun getFormattedFileSize(): String {
        return when {
            fileSize < 1024 -> "$fileSize B"
            fileSize < 1024 * 1024 -> "${fileSize / 1024} KB"
            fileSize < 1024 * 1024 * 1024 -> "${fileSize / (1024 * 1024)} MB"
            else -> "${fileSize / (1024 * 1024 * 1024)} GB"
        }
    }

    /**
     * 獲取格式化的創建時間
     */
    fun getFormattedCreatedTime(): String {
        val date = java.util.Date(createdTime)
        return android.text.format.DateFormat.format("yyyy-MM-dd HH:mm:ss", date).toString()
    }
}
