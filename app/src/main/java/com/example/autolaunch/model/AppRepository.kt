package com.example.autolaunch.model

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.content.pm.ResolveInfo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 應用程式資料儲存庫
 * 負責獲取和管理裝置上的應用程式資訊
 */
class AppRepository(private val context: Context) {
    
    private val packageManager: PackageManager = context.packageManager
    
    /**
     * 獲取所有可啟動的應用程式列表
     * @return 可啟動的應用程式列表
     */
    suspend fun getAllLaunchableApps(): List<AppInfo> = withContext(Dispatchers.IO) {
        try {
            val intent = Intent(Intent.ACTION_MAIN, null).apply {
                addCategory(Intent.CATEGORY_LAUNCHER)
            }
            
            val resolveInfoList = packageManager.queryIntentActivities(intent, 0)
            
            resolveInfoList.mapNotNull { resolveInfo ->
                try {
                    createAppInfoFromResolveInfo(resolveInfo)
                } catch (e: Exception) {
                    // 如果無法獲取某個應用程式的資訊，跳過它
                    null
                }
            }.sortedBy { it.appName.lowercase() }
            
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    /**
     * 根據包名獲取應用程式資訊
     * @param packageName 應用程式包名
     * @return 應用程式資訊，如果找不到則返回 null
     */
    suspend fun getAppInfoByPackageName(packageName: String): AppInfo? = withContext(Dispatchers.IO) {
        try {
            val intent = Intent(Intent.ACTION_MAIN, null).apply {
                addCategory(Intent.CATEGORY_LAUNCHER)
                setPackage(packageName)
            }
            
            val resolveInfoList = packageManager.queryIntentActivities(intent, 0)
            val resolveInfo = resolveInfoList.firstOrNull()
            
            resolveInfo?.let { createAppInfoFromResolveInfo(it) }
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 搜尋應用程式
     * @param query 搜尋關鍵字
     * @return 符合搜尋條件的應用程式列表
     */
    suspend fun searchApps(query: String): List<AppInfo> = withContext(Dispatchers.IO) {
        if (query.isBlank()) {
            getAllLaunchableApps()
        } else {
            val allApps = getAllLaunchableApps()
            allApps.filter { app ->
                app.appName.contains(query, ignoreCase = true) ||
                app.packageName.contains(query, ignoreCase = true)
            }
        }
    }
    
    /**
     * 從 ResolveInfo 創建 AppInfo
     */
    private fun createAppInfoFromResolveInfo(resolveInfo: ResolveInfo): AppInfo {
        val activityInfo = resolveInfo.activityInfo
        val applicationInfo = activityInfo.applicationInfo
        
        val appName = resolveInfo.loadLabel(packageManager).toString()
        val packageName = activityInfo.packageName
        val icon = resolveInfo.loadIcon(packageManager)
        
        // 獲取應用程式詳細資訊
        val packageInfo = try {
            packageManager.getPackageInfo(packageName, 0)
        } catch (e: Exception) {
            null
        }
        
        val isSystemApp = (applicationInfo.flags and android.content.pm.ApplicationInfo.FLAG_SYSTEM) != 0
        
        return AppInfo(
            appName = appName,
            packageName = packageName,
            icon = icon,
            isSystemApp = isSystemApp,
            versionName = packageInfo?.versionName ?: "",
            versionCode = packageInfo?.longVersionCode ?: 0L,
            installTime = packageInfo?.firstInstallTime ?: 0L,
            lastUpdateTime = packageInfo?.lastUpdateTime ?: 0L
        )
    }
} 