package com.example.autolaunch.model

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import java.util.*

/**
 * 排程類型枚舉
 */
enum class ScheduleType(val value: Int) {
    APP(0),    // 應用程式排程
    URL(1);    // URL 排程

    companion object {
        fun fromValue(value: Int): ScheduleType {
            return values().find { it.value == value } ?: APP
        }
    }
}

/**
 * 排程數據模型
 * 用於儲存應用程式排程的核心資訊
 */
@Entity(tableName = "schedules")
data class Schedule(
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "id")
    val id: Long = 0,

    @ColumnInfo(name = "schedule_type")
    val scheduleType: Int = ScheduleType.APP.value, // 排程類型：0=APP, 1=URL

    @ColumnInfo(name = "app_name")
    val appName: String? = null, // APP 類型時必填

    @ColumnInfo(name = "task_name")
    val taskName: String? = null, // 自定義任務名稱

    @ColumnInfo(name = "package_name")
    val packageName: String? = null, // APP 類型時必填

    @ColumnInfo(name = "url")
    val url: String? = null, // URL 類型時必填

    @ColumnInfo(name = "url_title")
    val urlTitle: String? = null, // URL 的自定義標題
    
    @ColumnInfo(name = "hour")
    val hour: Int, // 24小時制 (0-23)
    
    @ColumnInfo(name = "minute")
    val minute: Int, // 分鐘 (0-59)
    
    @ColumnInfo(name = "repeat_mode")
    val repeatMode: Int, // 對應 RepeatMode.value
    
    @ColumnInfo(name = "days_of_week")
    val daysOfWeek: Int = 0, // 位元遮罩：1=週一, 2=週二, 4=週三, 8=週四, 16=週五, 32=週六, 64=週日
    
    @ColumnInfo(name = "single_execute_date")
    val singleExecuteDate: Long? = null, // Unix timestamp，適用於單次模式
    
    @ColumnInfo(name = "is_enabled")
    val isEnabled: Boolean = true,
    
    @ColumnInfo(name = "last_executed_time")
    val lastExecutedTime: Long? = null, // Unix timestamp
    
    @ColumnInfo(name = "next_executed_time")
    val nextExecutedTime: Long? = null, // Unix timestamp
    
    @ColumnInfo(name = "created_time")
    val createdTime: Long = System.currentTimeMillis(), // 創建時間
    
    @ColumnInfo(name = "updated_time")
    val updatedTime: Long = System.currentTimeMillis(), // 更新時間

    @ColumnInfo(name = "sort_order")
    val sortOrder: Int = 0 // 自訂排序順序，數值越小越靠前
) {
    
    /**
     * 取得排程類型枚舉
     */
    fun getScheduleTypeEnum(): ScheduleType {
        return ScheduleType.fromValue(scheduleType)
    }

    /**
     * 檢查是否為 APP 類型排程
     */
    fun isAppSchedule(): Boolean {
        return getScheduleTypeEnum() == ScheduleType.APP
    }

    /**
     * 檢查是否為 URL 類型排程
     */
    fun isUrlSchedule(): Boolean {
        return getScheduleTypeEnum() == ScheduleType.URL
    }

    /**
     * 取得重複模式枚舉
     */
    fun getRepeatModeEnum(): RepeatMode {
        return RepeatMode.fromValue(repeatMode)
    }

    /**
     * 檢查是否為週期性排程
     */
    fun isRecurring(): Boolean {
        return getRepeatModeEnum() != RepeatMode.ONCE
    }
    
    /**
     * 取得格式化的時間字串
     */
    fun getFormattedTime(): String {
        return String.format("%02d:%02d", hour, minute)
    }
    
    /**
     * 取得顯示名稱
     */
    fun getDisplayName(): String {
        return when {
            !taskName.isNullOrBlank() -> taskName
            isAppSchedule() -> appName ?: "未知應用程式"
            isUrlSchedule() -> urlTitle ?: url ?: "未知 URL"
            else -> "未知排程"
        }
    }
    
    /**
     * 檢查指定的星期幾是否被選中 (適用於每週模式)
     * @param dayOfWeek 1=週一, 2=週二, ..., 7=週日
     */
    fun isDayOfWeekSelected(dayOfWeek: Int): Boolean {
        if (dayOfWeek < 1 || dayOfWeek > 7) return false
        val bitMask = 1 shl (dayOfWeek - 1)
        return (daysOfWeek and bitMask) != 0
    }
    
    /**
     * 取得選中的星期幾列表
     */
    fun getSelectedDaysOfWeek(): List<Int> {
        val selectedDays = mutableListOf<Int>()
        for (day in 1..7) {
            if (isDayOfWeekSelected(day)) {
                selectedDays.add(day)
            }
        }
        return selectedDays
    }
    
    /**
     * 取得星期幾的顯示名稱
     */
    fun getDaysOfWeekDisplayName(context: android.content.Context): String {
        if (getRepeatModeEnum() != RepeatMode.WEEKLY) return ""

        val dayNames = DaysOfWeek.getShortDayNames(context)
        val selectedDays = getSelectedDaysOfWeek()

        return if (selectedDays.size == 7) {
            context.getString(com.example.autolaunch.R.string.days_daily)
        } else if (selectedDays.size == 5 && selectedDays.all { it <= 5 }) {
            context.getString(com.example.autolaunch.R.string.days_weekdays)
        } else if (selectedDays.size == 2 && selectedDays.contains(6) && selectedDays.contains(7)) {
            context.getString(com.example.autolaunch.R.string.days_weekend)
        } else {
            // 如果選擇的天數超過2天，只顯示最近期的一天加上省略號
            if (selectedDays.size > 2) {
                val nextDay = getNextSelectedDay(selectedDays)
                "週${dayNames[nextDay - 1]}..."
            } else {
                selectedDays.joinToString("、") { "週${dayNames[it - 1]}" }
            }
        }
    }

    /**
     * 取得下一個最近期的選中星期
     */
    private fun getNextSelectedDay(selectedDays: List<Int>): Int {
        val calendar = Calendar.getInstance()
        val currentDayOfWeek = calendar.get(Calendar.DAY_OF_WEEK)
        // 將 Calendar 的星期轉換為我們的格式 (1=週一, 7=週日)
        val currentDay = if (currentDayOfWeek == Calendar.SUNDAY) 7 else currentDayOfWeek - 1

        // 找到當前時間之後最近的選中日期
        for (i in 0..6) {
            val checkDay = ((currentDay - 1 + i) % 7) + 1
            if (selectedDays.contains(checkDay)) {
                return checkDay
            }
        }

        // 如果沒找到，返回第一個選中的日期
        return selectedDays.first()
    }
    
    /**
     * 計算下次執行時間
     * @return Unix timestamp 或 null
     */
    fun calculateNextExecuteTime(): Long? {
        if (!isEnabled) return null
        
        val calendar = Calendar.getInstance()
        val now = System.currentTimeMillis()
        
        return when (getRepeatModeEnum()) {
            RepeatMode.ONCE -> {
                singleExecuteDate?.let { date ->
                    calendar.timeInMillis = date
                    calendar.set(Calendar.HOUR_OF_DAY, hour)
                    calendar.set(Calendar.MINUTE, minute)
                    calendar.set(Calendar.SECOND, 0)
                    calendar.set(Calendar.MILLISECOND, 0)
                    
                    val executeTime = calendar.timeInMillis
                    if (executeTime > now) executeTime else null
                }
            }
            
            RepeatMode.DAILY -> {
                calendar.set(Calendar.HOUR_OF_DAY, hour)
                calendar.set(Calendar.MINUTE, minute)
                calendar.set(Calendar.SECOND, 0)
                calendar.set(Calendar.MILLISECOND, 0)
                
                var executeTime = calendar.timeInMillis
                if (executeTime <= now) {
                    // 如果今天的時間已過，設定為明天
                    calendar.add(Calendar.DAY_OF_MONTH, 1)
                    executeTime = calendar.timeInMillis
                }
                executeTime
            }
            
            RepeatMode.WEEKLY -> {
                val selectedDays = getSelectedDaysOfWeek()
                if (selectedDays.isEmpty()) return null
                
                calendar.set(Calendar.HOUR_OF_DAY, hour)
                calendar.set(Calendar.MINUTE, minute)
                calendar.set(Calendar.SECOND, 0)
                calendar.set(Calendar.MILLISECOND, 0)
                
                val currentDayOfWeek = calendar.get(Calendar.DAY_OF_WEEK)
                val currentDayIndex = if (currentDayOfWeek == Calendar.SUNDAY) 7 else currentDayOfWeek - 1
                
                // 找到最近的執行日
                var nextDay: Int? = null
                val todayTime = calendar.timeInMillis
                
                // 檢查今天是否為執行日且時間未過
                if (selectedDays.contains(currentDayIndex) && todayTime > now) {
                    nextDay = currentDayIndex
                } else {
                    // 找下一個執行日
                    for (i in 1..7) {
                        val checkDay = (currentDayIndex + i - 1) % 7 + 1
                        if (selectedDays.contains(checkDay)) {
                            nextDay = checkDay
                            calendar.add(Calendar.DAY_OF_MONTH, i)
                            break
                        }
                    }
                }
                
                nextDay?.let { calendar.timeInMillis }
            }
            
            RepeatMode.MONTHLY -> {
                // 暫時不支援月重複
                null
            }
        }
    }
    
    /**
     * 檢查排程是否已過期（僅適用於單次排程）
     */
    fun isExpired(): Boolean {
        if (getRepeatModeEnum() != RepeatMode.ONCE) return false
        return calculateNextExecuteTime() == null
    }

    /**
     * 驗證排程數據是否有效
     */
    fun isValid(): Boolean {
        return when (getScheduleTypeEnum()) {
            ScheduleType.APP -> {
                !appName.isNullOrBlank() && !packageName.isNullOrBlank()
            }
            ScheduleType.URL -> {
                !url.isNullOrBlank() && isValidUrl(url)
            }
        }
    }

    /**
     * 檢查 URL 是否有效
     */
    private fun isValidUrl(url: String): Boolean {
        return try {
            val urlPattern = Regex("^(https?|ftp)://[^\\s/$.?#].[^\\s]*$", RegexOption.IGNORE_CASE)
            urlPattern.matches(url)
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 複製排程並更新指定欄位
     */
    fun copy(
        id: Long = this.id,
        scheduleType: Int = this.scheduleType,
        appName: String? = this.appName,
        taskName: String? = this.taskName,
        packageName: String? = this.packageName,
        url: String? = this.url,
        urlTitle: String? = this.urlTitle,
        hour: Int = this.hour,
        minute: Int = this.minute,
        repeatMode: Int = this.repeatMode,
        daysOfWeek: Int = this.daysOfWeek,
        singleExecuteDate: Long? = this.singleExecuteDate,
        isEnabled: Boolean = this.isEnabled,
        lastExecutedTime: Long? = this.lastExecutedTime,
        nextExecutedTime: Long? = this.nextExecutedTime,
        updatedTime: Long = System.currentTimeMillis()
    ): Schedule {
        return Schedule(
            id = id,
            scheduleType = scheduleType,
            appName = appName,
            taskName = taskName,
            packageName = packageName,
            url = url,
            urlTitle = urlTitle,
            hour = hour,
            minute = minute,
            repeatMode = repeatMode,
            daysOfWeek = daysOfWeek,
            singleExecuteDate = singleExecuteDate,
            isEnabled = isEnabled,
            lastExecutedTime = lastExecutedTime,
            nextExecutedTime = nextExecutedTime,
            createdTime = this.createdTime,
            updatedTime = updatedTime
        )
    }
} 