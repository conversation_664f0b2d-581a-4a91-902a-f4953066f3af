package com.example.autolaunch.model

import android.content.Context
import androidx.room.*
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase

/**
 * 應用程式主資料庫
 * 使用 Room Persistence Library 管理本地數據存儲
 */
@Database(
    entities = [Schedule::class, SystemLog::class],
    version = 5,
    exportSchema = false
)
abstract class AppDatabase : RoomDatabase() {
    
    /**
     * 獲取 ScheduleDao 實例
     */
    abstract fun scheduleDao(): ScheduleDao

    /**
     * 獲取 SystemLogDao 實例
     */
    abstract fun systemLogDao(): SystemLogDao
    
    companion object {
        /**
         * 資料庫名稱
         */
        const val DATABASE_NAME = "autolaunch_database"
        
        /**
         * 數據庫遷移：版本1到版本2
         * 添加task_name字段
         */
        private val MIGRATION_1_2 = object : Migration(1, 2) {
            override fun migrate(database: SupportSQLiteDatabase) {
                database.execSQL("ALTER TABLE schedules ADD COLUMN task_name TEXT")
            }
        }

        /**
         * 數據庫遷移：版本2到版本3
         * 添加URL排程支持的字段
         */
        private val MIGRATION_2_3 = object : Migration(2, 3) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // 添加排程類型字段，默認為0（APP類型）
                database.execSQL("ALTER TABLE schedules ADD COLUMN schedule_type INTEGER NOT NULL DEFAULT 0")
                // 添加URL字段
                database.execSQL("ALTER TABLE schedules ADD COLUMN url TEXT")
                // 添加URL標題字段
                database.execSQL("ALTER TABLE schedules ADD COLUMN url_title TEXT")
                // 將現有的app_name和package_name字段改為可空
                // 注意：SQLite不支持直接修改列的約束，所以我們需要重建表
                database.execSQL("""
                    CREATE TABLE schedules_new (
                        id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
                        schedule_type INTEGER NOT NULL DEFAULT 0,
                        app_name TEXT,
                        task_name TEXT,
                        package_name TEXT,
                        url TEXT,
                        url_title TEXT,
                        hour INTEGER NOT NULL,
                        minute INTEGER NOT NULL,
                        repeat_mode INTEGER NOT NULL,
                        days_of_week INTEGER NOT NULL DEFAULT 0,
                        single_execute_date INTEGER,
                        is_enabled INTEGER NOT NULL DEFAULT 1,
                        last_executed_time INTEGER,
                        next_executed_time INTEGER,
                        created_time INTEGER NOT NULL DEFAULT 0,
                        updated_time INTEGER NOT NULL DEFAULT 0
                    )
                """.trimIndent())

                // 複製現有數據
                database.execSQL("""
                    INSERT INTO schedules_new (
                        id, schedule_type, app_name, task_name, package_name, url, url_title,
                        hour, minute, repeat_mode, days_of_week, single_execute_date,
                        is_enabled, last_executed_time, next_executed_time, created_time, updated_time
                    )
                    SELECT
                        id, 0, app_name, task_name, package_name, NULL, NULL,
                        hour, minute, repeat_mode, days_of_week, single_execute_date,
                        is_enabled, last_executed_time, next_executed_time, created_time, updated_time
                    FROM schedules
                """.trimIndent())

                // 刪除舊表
                database.execSQL("DROP TABLE schedules")
                // 重命名新表
                database.execSQL("ALTER TABLE schedules_new RENAME TO schedules")
            }
        }

        /**
         * 數據庫遷移：版本3到版本4
         * 添加系統日志表
         */
        private val MIGRATION_3_4 = object : Migration(3, 4) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // 創建系統日志表
                database.execSQL("""
                    CREATE TABLE system_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
                        log_type INTEGER NOT NULL,
                        action_type INTEGER NOT NULL,
                        schedule_id INTEGER,
                        schedule_name TEXT,
                        message TEXT NOT NULL,
                        timestamp INTEGER NOT NULL,
                        details TEXT
                    )
                """.trimIndent())
            }
        }

        /**
         * 數據庫遷移：版本4到版本5
         * 添加排程排序字段
         */
        private val MIGRATION_4_5 = object : Migration(4, 5) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // 添加排序字段，默認值為0
                database.execSQL("ALTER TABLE schedules ADD COLUMN sort_order INTEGER NOT NULL DEFAULT 0")
            }
        }
        
        /**
         * 資料庫實例 (單例)
         */
        @Volatile
        private var INSTANCE: AppDatabase? = null
        
        /**
         * 獲取資料庫實例 (單例模式)
         * @param context 應用程式上下文
         * @return AppDatabase 實例
         */
        fun getDatabase(context: Context): AppDatabase {
            // 使用雙重檢查鎖定模式確保執行緒安全
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    AppDatabase::class.java,
                    DATABASE_NAME
                )
                .addCallback(DatabaseCallback())
                .addMigrations(MIGRATION_1_2, MIGRATION_2_3, MIGRATION_3_4, MIGRATION_4_5) // 添加數據庫遷移
                .fallbackToDestructiveMigration(false) // 如果迁移失败，重建数据库
                .build()
                
                INSTANCE = instance
                instance
            }
        }
        
        /**
         * 銷毀資料庫實例 (主要用於測試)
         */
        fun destroyInstance() {
            INSTANCE = null
        }
    }
    
    /**
     * 資料庫回調
     * 處理資料庫創建和開啟時的操作
     */
    private class DatabaseCallback : RoomDatabase.Callback() {
        
        override fun onCreate(db: SupportSQLiteDatabase) {
            super.onCreate(db)
            // 資料庫首次創建時的操作
            // 可以在此處添加預設數據或執行初始化操作
        }
        
        override fun onOpen(db: SupportSQLiteDatabase) {
            super.onOpen(db)
            // 每次打開資料庫時的操作
            // 可以在此處執行數據庫維護或驗證操作
        }
    }
}

/**
 * 類型轉換器
 * 用於將複雜類型轉換為 Room 支援的基本類型
 * 
 * 注意：目前 Schedule 實體使用的都是基本類型，暫時不需要額外的轉換器
 * 如果未來需要存儲 Date、List 等複雜類型，可以取消註解下面的轉換方法
 * 並在 AppDatabase 上添加 @TypeConverters(Converters::class) 註解
 */
/*
class Converters {
    // 範例：Date 轉換
    @TypeConverter
    fun fromTimestamp(value: Long?): Date? {
        return value?.let { Date(it) }
    }
    
    @TypeConverter
    fun dateToTimestamp(date: Date?): Long? {
        return date?.time
    }
}
*/ 