package com.example.autolaunch.model

import android.content.Context
import com.example.autolaunch.AlarmManagerService
import com.example.autolaunch.service.ScheduleService
import com.example.autolaunch.utils.SystemLogManager
import com.example.autolaunch.utils.ExceptionHandler
import com.example.autolaunch.utils.DatabaseTransactionManager
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first

/**
 * 排程資料儲存庫
 * 提供統一的數據存取接口，封裝數據來源的實現細節
 */
class ScheduleRepository(private val context: Context) {

    private val database = AppDatabase.getDatabase(context)
    private val scheduleDao = database.scheduleDao()
    private val alarmService = AlarmManagerService(context)
    private val transactionManager = DatabaseTransactionManager.getInstance(context)
    
    /**
     * 插入一個新的排程
     * @param schedule 要插入的排程物件
     * @return 插入的排程 ID
     * @throws IllegalArgumentException 如果排程數據無效
     */
    suspend fun insertSchedule(schedule: Schedule): Long {
        return transactionManager.executeTransaction {
            // 驗證排程數據
            if (!schedule.isValid()) {
                throw IllegalArgumentException("排程數據無效：${getValidationErrorMessage(schedule)}")
            }

            val scheduleId = scheduleDao.insert(schedule)
            if (scheduleId > 0) {
                val newSchedule = schedule.copy(id = scheduleId)

                // 記錄排程建立日志
                SystemLogManager.logScheduleCreated(context, newSchedule)

                if (schedule.isEnabled) {
                    // 確保前台服務正在運行
                    ExceptionHandler.safeExecute(context) {
                        ScheduleService.startService(context)
                    }
                    // 為新排程設定鬧鐘
                    ExceptionHandler.safeExecute(context) {
                        alarmService.setAlarm(newSchedule)
                    }
                }
            }
            scheduleId
        } ?: throw RuntimeException("Failed to insert schedule")
    }
    
    /**
     * 批量插入排程
     * @param schedules 要插入的排程列表
     */
    suspend fun insertSchedules(schedules: List<Schedule>) {
        scheduleDao.insertAll(schedules)
    }
    
    /**
     * 更新現有排程
     * @param schedule 要更新的排程物件
     * @return 受影響的列數
     * @throws IllegalArgumentException 如果排程數據無效
     */
    suspend fun updateSchedule(schedule: Schedule): Int {
        return transactionManager.executeTransaction {
            // 驗證排程數據
            if (!schedule.isValid()) {
                throw IllegalArgumentException("排程數據無效：${getValidationErrorMessage(schedule)}")
            }

            // 獲取修改前的排程信息用於比較
            val oldSchedule = scheduleDao.getScheduleById(schedule.id).first()

            val result = scheduleDao.update(schedule)
            if (result > 0) {
                // 記錄排程修改日志，包含修改詳情
                val changeDetails = generateChangeDetails(context, oldSchedule, schedule)
                SystemLogManager.logScheduleUpdated(context, schedule, changeDetails)

                // 更新鬧鐘設定
                ExceptionHandler.safeExecute(context) {
                    alarmService.updateAlarm(schedule)
                }
            }
            result
        } ?: 0
    }
    
    /**
     * 刪除一個排程
     * @param schedule 要刪除的排程物件
     * @return 受影響的列數
     */
    suspend fun deleteSchedule(schedule: Schedule): Int {
        // 先取消鬧鐘
        alarmService.cancelAlarm(schedule.id)
        // 再從資料庫刪除
        val result = scheduleDao.delete(schedule)
        if (result > 0) {
            // 記錄排程刪除日志
            SystemLogManager.logScheduleDeleted(context, schedule)
        }
        return result
    }
    
    /**
     * 根據 ID 刪除排程
     * @param id 排程 ID
     * @return 受影響的列數
     */
    suspend fun deleteScheduleById(id: Long): Int {
        // 先取消鬧鐘
        alarmService.cancelAlarm(id)
        // 再從資料庫刪除
        return scheduleDao.deleteById(id)
    }
    
    /**
     * 獲取所有排程
     * @return Flow<List<Schedule>> 所有排程的 Flow
     */
    fun getAllSchedules(): Flow<List<Schedule>> {
        return scheduleDao.getAllSchedules()
    }
    
    /**
     * 根據 ID 獲取特定排程
     * @param id 排程 ID
     * @return Flow<Schedule?> 指定排程的 Flow
     */
    fun getScheduleById(id: Long): Flow<Schedule?> {
        return scheduleDao.getScheduleById(id)
    }
    
    /**
     * 獲取所有啟用的排程
     * @return Flow<List<Schedule>> 啟用排程的 Flow
     */
    fun getEnabledSchedules(): Flow<List<Schedule>> {
        return scheduleDao.getEnabledSchedules()
    }
    
    /**
     * 根據應用程式包名獲取排程
     * @param packageName 應用程式包名
     * @return Flow<List<Schedule>> 指定應用程式的排程 Flow
     */
    fun getSchedulesByPackageName(packageName: String): Flow<List<Schedule>> {
        return scheduleDao.getSchedulesByPackageName(packageName)
    }
    
    /**
     * 獲取特定重複模式的排程
     * @param repeatMode 重複模式值
     * @return Flow<List<Schedule>> 指定重複模式的排程 Flow
     */
    fun getSchedulesByRepeatMode(repeatMode: Int): Flow<List<Schedule>> {
        return scheduleDao.getSchedulesByRepeatMode(repeatMode)
    }
    
    /**
     * 更新排程的啟用狀態
     * @param id 排程 ID
     * @param isEnabled 是否啟用
     * @return 受影響的列數
     */
    suspend fun updateScheduleEnabledStatus(id: Long, isEnabled: Boolean): Int {
        val result = scheduleDao.updateEnabledStatus(id, isEnabled)
        if (result > 0) {
            // 記錄排程啟用狀態變更日志
            scheduleDao.getScheduleById(id).first()?.let { schedule ->
                val statusText = if (isEnabled) "啟用" else "停用"
                SystemLogManager.logScheduleEnabledChanged(context, schedule, isEnabled, "狀態: $statusText")
            }

            // 根據啟用狀態設定或取消鬧鐘
            if (isEnabled) {
                // 重新載入排程並設定鬧鐘
                scheduleDao.getScheduleById(id).first()?.let { schedule ->
                    alarmService.setAlarm(schedule)
                }
            } else {
                // 取消鬧鐘
                alarmService.cancelAlarm(id)
            }
        }
        return result
    }
    
    /**
     * 更新排程的最後執行時間
     * @param id 排程 ID
     * @param lastExecutedTime 最後執行時間
     * @return 受影響的列數
     */
    suspend fun updateLastExecutedTime(id: Long, lastExecutedTime: Long): Int {
        return scheduleDao.updateLastExecutedTime(id, lastExecutedTime)
    }
    
    /**
     * 更新排程的下次執行時間
     * @param id 排程 ID
     * @param nextExecutedTime 下次執行時間
     * @return 受影響的列數
     */
    suspend fun updateNextExecutedTime(id: Long, nextExecutedTime: Long): Int {
        return scheduleDao.updateNextExecutedTime(id, nextExecutedTime)
    }
    
    /**
     * 清空所有排程
     * @return 受影響的列數
     */
    suspend fun deleteAllSchedules(): Int {
        return scheduleDao.deleteAllSchedules()
    }
    
    /**
     * 獲取排程總數
     * @return 排程總數
     */
    suspend fun getScheduleCount(): Int {
        return scheduleDao.getScheduleCount()
    }
    
    /**
     * 獲取啟用的排程總數
     * @return 啟用的排程總數
     */
    suspend fun getEnabledScheduleCount(): Int {
        return scheduleDao.getEnabledScheduleCount()
    }
    
    /**
     * 插入測試數據 (僅用於開發測試)
     */
    suspend fun insertTestData() {
        val testSchedules = listOf(
            // 應用程式排程
            Schedule(
                scheduleType = ScheduleType.APP.value,
                appName = "Chrome",
                packageName = "com.android.chrome",
                hour = 8,
                minute = 0,
                repeatMode = RepeatMode.DAILY.value,
                isEnabled = true
            ),
            Schedule(
                scheduleType = ScheduleType.APP.value,
                appName = "WhatsApp",
                packageName = "com.whatsapp",
                hour = 9,
                minute = 30,
                repeatMode = RepeatMode.WEEKLY.value,
                daysOfWeek = 31, // 週一到週五 (1+2+4+8+16)
                isEnabled = true
            ),
            Schedule(
                scheduleType = ScheduleType.APP.value,
                appName = "YouTube",
                packageName = "com.google.android.youtube",
                hour = 20,
                minute = 0,
                repeatMode = RepeatMode.WEEKLY.value,
                daysOfWeek = 96, // 週末 (32+64)
                isEnabled = false
            ),
            // 網頁排程
            Schedule(
                scheduleType = ScheduleType.URL.value,
                url = "https://www.google.com",
                urlTitle = "Google 搜尋",
                taskName = "每日 Google 搜尋",
                hour = 7,
                minute = 30,
                repeatMode = RepeatMode.DAILY.value,
                isEnabled = true
            ),
            Schedule(
                scheduleType = ScheduleType.URL.value,
                url = "https://github.com",
                urlTitle = "GitHub",
                taskName = "檢查 GitHub",
                hour = 18,
                minute = 0,
                repeatMode = RepeatMode.WEEKLY.value,
                daysOfWeek = 31, // 週一到週五
                isEnabled = true
            )
        )
        insertSchedules(testSchedules)
    }

    /**
     * 獲取排程驗證錯誤訊息
     * @param schedule 要驗證的排程
     * @return 錯誤訊息
     */
    private fun getValidationErrorMessage(schedule: Schedule): String {
        return when (schedule.getScheduleTypeEnum()) {
            ScheduleType.APP -> {
                when {
                    schedule.appName.isNullOrBlank() -> "應用程式名稱不能為空"
                    schedule.packageName.isNullOrBlank() -> "應用程式包名不能為空"
                    else -> "未知錯誤"
                }
            }
            ScheduleType.URL -> {
                when {
                    schedule.url.isNullOrBlank() -> "URL 不能為空"
                    !schedule.url.matches(Regex("^(https?|ftp)://[^\\s/$.?#].[^\\s]*$", RegexOption.IGNORE_CASE)) -> "URL 格式無效"
                    else -> "未知錯誤"
                }
            }
        }
    }

    /**
     * 生成排程修改詳情
     */
    private fun generateChangeDetails(context: Context, oldSchedule: Schedule?, newSchedule: Schedule): String {
        if (oldSchedule == null) return "新建排程"

        val changes = mutableListOf<String>()

        // 檢查啟用狀態變更
        if (oldSchedule.isEnabled != newSchedule.isEnabled) {
            val status = if (newSchedule.isEnabled) "啟用" else "停用"
            changes.add("狀態: $status")
        }

        // 檢查時間變更
        if (oldSchedule.hour != newSchedule.hour || oldSchedule.minute != newSchedule.minute) {
            val oldTime = String.format("%02d:%02d", oldSchedule.hour, oldSchedule.minute)
            val newTime = String.format("%02d:%02d", newSchedule.hour, newSchedule.minute)
            changes.add("時間: $oldTime → $newTime")
        }

        // 檢查任務名稱變更
        if (oldSchedule.taskName != newSchedule.taskName) {
            val oldName = oldSchedule.taskName?.takeIf { it.isNotBlank() } ?: "未設定"
            val newName = newSchedule.taskName?.takeIf { it.isNotBlank() } ?: "未設定"
            changes.add("標題: $oldName → $newName")
        }

        // 檢查重複模式變更
        if (oldSchedule.repeatMode != newSchedule.repeatMode) {
            val oldMode = RepeatMode.values().find { it.value == oldSchedule.repeatMode }?.displayName ?: "未知"
            val newMode = RepeatMode.values().find { it.value == newSchedule.repeatMode }?.displayName ?: "未知"
            changes.add("重複: $oldMode → $newMode")
        }

        // 檢查應用程式變更（僅限APP類型）
        if (newSchedule.scheduleType == ScheduleType.APP.value) {
            if (oldSchedule.appName != newSchedule.appName) {
                val oldApp = oldSchedule.appName?.takeIf { it.isNotBlank() } ?: "未設定"
                val newApp = newSchedule.appName?.takeIf { it.isNotBlank() } ?: "未設定"
                changes.add("應用: $oldApp → $newApp")
            }
        }

        // 檢查URL變更（僅限URL類型）
        if (newSchedule.scheduleType == ScheduleType.URL.value) {
            if (oldSchedule.url != newSchedule.url) {
                val oldUrl = oldSchedule.url?.takeIf { it.isNotBlank() } ?: "未設定"
                val newUrl = newSchedule.url?.takeIf { it.isNotBlank() } ?: "未設定"
                // 只顯示域名部分，避免URL太長
                val oldDomain = try { oldUrl.substringAfter("://").substringBefore("/") } catch (e: Exception) { oldUrl }
                val newDomain = try { newUrl.substringAfter("://").substringBefore("/") } catch (e: Exception) { newUrl }
                changes.add("網址: $oldDomain → $newDomain")
            }

            if (oldSchedule.urlTitle != newSchedule.urlTitle) {
                val oldTitle = oldSchedule.urlTitle?.takeIf { it.isNotBlank() } ?: "未設定"
                val newTitle = newSchedule.urlTitle?.takeIf { it.isNotBlank() } ?: "未設定"
                changes.add("網址標題: $oldTitle → $newTitle")
            }
        }

        // 檢查重複日期變更（僅限每週重複）
        if (newSchedule.repeatMode == RepeatMode.WEEKLY.value) {
            if (oldSchedule.daysOfWeek != newSchedule.daysOfWeek) {
                val oldDaysDisplay = oldSchedule.getDaysOfWeekDisplayName(context)
                val newDaysDisplay = newSchedule.getDaysOfWeekDisplayName(context)
                changes.add("重複日期: $oldDaysDisplay → $newDaysDisplay")
            }
        }

        return if (changes.isEmpty()) {
            "無明顯變更" // 無變更時返回空字串
        } else {
            changes.joinToString("; ")
        }
    }

    /**
     * 更新排程的排序順序
     * @param scheduleId 排程 ID
     * @param sortOrder 新的排序順序
     * @return 受影響的列數
     */
    suspend fun updateSortOrder(scheduleId: Long, sortOrder: Int): Int {
        return scheduleDao.updateSortOrder(scheduleId, sortOrder)
    }

    /**
     * 批量更新排程的排序順序
     * @param scheduleIds 排程 ID 列表
     * @param sortOrders 對應的排序順序列表
     */
    suspend fun updateSortOrders(scheduleIds: List<Long>, sortOrders: List<Int>) {
        scheduleDao.updateSortOrders(scheduleIds, sortOrders)
    }

    /**
     * 重新排序排程列表
     * @param schedules 重新排序後的排程列表
     */
    suspend fun reorderSchedules(schedules: List<Schedule>) {
        val scheduleIds = schedules.map { it.id }
        val sortOrders = schedules.mapIndexed { index, _ -> index }
        updateSortOrders(scheduleIds, sortOrders)
    }
}