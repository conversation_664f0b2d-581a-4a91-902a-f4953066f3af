package com.example.autolaunch.model

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import java.text.SimpleDateFormat
import java.util.*

/**
 * 系統日誌類型
 */
enum class LogType(val value: Int, val displayName: String) {
    INFO(0, "資訊"),
    WARNING(1, "警告"),
    ERROR(2, "錯誤"),
    SUCCESS(3, "成功")
}

/**
 * 操作類型
 */
enum class ActionType(val value: Int, val displayName: String) {
    SCHEDULE_CREATED(0, "排程建立"),
    SCHEDULE_UPDATED(1, "排程修改"),
    SCHEDULE_DELETED(2, "排程刪除"),
    SCHEDULE_EXECUTED(3, "排程執行"),
    SCHEDULE_ENABLED(4, "排程啟用"),
    SCHEDULE_DISABLED(5, "排程停用"),
    APP_STARTED(6, "應用啟動"),
    PERMISSION_GRANTED(7, "權限授予"),
    PERMISSION_DENIED(8, "權限拒絕"),
    SYSTEM_ERROR(9, "系統錯誤")
}

/**
 * 系統日志數據模型
 * 用於記錄應用程式的各種操作和事件
 */
@Entity(tableName = "system_logs")
data class SystemLog(
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "id")
    val id: Long = 0,

    @ColumnInfo(name = "log_type")
    val logType: Int, // 對應 LogType.value

    @ColumnInfo(name = "action_type")
    val actionType: Int, // 對應 ActionType.value

    @ColumnInfo(name = "schedule_id")
    val scheduleId: Long? = null, // 相關排程ID（如果適用）

    @ColumnInfo(name = "schedule_name")
    val scheduleName: String? = null, // 排程名稱（用於顯示）

    @ColumnInfo(name = "message")
    val message: String, // 日志消息

    @ColumnInfo(name = "timestamp")
    val timestamp: Long = System.currentTimeMillis(), // 時間戳

    @ColumnInfo(name = "details")
    val details: String? = null // 詳細資訊（可選）
) {
    /**
     * 獲取日誌類型枚舉
     */
    fun getLogTypeEnum(): LogType {
        return LogType.values().find { it.value == logType } ?: LogType.INFO
    }

    /**
     * 獲取操作類型枚舉
     */
    fun getActionTypeEnum(): ActionType {
        return ActionType.values().find { it.value == actionType } ?: ActionType.APP_STARTED
    }

    /**
     * 格式化時間戳為可讀字符串
     */
    fun getFormattedTime(): String {
        val sdf = SimpleDateFormat("HH:mm:ss", Locale.getDefault())
        return sdf.format(Date(timestamp))
    }

    /**
     * 格式化日期為可讀字符串
     */
    fun getFormattedDate(): String {
        val sdf = SimpleDateFormat("yyyy年MM月dd日", Locale.getDefault())
        return sdf.format(Date(timestamp))
    }

    /**
     * 獲取星期幾
     */
    fun getDayOfWeek(): String {
        val calendar = Calendar.getInstance()
        calendar.timeInMillis = timestamp
        val dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK)
        return when (dayOfWeek) {
            Calendar.SUNDAY -> "星期日"
            Calendar.MONDAY -> "星期一"
            Calendar.TUESDAY -> "星期二"
            Calendar.WEDNESDAY -> "星期三"
            Calendar.THURSDAY -> "星期四"
            Calendar.FRIDAY -> "星期五"
            Calendar.SATURDAY -> "星期六"
            else -> ""
        }
    }

    /**
     * 獲取完整的日期時間字符串
     */
    fun getFullDateTime(): String {
        val sdf = SimpleDateFormat("yyyy年MM月dd日 HH:mm:ss", Locale.getDefault())
        return sdf.format(Date(timestamp))
    }

    /**
     * 檢查是否是今天的日誌
     */
    fun isToday(): Boolean {
        val today = Calendar.getInstance()
        val logDate = Calendar.getInstance()
        logDate.timeInMillis = timestamp
        
        return today.get(Calendar.YEAR) == logDate.get(Calendar.YEAR) &&
               today.get(Calendar.DAY_OF_YEAR) == logDate.get(Calendar.DAY_OF_YEAR)
    }

    /**
     * 獲取相對時間描述（如：2小時前）
     */
    fun getRelativeTime(): String {
        val now = System.currentTimeMillis()
        val diff = now - timestamp
        
        return when {
            diff < 60 * 1000 -> "剛剛"
            diff < 60 * 60 * 1000 -> "${diff / (60 * 1000)}分鐘前"
            diff < 24 * 60 * 60 * 1000 -> "${diff / (60 * 60 * 1000)}小時前"
            diff < 7 * 24 * 60 * 60 * 1000 -> "${diff / (24 * 60 * 60 * 1000)}天前"
            else -> getFormattedDate()
        }
    }
}
