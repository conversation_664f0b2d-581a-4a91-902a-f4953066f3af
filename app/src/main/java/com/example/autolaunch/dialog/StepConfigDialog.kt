package com.example.autolaunch.dialog

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AlertDialog
import com.example.autolaunch.R
import com.example.autolaunch.databinding.DialogStepConfigBinding
import com.example.autolaunch.model.AutomationStep
import com.example.autolaunch.model.StepType
import com.example.autolaunch.model.TargetType

/**
 * 自動化步驟配置對話框
 */
class StepConfigDialog(
    context: Context,
    private val existingStep: AutomationStep? = null,
    private val onStepConfigured: (AutomationStep) -> Unit
) : Dialog(context) {

    private lateinit var binding: DialogStepConfigBinding
    private var selectedStepType: StepType = StepType.CLICK
    private var selectedTargetType: TargetType = TargetType.TEXT

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        binding = DialogStepConfigBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupDialog()
        setupUI()
        setupClickListeners()
        
        // 如果是編輯模式，填充現有數據
        existingStep?.let { loadExistingStep(it) }
    }

    private fun setupDialog() {
        // 設置對話框標題
        binding.tvDialogTitle.text = if (existingStep != null) {
            "編輯自動化步驟"
        } else {
            "新增自動化步驟"
        }
        
        // 設置對話框大小
        window?.setLayout(
            (context.resources.displayMetrics.widthPixels * 0.9).toInt(),
            android.view.ViewGroup.LayoutParams.WRAP_CONTENT
        )
    }

    private fun setupUI() {
        // 默認選擇點擊類型
        binding.btnTypeClick.isChecked = true
        selectedStepType = StepType.CLICK
        
        // 默認選擇文字定位
        binding.btnTargetText.isChecked = true
        selectedTargetType = TargetType.TEXT
        
        // 更新UI顯示
        updateStepTypeUI()
        updateTargetTypeUI()
    }

    private fun setupClickListeners() {
        // 步驟類型選擇
        binding.toggleGroupStepType.addOnButtonCheckedListener { _, checkedId, isChecked ->
            if (isChecked) {
                selectedStepType = when (checkedId) {
                    R.id.btnTypeClick -> StepType.CLICK
                    R.id.btnTypeInput -> StepType.INPUT_TEXT
                    R.id.btnTypeWait -> StepType.WAIT
                    else -> StepType.CLICK
                }
                
                // 清除其他組的選擇
                binding.toggleGroupStepTypeMore.clearChecked()
                updateStepTypeUI()
            }
        }
        
        binding.toggleGroupStepTypeMore.addOnButtonCheckedListener { _, checkedId, isChecked ->
            if (isChecked) {
                selectedStepType = when (checkedId) {
                    R.id.btnTypeSwipe -> StepType.SWIPE_DOWN
                    R.id.btnTypeBack -> StepType.BACK
                    R.id.btnTypeScroll -> StepType.SCROLL
                    else -> StepType.CLICK
                }
                
                // 清除其他組的選擇
                binding.toggleGroupStepType.clearChecked()
                updateStepTypeUI()
            }
        }

        // 目標類型選擇
        binding.toggleGroupTargetType.addOnButtonCheckedListener { _, checkedId, isChecked ->
            if (isChecked) {
                selectedTargetType = when (checkedId) {
                    R.id.btnTargetText -> TargetType.TEXT
                    R.id.btnTargetId -> TargetType.ID
                    R.id.btnTargetCoordinate -> TargetType.COORDINATE
                    else -> TargetType.TEXT
                }
                updateTargetTypeUI()
            }
        }

        // 操作按鈕
        binding.btnCancel.setOnClickListener {
            dismiss()
        }

        binding.btnSave.setOnClickListener {
            saveStep()
        }
    }

    private fun updateStepTypeUI() {
        // 根據步驟類型顯示/隱藏相關配置
        when (selectedStepType) {
            StepType.CLICK, StepType.LONG_CLICK -> {
                binding.layoutTargetConfig.visibility = View.VISIBLE
                binding.layoutInputText.visibility = View.GONE
                binding.layoutWaitDuration.visibility = View.GONE
            }
            StepType.INPUT_TEXT -> {
                binding.layoutTargetConfig.visibility = View.VISIBLE
                binding.layoutInputText.visibility = View.VISIBLE
                binding.layoutWaitDuration.visibility = View.GONE
            }
            StepType.WAIT -> {
                binding.layoutTargetConfig.visibility = View.GONE
                binding.layoutInputText.visibility = View.GONE
                binding.layoutWaitDuration.visibility = View.VISIBLE
            }
            StepType.BACK, StepType.HOME -> {
                binding.layoutTargetConfig.visibility = View.GONE
                binding.layoutInputText.visibility = View.GONE
                binding.layoutWaitDuration.visibility = View.GONE
            }
            else -> {
                binding.layoutTargetConfig.visibility = View.GONE
                binding.layoutInputText.visibility = View.GONE
                binding.layoutWaitDuration.visibility = View.GONE
            }
        }
    }

    private fun updateTargetTypeUI() {
        // 根據目標類型顯示/隱藏相關輸入框
        binding.layoutTargetText.visibility = if (selectedTargetType == TargetType.TEXT) View.VISIBLE else View.GONE
        binding.layoutTargetId.visibility = if (selectedTargetType == TargetType.ID) View.VISIBLE else View.GONE
        binding.layoutTargetCoordinate.visibility = if (selectedTargetType == TargetType.COORDINATE) View.VISIBLE else View.GONE
    }

    private fun loadExistingStep(step: AutomationStep) {
        // 設置步驟類型
        selectedStepType = step.getStepType()
        when (selectedStepType) {
            StepType.CLICK -> binding.btnTypeClick.isChecked = true
            StepType.INPUT_TEXT -> binding.btnTypeInput.isChecked = true
            StepType.WAIT -> binding.btnTypeWait.isChecked = true
            StepType.SWIPE_DOWN, StepType.SWIPE_UP, StepType.SWIPE_LEFT, StepType.SWIPE_RIGHT -> binding.btnTypeSwipe.isChecked = true
            StepType.BACK -> binding.btnTypeBack.isChecked = true
            StepType.SCROLL -> binding.btnTypeScroll.isChecked = true
            else -> binding.btnTypeClick.isChecked = true
        }

        // 設置目標類型
        selectedTargetType = step.getTargetType()
        when (selectedTargetType) {
            TargetType.TEXT -> binding.btnTargetText.isChecked = true
            TargetType.ID -> binding.btnTargetId.isChecked = true
            TargetType.COORDINATE -> binding.btnTargetCoordinate.isChecked = true
            else -> binding.btnTargetText.isChecked = true
        }

        // 填充數據
        binding.etStepName.setText(step.stepName ?: "")
        binding.etTargetText.setText(step.targetText ?: "")
        binding.etTargetId.setText(step.targetId ?: "")
        binding.etTargetX.setText(step.targetX?.toString() ?: "")
        binding.etTargetY.setText(step.targetY?.toString() ?: "")
        binding.etInputText.setText(step.inputText ?: "")
        binding.etWaitDuration.setText(step.waitDuration?.toString() ?: "1000")
        binding.switchOptional.isChecked = step.isOptional
        binding.etRetryCount.setText(step.retryCount.toString())
        binding.etTimeoutMs.setText(step.timeoutMs.toString())

        // 更新UI
        updateStepTypeUI()
        updateTargetTypeUI()
    }

    private fun saveStep() {
        try {
            // 驗證輸入
            if (!validateInput()) {
                return
            }

            // 創建步驟對象
            val step = AutomationStep(
                id = existingStep?.id ?: 0,
                scheduleId = existingStep?.scheduleId ?: 0,
                stepOrder = existingStep?.stepOrder ?: 1,
                stepType = selectedStepType.value,
                stepName = binding.etStepName.text?.toString()?.trim()?.takeIf { it.isNotEmpty() },
                targetType = selectedTargetType.value,
                targetText = binding.etTargetText.text?.toString()?.trim()?.takeIf { it.isNotEmpty() },
                targetId = binding.etTargetId.text?.toString()?.trim()?.takeIf { it.isNotEmpty() },
                targetX = binding.etTargetX.text?.toString()?.toIntOrNull(),
                targetY = binding.etTargetY.text?.toString()?.toIntOrNull(),
                inputText = binding.etInputText.text?.toString()?.trim()?.takeIf { it.isNotEmpty() },
                waitDuration = binding.etWaitDuration.text?.toString()?.toLongOrNull(),
                retryCount = binding.etRetryCount.text?.toString()?.toIntOrNull() ?: 3,
                timeoutMs = binding.etTimeoutMs.text?.toString()?.toLongOrNull() ?: 10000,
                isOptional = binding.switchOptional.isChecked,
                createdTime = existingStep?.createdTime ?: System.currentTimeMillis()
            )

            onStepConfigured(step)
            dismiss()

        } catch (e: Exception) {
            // 顯示錯誤訊息
            AlertDialog.Builder(context)
                .setTitle("配置錯誤")
                .setMessage("步驟配置有誤：${e.message}")
                .setPositiveButton("確定", null)
                .show()
        }
    }

    private fun validateInput(): Boolean {
        // 根據步驟類型驗證必要輸入
        when (selectedStepType) {
            StepType.CLICK, StepType.LONG_CLICK -> {
                when (selectedTargetType) {
                    TargetType.TEXT -> {
                        if (binding.etTargetText.text.isNullOrBlank()) {
                            binding.layoutTargetText.error = "請輸入目標文字"
                            return false
                        }
                    }
                    TargetType.ID -> {
                        if (binding.etTargetId.text.isNullOrBlank()) {
                            binding.layoutTargetId.error = "請輸入元素ID"
                            return false
                        }
                    }
                    TargetType.COORDINATE -> {
                        if (binding.etTargetX.text.isNullOrBlank() || binding.etTargetY.text.isNullOrBlank()) {
                            binding.layoutTargetCoordinate.error = "請輸入完整座標"
                            return false
                        }
                    }
                    else -> {}
                }
            }
            StepType.INPUT_TEXT -> {
                if (binding.etInputText.text.isNullOrBlank()) {
                    binding.layoutInputText.error = "請輸入文字內容"
                    return false
                }
                // 同時需要驗證目標元素
                when (selectedTargetType) {
                    TargetType.TEXT -> {
                        if (binding.etTargetText.text.isNullOrBlank()) {
                            binding.layoutTargetText.error = "請輸入目標文字"
                            return false
                        }
                    }
                    TargetType.ID -> {
                        if (binding.etTargetId.text.isNullOrBlank()) {
                            binding.layoutTargetId.error = "請輸入元素ID"
                            return false
                        }
                    }
                    TargetType.COORDINATE -> {
                        if (binding.etTargetX.text.isNullOrBlank() || binding.etTargetY.text.isNullOrBlank()) {
                            binding.layoutTargetCoordinate.error = "請輸入完整座標"
                            return false
                        }
                    }
                    else -> {}
                }
            }
            StepType.WAIT -> {
                val duration = binding.etWaitDuration.text?.toString()?.toLongOrNull()
                if (duration == null || duration <= 0) {
                    binding.layoutWaitDuration.error = "請輸入有效的等待時間"
                    return false
                }
            }
            else -> {
                // 其他類型不需要額外驗證
            }
        }

        // 驗證重試次數
        val retryCount = binding.etRetryCount.text?.toString()?.toIntOrNull()
        if (retryCount == null || retryCount < 0) {
            binding.etRetryCount.error = "重試次數必須為非負整數"
            return false
        }

        // 驗證超時時間
        val timeoutMs = binding.etTimeoutMs.text?.toString()?.toLongOrNull()
        if (timeoutMs == null || timeoutMs <= 0) {
            binding.etTimeoutMs.error = "超時時間必須為正整數"
            return false
        }

        return true
    }
}
