package com.example.autolaunch.dialog

import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.DialogFragment
import com.example.autolaunch.R
import com.example.autolaunch.utils.BatteryOptimizationHelper
import com.google.android.material.dialog.MaterialAlertDialogBuilder

/**
 * 電池優化設定引導對話框
 */
class BatteryOptimizationDialog : DialogFragment() {
    
    companion object {
        const val TAG = "BatteryOptimizationDialog"
        
        fun newInstance(): BatteryOptimizationDialog {
            return BatteryOptimizationDialog()
        }
    }
    
    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val context = requireContext()
        
        val explanation = BatteryOptimizationHelper.getBatteryOptimizationExplanation()
        val manufacturerHint = BatteryOptimizationHelper.getManufacturerSpecificHints()
        
        val fullMessage = buildString {
            append(explanation)
            append("\n\n")
            append(getString(R.string.battery_optimization_special_reminder))
            append(manufacturerHint)
        }

        return MaterialAlertDialogBuilder(context)
            .setTitle(getString(R.string.battery_optimization_title))
            .setMessage(fullMessage)
            .setIcon(R.drawable.ic_schedule_24)
            .setPositiveButton(getString(R.string.battery_optimization_go_to_settings)) { _, _ ->
                openBatterySettings(context)
                BatteryOptimizationHelper.markPromptShown(context)
            }
            .setNegativeButton(getString(R.string.battery_optimization_later)) { _, _ ->
                BatteryOptimizationHelper.markPromptDismissed(context)
            }
            .setNeutralButton(getString(R.string.battery_optimization_completed)) { _, _ ->
                // 檢查是否真的已設定
                if (BatteryOptimizationHelper.isIgnoringBatteryOptimizations(context)) {
                    Toast.makeText(context, getString(R.string.battery_optimization_success), Toast.LENGTH_SHORT).show()
                } else {
                    Toast.makeText(context, getString(R.string.battery_optimization_not_detected), Toast.LENGTH_LONG).show()
                }
                BatteryOptimizationHelper.markPromptShown(context)
            }
            .setCancelable(false)
            .create()
    }
    
    private fun openBatterySettings(context: Context) {
        try {
            val intent = BatteryOptimizationHelper.createBatteryOptimizationIntent(context)
            if (intent != null) {
                startActivity(intent)
            } else {
                Toast.makeText(context, getString(R.string.battery_optimization_cannot_open), Toast.LENGTH_LONG).show()
            }
        } catch (e: Exception) {
            Toast.makeText(context, getString(R.string.battery_optimization_error, e.message), Toast.LENGTH_LONG).show()
        }
    }
} 