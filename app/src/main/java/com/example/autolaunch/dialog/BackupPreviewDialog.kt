package com.example.autolaunch.dialog

import android.app.Dialog
import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.DialogFragment
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.autolaunch.R
import com.example.autolaunch.adapter.BackupPreviewAdapter
import com.example.autolaunch.adapter.PreviewItem
import com.example.autolaunch.databinding.DialogBackupPreviewBinding
import com.example.autolaunch.model.Schedule
import com.example.autolaunch.model.ScheduleType

class BackupPreviewDialog : DialogFragment() {

    private var _binding: DialogBackupPreviewBinding? = null
    private val binding get() = _binding!!

    private var schedules: List<Schedule>? = null
    private var onRestoreConfirmed: (() -> Unit)? = null

    companion object {
        fun newInstance(onRestoreConfirmed: () -> Unit): BackupPreviewDialog {
            val dialog = BackupPreviewDialog()
            dialog.onRestoreConfirmed = onRestoreConfirmed
            return dialog
        }
    }
    
    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        _binding = DialogBackupPreviewBinding.inflate(requireActivity().layoutInflater)
        setupViews()
        return AlertDialog.Builder(requireContext()).setView(binding.root).create()
    }

    private fun setupViews() {
        binding.btnCancel.setOnClickListener { dismiss() }
        binding.btnRestore.setOnClickListener {
            onRestoreConfirmed?.invoke()
            dismiss()
        }
        
        // Initially, show loading state
        binding.progressBar.visibility = View.VISIBLE
        binding.recyclerViewPreview.visibility = View.GONE
        binding.tvTotalCount.visibility = View.GONE
        binding.btnRestore.isEnabled = false
    }

    fun updateSchedules(newSchedules: List<Schedule>) {
        this.schedules = newSchedules
        
        if (_binding == null) return

        binding.progressBar.visibility = View.GONE
        binding.tvTotalCount.visibility = View.VISIBLE
        binding.tvTotalCount.text = getString(R.string.preview_total_count, newSchedules.size)

        if (newSchedules.isNotEmpty()) {
            val groupedSchedules = newSchedules.groupBy { it.getScheduleTypeEnum() }
            val previewItems = mutableListOf<PreviewItem>()

            groupedSchedules[ScheduleType.APP]?.let {
                if (it.isNotEmpty()) {
                    previewItems.add(PreviewItem.Header(getString(R.string.preview_header_apps, it.size)))
                    it.forEach { schedule -> previewItems.add(PreviewItem.ScheduleItem(schedule)) }
                }
            }

            groupedSchedules[ScheduleType.URL]?.let {
                if (it.isNotEmpty()) {
                    previewItems.add(PreviewItem.Header(getString(R.string.preview_header_urls, it.size)))
                    it.forEach { schedule -> previewItems.add(PreviewItem.ScheduleItem(schedule)) }
                }
            }
            
            binding.recyclerViewPreview.visibility = View.VISIBLE
            binding.recyclerViewPreview.layoutManager = LinearLayoutManager(context)
            binding.recyclerViewPreview.adapter = BackupPreviewAdapter(previewItems)
            binding.btnRestore.isEnabled = true
        } else {
            binding.dialogTitle.text = "無內容可預覽"
            binding.btnRestore.isEnabled = false
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
} 