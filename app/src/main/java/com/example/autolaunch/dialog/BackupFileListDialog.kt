package com.example.autolaunch.dialog

import android.app.Dialog
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.autolaunch.R
import com.example.autolaunch.model.BackupFileInfo
import com.example.autolaunch.utils.DriveFileInfo
import com.example.autolaunch.utils.setDebounceClickListener
import com.google.android.material.button.MaterialButton
import com.google.android.material.card.MaterialCardView
import java.io.File

/**
 * 備份文件列表對話框
 * 用於顯示和管理本地或雲端備份文件
 */
class BackupFileListDialog {
    
    companion object {
        /**
         * 顯示本地備份文件列表
         */
        fun showLocalBackupFiles(
            context: Context,
            files: List<File>,
            fileInfos: List<BackupFileInfo?>,
            onFileSelected: (File, BackupFileInfo?) -> Unit,
            onFileDeleted: (File) -> Unit
        ): Dialog {
            val dialogView = LayoutInflater.from(context).inflate(R.layout.dialog_backup_file_list, null)
            val recyclerView = dialogView.findViewById<RecyclerView>(R.id.recyclerViewFiles)
            val titleView = dialogView.findViewById<TextView>(R.id.tvDialogTitle)
            
            titleView.text = "本地備份文件"
            
            val adapter = LocalBackupFileAdapter(files, fileInfos, onFileSelected, onFileDeleted)
            recyclerView.layoutManager = LinearLayoutManager(context)
            recyclerView.adapter = adapter
            
            return AlertDialog.Builder(context)
                .setView(dialogView)
                .setNegativeButton("關閉", null)
                .create()
        }
        
        /**
         * 顯示雲端備份文件列表
         */
        fun showCloudBackupFiles(
            context: Context,
            files: List<DriveFileInfo>,
            onFileSelected: (DriveFileInfo) -> Unit,
            onFileDeleted: (DriveFileInfo) -> Unit
        ): Dialog {
            val dialogView = LayoutInflater.from(context).inflate(R.layout.dialog_backup_file_list, null)
            val recyclerView = dialogView.findViewById<RecyclerView>(R.id.recyclerViewFiles)
            val titleView = dialogView.findViewById<TextView>(R.id.tvDialogTitle)
            
            titleView.text = "雲端備份文件"
            
            val adapter = CloudBackupFileAdapter(files, onFileSelected, onFileDeleted)
            recyclerView.layoutManager = LinearLayoutManager(context)
            recyclerView.adapter = adapter
            
            return AlertDialog.Builder(context)
                .setView(dialogView)
                .setNegativeButton("關閉", null)
                .create()
        }
    }
}

/**
 * 本地備份文件適配器
 */
class LocalBackupFileAdapter(
    private val files: List<File>,
    private val fileInfos: List<BackupFileInfo?>,
    private val onFileSelected: (File, BackupFileInfo?) -> Unit,
    private val onFileDeleted: (File) -> Unit
) : RecyclerView.Adapter<LocalBackupFileAdapter.ViewHolder>() {
    
    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val cardView: MaterialCardView = view.findViewById(R.id.cardBackupFile)
        val tvFileName: TextView = view.findViewById(R.id.tvFileName)
        val tvFileInfo: TextView = view.findViewById(R.id.tvFileInfo)
        val tvFileSize: TextView = view.findViewById(R.id.tvFileSize)
        val tvCreatedTime: TextView = view.findViewById(R.id.tvCreatedTime)
        val btnRestore: MaterialButton = view.findViewById(R.id.btnRestore)
        val btnDelete: MaterialButton = view.findViewById(R.id.btnDelete)
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_backup_file, parent, false)
        return ViewHolder(view)
    }
    
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val file = files[position]
        val fileInfo = fileInfos[position]
        
        holder.tvFileName.text = file.name
        
        if (fileInfo != null) {
            holder.tvFileInfo.text = holder.itemView.context.getString(R.string.backup_file_schedule_count, fileInfo.scheduleCount)
            holder.tvFileSize.text = fileInfo.getFormattedFileSize()
            holder.tvCreatedTime.text = fileInfo.getFormattedCreatedTime()
            // 使用主題屬性而不是硬編碼顏色
            val typedValue = android.util.TypedValue()
            holder.itemView.context.theme.resolveAttribute(
                com.google.android.material.R.attr.colorOnSurface,
                typedValue,
                true
            )
            holder.tvFileInfo.setTextColor(typedValue.data)
        } else {
            holder.tvFileInfo.text = holder.itemView.context.getString(R.string.backup_file_cannot_read)
            holder.tvFileSize.text = "${file.length()} bytes"
            holder.tvCreatedTime.text = holder.itemView.context.getString(R.string.backup_file_unknown_time)
            // 使用錯誤顏色
            val typedValue = android.util.TypedValue()
            holder.itemView.context.theme.resolveAttribute(
                com.google.android.material.R.attr.colorError,
                typedValue,
                true
            )
            holder.tvFileInfo.setTextColor(typedValue.data)
        }
        
        holder.btnRestore.setDebounceClickListener(1500L) {
            onFileSelected(file, fileInfo)
        }
        
        holder.btnDelete.setDebounceClickListener(1000L) {
            onFileDeleted(file)
        }
        
        // 如果文件信息無效，禁用恢復按鈕
        holder.btnRestore.isEnabled = fileInfo?.isValid == true
    }
    
    override fun getItemCount() = files.size
}

/**
 * 雲端備份文件適配器
 */
class CloudBackupFileAdapter(
    private val files: List<DriveFileInfo>,
    private val onFileSelected: (DriveFileInfo) -> Unit,
    private val onFileDeleted: (DriveFileInfo) -> Unit
) : RecyclerView.Adapter<CloudBackupFileAdapter.ViewHolder>() {
    
    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val cardView: MaterialCardView = view.findViewById(R.id.cardBackupFile)
        val tvFileName: TextView = view.findViewById(R.id.tvFileName)
        val tvFileInfo: TextView = view.findViewById(R.id.tvFileInfo)
        val tvFileSize: TextView = view.findViewById(R.id.tvFileSize)
        val tvCreatedTime: TextView = view.findViewById(R.id.tvCreatedTime)
        val btnRestore: MaterialButton = view.findViewById(R.id.btnRestore)
        val btnDelete: MaterialButton = view.findViewById(R.id.btnDelete)
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_backup_file, parent, false)
        return ViewHolder(view)
    }
    
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val file = files[position]
        
        holder.tvFileName.text = file.name
        holder.tvFileInfo.text = "雲端備份文件"
        holder.tvFileSize.text = file.getFormattedSize()
        holder.tvCreatedTime.text = file.getFormattedCreatedTimeWithRelative(holder.itemView.context)
        
        holder.btnRestore.setDebounceClickListener(1500L) {
            onFileSelected(file)
        }
        
        holder.btnDelete.setDebounceClickListener(1000L) {
            onFileDeleted(file)
        }
    }
    
    override fun getItemCount() = files.size
}
