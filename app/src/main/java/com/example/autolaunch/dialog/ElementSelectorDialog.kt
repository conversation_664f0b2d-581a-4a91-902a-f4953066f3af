package com.example.autolaunch.dialog

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import androidx.appcompat.app.AlertDialog
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.autolaunch.R
import com.example.autolaunch.adapter.UIElementAdapter
import com.example.autolaunch.automation.UIElementFinder
import com.example.autolaunch.automation.UIElementInfo
import com.example.autolaunch.databinding.DialogElementSelectorBinding
import com.example.autolaunch.service.AutoLaunchAccessibilityService
import com.example.autolaunch.utils.AccessibilityPermissionHelper
import kotlinx.coroutines.*

/**
 * UI元素選擇對話框
 */
class ElementSelectorDialog(
    context: Context,
    private val onElementSelected: (UIElementInfo) -> Unit
) : Dialog(context) {

    private lateinit var binding: DialogElementSelectorBinding
    private lateinit var elementAdapter: UIElementAdapter
    private var allElements = listOf<UIElementInfo>()
    private var filteredElements = listOf<UIElementInfo>()
    private var selectedElement: UIElementInfo? = null
    
    private val dialogScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        binding = DialogElementSelectorBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupDialog()
        setupRecyclerView()
        setupClickListeners()
        setupSearch()
        setupFilters()
        
        loadElements()
    }

    override fun onStop() {
        super.onStop()
        dialogScope.cancel()
    }

    private fun setupDialog() {
        // 設置對話框大小
        window?.setLayout(
            (context.resources.displayMetrics.widthPixels * 0.9).toInt(),
            (context.resources.displayMetrics.heightPixels * 0.8).toInt()
        )
    }

    private fun setupRecyclerView() {
        elementAdapter = UIElementAdapter { element ->
            selectedElement = element
            binding.btnSelect.isEnabled = true
            
            // 更新選擇狀態
            elementAdapter.setSelectedElement(element)
        }
        
        binding.recyclerViewElements.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = elementAdapter
        }
    }

    private fun setupClickListeners() {
        binding.btnCancel.setOnClickListener {
            dismiss()
        }

        binding.btnSelect.setOnClickListener {
            selectedElement?.let { element ->
                onElementSelected(element)
                dismiss()
            }
        }

        binding.btnRefresh.setOnClickListener {
            loadElements()
        }
    }

    private fun setupSearch() {
        binding.etSearch.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                filterElements()
            }
        })
    }

    private fun setupFilters() {
        binding.chipGroupFilters.setOnCheckedStateChangeListener { _, _ ->
            filterElements()
        }
    }

    private fun loadElements() {
        dialogScope.launch {
            try {
                binding.btnRefresh.isEnabled = false
                
                // 檢查無障礙服務
                val accessibilityService = AccessibilityPermissionHelper.getAccessibilityService()
                if (accessibilityService == null) {
                    showError("無障礙服務未啟用，無法獲取UI元素")
                    return@launch
                }
                
                // 在後台線程獲取元素
                val elements = withContext(Dispatchers.IO) {
                    val finder = UIElementFinder(accessibilityService)
                    finder.getAllInteractiveElements()
                }
                
                allElements = elements
                filterElements()
                
            } catch (e: Exception) {
                showError("獲取UI元素失敗：${e.message}")
            } finally {
                binding.btnRefresh.isEnabled = true
            }
        }
    }

    private fun filterElements() {
        val searchText = binding.etSearch.text?.toString()?.trim() ?: ""
        val showClickable = binding.chipClickable.isChecked
        val showEditable = binding.chipEditable.isChecked
        val showScrollable = binding.chipScrollable.isChecked
        val showCheckable = binding.chipCheckable.isChecked

        filteredElements = allElements.filter { element ->
            // 文字搜索過濾
            val matchesSearch = if (searchText.isEmpty()) {
                true
            } else {
                element.text?.contains(searchText, ignoreCase = true) == true ||
                element.description?.contains(searchText, ignoreCase = true) == true ||
                element.viewId?.contains(searchText, ignoreCase = true) == true ||
                element.className?.contains(searchText, ignoreCase = true) == true
            }
            
            // 類型過濾
            val matchesType = when {
                showClickable && element.isClickable -> true
                showEditable && element.isEditable -> true
                showScrollable && element.isScrollable -> true
                showCheckable && element.isCheckable -> true
                !showClickable && !showEditable && !showScrollable && !showCheckable -> true
                else -> false
            }
            
            matchesSearch && matchesType
        }

        updateElementList()
    }

    private fun updateElementList() {
        if (filteredElements.isEmpty()) {
            binding.recyclerViewElements.visibility = View.GONE
            binding.layoutEmptyState.visibility = View.VISIBLE
        } else {
            binding.recyclerViewElements.visibility = View.VISIBLE
            binding.layoutEmptyState.visibility = View.GONE
            elementAdapter.submitList(filteredElements)
        }
        
        // 重置選擇狀態
        selectedElement = null
        binding.btnSelect.isEnabled = false
    }

    private fun showError(message: String) {
        AlertDialog.Builder(context)
            .setTitle("錯誤")
            .setMessage(message)
            .setPositiveButton("確定", null)
            .show()
    }
}
