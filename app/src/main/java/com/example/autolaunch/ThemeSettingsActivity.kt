package com.example.autolaunch

import android.os.Bundle
import android.util.Log
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.fragment.app.Fragment
import com.google.android.material.tabs.TabLayoutMediator
import com.example.autolaunch.adapter.ThemePagerAdapter
import com.example.autolaunch.base.BaseActivity
import com.example.autolaunch.databinding.ActivityThemeSettingsBinding
import com.example.autolaunch.fragment.ThemeListFragment
import com.example.autolaunch.utils.ThemeType

/**
 * 主題設定頁面
 * 允許用戶選擇和預覽不同的主題
 */
class ThemeSettingsActivity : BaseActivity() {

    companion object {
        private const val TAG = "ThemeSettingsActivity"
    }

    private lateinit var binding: ActivityThemeSettingsBinding
    private lateinit var pagerAdapter: ThemePagerAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        binding = ActivityThemeSettingsBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupWindowInsets()
        setupToolbar()
        setupViewPager()
        setupTabs()
    }
    
    private fun setupWindowInsets() {
        ViewCompat.setOnApplyWindowInsetsListener(binding.root) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }
    }
    
    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowHomeEnabled(true)
            title = getString(R.string.theme_settings_title)
        }
        
        binding.toolbar.setNavigationOnClickListener {
            finish()
        }
    }
    
    private fun setupViewPager() {
        pagerAdapter = ThemePagerAdapter(this)
        binding.viewPager.adapter = pagerAdapter
    }

    private fun setupTabs() {
        TabLayoutMediator(binding.tabLayout, binding.viewPager) { tab, position ->
            tab.text = when (position) {
                ThemePagerAdapter.LIGHT_MODE_TAB -> getString(R.string.tab_light_mode)
                ThemePagerAdapter.DARK_MODE_TAB -> getString(R.string.tab_dark_mode)
                else -> ""
            }
        }.attach()

        // 設定主題選擇監聽器
        setupThemeSelectionListeners()
    }

    private fun setupThemeSelectionListeners() {
        // 等待 Fragment 創建完成後設定監聽器
        binding.viewPager.post {
            setupFragmentListeners()
        }

        // 也在 ViewPager 頁面變更時重新設定監聽器
        binding.viewPager.registerOnPageChangeCallback(object : androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                // 延遲一點時間確保 Fragment 完全載入
                binding.viewPager.postDelayed({
                    setupFragmentListeners()
                }, 100)
            }
        })
    }

    private fun setupFragmentListeners() {
        val lightFragment = supportFragmentManager.findFragmentByTag("f${ThemePagerAdapter.LIGHT_MODE_TAB}") as? ThemeListFragment
        val darkFragment = supportFragmentManager.findFragmentByTag("f${ThemePagerAdapter.DARK_MODE_TAB}") as? ThemeListFragment

        Log.d(TAG, "Setting up fragment listeners - Light: ${lightFragment != null}, Dark: ${darkFragment != null}")

        lightFragment?.setOnThemeSelectedListener { theme ->
            Log.d(TAG, "Light fragment theme selected: ${theme.displayName}")
            onThemeSelected(theme)
        }

        darkFragment?.setOnThemeSelectedListener { theme ->
            Log.d(TAG, "Dark fragment theme selected: ${theme.displayName}")
            onThemeSelected(theme)
        }
    }
    

    
    private fun onThemeSelected(themeType: ThemeType) {
        Log.d(TAG, "Theme selected: ${themeType.displayName}")

        // 檢查是否已經是當前主題，避免重複處理
        if (themeManager.getCurrentTheme() == themeType) {
            Log.d(TAG, "Theme already selected, ignoring")
            return
        }

        // 設定新主題
        themeManager.setTheme(themeType)

        // 立即更新所有 Fragment 的當前主題
        updateAllFragmentsTheme(themeType)

        // 應用主題並重新創建Activity
        applyThemeAndRecreate()
    }

    private fun updateAllFragmentsTheme(newTheme: ThemeType) {
        val lightFragment = supportFragmentManager.findFragmentByTag("f${ThemePagerAdapter.LIGHT_MODE_TAB}") as? ThemeListFragment
        val darkFragment = supportFragmentManager.findFragmentByTag("f${ThemePagerAdapter.DARK_MODE_TAB}") as? ThemeListFragment

        Log.d(TAG, "Updating fragments theme to: ${newTheme.displayName}")
        Log.d(TAG, "Light fragment found: ${lightFragment != null}, Dark fragment found: ${darkFragment != null}")

        lightFragment?.updateCurrentTheme(newTheme)
        darkFragment?.updateCurrentTheme(newTheme)
    }
    
    private fun applyThemeAndRecreate() {
        Log.d(TAG, "Applying theme and recreating activity")

        // 簡化主題應用邏輯，直接使用全局應用
        binding.root.postDelayed({
            if (!isFinishing && !isDestroyed) {
                try {
                    themeManager.applyThemeGlobally(this)
                } catch (e: Exception) {
                    Log.e(TAG, "Error applying theme", e)
                    // 如果全局應用失敗，直接重新創建當前Activity
                    recreate()
                }
            }
        }, 100) // 稍微增加延遲，確保UI更新完成
    }
}
