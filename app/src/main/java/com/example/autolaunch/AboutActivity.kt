package com.example.autolaunch

import android.content.Intent
import android.os.Bundle
import android.widget.Toast
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import com.example.autolaunch.base.BaseActivity
import com.example.autolaunch.databinding.ActivityAboutBinding

/**
 * 關於此APP頁面Activity
 * 整合更新紀錄和邀請朋友功能
 */
class AboutActivity : BaseActivity() {

    private lateinit var binding: ActivityAboutBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityAboutBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupUI()
        setupMenuItems()
        setupWindowInsets()
    }
    
    private fun setupUI() {
        // 設定工具列
        binding.toolbar.setNavigationIcon(androidx.appcompat.R.drawable.abc_ic_ab_back_material)
        binding.toolbar.setNavigationOnClickListener {
            finish()
        }

        // 設定版本資訊
        try {
            val pInfo = packageManager.getPackageInfo(packageName, 0)
            binding.tvAppVersion.text = getString(R.string.app_version_text, pInfo.versionName)
        } catch (e: Exception) {
            binding.tvAppVersion.text = getString(R.string.app_version_default_text)
        }
    }

    private fun setupMenuItems() {
        // 更新紀錄
        binding.menuUpdateHistory.setOnClickListener {
            startActivity(Intent(this, UpdateHistoryActivity::class.java))
        }

        // 邀請朋友
        binding.menuInviteFriends.setOnClickListener {
            inviteFriends()
        }
    }

    /**
     * 邀請朋友
     */
    private fun inviteFriends() {
        try {
            val shareIntent = Intent(Intent.ACTION_SEND).apply {
                type = "text/plain"
                putExtra(Intent.EXTRA_SUBJECT, getString(R.string.invite_friends_subject))
                putExtra(Intent.EXTRA_TEXT, getString(R.string.invite_friends_content))
            }
            startActivity(Intent.createChooser(shareIntent, getString(R.string.invite_friends_chooser_title)))
        } catch (e: Exception) {
            Toast.makeText(this, getString(R.string.share_function_unavailable), Toast.LENGTH_SHORT).show()
        }
    }

    private fun setupWindowInsets() {
        ViewCompat.setOnApplyWindowInsetsListener(binding.root) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }
    }
} 