package com.example.autolaunch.worker

import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.os.Build
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import com.example.autolaunch.AlarmManagerService
import com.example.autolaunch.R
import com.example.autolaunch.model.AppDatabase
import kotlinx.coroutines.flow.first

/**
 * 應用程式卸載後的排程清理 Worker
 * 處理被卸載應用程式相關的排程清理工作
 */
class PackageRemovedWorker(
    context: Context,
    params: WorkerParameters
) : CoroutineWorker(context, params) {
    
    companion object {
        private const val TAG = "PackageRemovedWorker"
        private const val NOTIFICATION_CHANNEL_ID = "package_removed"
        private const val NOTIFICATION_ID = 1002
    }
    
    override suspend fun doWork(): Result {
        val packageName = inputData.getString("package_name")
        if (packageName.isNullOrEmpty()) {
            Log.e(TAG, "Package name is null or empty")
            return Result.failure()
        }
        
        Log.d(TAG, "Processing package removal: $packageName")
        
        return try {
            val database = AppDatabase.getDatabase(applicationContext)
            val scheduleDao = database.scheduleDao()
            val alarmService = AlarmManagerService(applicationContext)
            
            // 查找所有使用該包名的排程
            val affectedSchedules = scheduleDao.getSchedulesByPackageName(packageName).first()
            
            if (affectedSchedules.isNotEmpty()) {
                Log.i(TAG, "Found ${affectedSchedules.size} schedules for removed package: $packageName")
                
                // 取消所有相關的鬧鐘
                affectedSchedules.forEach { schedule ->
                    try {
                        alarmService.cancelAlarm(schedule.id)
                        Log.d(TAG, "Cancelled alarm for schedule ${schedule.id}")
                    } catch (e: Exception) {
                        Log.e(TAG, "Failed to cancel alarm for schedule ${schedule.id}", e)
                    }
                }
                
                // 從資料庫刪除所有相關排程
                affectedSchedules.forEach { schedule ->
                    try {
                        scheduleDao.delete(schedule)
                        Log.d(TAG, "Deleted schedule ${schedule.id} for app: ${schedule.getDisplayName()}")
                    } catch (e: Exception) {
                        Log.e(TAG, "Failed to delete schedule ${schedule.id}", e)
                    }
                }
                
                // 發送通知告知用戶
                sendPackageRemovedNotification(affectedSchedules.size, getAppNameFromSchedules(affectedSchedules))
                
                Log.i(TAG, "Successfully processed removal of $packageName, cleaned ${affectedSchedules.size} schedules")
            } else {
                Log.d(TAG, "No schedules found for removed package: $packageName")
            }
            
            Result.success()
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to process package removal for $packageName", e)
            Result.failure()
        }
    }
    
    private fun getAppNameFromSchedules(schedules: List<com.example.autolaunch.model.Schedule>): String {
        return schedules.firstOrNull()?.getDisplayName() ?: applicationContext.getString(R.string.unknown_app)
    }
    
    private fun sendPackageRemovedNotification(scheduleCount: Int, appName: String) {
        val notificationManager = applicationContext.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        
        // 創建通知通道（Android 8.0+）
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                NOTIFICATION_CHANNEL_ID,
                applicationContext.getString(R.string.notification_channel_name_package_removed),
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = applicationContext.getString(R.string.notification_channel_description_package_removed)
            }
            notificationManager.createNotificationChannel(channel)
        }
        
        // 創建通知
        val notification = NotificationCompat.Builder(applicationContext, NOTIFICATION_CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_schedule_24)
            .setContentTitle(applicationContext.getString(R.string.notification_title_schedule_cleaned))
            .setContentText(applicationContext.getString(R.string.notification_text_package_removed, appName, scheduleCount))
            .setStyle(NotificationCompat.BigTextStyle()
                .bigText(applicationContext.getString(R.string.notification_big_text_package_removed, appName, scheduleCount)))
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setAutoCancel(true)
            .build()
        
        notificationManager.notify(NOTIFICATION_ID, notification)
    }
} 