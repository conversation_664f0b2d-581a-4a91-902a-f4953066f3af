package com.example.autolaunch.worker

import android.content.Context
import android.util.Log
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import com.example.autolaunch.AlarmManagerService
import com.example.autolaunch.model.AppDatabase
import kotlinx.coroutines.flow.first

/**
 * 排程重新註冊 Worker
 * 用於在設備重啟或應用程式更新後重新註冊所有啟用的排程
 */
class ScheduleReregistrationWorker(
    context: Context,
    params: WorkerParameters
) : CoroutineWorker(context, params) {
    
    companion object {
        private const val TAG = "ScheduleReregistrationWorker"
    }
    
    override suspend fun doWork(): Result {
        Log.d(TAG, "Starting schedule re-registration")
        
        return try {
            val database = AppDatabase.getDatabase(applicationContext)
            val scheduleDao = database.scheduleDao()
            val alarmService = AlarmManagerService(applicationContext)
            
            // 獲取所有啟用的排程
            val enabledSchedules = scheduleDao.getEnabledSchedules().first()
            
            Log.i(TAG, "Found ${enabledSchedules.size} enabled schedules to re-register")
            
            // 重新註冊每個啟用的排程
            enabledSchedules.forEach { schedule ->
                try {
                    alarmService.setAlarm(schedule)
                    Log.d(TAG, "Re-registered alarm for schedule ${schedule.id}: ${schedule.getDisplayName()}")
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to re-register alarm for schedule ${schedule.id}", e)
                }
            }
            
            Log.i(TAG, "Schedule re-registration completed successfully")
            Result.success()
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to re-register schedules", e)
            Result.failure()
        }
    }
} 