package com.example.autolaunch.worker

import android.content.Context
import android.util.Log
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import com.example.autolaunch.AlarmManagerService
import com.example.autolaunch.model.AppDatabase
import com.example.autolaunch.service.ScheduleService
import com.example.autolaunch.utils.BackgroundExecutionHelper
import com.example.autolaunch.utils.BatteryOptimizationHelper
import com.example.autolaunch.utils.ExceptionHandler
import com.example.autolaunch.utils.SystemLogManager
import com.example.autolaunch.utils.SystemPermissionHelper
import kotlinx.coroutines.flow.first

/**
 * 健康檢查 Worker
 * 定期檢查系統狀態並進行必要的修復
 */
class HealthCheckWorker(
    context: Context,
    params: WorkerParameters
) : CoroutineWorker(context, params) {
    
    companion object {
        private const val TAG = "HealthCheckWorker"
    }
    
    override suspend fun doWork(): Result {
        Log.d(TAG, "Starting health check")
        
        return ExceptionHandler.safeExecute(applicationContext) {
            performHealthCheck()
            Result.success()
        } ?: Result.retry()
    }
    
    private suspend fun performHealthCheck() {
        val issues = mutableListOf<String>()
        
        // 1. 檢查前台服務狀態
        if (!BackgroundExecutionHelper.isScheduleServiceRunning(applicationContext)) {
            Log.w(TAG, "Foreground service not running, attempting to restart")
            issues.add("前台服務未運行")
            
            try {
                ScheduleService.startService(applicationContext)
                Log.i(TAG, "Foreground service restarted successfully")
            } catch (e: Exception) {
                Log.e(TAG, "Failed to restart foreground service", e)
                issues.add("前台服務重啟失敗")
            }
        }
        
        // 2. 檢查排程狀態
        val database = AppDatabase.getDatabase(applicationContext)
        val scheduleDao = database.scheduleDao()
        val alarmService = AlarmManagerService(applicationContext)
        
        val enabledSchedules = scheduleDao.getEnabledSchedules().first()
        Log.d(TAG, "Found ${enabledSchedules.size} enabled schedules")
        
        // 3. 驗證每個排程的鬧鐘狀態
        var repairedSchedules = 0
        enabledSchedules.forEach { schedule ->
            try {
                // 重新設定鬧鐘以確保其有效性
                alarmService.setAlarm(schedule)
                repairedSchedules++
            } catch (e: Exception) {
                Log.e(TAG, "Failed to repair alarm for schedule ${schedule.id}", e)
                issues.add("排程 ${schedule.id} 鬧鐘修復失敗")
            }
        }
        
        if (repairedSchedules > 0) {
            Log.i(TAG, "Repaired $repairedSchedules schedule alarms")
        }
        
        // 4. 檢查權限狀態
        checkPermissions(issues)
        
        // 5. 檢查電池優化狀態
        checkBatteryOptimization(issues)
        
        // 6. 記錄健康檢查結果
        val healthStatus = if (issues.isEmpty()) "健康" else "發現 ${issues.size} 個問題"
        SystemLogManager.logAppStarted(
            context = applicationContext,
            details = "健康檢查完成 - 狀態: $healthStatus, 啟用排程: ${enabledSchedules.size}, 修復排程: $repairedSchedules"
        )
        
        if (issues.isNotEmpty()) {
            Log.w(TAG, "Health check found issues: ${issues.joinToString(", ")}")
        } else {
            Log.i(TAG, "Health check completed successfully - all systems healthy")
        }
    }
    
    private fun checkPermissions(issues: MutableList<String>) {
        // 檢查系統彈窗權限
        if (!SystemPermissionHelper.hasSystemAlertWindowPermission(applicationContext)) {
            issues.add("缺少系統彈窗權限")
        }
        
        // 檢查精確鬧鐘權限
        if (!AlarmManagerService.canScheduleExactAlarms(applicationContext)) {
            issues.add("缺少精確鬧鐘權限")
        }
    }
    
    private fun checkBatteryOptimization(issues: MutableList<String>) {
        if (!BatteryOptimizationHelper.isIgnoringBatteryOptimizations(applicationContext)) {
            issues.add("電池優化未關閉")
        }
    }
}

/**
 * 看門狗 Worker
 * 執行深度系統檢查和恢復
 */
class WatchdogWorker(
    context: Context,
    params: WorkerParameters
) : CoroutineWorker(context, params) {
    
    companion object {
        private const val TAG = "WatchdogWorker"
    }
    
    override suspend fun doWork(): Result {
        Log.d(TAG, "Starting watchdog check")
        
        return ExceptionHandler.safeExecute(applicationContext) {
            performWatchdogCheck()
            Result.success()
        } ?: Result.retry()
    }
    
    private suspend fun performWatchdogCheck() {
        val database = AppDatabase.getDatabase(applicationContext)
        val scheduleDao = database.scheduleDao()
        val alarmService = AlarmManagerService(applicationContext)
        
        // 1. 深度檢查所有排程
        val allSchedules = scheduleDao.getAllSchedules().first()
        val enabledSchedules = allSchedules.filter { it.isEnabled }
        
        Log.i(TAG, "Watchdog checking ${allSchedules.size} total schedules, ${enabledSchedules.size} enabled")
        
        // 2. 檢查是否有過期的單次排程需要清理
        val currentTime = System.currentTimeMillis()
        val expiredSingleSchedules = allSchedules.filter { schedule ->
            schedule.getRepeatModeEnum() == com.example.autolaunch.model.RepeatMode.ONCE &&
            schedule.singleExecuteDate != null &&
            schedule.singleExecuteDate < currentTime &&
            schedule.isEnabled
        }
        
        if (expiredSingleSchedules.isNotEmpty()) {
            Log.i(TAG, "Found ${expiredSingleSchedules.size} expired single schedules to disable")
            expiredSingleSchedules.forEach { schedule ->
                scheduleDao.updateEnabledStatus(schedule.id, false)
                alarmService.cancelAlarm(schedule)
            }
        }
        
        // 3. 檢查資料庫完整性
        val totalScheduleCount = scheduleDao.getScheduleCount()
        val enabledScheduleCount = scheduleDao.getEnabledScheduleCount()
        
        // 4. 檢查系統資源使用情況
        val runtime = Runtime.getRuntime()
        val usedMemory = runtime.totalMemory() - runtime.freeMemory()
        val maxMemory = runtime.maxMemory()
        val memoryUsagePercent = (usedMemory * 100) / maxMemory
        
        // 5. 執行資料庫維護
        if (totalScheduleCount > 100) {
            // 如果排程數量過多，清理舊的已禁用排程
            val thirtyDaysAgo = currentTime - (30 * 24 * 60 * 60 * 1000)
            val deletedCount = scheduleDao.deleteOldDisabledSchedules(thirtyDaysAgo)
            if (deletedCount > 0) {
                Log.i(TAG, "Cleaned up $deletedCount old disabled schedules")
            }
        }
        
        // 6. 記錄看門狗檢查結果
        SystemLogManager.logAppStarted(
            context = applicationContext,
            details = "看門狗檢查完成 - 總排程: $totalScheduleCount, 啟用: $enabledScheduleCount, " +
                    "過期清理: ${expiredSingleSchedules.size}, 記憶體使用: ${memoryUsagePercent}%"
        )
        
        Log.i(TAG, "Watchdog check completed - Memory usage: ${memoryUsagePercent}%")
    }
}

/**
 * 恢復 Worker
 * 執行緊急恢復操作
 */
class RecoveryWorker(
    context: Context,
    params: WorkerParameters
) : CoroutineWorker(context, params) {
    
    companion object {
        private const val TAG = "RecoveryWorker"
    }
    
    override suspend fun doWork(): Result {
        Log.d(TAG, "Starting recovery operations")
        
        return ExceptionHandler.safeExecute(applicationContext) {
            performRecoveryOperations()
            Result.success()
        } ?: Result.failure()
    }
    
    private suspend fun performRecoveryOperations() {
        val database = AppDatabase.getDatabase(applicationContext)
        val scheduleDao = database.scheduleDao()
        val alarmService = AlarmManagerService(applicationContext)
        
        // 1. 強制重啟前台服務
        try {
            ScheduleService.startService(applicationContext)
            Log.i(TAG, "Foreground service recovery completed")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to recover foreground service", e)
        }
        
        // 2. 重新註冊所有啟用的排程
        val enabledSchedules = scheduleDao.getEnabledSchedules().first()
        var recoveredCount = 0
        
        enabledSchedules.forEach { schedule ->
            try {
                alarmService.setAlarm(schedule)
                recoveredCount++
            } catch (e: Exception) {
                Log.e(TAG, "Failed to recover schedule ${schedule.id}", e)
            }
        }
        
        // 3. 檢查並修復資料庫狀態
        try {
            // 更新所有排程的下次執行時間
            enabledSchedules.forEach { schedule ->
                val nextTime = alarmService.calculateNextExecutionTime(schedule)
                if (nextTime != null) {
                    // 使用 update 方法更新排程
                    val updatedSchedule = schedule.copy(nextExecutedTime = nextTime)
                    scheduleDao.update(updatedSchedule)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to update next execution times", e)
        }
        
        // 4. 記錄恢復結果
        SystemLogManager.logAppStarted(
            context = applicationContext,
            details = "緊急恢復操作完成 - 恢復排程數: $recoveredCount/${enabledSchedules.size}"
        )
        
        Log.i(TAG, "Recovery operations completed - Recovered $recoveredCount/${enabledSchedules.size} schedules")
    }
}
