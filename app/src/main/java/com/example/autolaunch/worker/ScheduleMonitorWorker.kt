package com.example.autolaunch.worker

import android.content.Context
import android.util.Log
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import com.example.autolaunch.AlarmManagerService
import com.example.autolaunch.model.AppDatabase
import com.example.autolaunch.service.ScheduleService
import kotlinx.coroutines.flow.first

/**
 * 排程監控 Worker
 * 定期檢查並確保所有排程都正常運行
 */
class ScheduleMonitorWorker(
    context: Context,
    params: WorkerParameters
) : CoroutineWorker(context, params) {
    
    companion object {
        private const val TAG = "ScheduleMonitorWorker"
    }
    
    override suspend fun doWork(): Result {
        Log.d(TAG, "Starting schedule monitoring check")
        
        return try {
            // 確保前台服務正在運行
            ScheduleService.startService(applicationContext)
            
            val database = AppDatabase.getDatabase(applicationContext)
            val scheduleDao = database.scheduleDao()
            val alarmService = AlarmManagerService(applicationContext)
            
            // 獲取所有啟用的排程
            val enabledSchedules = scheduleDao.getEnabledSchedules().first()
            
            Log.i(TAG, "Monitoring ${enabledSchedules.size} enabled schedules")
            
            // 檢查每個排程是否仍然有效
            enabledSchedules.forEach { schedule ->
                try {
                    // 重新設定鬧鐘以確保它們仍然有效
                    alarmService.setAlarm(schedule)
                    Log.d(TAG, "Verified alarm for schedule ${schedule.id}: ${schedule.getDisplayName()}")
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to verify alarm for schedule ${schedule.id}", e)
                }
            }
            
            Log.i(TAG, "Schedule monitoring check completed successfully")
            Result.success()
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to complete schedule monitoring check", e)
            Result.retry()
        }
    }
}
