package com.example.autolaunch

import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AlertDialog
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.autolaunch.adapter.SystemLogAdapter
import com.example.autolaunch.base.BaseActivity
import com.example.autolaunch.databinding.ActivitySystemLogBinding
import com.example.autolaunch.utils.SystemLogManager
import com.example.autolaunch.viewmodel.SystemLogViewModel
import kotlinx.coroutines.launch

/**
 * 系統日誌頁面Activity
 */
class SystemLogActivity : BaseActivity() {
    
    private lateinit var binding: ActivitySystemLogBinding
    private lateinit var systemLogAdapter: SystemLogAdapter
    private val viewModel: SystemLogViewModel by viewModels()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        binding = ActivitySystemLogBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupUI()
        setupRecyclerView()
        setupWindowInsets()
        observeViewModel()
    }
    
    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.menu_system_log, menu)
        return true
    }
    
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_storage_info -> {
                showStorageInfoDialog()
                true
            }
            R.id.action_export_logs -> {
                exportLogs()
                true
            }
            R.id.action_manual_cleanup -> {
                showManualCleanupDialog()
                true
            }
            R.id.action_clear_logs -> {
                showClearLogsDialog()
                true
            }
            R.id.action_filter -> {
                showFilterDialog()
                true
            }
            R.id.action_search -> {
                showSearchDialog()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }
    
    private fun setupUI() {
        // 設定工具列
        binding.toolbar.setNavigationIcon(androidx.appcompat.R.drawable.abc_ic_ab_back_material)
        binding.toolbar.setNavigationOnClickListener {
            finish()
        }

        // 設定工具列選單
        setSupportActionBar(binding.toolbar)

        // 設定下拉刷新
        binding.swipeRefreshLayout.setOnRefreshListener {
            viewModel.refreshLogs()
        }

        // 設定重試按鈕
        binding.btnRetry.setOnClickListener {
            viewModel.refreshLogs()
        }
    }
    
    private fun setupRecyclerView() {
        systemLogAdapter = SystemLogAdapter { systemLog ->
            // 處理日誌項目點選事件 - 顯示詳細資訊
            showLogDetails(systemLog)
        }
        
        binding.recyclerViewLogs.apply {
            layoutManager = LinearLayoutManager(this@SystemLogActivity)
            adapter = systemLogAdapter
        }
    }
    
    private fun setupWindowInsets() {
        ViewCompat.setOnApplyWindowInsetsListener(binding.root) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }
    }
    
    private fun observeViewModel() {
        // 觀察日誌清單
        viewModel.logs.observe(this) { logs ->
            try {
                systemLogAdapter.submitLogList(logs)
                binding.swipeRefreshLayout.isRefreshing = false

                // 更新空狀態顯示
                updateEmptyState(logs.isEmpty())
            } catch (e: Exception) {
                handleError("更新日誌列表時發生錯誤", e)
                binding.swipeRefreshLayout.isRefreshing = false
            }
        }

        // 觀察載入狀態
        viewModel.isLoading.observe(this) { isLoading ->
            try {
                binding.swipeRefreshLayout.isRefreshing = isLoading
                // 顯示/隱藏載入指示器
                updateLoadingState(isLoading)
            } catch (e: Exception) {
                handleError("更新載入狀態時發生錯誤", e)
            }
        }

        // 觀察錯誤信息
        viewModel.error.observe(this) { error ->
            if (error != null) {
                showErrorMessage(error)
                viewModel.clearError()
            }
        }

        // 觀察統計信息
        viewModel.statistics.observe(this) { stats ->
            try {
                updateStatistics(stats)
            } catch (e: Exception) {
                handleError("更新統計信息時發生錯誤", e)
            }
        }
    }

    /**
     * 更新UI狀態
     */
    private fun updateUIState(state: UIState) {
        // 隱藏所有狀態佈局
        binding.recyclerViewLogs.visibility = android.view.View.GONE
        binding.emptyStateLayout.visibility = android.view.View.GONE
        binding.loadingStateLayout.visibility = android.view.View.GONE
        binding.errorStateLayout.visibility = android.view.View.GONE

        when (state) {
            is UIState.Loading -> {
                binding.loadingStateLayout.visibility = android.view.View.VISIBLE
                binding.swipeRefreshLayout.isRefreshing = false // 避免重複顯示載入指示器
            }
            is UIState.Success -> {
                if (state.isEmpty) {
                    binding.emptyStateLayout.visibility = android.view.View.VISIBLE
                } else {
                    binding.recyclerViewLogs.visibility = android.view.View.VISIBLE
                }
                binding.swipeRefreshLayout.isRefreshing = false
            }
            is UIState.Error -> {
                binding.errorStateLayout.visibility = android.view.View.VISIBLE
                binding.tvErrorMessage.text = state.message
                binding.swipeRefreshLayout.isRefreshing = false
            }
        }
    }

    /**
     * 更新空狀態顯示
     */
    private fun updateEmptyState(isEmpty: Boolean) {
        updateUIState(if (isEmpty) UIState.Success(true) else UIState.Success(false))
    }

    /**
     * 更新載入狀態
     */
    private fun updateLoadingState(isLoading: Boolean) {
        if (isLoading) {
            updateUIState(UIState.Loading)
        }
        binding.swipeRefreshLayout.isRefreshing = isLoading
    }

    /**
     * UI狀態密封類
     */
    private sealed class UIState {
        object Loading : UIState()
        data class Success(val isEmpty: Boolean) : UIState()
        data class Error(val message: String) : UIState()
    }

    /**
     * 顯示錯誤訊息
     */
    private fun showErrorMessage(error: String) {
        try {
            Toast.makeText(this, error, Toast.LENGTH_LONG).show()
        } catch (e: Exception) {
            // 如果連 Toast 都無法顯示，記錄到系統日誌
            android.util.Log.e("SystemLogActivity", "Failed to show error message: $error", e)
        }
    }

    /**
     * 統一錯誤處理
     */
    private fun handleError(message: String, exception: Exception) {
        android.util.Log.e("SystemLogActivity", message, exception)
        showErrorMessage("$message：${exception.message}")

        // 記錄錯誤到系統日誌
        SystemLogManager.logSystemError(
            context = this,
            errorMessage = message,
            exception = exception,
            details = "Activity: SystemLogActivity"
        )
    }
    
    private fun updateStatistics(stats: com.example.autolaunch.utils.LogStatistics) {
        binding.tvTotalCount.text = "總計：${stats.totalCount}"
        binding.tvSuccessCount.text = "成功：${stats.successCount}"
        binding.tvWarningCount.text = "警告：${stats.warningCount}"
        binding.tvErrorCount.text = "錯誤：${stats.errorCount}"
    }
    
    private fun showLogDetails(systemLog: com.example.autolaunch.model.SystemLog) {
        try {
            val details = buildString {
                append("時間：${systemLog.getFullDateTime()}\n")
                append("類型：${systemLog.getLogTypeEnum().displayName}\n")
                append("操作：${systemLog.getActionTypeEnum().displayName}\n")
                append("訊息：${systemLog.message}\n")

                if (!systemLog.scheduleName.isNullOrBlank()) {
                    append("排程：${systemLog.scheduleName}\n")
                }

                if (!systemLog.details.isNullOrBlank()) {
                    append("\n詳細資訊：\n${systemLog.details}")
                }
            }

            AlertDialog.Builder(this)
                .setTitle("日誌詳情")
                .setMessage(details)
                .setPositiveButton("確定", null)
                .setCancelable(true)
                .show()
        } catch (e: Exception) {
            handleError("顯示日誌詳情時發生錯誤", e)
        }
    }
    
    private fun showClearLogsDialog() {
        AlertDialog.Builder(this)
            .setTitle(getString(R.string.clear_logs_title))
            .setMessage(getString(R.string.clear_logs_message))
            .setPositiveButton(getString(R.string.confirm)) { _, _ ->
                clearAllLogs()
            }
            .setNegativeButton(getString(R.string.cancel), null)
            .show()
    }
    
    private fun showFilterDialog() {
        try {
            val filterOptions = arrayOf(
                "全部日誌",
                "成功日誌",
                "資訊日誌",
                "警告日誌",
                "錯誤日誌",
                "排程相關",
                "今天的日誌"
            )

            AlertDialog.Builder(this)
                .setTitle("篩選日誌")
                .setItems(filterOptions) { _, which ->
                    try {
                        when (which) {
                            0 -> viewModel.showAllLogs()
                            1 -> viewModel.filterByType(com.example.autolaunch.model.LogType.SUCCESS)
                            2 -> viewModel.filterByType(com.example.autolaunch.model.LogType.INFO)
                            3 -> viewModel.filterByType(com.example.autolaunch.model.LogType.WARNING)
                            4 -> viewModel.filterByType(com.example.autolaunch.model.LogType.ERROR)
                            5 -> viewModel.filterScheduleRelated()
                            6 -> viewModel.filterTodayLogs()
                        }
                    } catch (e: Exception) {
                        handleError("篩選日誌時發生錯誤", e)
                    }
                }
                .setCancelable(true)
                .show()
        } catch (e: Exception) {
            handleError("顯示篩選對話框時發生錯誤", e)
        }
    }

    private fun showSearchDialog() {
        try {
            val editText = android.widget.EditText(this).apply {
                hint = getString(R.string.search_keyword_hint)
                maxLines = 1
                inputType = android.text.InputType.TYPE_CLASS_TEXT
            }

            AlertDialog.Builder(this)
                .setTitle(getString(R.string.search_logs_title))
                .setView(editText)
                .setPositiveButton(getString(R.string.search)) { _, _ ->
                    try {
                        val query = editText.text.toString().trim()
                        if (query.isNotEmpty() && query.length >= 2) {
                            searchLogs(query)
                        } else if (query.isEmpty()) {
                            viewModel.showAllLogs()
                        } else {
                            showErrorMessage("搜尋關鍵字至少需要2個字元")
                        }
                    } catch (e: Exception) {
                        handleError("執行搜尋時發生錯誤", e)
                    }
                }
                .setNegativeButton(getString(R.string.clear_search)) { _, _ ->
                    try {
                        viewModel.showAllLogs()
                    } catch (e: Exception) {
                        handleError("清除搜尋時發生錯誤", e)
                    }
                }
                .setCancelable(true)
                .show()
        } catch (e: Exception) {
            handleError("顯示搜尋對話框時發生錯誤", e)
        }
    }

    private fun searchLogs(query: String) {
        try {
            if (query.isBlank()) {
                showErrorMessage("搜尋關鍵字不能為空")
                return
            }

            viewModel.searchLogs(query).observe(this) { searchResults ->
                try {
                    systemLogAdapter.submitLogList(searchResults)

                    val message = if (searchResults.isEmpty()) {
                        getString(R.string.no_matching_logs)
                    } else {
                        getString(R.string.found_matching_logs, searchResults.size)
                    }
                    Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
                } catch (e: Exception) {
                    handleError("處理搜尋結果時發生錯誤", e)
                }
            }
        } catch (e: Exception) {
            handleError("搜尋日誌時發生錯誤", e)
        }
    }

    private fun clearAllLogs() {
        lifecycleScope.launch {
            try {
                // 顯示載入狀態
                runOnUiThread {
                    binding.swipeRefreshLayout.isRefreshing = true
                }

                SystemLogManager.clearAllLogs(this@SystemLogActivity) { success ->
                    runOnUiThread {
                        binding.swipeRefreshLayout.isRefreshing = false
                        if (success) {
                            Toast.makeText(this@SystemLogActivity, "日誌已清除", Toast.LENGTH_SHORT).show()
                            viewModel.refreshLogs()
                        } else {
                            showErrorMessage("清除日誌失敗")
                        }
                    }
                }
            } catch (e: Exception) {
                runOnUiThread {
                    binding.swipeRefreshLayout.isRefreshing = false
                }
                handleError("清除日誌時發生錯誤", e)
            }
        }
    }

    private fun exportLogs() {
        lifecycleScope.launch {
            try {
                // 顯示載入狀態
                runOnUiThread {
                    binding.swipeRefreshLayout.isRefreshing = true
                }

                val exportText = SystemLogManager.exportLogsAsText(this@SystemLogActivity)

                runOnUiThread {
                    binding.swipeRefreshLayout.isRefreshing = false

                    if (exportText.isNotBlank()) {
                        // 創建分享Intent
                        val shareIntent = Intent().apply {
                            action = Intent.ACTION_SEND
                            type = "text/plain"
                            putExtra(Intent.EXTRA_TEXT, exportText)
                            putExtra(Intent.EXTRA_SUBJECT, "AutoLaunch 系統日誌")
                        }

                        try {
                            startActivity(Intent.createChooser(shareIntent, "分享日誌"))
                        } catch (e: Exception) {
                            handleError("無法開啟分享功能", e)
                        }
                    } else {
                        showErrorMessage("沒有日誌可以匯出")
                    }
                }

            } catch (e: Exception) {
                runOnUiThread {
                    binding.swipeRefreshLayout.isRefreshing = false
                }
                handleError("匯出日誌時發生錯誤", e)
            }
        }
    }

    /**
     * 顯示儲存資訊對話框
     */
    private fun showStorageInfoDialog() {
        viewModel.getStorageInfo().observe(this) { storageInfo ->
            try {
                val message = buildString {
                    appendLine("日誌數量：${storageInfo.totalLogs} 條")
                    appendLine("估計大小：${storageInfo.getReadableSize()}")
                    appendLine("保留天數：${storageInfo.retentionDays} 天")
                    appendLine("時間範圍：${storageInfo.getTimeRangeDescription()}")
                }

                AlertDialog.Builder(this)
                    .setTitle("儲存資訊")
                    .setMessage(message)
                    .setPositiveButton("確定", null)
                    .setNeutralButton("清理舊日誌") { _, _ ->
                        showManualCleanupDialog()
                    }
                    .setCancelable(true)
                    .show()
            } catch (e: Exception) {
                handleError("顯示儲存資訊時發生錯誤", e)
            }
        }
    }

    /**
     * 顯示手動清理對話框
     */
    private fun showManualCleanupDialog() {
        try {
            val options = arrayOf(
                "清理 3 天前的日誌",
                "清理 7 天前的日誌",
                "清理 14 天前的日誌",
                "清理 30 天前的日誌"
            )

            AlertDialog.Builder(this)
                .setTitle("手動清理")
                .setMessage("選擇要清理的日誌範圍：")
                .setItems(options) { _, which ->
                    val days = when (which) {
                        0 -> 3
                        1 -> 7
                        2 -> 14
                        3 -> 30
                        else -> 7
                    }
                    performManualCleanup(days)
                }
                .setCancelable(true)
                .show()
        } catch (e: Exception) {
            handleError("顯示清理對話框時發生錯誤", e)
        }
    }

    /**
     * 執行手動清理
     */
    private fun performManualCleanup(beforeDays: Int) {
        viewModel.performManualCleanup(beforeDays).observe(this) { result ->
            try {
                if (result.success) {
                    Toast.makeText(this, result.message, Toast.LENGTH_SHORT).show()
                    viewModel.refreshLogs()
                } else {
                    showErrorMessage(result.message)
                }
            } catch (e: Exception) {
                handleError("處理清理結果時發生錯誤", e)
            }
        }
    }
}
