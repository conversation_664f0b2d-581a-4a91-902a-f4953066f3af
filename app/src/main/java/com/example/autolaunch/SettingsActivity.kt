package com.example.autolaunch

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.View
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import com.example.autolaunch.base.BaseActivity
import com.example.autolaunch.databinding.ActivitySettingsBinding
import com.example.autolaunch.utils.PersistentNotificationManager
import com.example.autolaunch.utils.SettingsManager

/**
 * 設置頁面
 * 整合所有設置選項：主題、語言、備份、系統紀錄等
 */
class SettingsActivity : BaseActivity() {

    companion object {
        private const val TAG = "SettingsActivity"
    }

    private lateinit var binding: ActivitySettingsBinding
    private lateinit var settingsManager: SettingsManager
    private lateinit var notificationManager: PersistentNotificationManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivitySettingsBinding.inflate(layoutInflater)
        setContentView(binding.root)

        settingsManager = SettingsManager.getInstance(this)
        notificationManager = PersistentNotificationManager.getInstance(this)

        setupUI()
        setupSettings()
        setupMenuItems()
        setupWindowInsets()
    }
    
    private fun setupUI() {
        // 設定工具列
        binding.toolbar.setNavigationIcon(androidx.appcompat.R.drawable.abc_ic_ab_back_material)
        binding.toolbar.setNavigationOnClickListener {
            finish()
        }
    }
    
    private fun setupSettings() {
        // 設定常駐通知開關
        val isPersistentNotificationEnabled = settingsManager.isPersistentNotificationEnabled()
        binding.switchPersistentNotification.isChecked = isPersistentNotificationEnabled

        binding.switchPersistentNotification.setOnCheckedChangeListener { _, isChecked ->
            Log.d(TAG, "Persistent notification switch changed: $isChecked")

            settingsManager.setPersistentNotificationEnabled(isChecked)
            notificationManager.updatePersistentNotificationState()
        }

        // 設定歡迎卡片顯示開關
        val isWelcomeCardVisible = settingsManager.isWelcomeCardVisible()
        binding.switchWelcomeCard.isChecked = isWelcomeCardVisible

        binding.switchWelcomeCard.setOnCheckedChangeListener { _, isChecked ->
            Log.d(TAG, "Welcome card switch changed: $isChecked")

            settingsManager.setWelcomeCardVisible(isChecked)
        }

        // 設定重新顯示歡迎導覽按鈕
        binding.btnShowWelcomeGuide.setOnClickListener {
            Log.d(TAG, "Show welcome guide button clicked")

            // 重設首次啟動標記
            settingsManager.setFirstLaunch(true)

            // 啟動歡迎導覽頁面
            val intent = Intent(this, WelcomeActivity::class.java)
            startActivity(intent)
        }
    }

    private fun setupMenuItems() {
        // 由於移除了外觀設置和資料管理區塊，這裡不再需要設置點擊事件
    }

    private fun setupWindowInsets() {
        ViewCompat.setOnApplyWindowInsetsListener(binding.root) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }
    }
}
