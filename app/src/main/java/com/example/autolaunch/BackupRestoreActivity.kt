package com.example.autolaunch

import android.os.Bundle
import android.view.View
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.viewpager2.widget.ViewPager2
import com.example.autolaunch.adapter.BackupPagerAdapter
import com.example.autolaunch.base.BaseActivity
import com.example.autolaunch.databinding.ActivityBackupRestoreBinding
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.google.android.material.tabs.TabLayoutMediator

/**
 * 備份與匯入Activity
 * 提供排程數據的備份和恢復功能，使用Tab布局
 */
class BackupRestoreActivity : BaseActivity() {
    
    companion object {
        private const val TAG = "BackupRestoreActivity"
    }
    
    private lateinit var binding: ActivityBackupRestoreBinding
    private lateinit var pagerAdapter: BackupPagerAdapter
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityBackupRestoreBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupToolbar()
        setupViewPager()
        setupTabs()
        setupWindowInsets()
        checkGoogleSignInStatus()
    }
    
    private fun setupToolbar() {
        // 設定工具列
        binding.toolbar.setNavigationIcon(androidx.appcompat.R.drawable.abc_ic_ab_back_material)
        binding.toolbar.setNavigationOnClickListener {
            finish()
        }
    }
    
    private fun setupViewPager() {
        // 設定ViewPager2
        pagerAdapter = BackupPagerAdapter(this)
        binding.viewPager.adapter = pagerAdapter
        
        // 禁用滑動切換（可選）
        binding.viewPager.isUserInputEnabled = true
    }
    
    private fun setupTabs() {
        // 連接TabLayout和ViewPager2
        TabLayoutMediator(binding.tabLayout, binding.viewPager) { tab, position ->
            tab.text = pagerAdapter.getTabTitle(position)
        }.attach()
    }
    
    private fun checkGoogleSignInStatus() {
        val account = GoogleSignIn.getLastSignedInAccount(this)
        if (account != null) {
            // 如果已登錄Google帳戶，預設選擇雲端備份標籤
            binding.viewPager.setCurrentItem(1, false) // false表示無動畫切換
        }
    }
    
    private fun setupWindowInsets() {
        ViewCompat.setOnApplyWindowInsetsListener(binding.root) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }
    }

    /**
     * 顯示/隱藏進度條
     * 供Fragment調用
     */
    fun showProgress(show: Boolean) {
        binding.progressBar.visibility = if (show) View.VISIBLE else View.GONE
    }
}
