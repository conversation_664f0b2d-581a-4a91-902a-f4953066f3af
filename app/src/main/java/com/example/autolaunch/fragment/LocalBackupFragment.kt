package com.example.autolaunch.fragment

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.autolaunch.R
import com.example.autolaunch.adapter.LocalBackupFileAdapter
import com.example.autolaunch.databinding.FragmentLocalBackupBinding
import com.example.autolaunch.dialog.BackupFileListDialog
import com.example.autolaunch.dialog.BackupPreviewDialog
import com.example.autolaunch.model.BackupFileInfo
import com.example.autolaunch.utils.BackupManager
import com.example.autolaunch.utils.CloudBackupManager
import com.example.autolaunch.utils.PermissionHelper
import com.example.autolaunch.utils.setDebounceClickListener
import kotlinx.coroutines.launch
import java.io.File

/**
 * 本地備份Fragment
 */
class LocalBackupFragment : Fragment() {
    
    companion object {
        private const val TAG = "LocalBackupFragment"
        
        fun newInstance(): LocalBackupFragment {
            return LocalBackupFragment()
        }
    }
    
    private var _binding: FragmentLocalBackupBinding? = null
    private val binding get() = _binding!!
    
    private lateinit var cloudBackupManager: CloudBackupManager
    private lateinit var backupFileAdapter: LocalBackupFileAdapter
    private lateinit var backupManager: BackupManager
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentLocalBackupBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        cloudBackupManager = CloudBackupManager(requireContext())
        backupManager = BackupManager(requireContext())
        
        setupUI()
        setupRecyclerView()
        loadBackupFiles()
        updateBackupLocationInfo()
    }
    
    private fun setupUI() {
        // 創建備份按鈕 - 使用防抖動點擊監聽器
        binding.btnCreateBackup.setDebounceClickListener(2000L) {
            createLocalBackup()
        }
        
        // 複製路徑按鈕
        binding.btnCopyPath.setOnClickListener {
            copyBackupPathToClipboard()
        }
    }
    
    private fun setupRecyclerView() {
        backupFileAdapter = LocalBackupFileAdapter(
            onFileSelected = { file, _ ->
                showRestorePreviewDialog(file)
            },
            onFileDeleted = { file ->
                showDeleteConfirmationDialog(file)
            }
        )
        
        binding.recyclerViewBackups.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = backupFileAdapter
        }
    }
    
    private fun loadBackupFiles() {
        lifecycleScope.launch {
            try {
                val files = cloudBackupManager.getLocalBackupFiles()
                if (files.isEmpty()) {
                    showEmptyState()
                } else {
                    showBackupFiles(files)
                }
            } catch (e: Exception) {
                Log.e(TAG, "Failed to load backup files", e)
                Toast.makeText(requireContext(), getString(R.string.load_backup_file_failed, e.message), Toast.LENGTH_LONG).show()
                showEmptyState()
            }
        }
    }
    
    private fun showEmptyState() {
        binding.recyclerViewBackups.visibility = View.GONE
        binding.layoutEmptyState.visibility = View.VISIBLE
    }
    
    private fun showBackupFiles(files: List<File>) {
        binding.recyclerViewBackups.visibility = View.VISIBLE
        binding.layoutEmptyState.visibility = View.GONE
        
        lifecycleScope.launch {
            try {
                val fileInfos = files.map { file ->
                    cloudBackupManager.getLocalBackupFileInfo(file)
                }
                backupFileAdapter.updateFiles(files, fileInfos)
            } catch (e: Exception) {
                Log.e(TAG, "Failed to get file info", e)
                backupFileAdapter.updateFiles(files, emptyList())
            }
        }
    }
    
    private fun updateBackupLocationInfo() {
        lifecycleScope.launch {
            try {
                // 獲取備份文件列表來推斷備份目錄
                val files = cloudBackupManager.getLocalBackupFiles()
                val backupPath = if (files.isNotEmpty()) {
                    files.first().parent ?: "無法獲取備份位置"
                } else {
                    // 如果沒有備份文件，顯示默認路徑
                    "${requireContext().getExternalFilesDir(android.os.Environment.DIRECTORY_DOWNLOADS)}/AutoBackup"
                }
                binding.tvBackupLocation.text = backupPath
            } catch (e: Exception) {
                Log.e(TAG, "Failed to get backup directory", e)
                binding.tvBackupLocation.text = "無法獲取備份位置"
            }
        }
    }
    
    private fun createLocalBackup() {
        if (!PermissionHelper.hasStoragePermission(requireContext())) {
            PermissionHelper.requestStoragePermission(requireActivity())
            return
        }
        
        lifecycleScope.launch {
            try {
                showProgress(true)
                val result = cloudBackupManager.createLocalBackup()
                if (result.success) {
                    Toast.makeText(
                        requireContext(),
                        "本地備份創建成功\n文件：${result.filePath}",
                        Toast.LENGTH_LONG
                    ).show()
                    loadBackupFiles() // 重新載入文件列表
                } else {
                    Toast.makeText(requireContext(), result.message, Toast.LENGTH_LONG).show()
                }
            } catch (e: Exception) {
                Log.e(TAG, "Failed to create local backup", e)
                Toast.makeText(requireContext(), getString(R.string.create_local_backup_failed, e.message), Toast.LENGTH_LONG).show()
            } finally {
                showProgress(false)
            }
        }
    }
    
    private fun showRestorePreviewDialog(file: File) {
        val previewDialog = BackupPreviewDialog.newInstance {
            // Restore confirmed
            restoreFromLocalFile(file)
        }
        previewDialog.show(childFragmentManager, "BackupPreviewDialog")

        lifecycleScope.launch {
            val result = backupManager.getSchedulesFromLocalBackup(file)
            result.onSuccess { schedules ->
                previewDialog.updateSchedules(schedules)
            }.onFailure {
                Toast.makeText(requireContext(), "無法載入預覽: ${it.message}", Toast.LENGTH_LONG).show()
                previewDialog.dismiss()
            }
        }
    }
    
    private fun showDeleteConfirmationDialog(file: File) {
        AlertDialog.Builder(requireContext())
            .setTitle("刪除備份")
            .setMessage("確定要刪除這個備份嗎？")
            .setPositiveButton("刪除") { _, _ ->
                deleteLocalBackupFile(file)
            }
            .setNegativeButton("取消", null)
            .show()
    }
    
    private fun restoreFromLocalFile(file: File) {
        lifecycleScope.launch {
            (activity as? com.example.autolaunch.BackupRestoreActivity)?.showProgress(true)
            try {
                val result = backupManager.restoreFromLocalFile(file.absolutePath)
                if (result.success) {
                    Toast.makeText(requireContext(), "從本地還原成功", Toast.LENGTH_LONG).show()
                } else {
                    Toast.makeText(requireContext(), "還原失敗: ${result.message}", Toast.LENGTH_LONG).show()
                }
            } catch (e: Exception) {
                Toast.makeText(requireContext(), "還原時發生錯誤: ${e.message}", Toast.LENGTH_LONG).show()
            } finally {
                (activity as? com.example.autolaunch.BackupRestoreActivity)?.showProgress(false)
            }
        }
    }
    
    private fun deleteLocalBackupFile(file: File) {
        lifecycleScope.launch {
            try {
                if (file.delete()) {
                    Toast.makeText(requireContext(), getString(R.string.backup_file_deleted), Toast.LENGTH_SHORT).show()
                    loadBackupFiles() // 重新載入文件列表
                } else {
                    Toast.makeText(requireContext(), getString(R.string.delete_backup_file_failed), Toast.LENGTH_SHORT).show()
                }
            } catch (e: Exception) {
                Log.e(TAG, "Failed to delete backup file", e)
                Toast.makeText(requireContext(), getString(R.string.delete_backup_file_error, e.message), Toast.LENGTH_LONG).show()
            }
        }
    }
    
    private fun copyBackupPathToClipboard() {
        try {
            val clipboard = requireContext().getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            val clip = ClipData.newPlainText("備份路徑", binding.tvBackupLocation.text)
            clipboard.setPrimaryClip(clip)
            Toast.makeText(requireContext(), getString(R.string.backup_path_copied), Toast.LENGTH_SHORT).show()
        } catch (e: Exception) {
            Log.e(TAG, "Failed to copy path to clipboard", e)
            Toast.makeText(requireContext(), getString(R.string.copy_path_failed), Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun showProgress(show: Boolean) {
        // 通知父Activity顯示/隱藏進度條
        (activity as? com.example.autolaunch.BackupRestoreActivity)?.showProgress(show)
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
