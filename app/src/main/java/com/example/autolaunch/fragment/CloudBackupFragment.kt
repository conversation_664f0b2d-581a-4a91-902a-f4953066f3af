package com.example.autolaunch.fragment

import android.app.Activity
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.autolaunch.R
import com.example.autolaunch.adapter.CloudBackupFileAdapter
import com.example.autolaunch.databinding.FragmentCloudBackupBinding
import com.example.autolaunch.dialog.BackupPreviewDialog
import com.example.autolaunch.utils.CloudBackupManager
import com.example.autolaunch.utils.DriveFileInfo
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.google.android.gms.auth.api.signin.GoogleSignInAccount
import com.google.android.gms.auth.api.signin.GoogleSignInStatusCodes
import com.google.android.gms.common.api.ApiException
import kotlinx.coroutines.launch

/**
 * 雲端備份Fragment
 */
class CloudBackupFragment : Fragment() {
    
    companion object {
        private const val TAG = "CloudBackupFragment"
        
        fun newInstance(): CloudBackupFragment {
            return CloudBackupFragment()
        }
    }
    
    private var _binding: FragmentCloudBackupBinding? = null
    private val binding get() = _binding!!

    private lateinit var cloudBackupManager: CloudBackupManager
    private lateinit var adapter: CloudBackupFileAdapter

    // Google 登錄結果處理器
    private val googleSignInLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val task = GoogleSignIn.getSignedInAccountFromIntent(result.data)
            try {
                handleSignInSuccess(task.getResult(ApiException::class.java))
            } catch (e: ApiException) {
                handleSignInFailure(e)
            }
        } else {
            Toast.makeText(requireContext(), getString(R.string.google_login_cancelled), Toast.LENGTH_SHORT).show()
        }
    }
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentCloudBackupBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        cloudBackupManager = CloudBackupManager(requireContext())
        
        setupUI()
        updateGoogleAccountStatus()
    }
    
    override fun onResume() {
        super.onResume()
        updateGoogleAccountStatus()
        if (cloudBackupManager.isGoogleSignedIn()) {
            loadCloudBackupFiles()
        } else {
            binding.tvEmptyOrLoading.visibility = View.VISIBLE
        }
    }
    
    private fun setupUI() {
        // Google 登錄按鈕
        binding.btnGoogleSignIn.setOnClickListener {
            handleGoogleSignInClick()
        }
        
        // 創建雲端備份按鈕
        binding.btnCreateCloudBackup.setOnClickListener {
            createCloudBackup()
        }

        adapter = CloudBackupFileAdapter(
            onFileRestore = { file -> showRestorePreviewDialog(file) },
            onFileDelete = { file -> showDeleteConfirmationDialog(file) },
            onGetScheduleCount = { fileId, callback ->
                lifecycleScope.launch {
                    val count = cloudBackupManager.getScheduleCount(fileId)
                    callback(count)
                }
            }
        )
        binding.recyclerView.layoutManager = LinearLayoutManager(requireContext())
        binding.recyclerView.adapter = adapter
    }
    
    private fun loadCloudBackupFiles() {
        lifecycleScope.launch {
            showLoadingUI(true)
            try {
                val files = cloudBackupManager.getCloudBackupFiles()
                if (files.isEmpty()) {
                    showEmptyUI()
                } else {
                    adapter.updateFiles(files)
                    showListUI()
                }
            } catch (e: Exception) {
                Log.e(TAG, "Failed to load cloud backups", e)
                Toast.makeText(requireContext(), "無法載入雲端備份: ${e.message}", Toast.LENGTH_LONG).show()
                showEmptyUI()
            }
        }
    }

    private fun showLoadingUI(isLoading: Boolean) {
        if (isLoading) {
            binding.tvEmptyOrLoading.text = "正在從雲端載入備份..."
            binding.tvEmptyOrLoading.visibility = View.VISIBLE
            binding.recyclerView.visibility = View.GONE
        }
    }

    private fun showEmptyUI() {
        binding.tvEmptyOrLoading.text = getString(R.string.no_cloud_backups_found)
        binding.tvEmptyOrLoading.visibility = View.VISIBLE
        binding.recyclerView.visibility = View.GONE
    }

    private fun showListUI() {
        binding.tvEmptyOrLoading.visibility = View.GONE
        binding.recyclerView.visibility = View.VISIBLE
    }
    
    private fun updateGoogleAccountStatus() {
        val account = GoogleSignIn.getLastSignedInAccount(requireContext())
        if (account != null) {
            binding.tvGoogleAccountStatus.text = getString(R.string.google_signed_in_as, account.email)
            binding.btnGoogleSignIn.text = getString(R.string.google_sign_out)
            binding.btnGoogleSignIn.setIconResource(R.drawable.ic_logout_24)
            binding.btnCreateCloudBackup.isEnabled = true
            loadCloudBackupFiles()
        } else {
            binding.tvGoogleAccountStatus.text = getString(R.string.google_not_signed_in)
            binding.btnGoogleSignIn.text = getString(R.string.google_sign_in)
            binding.btnGoogleSignIn.setIconResource(R.drawable.ic_login_24)
            binding.btnCreateCloudBackup.isEnabled = false
            showLoginPromptUI()
        }
    }
    
    private fun handleGoogleSignInClick() {
        if (GoogleSignIn.getLastSignedInAccount(requireContext()) != null) {
            showSignOutConfirmationDialog()
        } else {
            signInGoogle()
        }
    }
    
    private fun showSignOutConfirmationDialog() {
        AlertDialog.Builder(requireContext())
            .setTitle(getString(R.string.google_sign_out_confirm_title))
            .setMessage(getString(R.string.google_sign_out_confirm_message))
            .setPositiveButton(getString(R.string.google_sign_out)) { _, _ ->
                signOutGoogle()
            }
            .setNegativeButton(getString(R.string.cancel), null)
            .show()
    }

    private fun signInGoogle() {
        googleSignInLauncher.launch(cloudBackupManager.getGoogleSignInClient().signInIntent)
    }

    private fun handleSignInSuccess(account: GoogleSignInAccount) {
        lifecycleScope.launch {
            (activity as? com.example.autolaunch.BackupRestoreActivity)?.showProgress(true)
            try {
                cloudBackupManager.initializeGoogleDrive()
                Toast.makeText(requireContext(), "Google 帳戶登錄成功", Toast.LENGTH_SHORT).show()
                updateGoogleAccountStatus()
            } catch (e: Exception) {
                Toast.makeText(requireContext(), "Google Drive 初始化失敗", Toast.LENGTH_LONG).show()
                updateGoogleAccountStatus()
            } finally {
                (activity as? com.example.autolaunch.BackupRestoreActivity)?.showProgress(false)
            }
        }
    }

    private fun handleSignInFailure(e: ApiException) {
        val message = GoogleSignInStatusCodes.getStatusCodeString(e.statusCode)
        Toast.makeText(requireContext(), "Google 登入失敗: $message", Toast.LENGTH_LONG).show()
    }
    
    private fun signOutGoogle() {
        lifecycleScope.launch {
            (activity as? com.example.autolaunch.BackupRestoreActivity)?.showProgress(true)
            try {
                cloudBackupManager.signOutGoogle()
                Toast.makeText(requireContext(), "已登出 Google 帳戶", Toast.LENGTH_SHORT).show()
            } catch (e: Exception) {
                Toast.makeText(requireContext(), "Google 登出失敗", Toast.LENGTH_LONG).show()
            } finally {
                updateGoogleAccountStatus()
                (activity as? com.example.autolaunch.BackupRestoreActivity)?.showProgress(false)
            }
        }
    }
    
    private fun createCloudBackup() {
        lifecycleScope.launch {
            (activity as? com.example.autolaunch.BackupRestoreActivity)?.showProgress(true)
            try {
                val result = cloudBackupManager.createCloudBackup()
                if (result.success) {
                    Toast.makeText(requireContext(), "雲端備份創建成功", Toast.LENGTH_SHORT).show()
                    loadCloudBackupFiles()
                } else {
                    Toast.makeText(requireContext(), "備份失敗: ${result.message}", Toast.LENGTH_LONG).show()
                }
            } catch (e: Exception) {
                Toast.makeText(requireContext(), "備份時發生錯誤: ${e.message}", Toast.LENGTH_LONG).show()
            } finally {
                (activity as? com.example.autolaunch.BackupRestoreActivity)?.showProgress(false)
            }
        }
    }
    
    private fun showRestorePreviewDialog(file: DriveFileInfo) {
        val previewDialog = BackupPreviewDialog.newInstance {
            // Restore confirmed
            restoreFromCloudFile(file.id)
        }
        previewDialog.show(childFragmentManager, "BackupPreviewDialog")

        lifecycleScope.launch {
            val result = cloudBackupManager.getSchedulesFromBackupFile(file.id)
            result.onSuccess { schedules ->
                previewDialog.updateSchedules(schedules)
            }.onFailure {
                Toast.makeText(requireContext(), "無法載入預覽: ${it.message}", Toast.LENGTH_LONG).show()
                previewDialog.dismiss()
            }
        }
    }

    private fun restoreFromCloudFile(fileId: String) {
        lifecycleScope.launch {
            (activity as? com.example.autolaunch.BackupRestoreActivity)?.showProgress(true)
            try {
                val result = cloudBackupManager.restoreFromCloudFile(fileId)
                if (result.success) {
                    Toast.makeText(requireContext(), "從雲端還原成功", Toast.LENGTH_LONG).show()
                } else {
                    Toast.makeText(requireContext(), "還原失敗: ${result.message}", Toast.LENGTH_LONG).show()
                }
            } catch (e: Exception) {
                Toast.makeText(requireContext(), "還原時發生錯誤: ${e.message}", Toast.LENGTH_LONG).show()
            } finally {
                (activity as? com.example.autolaunch.BackupRestoreActivity)?.showProgress(false)
            }
        }
    }
    
    private fun showDeleteConfirmationDialog(file: DriveFileInfo) {
        AlertDialog.Builder(requireContext())
            .setTitle("刪除備份")
            .setMessage("您確定要刪除雲端備份檔案 ${file.name} 嗎？此操作無法復原。")
            .setPositiveButton("刪除") { _, _ -> deleteCloudBackupFile(file) }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun deleteCloudBackupFile(file: DriveFileInfo) {
        lifecycleScope.launch {
            (activity as? com.example.autolaunch.BackupRestoreActivity)?.showProgress(true)
            try {
                val result = cloudBackupManager.deleteCloudBackupFile(file.id)
                if (result.success) {
                    Toast.makeText(requireContext(), "雲端備份已刪除", Toast.LENGTH_SHORT).show()
                    loadCloudBackupFiles()
                } else {
                    Toast.makeText(requireContext(), "刪除失敗: ${result.message}", Toast.LENGTH_LONG).show()
                }
            } catch (e: Exception) {
                Toast.makeText(requireContext(), "刪除時發生錯誤: ${e.message}", Toast.LENGTH_LONG).show()
            } finally {
                (activity as? com.example.autolaunch.BackupRestoreActivity)?.showProgress(false)
            }
        }
    }
    
    private fun showLoginPromptUI() {
        binding.tvEmptyOrLoading.text = getString(R.string.google_login_prompt_for_backup)
        binding.tvEmptyOrLoading.visibility = View.VISIBLE
        binding.recyclerView.visibility = View.GONE
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
