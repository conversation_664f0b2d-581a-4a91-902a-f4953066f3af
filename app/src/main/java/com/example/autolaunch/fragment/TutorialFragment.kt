package com.example.autolaunch.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import com.example.autolaunch.databinding.FragmentTutorialBinding

/**
 * 使用教學Fragment
 */
class TutorialFragment : Fragment() {
    
    private var _binding: FragmentTutorialBinding? = null
    private val binding get() = _binding!!
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentTutorialBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        // 教學內容已經在布局文件中定義，這裡不需要額外設置
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
    
    companion object {
        fun newInstance(): TutorialFragment {
            return TutorialFragment()
        }
    }
}
