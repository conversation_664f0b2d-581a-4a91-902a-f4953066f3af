package com.example.autolaunch.fragment

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.autolaunch.AddEditScheduleActivity
import com.example.autolaunch.R
import com.example.autolaunch.adapter.UnifiedScheduleAdapter
import com.example.autolaunch.databinding.FragmentWebScheduleBinding
import com.example.autolaunch.model.Schedule
import com.example.autolaunch.model.ScheduleType
import com.example.autolaunch.utils.ScheduleItemTouchHelper
import com.example.autolaunch.ScheduleViewModel
import kotlinx.coroutines.launch

/**
 * 網頁排程列表 Fragment
 */
class WebScheduleFragment : Fragment() {
    
    companion object {
        fun newInstance() = WebScheduleFragment()
    }
    
    private var _binding: FragmentWebScheduleBinding? = null
    private val binding get() = _binding!!
    
    private val viewModel: ScheduleViewModel by activityViewModels()
    private lateinit var scheduleAdapter: UnifiedScheduleAdapter
    private lateinit var itemTouchHelper: ItemTouchHelper
    private var isSortingMode = false
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentWebScheduleBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupRecyclerView()
        observeViewModel()
    }
    
    private fun setupRecyclerView() {
        scheduleAdapter = UnifiedScheduleAdapter(
            onItemClick = { schedule ->
                if (!isSortingMode) {
                    // 處理排程項目點擊事件 - 導航到編輯頁面
                    val intent = Intent(requireContext(), AddEditScheduleActivity::class.java)
                    intent.putExtra(AddEditScheduleActivity.EXTRA_SCHEDULE_ID, schedule.id)
                    startActivity(intent)
                }
            },
            onToggleEnabled = { schedule, isEnabled ->
                if (!isSortingMode) {
                    // 處理啟用/禁用開關切換事件
                    viewModel.updateScheduleEnabledStatus(schedule, isEnabled)
                }
            },
            onStartDrag = { viewHolder ->
                // 長按直接啟用排序模式並開始拖拽
                if (!isSortingMode) {
                    isSortingMode = true
                    Toast.makeText(requireContext(), getString(R.string.sort_mode_enabled), Toast.LENGTH_SHORT).show()
                }
                // 開始拖拽
                itemTouchHelper.startDrag(viewHolder)
            }
        )
        
        binding.recyclerViewWebSchedules.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = scheduleAdapter
        }
        
        setupDragToSort()
    }
    
    private fun setupDragToSort() {
        val dragToSortCallback = ScheduleItemTouchHelper(
            adapter = scheduleAdapter,
            onMoveFinished = { reorderedSchedules ->
                // 拖拽結束後更新排序
                viewModel.reorderSchedules(reorderedSchedules)
                
                // 自動退出排序模式
                if (isSortingMode) {
                    isSortingMode = false
                    Toast.makeText(requireContext(), getString(R.string.sort_mode_disabled), Toast.LENGTH_SHORT).show()
                }
            },
            onSwipeToDelete = { position ->
                val item = scheduleAdapter.currentList[position]
                if (item is UnifiedScheduleAdapter.ScheduleItem.ScheduleData) {
                    showDeleteConfirmationDialog(item.schedule, position)
                }
            }
        )
        
        itemTouchHelper = ItemTouchHelper(dragToSortCallback)
        itemTouchHelper.attachToRecyclerView(binding.recyclerViewWebSchedules)
    }
    
    private fun observeViewModel() {
        lifecycleScope.launch {
            viewModel.schedules.collect { schedules ->
                val webSchedules = schedules.filter { it.scheduleType == ScheduleType.URL.value }
                updateScheduleList(webSchedules)
            }
        }
    }
    
    private fun updateScheduleList(schedules: List<Schedule>) {
        if (schedules.isEmpty()) {
            showEmptyState()
        } else {
            showScheduleList(schedules)
        }
    }
    
    private fun showEmptyState() {
        binding.apply {
            emptyStateLayout.visibility = View.VISIBLE
            recyclerViewWebSchedules.visibility = View.GONE
        }
    }
    
    private fun showScheduleList(schedules: List<Schedule>) {
        binding.apply {
            emptyStateLayout.visibility = View.GONE
            recyclerViewWebSchedules.visibility = View.VISIBLE
        }
        
        // 創建統一列表項目（不需要標題，因為已經在 tab 中分類了）
        val unifiedItems = schedules.map { schedule ->
            UnifiedScheduleAdapter.ScheduleItem.ScheduleData(schedule)
        }
        
        scheduleAdapter.submitList(unifiedItems)
    }
    
    private fun showDeleteConfirmationDialog(schedule: Schedule, position: Int) {
        // 實現刪除確認對話框
        androidx.appcompat.app.AlertDialog.Builder(requireContext())
            .setTitle(getString(R.string.delete_schedule_title))
            .setMessage(getString(R.string.delete_schedule_message, schedule.getDisplayName()))
            .setPositiveButton(getString(R.string.delete)) { _, _ ->
                viewModel.deleteSchedule(schedule)
            }
            .setNegativeButton(getString(R.string.cancel)) { _, _ ->
                // 取消時恢復項目位置
                scheduleAdapter.notifyItemChanged(position)
            }
            .setOnCancelListener {
                // 取消時恢復項目位置
                scheduleAdapter.notifyItemChanged(position)
            }
            .show()
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
