package com.example.autolaunch.fragment

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.autolaunch.adapter.ThemeListAdapter
import com.example.autolaunch.databinding.FragmentThemeListBinding
import com.example.autolaunch.utils.ThemeManager
import com.example.autolaunch.utils.ThemeType

/**
 * 主題列表 Fragment
 * 顯示特定模式（淺色或深色）的主題列表
 */
class ThemeListFragment : Fragment() {

    companion object {
        private const val TAG = "ThemeListFragment"
        private const val ARG_IS_DARK_MODE = "is_dark_mode"

        fun newInstance(isDarkMode: Boolean): ThemeListFragment {
            val fragment = ThemeListFragment()
            val args = Bundle()
            args.putBoolean(ARG_IS_DARK_MODE, isDarkMode)
            fragment.arguments = args
            return fragment
        }
    }

    private var _binding: FragmentThemeListBinding? = null
    private val binding get() = _binding!!
    
    private lateinit var themeManager: ThemeManager
    private lateinit var themeAdapter: ThemeListAdapter
    private var isDarkMode: Boolean = false
    private var onThemeSelected: ((ThemeType) -> Unit)? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        isDarkMode = arguments?.getBoolean(ARG_IS_DARK_MODE, false) ?: false
        themeManager = ThemeManager.getInstance(requireContext())
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentThemeListBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupRecyclerView()
    }

    private fun setupRecyclerView() {
        val themes = if (isDarkMode) {
            ThemeType.getDarkThemes()
        } else {
            ThemeType.getLightThemes()
        }
        
        val currentTheme = themeManager.getCurrentTheme()
        
        themeAdapter = ThemeListAdapter(
            themes = themes,
            currentTheme = currentTheme,
            themeManager = themeManager
        ) { selectedTheme ->
            Log.d(TAG, "Theme selected: ${selectedTheme.displayName}")
            onThemeSelected?.invoke(selectedTheme)
        }
        
        binding.recyclerViewThemes.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = themeAdapter
        }
    }

    fun updateCurrentTheme(newTheme: ThemeType) {
        if (::themeAdapter.isInitialized) {
            themeAdapter.updateCurrentTheme(newTheme)
        }
    }

    fun setOnThemeSelectedListener(listener: (ThemeType) -> Unit) {
        onThemeSelected = listener
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
