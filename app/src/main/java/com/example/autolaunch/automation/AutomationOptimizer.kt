package com.example.autolaunch.automation

import android.content.Context
import android.util.Log
import com.example.autolaunch.model.AppDatabase
import com.example.autolaunch.model.AutomationStep
import com.example.autolaunch.model.StepType
import kotlinx.coroutines.flow.first

/**
 * 自動化優化器
 * 提供自動化步驟的性能優化和建議
 */
class AutomationOptimizer(private val context: Context) {
    
    companion object {
        private const val TAG = "AutomationOptimizer"
        private const val MAX_RECOMMENDED_STEPS = 20
        private const val MAX_RECOMMENDED_WAIT_TIME = 10000L
        private const val MIN_RECOMMENDED_WAIT_TIME = 500L
        private const val MAX_RECOMMENDED_RETRY_COUNT = 5
    }
    
    private val database = AppDatabase.getDatabase(context)
    private val automationStepDao = database.automationStepDao()
    
    /**
     * 分析排程的自動化步驟並提供優化建議
     */
    suspend fun analyzeAndOptimize(scheduleId: Long): OptimizationReport {
        Log.i(TAG, "Analyzing automation steps for schedule $scheduleId")
        
        val steps = automationStepDao.getStepsByScheduleId(scheduleId).first()
        val issues = mutableListOf<OptimizationIssue>()
        val suggestions = mutableListOf<OptimizationSuggestion>()
        
        // 分析步驟數量
        analyzeStepCount(steps, issues, suggestions)
        
        // 分析等待時間
        analyzeWaitTimes(steps, issues, suggestions)
        
        // 分析重試配置
        analyzeRetryConfiguration(steps, issues, suggestions)
        
        // 分析步驟順序
        analyzeStepOrder(steps, issues, suggestions)
        
        // 分析可選步驟配置
        analyzeOptionalSteps(steps, issues, suggestions)
        
        // 分析超時配置
        analyzeTimeoutConfiguration(steps, issues, suggestions)
        
        // 計算性能評分
        val performanceScore = calculatePerformanceScore(steps, issues)
        
        return OptimizationReport(
            scheduleId = scheduleId,
            totalSteps = steps.size,
            enabledSteps = steps.count { it.isEnabled },
            issues = issues,
            suggestions = suggestions,
            performanceScore = performanceScore,
            estimatedExecutionTime = estimateExecutionTime(steps)
        )
    }
    
    /**
     * 分析步驟數量
     */
    private fun analyzeStepCount(
        steps: List<AutomationStep>,
        issues: MutableList<OptimizationIssue>,
        suggestions: MutableList<OptimizationSuggestion>
    ) {
        if (steps.size > MAX_RECOMMENDED_STEPS) {
            issues.add(
                OptimizationIssue(
                    type = IssueType.PERFORMANCE,
                    severity = IssueSeverity.MEDIUM,
                    message = "步驟數量過多（${steps.size}個），可能影響執行性能",
                    affectedSteps = steps.map { it.id }
                )
            )
            
            suggestions.add(
                OptimizationSuggestion(
                    type = SuggestionType.REDUCE_STEPS,
                    message = "建議將複雜的自動化流程拆分為多個較小的排程",
                    priority = SuggestionPriority.MEDIUM
                )
            )
        }
    }
    
    /**
     * 分析等待時間
     */
    private fun analyzeWaitTimes(
        steps: List<AutomationStep>,
        issues: MutableList<OptimizationIssue>,
        suggestions: MutableList<OptimizationSuggestion>
    ) {
        val waitSteps = steps.filter { it.getStepType() == StepType.WAIT }
        val totalWaitTime = waitSteps.sumOf { it.waitDuration ?: 0L }
        
        // 檢查過長的等待時間
        waitSteps.forEach { step ->
            val waitDuration = step.waitDuration ?: 0L
            if (waitDuration > MAX_RECOMMENDED_WAIT_TIME) {
                issues.add(
                    OptimizationIssue(
                        type = IssueType.PERFORMANCE,
                        severity = IssueSeverity.LOW,
                        message = "等待時間過長（${waitDuration}ms），可能導致執行緩慢",
                        affectedSteps = listOf(step.id)
                    )
                )
            } else if (waitDuration < MIN_RECOMMENDED_WAIT_TIME) {
                issues.add(
                    OptimizationIssue(
                        type = IssueType.RELIABILITY,
                        severity = IssueSeverity.MEDIUM,
                        message = "等待時間過短（${waitDuration}ms），可能導致執行不穩定",
                        affectedSteps = listOf(step.id)
                    )
                )
            }
        }
        
        // 檢查總等待時間
        if (totalWaitTime > MAX_RECOMMENDED_WAIT_TIME * 2) {
            suggestions.add(
                OptimizationSuggestion(
                    type = SuggestionType.OPTIMIZE_TIMING,
                    message = "總等待時間過長（${totalWaitTime}ms），建議優化等待策略",
                    priority = SuggestionPriority.MEDIUM
                )
            )
        }
    }
    
    /**
     * 分析重試配置
     */
    private fun analyzeRetryConfiguration(
        steps: List<AutomationStep>,
        issues: MutableList<OptimizationIssue>,
        suggestions: MutableList<OptimizationSuggestion>
    ) {
        steps.forEach { step ->
            if (step.retryCount > MAX_RECOMMENDED_RETRY_COUNT) {
                issues.add(
                    OptimizationIssue(
                        type = IssueType.PERFORMANCE,
                        severity = IssueSeverity.LOW,
                        message = "重試次數過多（${step.retryCount}次），可能延長執行時間",
                        affectedSteps = listOf(step.id)
                    )
                )
            } else if (step.retryCount == 0 && !step.isOptional) {
                issues.add(
                    OptimizationIssue(
                        type = IssueType.RELIABILITY,
                        severity = IssueSeverity.HIGH,
                        message = "關鍵步驟沒有重試機制，可能導致執行失敗",
                        affectedSteps = listOf(step.id)
                    )
                )
            }
        }
    }
    
    /**
     * 分析步驟順序
     */
    private fun analyzeStepOrder(
        steps: List<AutomationStep>,
        issues: MutableList<OptimizationIssue>,
        suggestions: MutableList<OptimizationSuggestion>
    ) {
        val sortedSteps = steps.sortedBy { it.stepOrder }
        
        // 檢查步驟順序是否連續
        for (i in sortedSteps.indices) {
            val expectedOrder = i + 1
            if (sortedSteps[i].stepOrder != expectedOrder) {
                issues.add(
                    OptimizationIssue(
                        type = IssueType.CONFIGURATION,
                        severity = IssueSeverity.MEDIUM,
                        message = "步驟順序不連續，可能影響執行邏輯",
                        affectedSteps = listOf(sortedSteps[i].id)
                    )
                )
                break
            }
        }
        
        // 檢查是否有連續的等待步驟
        for (i in 0 until sortedSteps.size - 1) {
            val currentStep = sortedSteps[i]
            val nextStep = sortedSteps[i + 1]
            
            if (currentStep.getStepType() == StepType.WAIT && nextStep.getStepType() == StepType.WAIT) {
                suggestions.add(
                    OptimizationSuggestion(
                        type = SuggestionType.MERGE_STEPS,
                        message = "發現連續的等待步驟，建議合併以提高效率",
                        priority = SuggestionPriority.LOW
                    )
                )
                break
            }
        }
    }
    
    /**
     * 分析可選步驟配置
     */
    private fun analyzeOptionalSteps(
        steps: List<AutomationStep>,
        issues: MutableList<OptimizationIssue>,
        suggestions: MutableList<OptimizationSuggestion>
    ) {
        val optionalSteps = steps.filter { it.isOptional }
        val criticalSteps = steps.filter { !it.isOptional }
        
        if (optionalSteps.size > criticalSteps.size) {
            suggestions.add(
                OptimizationSuggestion(
                    type = SuggestionType.REVIEW_OPTIONAL,
                    message = "可選步驟比例較高，建議檢查是否有步驟可以設為必需",
                    priority = SuggestionPriority.LOW
                )
            )
        }
        
        // 檢查關鍵操作是否被設為可選
        steps.forEach { step ->
            if (step.isOptional && step.getStepType() in listOf(StepType.CLICK, StepType.INPUT_TEXT)) {
                val stepName = step.stepName ?: step.getDisplayName()
                if (stepName.contains("登入", ignoreCase = true) || 
                    stepName.contains("確認", ignoreCase = true) ||
                    stepName.contains("提交", ignoreCase = true)) {
                    
                    issues.add(
                        OptimizationIssue(
                            type = IssueType.CONFIGURATION,
                            severity = IssueSeverity.MEDIUM,
                            message = "關鍵操作被設為可選步驟，可能影響功能完整性",
                            affectedSteps = listOf(step.id)
                        )
                    )
                }
            }
        }
    }
    
    /**
     * 分析超時配置
     */
    private fun analyzeTimeoutConfiguration(
        steps: List<AutomationStep>,
        issues: MutableList<OptimizationIssue>,
        suggestions: MutableList<OptimizationSuggestion>
    ) {
        steps.forEach { step ->
            when (step.getStepType()) {
                StepType.CLICK, StepType.LONG_CLICK -> {
                    if (step.timeoutMs > 15000) {
                        issues.add(
                            OptimizationIssue(
                                type = IssueType.PERFORMANCE,
                                severity = IssueSeverity.LOW,
                                message = "點擊操作超時時間過長（${step.timeoutMs}ms）",
                                affectedSteps = listOf(step.id)
                            )
                        )
                    }
                }
                StepType.INPUT_TEXT -> {
                    if (step.timeoutMs < 5000) {
                        issues.add(
                            OptimizationIssue(
                                type = IssueType.RELIABILITY,
                                severity = IssueSeverity.MEDIUM,
                                message = "輸入操作超時時間過短（${step.timeoutMs}ms），可能導致失敗",
                                affectedSteps = listOf(step.id)
                            )
                        )
                    }
                }
                else -> {}
            }
        }
    }
    
    /**
     * 計算性能評分
     */
    private fun calculatePerformanceScore(
        steps: List<AutomationStep>,
        issues: List<OptimizationIssue>
    ): Int {
        var score = 100
        
        // 根據問題嚴重程度扣分
        issues.forEach { issue ->
            score -= when (issue.severity) {
                IssueSeverity.HIGH -> 20
                IssueSeverity.MEDIUM -> 10
                IssueSeverity.LOW -> 5
            }
        }
        
        // 根據步驟複雜度扣分
        if (steps.size > MAX_RECOMMENDED_STEPS) {
            score -= (steps.size - MAX_RECOMMENDED_STEPS) * 2
        }
        
        return score.coerceAtLeast(0)
    }
    
    /**
     * 估算執行時間
     */
    private fun estimateExecutionTime(steps: List<AutomationStep>): Long {
        return steps.sumOf { step ->
            when (step.getStepType()) {
                StepType.WAIT -> step.waitDuration ?: 1000L
                StepType.SWIPE -> step.swipeDuration ?: 500L
                StepType.INPUT_TEXT -> {
                    val textLength = step.inputText?.length ?: 0
                    1000L + (textLength * 100L) // 基礎時間 + 每字符100ms
                }
                else -> 1500L // 其他操作默認1.5秒
            }
        }
    }
}

/**
 * 優化報告數據類
 */
data class OptimizationReport(
    val scheduleId: Long,
    val totalSteps: Int,
    val enabledSteps: Int,
    val issues: List<OptimizationIssue>,
    val suggestions: List<OptimizationSuggestion>,
    val performanceScore: Int,
    val estimatedExecutionTime: Long,
    val generatedTime: Long = System.currentTimeMillis()
) {
    fun hasIssues(): Boolean = issues.isNotEmpty()
    fun hasSuggestions(): Boolean = suggestions.isNotEmpty()
    fun getHighPriorityIssues(): List<OptimizationIssue> = issues.filter { it.severity == IssueSeverity.HIGH }
    fun getFormattedExecutionTime(): String {
        return when {
            estimatedExecutionTime < 1000 -> "${estimatedExecutionTime}ms"
            estimatedExecutionTime < 60000 -> "${estimatedExecutionTime / 1000}s"
            else -> "${estimatedExecutionTime / 60000}m ${(estimatedExecutionTime % 60000) / 1000}s"
        }
    }
}

/**
 * 優化問題數據類
 */
data class OptimizationIssue(
    val type: IssueType,
    val severity: IssueSeverity,
    val message: String,
    val affectedSteps: List<Long>
)

/**
 * 優化建議數據類
 */
data class OptimizationSuggestion(
    val type: SuggestionType,
    val message: String,
    val priority: SuggestionPriority
)

/**
 * 問題類型枚舉
 */
enum class IssueType {
    PERFORMANCE,     // 性能問題
    RELIABILITY,     // 可靠性問題
    CONFIGURATION    // 配置問題
}

/**
 * 問題嚴重程度枚舉
 */
enum class IssueSeverity {
    HIGH,    // 高
    MEDIUM,  // 中
    LOW      // 低
}

/**
 * 建議類型枚舉
 */
enum class SuggestionType {
    REDUCE_STEPS,     // 減少步驟
    OPTIMIZE_TIMING,  // 優化時間
    MERGE_STEPS,      // 合併步驟
    REVIEW_OPTIONAL   // 檢查可選步驟
}

/**
 * 建議優先級枚舉
 */
enum class SuggestionPriority {
    HIGH,    // 高
    MEDIUM,  // 中
    LOW      // 低
}
