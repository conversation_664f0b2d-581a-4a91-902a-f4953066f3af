package com.example.autolaunch.automation.steps

import com.example.autolaunch.model.AutomationStep
import com.example.autolaunch.model.StepType
import com.example.autolaunch.service.AutoLaunchAccessibilityService
import kotlinx.coroutines.delay

/**
 * 等待步驟執行器
 * 處理等待操作
 */
class WaitStepExecutor : BaseStepExecutor() {
    
    override suspend fun execute(
        step: AutomationStep,
        accessibilityService: AutoLaunchAccessibilityService
    ): <PERSON><PERSON>an {
        if (!isStepEnabled(step)) {
            logExecution(step, "Step is disabled, skipping")
            return true
        }
        
        logExecutionStart(step)
        
        return safeExecute(step) {
            val waitDuration = step.waitDuration
            if (waitDuration == null || waitDuration <= 0) {
                logExecutionFailure(step, "Invalid wait duration: $waitDuration")
                return@safeExecute false
            }
            
            logExecution(step, "Waiting for ${waitDuration}ms")
            delay(waitDuration)
            
            logExecutionSuccess(step)
            true
        } ?: false
    }
    
    override fun validate(step: AutomationStep): Boolean {
        // 檢查步驟類型
        if (step.getStepType() != StepType.WAIT) {
            return false
        }
        
        // 檢查等待時間
        return step.waitDuration != null && step.waitDuration > 0
    }
    
    override fun getDescription(step: AutomationStep): String {
        val duration = step.waitDuration ?: 0
        return when {
            duration < 1000 -> "等待 ${duration}ms"
            duration < 60000 -> "等待 ${duration / 1000}s"
            else -> "等待 ${duration / 60000}m ${(duration % 60000) / 1000}s"
        }
    }
}
