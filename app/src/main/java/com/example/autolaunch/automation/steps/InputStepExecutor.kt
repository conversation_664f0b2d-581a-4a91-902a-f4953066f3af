package com.example.autolaunch.automation.steps

import android.os.Bundle
import android.view.accessibility.AccessibilityNodeInfo
import com.example.autolaunch.model.AutomationStep
import com.example.autolaunch.model.StepType
import com.example.autolaunch.model.TargetType
import com.example.autolaunch.service.AutoLaunchAccessibilityService
import kotlinx.coroutines.delay

/**
 * 輸入步驟執行器
 * 處理文字輸入操作
 */
class InputStepExecutor : BaseStepExecutor() {
    
    override suspend fun execute(
        step: AutomationStep,
        accessibilityService: AutoLaunchAccessibilityService
    ): Boolean {
        if (!isStepEnabled(step)) {
            logExecution(step, "Step is disabled, skipping")
            return true
        }
        
        logExecutionStart(step)
        
        return safeExecute(step) {
            val inputText = step.inputText
            if (inputText.isNullOrBlank()) {
                logExecutionFailure(step, "Input text is empty")
                return@safeExecute false
            }
            
            // 等待目標輸入框出現
            val targetNode = accessibilityService.waitForElement(step, getStepTimeout(step))
            if (targetNode == null) {
                logExecutionFailure(step, "Target input field not found")
                return@safeExecute false
            }
            
            // 執行輸入操作
            val success = performTextInput(targetNode, inputText, step)
            
            if (success) {
                logExecutionSuccess(step)
                // 輸入後短暫延遲，等待UI響應
                delay(500)
            } else {
                logExecutionFailure(step, "Text input failed")
            }
            
            success
        } ?: false
    }
    
    override fun validate(step: AutomationStep): Boolean {
        // 檢查步驟類型
        if (step.getStepType() != StepType.INPUT_TEXT) {
            return false
        }
        
        // 檢查輸入文字
        if (step.inputText.isNullOrBlank()) {
            return false
        }
        
        // 檢查目標配置
        return when (step.getTargetType()) {
            TargetType.TEXT -> !step.targetText.isNullOrBlank()
            TargetType.ID -> !step.targetId.isNullOrBlank()
            TargetType.COORDINATE -> step.targetX != null && step.targetY != null
            TargetType.DESCRIPTION -> !step.targetDescription.isNullOrBlank()
            TargetType.CLASS_NAME -> !step.targetClassName.isNullOrBlank()
        }
    }
    
    override fun getDescription(step: AutomationStep): String {
        val targetText = when (step.getTargetType()) {
            TargetType.TEXT -> "文字「${step.targetText}」"
            TargetType.ID -> "ID「${step.targetId}」"
            TargetType.COORDINATE -> "座標(${step.targetX}, ${step.targetY})"
            TargetType.DESCRIPTION -> "描述「${step.targetDescription}」"
            TargetType.CLASS_NAME -> "類別「${step.targetClassName}」"
        }
        
        return "在 $targetText 輸入「${step.inputText}」"
    }
    
    /**
     * 執行文字輸入操作
     */
    private suspend fun performTextInput(
        node: AccessibilityNodeInfo,
        text: String,
        step: AutomationStep
    ): Boolean {
        try {
            // 首先嘗試點擊輸入框獲得焦點
            if (!focusInputField(node, step)) {
                return false
            }
            
            // 短暫延遲等待焦點設置
            delay(300)
            
            // 清空現有文字（如果需要）
            if (shouldClearExistingText(node)) {
                clearExistingText(node, step)
                delay(200)
            }
            
            // 輸入新文字
            return inputText(node, text, step)
            
        } catch (e: Exception) {
            logExecutionFailure(step, "Text input exception: ${e.message}")
            return false
        }
    }
    
    /**
     * 讓輸入框獲得焦點
     */
    private suspend fun focusInputField(node: AccessibilityNodeInfo, step: AutomationStep): Boolean {
        // 嘗試直接設置焦點
        if (node.performAction(AccessibilityNodeInfo.ACTION_FOCUS)) {
            logExecution(step, "Input field focused successfully")
            return true
        }
        
        // 如果直接設置焦點失敗，嘗試點擊
        if (node.isClickable && node.performAction(AccessibilityNodeInfo.ACTION_CLICK)) {
            logExecution(step, "Input field clicked to gain focus")
            return true
        }
        
        // 嘗試點擊父節點
        val clickableParent = findClickableParent(node)
        if (clickableParent != null && clickableParent.performAction(AccessibilityNodeInfo.ACTION_CLICK)) {
            logExecution(step, "Parent node clicked to focus input field")
            return true
        }
        
        logExecutionFailure(step, "Failed to focus input field")
        return false
    }
    
    /**
     * 檢查是否需要清空現有文字
     */
    private fun shouldClearExistingText(node: AccessibilityNodeInfo): Boolean {
        val existingText = node.text?.toString()
        return !existingText.isNullOrBlank()
    }
    
    /**
     * 清空現有文字
     */
    private suspend fun clearExistingText(node: AccessibilityNodeInfo, step: AutomationStep): Boolean {
        // 嘗試全選然後刪除
        if (node.performAction(AccessibilityNodeInfo.ACTION_SELECT_ALL)) {
            logExecution(step, "Selected all existing text")
            delay(100)
            
            // 輸入空字符串來清空
            val arguments = Bundle().apply {
                putCharSequence(AccessibilityNodeInfo.ACTION_ARGUMENT_SET_TEXT_CHARSEQUENCE, "")
            }
            
            if (node.performAction(AccessibilityNodeInfo.ACTION_SET_TEXT, arguments)) {
                logExecution(step, "Cleared existing text")
                return true
            }
        }
        
        logExecution(step, "Failed to clear existing text, will append instead")
        return false
    }
    
    /**
     * 輸入文字
     */
    private suspend fun inputText(node: AccessibilityNodeInfo, text: String, step: AutomationStep): Boolean {
        // 嘗試使用 SET_TEXT 動作
        val arguments = Bundle().apply {
            putCharSequence(AccessibilityNodeInfo.ACTION_ARGUMENT_SET_TEXT_CHARSEQUENCE, text)
        }
        
        if (node.performAction(AccessibilityNodeInfo.ACTION_SET_TEXT, arguments)) {
            logExecution(step, "Text input successful using SET_TEXT")
            return true
        }
        
        // 如果 SET_TEXT 失敗，嘗試逐字符輸入
        return inputTextCharByChar(node, text, step)
    }
    
    /**
     * 逐字符輸入文字（備用方法）
     */
    private suspend fun inputTextCharByChar(
        node: AccessibilityNodeInfo,
        text: String,
        step: AutomationStep
    ): Boolean {
        try {
            for (char in text) {
                val charArguments = Bundle().apply {
                    putCharSequence(AccessibilityNodeInfo.ACTION_ARGUMENT_SET_TEXT_CHARSEQUENCE, char.toString())
                }
                
                if (!node.performAction(AccessibilityNodeInfo.ACTION_SET_TEXT, charArguments)) {
                    logExecutionFailure(step, "Failed to input character: $char")
                    return false
                }
                
                // 字符間短暫延遲
                delay(50)
            }
            
            logExecution(step, "Text input successful using char-by-char method")
            return true
            
        } catch (e: Exception) {
            logExecutionFailure(step, "Char-by-char input failed: ${e.message}")
            return false
        }
    }
    
    /**
     * 查找可點擊的父節點
     */
    private fun findClickableParent(node: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        var parent = node.parent
        while (parent != null) {
            if (parent.isClickable) {
                return parent
            }
            parent = parent.parent
        }
        return null
    }
}
