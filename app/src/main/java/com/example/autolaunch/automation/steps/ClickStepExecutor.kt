package com.example.autolaunch.automation.steps

import android.view.accessibility.AccessibilityNodeInfo
import com.example.autolaunch.model.AutomationStep
import com.example.autolaunch.model.StepType
import com.example.autolaunch.model.TargetType
import com.example.autolaunch.service.AutoLaunchAccessibilityService
import kotlinx.coroutines.delay

/**
 * 點擊步驟執行器
 * 處理點擊和長按操作
 */
class ClickStepExecutor : BaseStepExecutor() {
    
    override suspend fun execute(
        step: AutomationStep,
        accessibilityService: AutoLaunchAccessibilityService
    ): Boolean {
        if (!isStepEnabled(step)) {
            logExecution(step, "Step is disabled, skipping")
            return true
        }
        
        logExecutionStart(step)
        
        return safeExecute(step) {
            // 等待目標元素出現
            val targetNode = accessibilityService.waitForElement(step, getStepTimeout(step))
            if (targetNode == null) {
                logExecutionFailure(step, "Target element not found")
                return@safeExecute false
            }
            
            // 執行點擊操作
            val success = when (step.getStepType()) {
                StepType.CLICK -> performClick(targetNode, step)
                StepType.LONG_CLICK -> performLongClick(targetNode, step)
                else -> {
                    logExecutionFailure(step, "Unsupported step type: ${step.getStepType()}")
                    false
                }
            }
            
            if (success) {
                logExecutionSuccess(step)
                // 點擊後短暫延遲，等待UI響應
                delay(500)
            } else {
                logExecutionFailure(step, "Click operation failed")
            }
            
            success
        } ?: false
    }
    
    override fun validate(step: AutomationStep): Boolean {
        // 檢查步驟類型
        if (step.getStepType() !in listOf(StepType.CLICK, StepType.LONG_CLICK)) {
            return false
        }
        
        // 檢查目標配置
        return when (step.getTargetType()) {
            TargetType.TEXT -> !step.targetText.isNullOrBlank()
            TargetType.ID -> !step.targetId.isNullOrBlank()
            TargetType.COORDINATE -> step.targetX != null && step.targetY != null
            TargetType.DESCRIPTION -> !step.targetDescription.isNullOrBlank()
            TargetType.CLASS_NAME -> !step.targetClassName.isNullOrBlank()
        }
    }
    
    override fun getDescription(step: AutomationStep): String {
        val actionText = when (step.getStepType()) {
            StepType.CLICK -> "點擊"
            StepType.LONG_CLICK -> "長按"
            else -> "操作"
        }
        
        val targetText = when (step.getTargetType()) {
            TargetType.TEXT -> "文字「${step.targetText}」"
            TargetType.ID -> "ID「${step.targetId}」"
            TargetType.COORDINATE -> "座標(${step.targetX}, ${step.targetY})"
            TargetType.DESCRIPTION -> "描述「${step.targetDescription}」"
            TargetType.CLASS_NAME -> "類別「${step.targetClassName}」"
        }
        
        return "$actionText $targetText"
    }
    
    /**
     * 執行點擊操作
     */
    private suspend fun performClick(node: AccessibilityNodeInfo, step: AutomationStep): Boolean {
        return if (node.isClickable) {
            node.performAction(AccessibilityNodeInfo.ACTION_CLICK)
        } else {
            // 如果節點不可點擊，嘗試點擊父節點
            val clickableParent = findClickableParent(node)
            if (clickableParent != null) {
                logExecution(step, "Target not clickable, clicking parent node")
                clickableParent.performAction(AccessibilityNodeInfo.ACTION_CLICK)
            } else {
                logExecutionFailure(step, "No clickable element found")
                false
            }
        }
    }
    
    /**
     * 執行長按操作
     */
    private suspend fun performLongClick(node: AccessibilityNodeInfo, step: AutomationStep): Boolean {
        return if (node.isLongClickable) {
            node.performAction(AccessibilityNodeInfo.ACTION_LONG_CLICK)
        } else if (node.isClickable) {
            // 如果不支持長按但支持點擊，嘗試長按父節點
            val longClickableParent = findLongClickableParent(node)
            if (longClickableParent != null) {
                logExecution(step, "Target not long-clickable, long-clicking parent node")
                longClickableParent.performAction(AccessibilityNodeInfo.ACTION_LONG_CLICK)
            } else {
                logExecutionFailure(step, "No long-clickable element found")
                false
            }
        } else {
            logExecutionFailure(step, "Element is not clickable")
            false
        }
    }
    
    /**
     * 查找可點擊的父節點
     */
    private fun findClickableParent(node: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        var parent = node.parent
        while (parent != null) {
            if (parent.isClickable) {
                return parent
            }
            parent = parent.parent
        }
        return null
    }
    
    /**
     * 查找可長按的父節點
     */
    private fun findLongClickableParent(node: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        var parent = node.parent
        while (parent != null) {
            if (parent.isLongClickable) {
                return parent
            }
            parent = parent.parent
        }
        return null
    }
}
