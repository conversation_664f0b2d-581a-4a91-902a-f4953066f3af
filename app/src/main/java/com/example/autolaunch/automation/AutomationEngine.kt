package com.example.autolaunch.automation

import android.content.Context
import android.util.Log
import com.example.autolaunch.model.AppDatabase
import com.example.autolaunch.model.AutomationStep
import com.example.autolaunch.service.AutoLaunchAccessibilityService
import com.example.autolaunch.utils.AccessibilityPermissionHelper
import com.example.autolaunch.utils.SystemLogManager
import com.example.autolaunch.automation.steps.StepExecutorFactory
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.first

/**
 * 自動化執行引擎
 * 負責解析和執行自動化步驟序列
 */
class AutomationEngine(private val context: Context) {
    
    companion object {
        private const val TAG = "AutomationEngine"
        private const val DEFAULT_STEP_DELAY = 1000L // 步驟間默認延遲1秒
        private const val APP_LAUNCH_WAIT_TIME = 5000L // 等待APP啟動時間
    }
    
    private val database = AppDatabase.getDatabase(context)
    private val automationStepDao = database.automationStepDao()
    private val errorHandler = AutomationErrorHandler(context)
    
    /**
     * 執行指定排程的所有自動化步驟
     */
    suspend fun executeScheduleSteps(scheduleId: Long, targetPackageName: String): AutomationResult {
        Log.i(TAG, "Starting automation execution for schedule $scheduleId, target app: $targetPackageName")
        
        try {
            // 檢查無障礙服務是否可用
            if (!AccessibilityPermissionHelper.isAccessibilityServiceEnabled(context)) {
                return AutomationResult.failure("無障礙服務未啟用")
            }
            
            val accessibilityService = AccessibilityPermissionHelper.waitForAccessibilityService(10000)
                ?: return AutomationResult.failure("無法連接到無障礙服務")
            
            // 獲取自動化步驟
            val steps = automationStepDao.getEnabledStepsByScheduleId(scheduleId).first()
            if (steps.isEmpty()) {
                Log.i(TAG, "No automation steps found for schedule $scheduleId")
                return AutomationResult.success("沒有配置自動化步驟")
            }
            
            Log.i(TAG, "Found ${steps.size} automation steps to execute")
            
            // 等待目標應用啟動
            val appLaunched = accessibilityService.waitForApp(targetPackageName, APP_LAUNCH_WAIT_TIME)
            if (!appLaunched) {
                return AutomationResult.failure("目標應用未能在指定時間內啟動")
            }
            
            Log.i(TAG, "Target app $targetPackageName is now in foreground")
            
            // 執行步驟序列
            val executionResults = mutableListOf<StepExecutionResult>()
            var successCount = 0
            var failureCount = 0
            
            for ((index, step) in steps.withIndex()) {
                Log.d(TAG, "Executing step ${index + 1}/${steps.size}: ${step.getDisplayName()}")
                
                val stepResult = executeStep(step, accessibilityService)
                executionResults.add(stepResult)
                
                if (stepResult.success) {
                    successCount++
                    Log.d(TAG, "Step ${index + 1} executed successfully")
                } else {
                    failureCount++
                    Log.w(TAG, "Step ${index + 1} failed: ${stepResult.errorMessage}")
                    
                    // 如果步驟不是可選的且失敗了，決定是否繼續
                    if (!step.isOptional) {
                        Log.e(TAG, "Critical step failed, stopping execution")
                        break
                    }
                }
                
                // 步驟間延遲
                if (index < steps.size - 1) {
                    delay(DEFAULT_STEP_DELAY)
                }
            }
            
            // 記錄執行結果
            val totalSteps = steps.size
            val message = "自動化執行完成：成功 $successCount/$totalSteps 步驟"
            
            SystemLogManager.logAutomationExecution(
                context, scheduleId, targetPackageName, 
                successCount, failureCount, message
            )
            
            return if (failureCount == 0) {
                AutomationResult.success(message, executionResults)
            } else {
                AutomationResult.partial(message, executionResults)
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Automation execution failed", e)
            SystemLogManager.logAutomationExecution(
                context, scheduleId, targetPackageName, 
                0, 1, "自動化執行異常：${e.message}"
            )
            return AutomationResult.failure("執行異常：${e.message}")
        }
    }
    
    /**
     * 執行單個自動化步驟
     */
    private suspend fun executeStep(
        step: AutomationStep,
        accessibilityService: AutoLaunchAccessibilityService
    ): StepExecutionResult {
        val startTime = System.currentTimeMillis()

        try {
            // 檢查步驟配置是否有效
            if (!StepExecutorFactory.validateStep(step)) {
                return StepExecutionResult.failure(step, "步驟配置無效")
            }

            // 獲取步驟執行器
            val executor = StepExecutorFactory.getExecutor(step)

            // 使用錯誤處理器執行步驟
            val success = errorHandler.executeWithRetry(
                step = step,
                operation = {
                    executor.execute(step, accessibilityService)
                },
                onError = { exception, attempt ->
                    Log.w(TAG, "Step execution failed on attempt $attempt: ${exception.message}")
                }
            ) ?: false

            val executionTime = System.currentTimeMillis() - startTime

            return if (success) {
                StepExecutionResult.success(step, executionTime)
            } else {
                StepExecutionResult.failure(step, "步驟執行失敗", executionTime)
            }

        } catch (e: Exception) {
            val executionTime = System.currentTimeMillis() - startTime
            Log.e(TAG, "Unexpected error executing step: ${step.getDisplayName()}", e)
            return StepExecutionResult.failure(step, "執行異常：${e.message}", executionTime)
        }
    }
    
    /**
     * 驗證自動化步驟序列
     */
    suspend fun validateSteps(scheduleId: Long): ValidationResult {
        try {
            val steps = automationStepDao.getStepsByScheduleId(scheduleId).first()
            val issues = mutableListOf<String>()
            
            if (steps.isEmpty()) {
                return ValidationResult.valid("沒有配置自動化步驟")
            }
            
            // 檢查步驟順序
            val sortedSteps = steps.sortedBy { it.stepOrder }
            for (i in sortedSteps.indices) {
                val expectedOrder = i + 1
                if (sortedSteps[i].stepOrder != expectedOrder) {
                    issues.add("步驟順序不連續：期望 $expectedOrder，實際 ${sortedSteps[i].stepOrder}")
                }
            }
            
            // 檢查每個步驟的配置
            for ((index, step) in sortedSteps.withIndex()) {
                if (!step.isValid()) {
                    issues.add("步驟 ${index + 1} 配置無效：${step.getDisplayName()}")
                }
                
                // 檢查超時設置
                if (step.timeoutMs <= 0) {
                    issues.add("步驟 ${index + 1} 超時設置無效：${step.timeoutMs}ms")
                }
                
                // 檢查重試次數
                if (step.retryCount < 0) {
                    issues.add("步驟 ${index + 1} 重試次數無效：${step.retryCount}")
                }
            }
            
            return if (issues.isEmpty()) {
                ValidationResult.valid("步驟序列配置正確")
            } else {
                ValidationResult.invalid(issues)
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error validating steps", e)
            return ValidationResult.invalid(listOf("驗證過程發生錯誤：${e.message}"))
        }
    }
    
    /**
     * 獲取步驟執行預覽
     */
    suspend fun getExecutionPreview(scheduleId: Long): List<StepPreview> {
        return try {
            val steps = automationStepDao.getEnabledStepsByScheduleId(scheduleId).first()
            steps.mapIndexed { index, step ->
                StepPreview(
                    order = index + 1,
                    displayName = step.getDisplayName(),
                    stepType = step.getStepType().displayName,
                    isOptional = step.isOptional,
                    estimatedDuration = estimateStepDuration(step)
                )
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting execution preview", e)
            emptyList()
        }
    }
    
    /**
     * 估算步驟執行時間
     */
    private fun estimateStepDuration(step: AutomationStep): Long {
        return when (step.getStepType()) {
            com.example.autolaunch.model.StepType.WAIT -> step.waitDuration ?: 1000L
            com.example.autolaunch.model.StepType.SWIPE -> step.swipeDuration ?: 500L
            com.example.autolaunch.model.StepType.INPUT_TEXT -> {
                val textLength = step.inputText?.length ?: 0
                500L + (textLength * 50L) // 基礎時間 + 每字符50ms
            }
            else -> 1000L // 其他操作默認1秒
        }
    }
}
