package com.example.autolaunch.automation.steps

import android.util.Log
import com.example.autolaunch.model.AutomationStep
import com.example.autolaunch.service.AutoLaunchAccessibilityService

/**
 * 基礎步驟執行器
 * 所有具體步驟執行器的基類
 */
abstract class BaseStepExecutor {
    
    companion object {
        private const val TAG = "BaseStepExecutor"
    }
    
    /**
     * 執行步驟
     * @param step 要執行的步驟
     * @param accessibilityService 無障礙服務實例
     * @return 執行是否成功
     */
    abstract suspend fun execute(
        step: AutomationStep,
        accessibilityService: AutoLaunchAccessibilityService
    ): Boolean
    
    /**
     * 驗證步驟配置
     * @param step 要驗證的步驟
     * @return 配置是否有效
     */
    abstract fun validate(step: AutomationStep): Boolean
    
    /**
     * 獲取步驟描述
     * @param step 步驟
     * @return 步驟描述
     */
    abstract fun getDescription(step: AutomationStep): String
    
    /**
     * 記錄執行日誌
     */
    protected fun logExecution(step: AutomationStep, message: String, isError: Boolean = false) {
        val logLevel = if (isError) Log.ERROR else Log.DEBUG
        Log.println(logLevel, TAG, "Step ${step.stepOrder}: $message")
    }
    
    /**
     * 記錄執行開始
     */
    protected fun logExecutionStart(step: AutomationStep) {
        logExecution(step, "Starting execution: ${step.getDisplayName()}")
    }
    
    /**
     * 記錄執行成功
     */
    protected fun logExecutionSuccess(step: AutomationStep) {
        logExecution(step, "Execution successful: ${step.getDisplayName()}")
    }
    
    /**
     * 記錄執行失敗
     */
    protected fun logExecutionFailure(step: AutomationStep, reason: String) {
        logExecution(step, "Execution failed: ${step.getDisplayName()} - $reason", true)
    }
    
    /**
     * 安全執行操作
     */
    protected suspend fun <T> safeExecute(
        step: AutomationStep,
        operation: suspend () -> T
    ): T? {
        return try {
            operation()
        } catch (e: Exception) {
            logExecutionFailure(step, "Exception: ${e.message}")
            null
        }
    }
    
    /**
     * 檢查步驟是否啟用
     */
    protected fun isStepEnabled(step: AutomationStep): Boolean {
        return step.isEnabled
    }
    
    /**
     * 檢查步驟是否可選
     */
    protected fun isStepOptional(step: AutomationStep): Boolean {
        return step.isOptional
    }
    
    /**
     * 獲取步驟超時時間
     */
    protected fun getStepTimeout(step: AutomationStep): Long {
        return step.timeoutMs
    }
    
    /**
     * 獲取步驟重試次數
     */
    protected fun getRetryCount(step: AutomationStep): Int {
        return step.retryCount
    }
}
