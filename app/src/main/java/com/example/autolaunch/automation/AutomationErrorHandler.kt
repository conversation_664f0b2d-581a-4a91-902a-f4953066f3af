package com.example.autolaunch.automation

import android.content.Context
import android.util.Log
import com.example.autolaunch.model.AutomationStep
import com.example.autolaunch.utils.SystemLogManager
import kotlinx.coroutines.delay

/**
 * 自動化錯誤處理器
 * 處理自動化執行過程中的各種錯誤和重試邏輯
 */
class AutomationErrorHandler(private val context: Context) {
    
    companion object {
        private const val TAG = "AutomationErrorHandler"
        private const val DEFAULT_RETRY_DELAY = 1000L // 1秒
        private const val MAX_RETRY_DELAY = 5000L // 最大5秒
    }
    
    /**
     * 執行帶重試機制的操作
     */
    suspend fun <T> executeWithRetry(
        step: AutomationStep,
        operation: suspend () -> T,
        onError: ((Exception, Int) -> Unit)? = null
    ): T? {
        val maxRetries = step.retryCount.coerceAtLeast(1)
        var lastException: Exception? = null
        
        for (attempt in 1..maxRetries) {
            try {
                Log.d(TAG, "Executing step attempt $attempt/$maxRetries: ${step.getDisplayName()}")
                return operation()
                
            } catch (e: Exception) {
                lastException = e
                Log.w(TAG, "Step execution failed on attempt $attempt/$maxRetries: ${e.message}")
                
                // 調用錯誤回調
                onError?.invoke(e, attempt)
                
                // 如果不是最後一次嘗試，等待後重試
                if (attempt < maxRetries) {
                    val retryDelay = calculateRetryDelay(attempt, step)
                    Log.d(TAG, "Waiting ${retryDelay}ms before retry...")
                    delay(retryDelay)
                }
            }
        }
        
        // 所有重試都失敗了
        val finalException = lastException ?: Exception("Unknown error")
        handleFinalFailure(step, finalException)
        return null
    }
    
    /**
     * 計算重試延遲時間
     */
    private fun calculateRetryDelay(attempt: Int, step: AutomationStep): Long {
        // 指數退避算法：基礎延遲 * 2^(attempt-1)
        val baseDelay = DEFAULT_RETRY_DELAY
        val exponentialDelay = baseDelay * (1 shl (attempt - 1))
        
        // 限制最大延遲時間
        return exponentialDelay.coerceAtMost(MAX_RETRY_DELAY)
    }
    
    /**
     * 處理最終失敗
     */
    private fun handleFinalFailure(step: AutomationStep, exception: Exception) {
        Log.e(TAG, "Step execution failed after all retries: ${step.getDisplayName()}", exception)
        
        // 記錄錯誤日誌
        SystemLogManager.logEvent(
            context = context,
            logType = SystemLogManager.LogType.ERROR,
            actionType = SystemLogManager.ActionType.SCHEDULE_EXECUTED,
            scheduleId = step.scheduleId,
            scheduleName = "自動化步驟",
            message = "步驟執行失敗：${step.getDisplayName()}",
            details = "錯誤：${exception.message}，重試次數：${step.retryCount}"
        )
    }
    
    /**
     * 處理超時錯誤
     */
    fun handleTimeoutError(step: AutomationStep, timeoutMs: Long): AutomationError {
        val message = "步驟執行超時：${step.getDisplayName()}（${timeoutMs}ms）"
        Log.w(TAG, message)
        
        return AutomationError(
            type = AutomationErrorType.TIMEOUT,
            message = message,
            step = step,
            isRecoverable = true
        )
    }
    
    /**
     * 處理元素未找到錯誤
     */
    fun handleElementNotFoundError(step: AutomationStep): AutomationError {
        val message = "目標元素未找到：${step.getDisplayName()}"
        Log.w(TAG, message)
        
        return AutomationError(
            type = AutomationErrorType.ELEMENT_NOT_FOUND,
            message = message,
            step = step,
            isRecoverable = true
        )
    }
    
    /**
     * 處理權限錯誤
     */
    fun handlePermissionError(step: AutomationStep, permission: String): AutomationError {
        val message = "權限不足：$permission"
        Log.e(TAG, message)
        
        return AutomationError(
            type = AutomationErrorType.PERMISSION_DENIED,
            message = message,
            step = step,
            isRecoverable = false
        )
    }
    
    /**
     * 處理無障礙服務錯誤
     */
    fun handleAccessibilityServiceError(step: AutomationStep): AutomationError {
        val message = "無障礙服務不可用"
        Log.e(TAG, message)
        
        return AutomationError(
            type = AutomationErrorType.ACCESSIBILITY_SERVICE_UNAVAILABLE,
            message = message,
            step = step,
            isRecoverable = false
        )
    }
    
    /**
     * 處理應用狀態錯誤
     */
    fun handleAppStateError(step: AutomationStep, expectedApp: String, currentApp: String?): AutomationError {
        val message = "應用狀態錯誤：期望 $expectedApp，實際 ${currentApp ?: "未知"}"
        Log.w(TAG, message)
        
        return AutomationError(
            type = AutomationErrorType.APP_STATE_ERROR,
            message = message,
            step = step,
            isRecoverable = true
        )
    }
    
    /**
     * 處理配置錯誤
     */
    fun handleConfigurationError(step: AutomationStep, details: String): AutomationError {
        val message = "步驟配置錯誤：$details"
        Log.e(TAG, message)
        
        return AutomationError(
            type = AutomationErrorType.CONFIGURATION_ERROR,
            message = message,
            step = step,
            isRecoverable = false
        )
    }
    
    /**
     * 處理系統錯誤
     */
    fun handleSystemError(step: AutomationStep, exception: Exception): AutomationError {
        val message = "系統錯誤：${exception.message}"
        Log.e(TAG, message, exception)
        
        return AutomationError(
            type = AutomationErrorType.SYSTEM_ERROR,
            message = message,
            step = step,
            isRecoverable = false,
            exception = exception
        )
    }
    
    /**
     * 檢查錯誤是否可恢復
     */
    fun isRecoverableError(error: AutomationError): Boolean {
        return error.isRecoverable && when (error.type) {
            AutomationErrorType.TIMEOUT,
            AutomationErrorType.ELEMENT_NOT_FOUND,
            AutomationErrorType.APP_STATE_ERROR -> true
            
            AutomationErrorType.PERMISSION_DENIED,
            AutomationErrorType.ACCESSIBILITY_SERVICE_UNAVAILABLE,
            AutomationErrorType.CONFIGURATION_ERROR,
            AutomationErrorType.SYSTEM_ERROR -> false
        }
    }
    
    /**
     * 獲取錯誤恢復建議
     */
    fun getRecoverySuggestion(error: AutomationError): String {
        return when (error.type) {
            AutomationErrorType.TIMEOUT -> "增加步驟超時時間或檢查目標應用響應速度"
            AutomationErrorType.ELEMENT_NOT_FOUND -> "檢查目標元素是否存在或更新元素定位方式"
            AutomationErrorType.PERMISSION_DENIED -> "檢查應用權限設置"
            AutomationErrorType.ACCESSIBILITY_SERVICE_UNAVAILABLE -> "啟用無障礙服務"
            AutomationErrorType.APP_STATE_ERROR -> "確保目標應用處於正確狀態"
            AutomationErrorType.CONFIGURATION_ERROR -> "檢查步驟配置是否正確"
            AutomationErrorType.SYSTEM_ERROR -> "檢查系統狀態或重啟應用"
        }
    }
}

/**
 * 自動化錯誤類型枚舉
 */
enum class AutomationErrorType {
    TIMEOUT,                              // 超時
    ELEMENT_NOT_FOUND,                    // 元素未找到
    PERMISSION_DENIED,                    // 權限被拒絕
    ACCESSIBILITY_SERVICE_UNAVAILABLE,    // 無障礙服務不可用
    APP_STATE_ERROR,                      // 應用狀態錯誤
    CONFIGURATION_ERROR,                  // 配置錯誤
    SYSTEM_ERROR                          // 系統錯誤
}

/**
 * 自動化錯誤數據類
 */
data class AutomationError(
    val type: AutomationErrorType,
    val message: String,
    val step: AutomationStep,
    val isRecoverable: Boolean,
    val exception: Exception? = null,
    val timestamp: Long = System.currentTimeMillis()
) {
    /**
     * 獲取錯誤的簡短描述
     */
    fun getShortDescription(): String {
        return "${type.name}: ${message.take(50)}${if (message.length > 50) "..." else ""}"
    }
    
    /**
     * 獲取錯誤的詳細描述
     */
    fun getDetailedDescription(): String {
        return buildString {
            appendLine("錯誤類型: ${type.name}")
            appendLine("錯誤訊息: $message")
            appendLine("相關步驟: ${step.getDisplayName()}")
            appendLine("是否可恢復: ${if (isRecoverable) "是" else "否"}")
            appendLine("發生時間: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(java.util.Date(timestamp))}")
            exception?.let {
                appendLine("異常詳情: ${it.stackTraceToString()}")
            }
        }
    }
}
