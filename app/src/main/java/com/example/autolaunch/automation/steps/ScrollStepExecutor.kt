package com.example.autolaunch.automation.steps

import android.view.accessibility.AccessibilityNodeInfo
import com.example.autolaunch.model.AutomationStep
import com.example.autolaunch.model.StepType
import com.example.autolaunch.service.AutoLaunchAccessibilityService
import kotlinx.coroutines.delay

/**
 * 滾動步驟執行器
 * 處理滾動操作
 */
class ScrollStepExecutor : BaseStepExecutor() {
    
    override suspend fun execute(
        step: AutomationStep,
        accessibilityService: AutoLaunchAccessibilityService
    ): Boolean {
        if (!isStepEnabled(step)) {
            logExecution(step, "Step is disabled, skipping")
            return true
        }
        
        logExecutionStart(step)
        
        return safeExecute(step) {
            val scrollDirection = step.scrollDirection
            if (scrollDirection == null || scrollDirection !in 1..4) {
                logExecutionFailure(step, "Invalid scroll direction: $scrollDirection")
                return@safeExecute false
            }
            
            val success = performScroll(accessibilityService, scrollDirection, step)
            
            if (success) {
                logExecutionSuccess(step)
                // 滾動後短暫延遲，等待UI響應
                delay(300)
            } else {
                logExecutionFailure(step, "Scroll operation failed")
            }
            
            success
        } ?: false
    }
    
    override fun validate(step: AutomationStep): Boolean {
        // 檢查步驟類型
        if (step.getStepType() != StepType.SCROLL) {
            return false
        }
        
        // 檢查滾動方向
        return step.scrollDirection != null && step.scrollDirection in 1..4
    }
    
    override fun getDescription(step: AutomationStep): String {
        // 使用舊版本的getDisplayName方法，保持向後兼容
        return step.getDisplayName()
    }
    
    /**
     * 執行滾動操作
     */
    private suspend fun performScroll(
        accessibilityService: AutoLaunchAccessibilityService,
        direction: Int,
        step: AutomationStep
    ): Boolean {
        // 首先嘗試在當前窗口中查找可滾動的元素
        val scrollableNode = findScrollableNode(accessibilityService)
        
        if (scrollableNode != null) {
            logExecution(step, "Found scrollable element, performing targeted scroll")
            return performNodeScroll(scrollableNode, direction, step)
        }
        
        // 如果沒有找到可滾動元素，嘗試在根節點上滾動
        logExecution(step, "No specific scrollable element found, trying root node scroll")
        return performRootScroll(accessibilityService, direction, step)
    }
    
    /**
     * 查找可滾動的節點
     */
    private fun findScrollableNode(accessibilityService: AutoLaunchAccessibilityService): AccessibilityNodeInfo? {
        val rootNode = accessibilityService.rootInActiveWindow ?: return null
        return findScrollableNodeRecursively(rootNode)
    }
    
    /**
     * 遞歸查找可滾動節點
     */
    private fun findScrollableNodeRecursively(node: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        if (node.isScrollable) {
            return node
        }
        
        for (i in 0 until node.childCount) {
            val child = node.getChild(i) ?: continue
            val result = findScrollableNodeRecursively(child)
            if (result != null) {
                return result
            }
        }
        
        return null
    }
    
    /**
     * 在特定節點上執行滾動
     */
    private suspend fun performNodeScroll(
        node: AccessibilityNodeInfo,
        direction: Int,
        step: AutomationStep
    ): Boolean {
        val action = when (direction) {
            1 -> AccessibilityNodeInfo.ACTION_SCROLL_BACKWARD  // 向上
            2 -> AccessibilityNodeInfo.ACTION_SCROLL_FORWARD   // 向下
            3 -> AccessibilityNodeInfo.ACTION_SCROLL_BACKWARD  // 向左（水平滾動）
            4 -> AccessibilityNodeInfo.ACTION_SCROLL_FORWARD   // 向右（水平滾動）
            else -> return false
        }
        
        return try {
            val success = node.performAction(action)
            if (success) {
                logExecution(step, "Node scroll successful")
            } else {
                logExecution(step, "Node scroll failed, trying alternative method")
                // 嘗試替代滾動方法
                performAlternativeScroll(node, direction, step)
            }
            success
        } catch (e: Exception) {
            logExecutionFailure(step, "Node scroll exception: ${e.message}")
            false
        }
    }
    
    /**
     * 在根節點上執行滾動
     */
    private suspend fun performRootScroll(
        accessibilityService: AutoLaunchAccessibilityService,
        direction: Int,
        step: AutomationStep
    ): Boolean {
        val rootNode = accessibilityService.rootInActiveWindow ?: return false
        
        val action = when (direction) {
            1 -> AccessibilityNodeInfo.ACTION_SCROLL_BACKWARD  // 向上
            2 -> AccessibilityNodeInfo.ACTION_SCROLL_FORWARD   // 向下
            3 -> AccessibilityNodeInfo.ACTION_SCROLL_BACKWARD  // 向左
            4 -> AccessibilityNodeInfo.ACTION_SCROLL_FORWARD   // 向右
            else -> return false
        }
        
        return try {
            val success = rootNode.performAction(action)
            if (success) {
                logExecution(step, "Root scroll successful")
            } else {
                logExecution(step, "Root scroll failed")
            }
            success
        } catch (e: Exception) {
            logExecutionFailure(step, "Root scroll exception: ${e.message}")
            false
        }
    }
    
    /**
     * 執行替代滾動方法
     */
    private suspend fun performAlternativeScroll(
        node: AccessibilityNodeInfo,
        direction: Int,
        step: AutomationStep
    ): Boolean {
        // 這裡可以實現其他滾動方法，比如模擬滑動手勢
        logExecution(step, "Attempting alternative scroll method")
        
        // 可以調用 SwipeStepExecutor 來模擬滑動
        return try {
            // 創建一個臨時的滑動步驟
            val swipeStep = createSwipeStepForScroll(step, direction)
            val swipeExecutor = SwipeStepExecutor()
            
            // 獲取 AccessibilityService 實例
            val accessibilityService = AutoLaunchAccessibilityService.getInstance()
            if (accessibilityService != null) {
                swipeExecutor.execute(swipeStep, accessibilityService)
            } else {
                false
            }
        } catch (e: Exception) {
            logExecutionFailure(step, "Alternative scroll failed: ${e.message}")
            false
        }
    }
    
    /**
     * 為滾動創建對應的滑動步驟
     */
    private fun createSwipeStepForScroll(originalStep: AutomationStep, direction: Int): AutomationStep {
        val swipeType = when (direction) {
            1 -> com.example.autolaunch.model.StepType.SWIPE_UP
            2 -> com.example.autolaunch.model.StepType.SWIPE_DOWN
            3 -> com.example.autolaunch.model.StepType.SWIPE_LEFT
            4 -> com.example.autolaunch.model.StepType.SWIPE_RIGHT
            else -> com.example.autolaunch.model.StepType.SWIPE_DOWN
        }
        
        return originalStep.copy(
            stepType = swipeType.value,
            stepName = "滾動轉滑動"
        )
    }
}
