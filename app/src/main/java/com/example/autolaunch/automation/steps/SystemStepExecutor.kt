package com.example.autolaunch.automation.steps

import com.example.autolaunch.model.AutomationStep
import com.example.autolaunch.model.StepType
import com.example.autolaunch.service.AutoLaunchAccessibilityService
import kotlinx.coroutines.delay

/**
 * 系統操作步驟執行器
 * 處理返回、主頁等系統級操作
 */
class SystemStepExecutor : BaseStepExecutor() {
    
    override suspend fun execute(
        step: AutomationStep,
        accessibilityService: AutoLaunchAccessibilityService
    ): Boolean {
        if (!isStepEnabled(step)) {
            logExecution(step, "Step is disabled, skipping")
            return true
        }
        
        logExecutionStart(step)
        
        return safeExecute(step) {
            val success = when (step.getStepType()) {
                StepType.BACK -> performBackAction(accessibilityService, step)
                StepType.HOME -> performHomeAction(accessibilityService, step)
                else -> {
                    logExecutionFailure(step, "Unsupported system action: ${step.getStepType()}")
                    false
                }
            }
            
            if (success) {
                logExecutionSuccess(step)
                // 系統操作後短暫延遲，等待UI響應
                delay(500)
            } else {
                logExecutionFailure(step, "System action failed")
            }
            
            success
        } ?: false
    }
    
    override fun validate(step: AutomationStep): Boolean {
        // 系統操作步驟類型檢查
        return step.getStepType() in listOf(StepType.BACK, StepType.HOME)
    }
    
    override fun getDescription(step: AutomationStep): String {
        return when (step.getStepType()) {
            StepType.BACK -> "返回"
            StepType.HOME -> "回到主頁"
            else -> "系統操作"
        }
    }
    
    /**
     * 執行返回操作
     */
    private suspend fun performBackAction(
        accessibilityService: AutoLaunchAccessibilityService,
        step: AutomationStep
    ): Boolean {
        logExecution(step, "Performing back action")
        
        return try {
            accessibilityService.performGlobalAction(AutoLaunchAccessibilityService.GLOBAL_ACTION_BACK)
        } catch (e: Exception) {
            logExecutionFailure(step, "Back action failed: ${e.message}")
            false
        }
    }
    
    /**
     * 執行回到主頁操作
     */
    private suspend fun performHomeAction(
        accessibilityService: AutoLaunchAccessibilityService,
        step: AutomationStep
    ): Boolean {
        logExecution(step, "Performing home action")
        
        return try {
            accessibilityService.performGlobalAction(AutoLaunchAccessibilityService.GLOBAL_ACTION_HOME)
        } catch (e: Exception) {
            logExecutionFailure(step, "Home action failed: ${e.message}")
            false
        }
    }
}
