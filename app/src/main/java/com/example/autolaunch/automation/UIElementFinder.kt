package com.example.autolaunch.automation

import android.graphics.Rect
import android.view.accessibility.AccessibilityNodeInfo
import com.example.autolaunch.model.AutomationStep
import com.example.autolaunch.model.TargetType
import com.example.autolaunch.service.AutoLaunchAccessibilityService

/**
 * UI元素查找器
 * 提供多種方式定位和查找UI元素
 */
class UIElementFinder(private val accessibilityService: AutoLaunchAccessibilityService) {
    
    /**
     * 根據步驟配置查找目標元素
     */
    fun findElement(step: AutomationStep): AccessibilityNodeInfo? {
        val rootNode = accessibilityService.rootInActiveWindow ?: return null
        
        return when (step.getTargetType()) {
            TargetType.TEXT -> findByText(rootNode, step.targetText)
            TargetType.ID -> findById(rootNode, step.targetId)
            TargetType.COORDINATE -> findByCoordinate(rootNode, step.targetX, step.targetY)
            TargetType.DESCRIPTION -> findByDescription(rootNode, step.targetDescription)
            TargetType.CLASS_NAME -> findByClassName(rootNode, step.targetClassName)
        }
    }
    
    /**
     * 根據文字查找元素
     */
    fun findByText(rootNode: AccessibilityNodeInfo, text: String?): AccessibilityNodeInfo? {
        if (text.isNullOrBlank()) return null
        
        // 首先嘗試精確匹配
        val exactMatches = rootNode.findAccessibilityNodeInfosByText(text)
        if (!exactMatches.isNullOrEmpty()) {
            return exactMatches.first()
        }
        
        // 如果精確匹配失敗，嘗試模糊匹配
        return findByTextFuzzy(rootNode, text)
    }
    
    /**
     * 根據ID查找元素
     */
    fun findById(rootNode: AccessibilityNodeInfo, viewId: String?): AccessibilityNodeInfo? {
        if (viewId.isNullOrBlank()) return null
        
        // 嘗試完整ID匹配
        val fullIdMatches = rootNode.findAccessibilityNodeInfosByViewId(viewId)
        if (!fullIdMatches.isNullOrEmpty()) {
            return fullIdMatches.first()
        }
        
        // 如果完整ID匹配失敗，嘗試部分ID匹配
        return findByIdPartial(rootNode, viewId)
    }
    
    /**
     * 根據座標查找元素
     */
    fun findByCoordinate(rootNode: AccessibilityNodeInfo, x: Int?, y: Int?): AccessibilityNodeInfo? {
        if (x == null || y == null) return null
        
        return findElementAtPosition(rootNode, x, y)
    }
    
    /**
     * 根據描述查找元素
     */
    fun findByDescription(rootNode: AccessibilityNodeInfo, description: String?): AccessibilityNodeInfo? {
        if (description.isNullOrBlank()) return null
        
        return findElementRecursively(rootNode) { node ->
            val contentDesc = node.contentDescription?.toString()
            contentDesc?.contains(description, ignoreCase = true) == true
        }
    }
    
    /**
     * 根據類別名稱查找元素
     */
    fun findByClassName(rootNode: AccessibilityNodeInfo, className: String?): AccessibilityNodeInfo? {
        if (className.isNullOrBlank()) return null
        
        return findElementRecursively(rootNode) { node ->
            val nodeClassName = node.className?.toString()
            nodeClassName?.contains(className, ignoreCase = true) == true
        }
    }
    
    /**
     * 模糊文字匹配
     */
    private fun findByTextFuzzy(rootNode: AccessibilityNodeInfo, text: String): AccessibilityNodeInfo? {
        return findElementRecursively(rootNode) { node ->
            val nodeText = node.text?.toString()
            nodeText?.contains(text, ignoreCase = true) == true
        }
    }
    
    /**
     * 部分ID匹配
     */
    private fun findByIdPartial(rootNode: AccessibilityNodeInfo, viewId: String): AccessibilityNodeInfo? {
        return findElementRecursively(rootNode) { node ->
            val nodeViewId = node.viewIdResourceName
            nodeViewId?.contains(viewId, ignoreCase = true) == true
        }
    }
    
    /**
     * 在指定位置查找元素
     */
    private fun findElementAtPosition(rootNode: AccessibilityNodeInfo, x: Int, y: Int): AccessibilityNodeInfo? {
        return findElementRecursively(rootNode) { node ->
            val rect = Rect()
            node.getBoundsInScreen(rect)
            rect.contains(x, y)
        }
    }
    
    /**
     * 遞歸查找元素
     */
    private fun findElementRecursively(
        node: AccessibilityNodeInfo,
        predicate: (AccessibilityNodeInfo) -> Boolean
    ): AccessibilityNodeInfo? {
        if (predicate(node)) {
            return node
        }
        
        for (i in 0 until node.childCount) {
            val child = node.getChild(i) ?: continue
            val result = findElementRecursively(child, predicate)
            if (result != null) {
                return result
            }
        }
        
        return null
    }
    
    /**
     * 獲取所有可交互的元素
     */
    fun getAllInteractiveElements(): List<UIElementInfo> {
        val rootNode = accessibilityService.rootInActiveWindow ?: return emptyList()
        val elements = mutableListOf<UIElementInfo>()
        
        collectInteractiveElements(rootNode, elements)
        return elements
    }
    
    /**
     * 收集可交互元素
     */
    private fun collectInteractiveElements(node: AccessibilityNodeInfo, result: MutableList<UIElementInfo>) {
        if (isInteractiveElement(node)) {
            val elementInfo = createElementInfo(node)
            result.add(elementInfo)
        }
        
        for (i in 0 until node.childCount) {
            val child = node.getChild(i) ?: continue
            collectInteractiveElements(child, result)
        }
    }
    
    /**
     * 檢查是否為可交互元素
     */
    private fun isInteractiveElement(node: AccessibilityNodeInfo): Boolean {
        return node.isClickable || node.isLongClickable || node.isEditable || 
               node.isCheckable || node.isFocusable
    }
    
    /**
     * 創建元素信息
     */
    private fun createElementInfo(node: AccessibilityNodeInfo): UIElementInfo {
        val rect = Rect()
        node.getBoundsInScreen(rect)
        
        return UIElementInfo(
            text = node.text?.toString(),
            description = node.contentDescription?.toString(),
            className = node.className?.toString(),
            viewId = node.viewIdResourceName,
            bounds = rect,
            isClickable = node.isClickable,
            isLongClickable = node.isLongClickable,
            isEditable = node.isEditable,
            isCheckable = node.isCheckable,
            isFocusable = node.isFocusable,
            isScrollable = node.isScrollable
        )
    }
    
    /**
     * 根據元素信息查找元素
     */
    fun findElementByInfo(elementInfo: UIElementInfo): AccessibilityNodeInfo? {
        val rootNode = accessibilityService.rootInActiveWindow ?: return null
        
        return findElementRecursively(rootNode) { node ->
            matchesElementInfo(node, elementInfo)
        }
    }
    
    /**
     * 檢查節點是否匹配元素信息
     */
    private fun matchesElementInfo(node: AccessibilityNodeInfo, elementInfo: UIElementInfo): Boolean {
        // 檢查文字匹配
        if (!elementInfo.text.isNullOrBlank()) {
            val nodeText = node.text?.toString()
            if (nodeText != elementInfo.text) {
                return false
            }
        }
        
        // 檢查描述匹配
        if (!elementInfo.description.isNullOrBlank()) {
            val nodeDesc = node.contentDescription?.toString()
            if (nodeDesc != elementInfo.description) {
                return false
            }
        }
        
        // 檢查類別名稱匹配
        if (!elementInfo.className.isNullOrBlank()) {
            val nodeClassName = node.className?.toString()
            if (nodeClassName != elementInfo.className) {
                return false
            }
        }
        
        // 檢查ViewID匹配
        if (!elementInfo.viewId.isNullOrBlank()) {
            if (node.viewIdResourceName != elementInfo.viewId) {
                return false
            }
        }
        
        // 檢查位置匹配（允許一定誤差）
        val rect = Rect()
        node.getBoundsInScreen(rect)
        val tolerance = 10 // 10像素誤差
        
        return Math.abs(rect.centerX() - elementInfo.bounds.centerX()) <= tolerance &&
               Math.abs(rect.centerY() - elementInfo.bounds.centerY()) <= tolerance
    }
    
    /**
     * 獲取元素的詳細信息
     */
    fun getElementDetails(node: AccessibilityNodeInfo): ElementDetails {
        val rect = Rect()
        node.getBoundsInScreen(rect)
        
        return ElementDetails(
            text = node.text?.toString(),
            description = node.contentDescription?.toString(),
            className = node.className?.toString(),
            viewId = node.viewIdResourceName,
            packageName = node.packageName?.toString(),
            bounds = rect,
            isClickable = node.isClickable,
            isLongClickable = node.isLongClickable,
            isEditable = node.isEditable,
            isCheckable = node.isCheckable,
            isChecked = node.isChecked,
            isFocusable = node.isFocusable,
            isFocused = node.isFocused,
            isScrollable = node.isScrollable,
            isEnabled = node.isEnabled,
            isSelected = node.isSelected,
            childCount = node.childCount,
            hasParent = node.parent != null
        )
    }
}

/**
 * UI元素信息數據類
 */
data class UIElementInfo(
    val text: String?,
    val description: String?,
    val className: String?,
    val viewId: String?,
    val bounds: Rect,
    val isClickable: Boolean,
    val isLongClickable: Boolean,
    val isEditable: Boolean,
    val isCheckable: Boolean,
    val isFocusable: Boolean,
    val isScrollable: Boolean
) {
    /**
     * 獲取元素的顯示名稱
     */
    fun getDisplayName(): String {
        return when {
            !text.isNullOrBlank() -> "文字: $text"
            !description.isNullOrBlank() -> "描述: $description"
            !viewId.isNullOrBlank() -> "ID: ${viewId.substringAfterLast("/")}"
            !className.isNullOrBlank() -> "類別: ${className.substringAfterLast(".")}"
            else -> "未知元素"
        }
    }
    
    /**
     * 獲取元素類型描述
     */
    fun getTypeDescription(): String {
        val types = mutableListOf<String>()
        if (isClickable) types.add("可點擊")
        if (isLongClickable) types.add("可長按")
        if (isEditable) types.add("可編輯")
        if (isCheckable) types.add("可勾選")
        if (isScrollable) types.add("可滾動")
        
        return if (types.isNotEmpty()) types.joinToString(", ") else "靜態元素"
    }
}

/**
 * 元素詳細信息數據類
 */
data class ElementDetails(
    val text: String?,
    val description: String?,
    val className: String?,
    val viewId: String?,
    val packageName: String?,
    val bounds: Rect,
    val isClickable: Boolean,
    val isLongClickable: Boolean,
    val isEditable: Boolean,
    val isCheckable: Boolean,
    val isChecked: Boolean,
    val isFocusable: Boolean,
    val isFocused: Boolean,
    val isScrollable: Boolean,
    val isEnabled: Boolean,
    val isSelected: Boolean,
    val childCount: Int,
    val hasParent: Boolean
)
