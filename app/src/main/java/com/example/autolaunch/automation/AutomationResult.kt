package com.example.autolaunch.automation

import com.example.autolaunch.model.AutomationStep
import com.example.autolaunch.model.StepType

/**
 * 自動化執行結果
 */
data class AutomationResult(
    val success: Boolean,
    val message: String,
    val stepResults: List<StepExecutionResult> = emptyList(),
    val executionTime: Long = 0L
) {
    companion object {
        fun success(message: String, stepResults: List<StepExecutionResult> = emptyList()): AutomationResult {
            return AutomationResult(
                success = true,
                message = message,
                stepResults = stepResults,
                executionTime = stepResults.sumOf { it.executionTime }
            )
        }
        
        fun failure(message: String, stepResults: List<StepExecutionResult> = emptyList()): AutomationResult {
            return AutomationResult(
                success = false,
                message = message,
                stepResults = stepResults,
                executionTime = stepResults.sumOf { it.executionTime }
            )
        }
        
        fun partial(message: String, stepResults: List<StepExecutionResult> = emptyList()): AutomationResult {
            return AutomationResult(
                success = false,
                message = message,
                stepResults = stepResults,
                executionTime = stepResults.sumOf { it.executionTime }
            )
        }
    }
    
    /**
     * 獲取成功步驟數量
     */
    fun getSuccessCount(): Int = stepResults.count { it.success }
    
    /**
     * 獲取失敗步驟數量
     */
    fun getFailureCount(): Int = stepResults.count { !it.success }
    
    /**
     * 獲取總步驟數量
     */
    fun getTotalSteps(): Int = stepResults.size
    
    /**
     * 獲取成功率
     */
    fun getSuccessRate(): Float {
        return if (stepResults.isEmpty()) 0f else getSuccessCount().toFloat() / getTotalSteps()
    }
}

/**
 * 單個步驟執行結果
 */
data class StepExecutionResult(
    val step: AutomationStep,
    val success: Boolean,
    val errorMessage: String? = null,
    val executionTime: Long = 0L,
    val timestamp: Long = System.currentTimeMillis()
) {
    companion object {
        fun success(step: AutomationStep, executionTime: Long = 0L): StepExecutionResult {
            return StepExecutionResult(
                step = step,
                success = true,
                executionTime = executionTime
            )
        }
        
        fun failure(step: AutomationStep, errorMessage: String, executionTime: Long = 0L): StepExecutionResult {
            return StepExecutionResult(
                step = step,
                success = false,
                errorMessage = errorMessage,
                executionTime = executionTime
            )
        }
    }
    
    /**
     * 獲取步驟顯示名稱
     */
    fun getStepDisplayName(): String = step.getDisplayName()
    
    /**
     * 獲取步驟類型
     */
    fun getStepType(): StepType = step.getStepType()
    
    /**
     * 是否為可選步驟
     */
    fun isOptionalStep(): Boolean = step.isOptional
}

/**
 * 步驟驗證結果
 */
data class ValidationResult(
    val isValid: Boolean,
    val message: String,
    val issues: List<String> = emptyList()
) {
    companion object {
        fun valid(message: String): ValidationResult {
            return ValidationResult(isValid = true, message = message)
        }
        
        fun invalid(issues: List<String>): ValidationResult {
            return ValidationResult(
                isValid = false,
                message = "發現 ${issues.size} 個問題",
                issues = issues
            )
        }
    }
    
    /**
     * 獲取問題數量
     */
    fun getIssueCount(): Int = issues.size
    
    /**
     * 獲取格式化的問題列表
     */
    fun getFormattedIssues(): String {
        return issues.joinToString("\n") { "• $it" }
    }
}

/**
 * 步驟執行預覽
 */
data class StepPreview(
    val order: Int,
    val displayName: String,
    val stepType: String,
    val isOptional: Boolean,
    val estimatedDuration: Long
) {
    /**
     * 獲取格式化的持續時間
     */
    fun getFormattedDuration(): String {
        return when {
            estimatedDuration < 1000 -> "${estimatedDuration}ms"
            estimatedDuration < 60000 -> "${estimatedDuration / 1000}s"
            else -> "${estimatedDuration / 60000}m ${(estimatedDuration % 60000) / 1000}s"
        }
    }
    
    /**
     * 獲取步驟描述
     */
    fun getDescription(): String {
        val optionalText = if (isOptional) " (可選)" else ""
        return "$order. $displayName$optionalText - 預估 ${getFormattedDuration()}"
    }
}

/**
 * 自動化執行統計
 */
data class AutomationStatistics(
    val totalExecutions: Int,
    val successfulExecutions: Int,
    val failedExecutions: Int,
    val averageExecutionTime: Long,
    val mostCommonFailureReason: String? = null
) {
    /**
     * 獲取成功率
     */
    fun getSuccessRate(): Float {
        return if (totalExecutions == 0) 0f else successfulExecutions.toFloat() / totalExecutions
    }
    
    /**
     * 獲取失敗率
     */
    fun getFailureRate(): Float {
        return if (totalExecutions == 0) 0f else failedExecutions.toFloat() / totalExecutions
    }
    
    /**
     * 獲取格式化的平均執行時間
     */
    fun getFormattedAverageTime(): String {
        return when {
            averageExecutionTime < 1000 -> "${averageExecutionTime}ms"
            averageExecutionTime < 60000 -> "${averageExecutionTime / 1000}s"
            else -> "${averageExecutionTime / 60000}m ${(averageExecutionTime % 60000) / 1000}s"
        }
    }
}
