package com.example.autolaunch.automation.steps

import com.example.autolaunch.model.AutomationStep
import com.example.autolaunch.model.StepType

/**
 * 步驟執行器工廠
 * 根據步驟類型創建對應的執行器
 */
object StepExecutorFactory {
    
    /**
     * 根據步驟類型獲取對應的執行器
     */
    fun getExecutor(step: AutomationStep): BaseStepExecutor {
        return when (step.getStepType()) {
            StepType.CLICK, StepType.LONG_CLICK -> ClickStepExecutor()
            StepType.INPUT_TEXT -> InputStepExecutor()
            StepType.WAIT -> WaitStepExecutor()
            StepType.SWIPE, StepType.SWIPE_UP, StepType.SWIPE_DOWN, 
            StepType.SWIPE_LEFT, StepType.SWIPE_RIGHT -> SwipeStepExecutor()
            StepType.SCROLL -> ScrollStepExecutor()
            StepType.BACK, StepType.HOME -> SystemStepExecutor()
        }
    }
    
    /**
     * 根據步驟類型獲取對應的執行器
     */
    fun getExecutor(stepType: StepType): BaseStepExecutor {
        return when (stepType) {
            StepType.CLICK, StepType.LONG_CLICK -> ClickStepExecutor()
            StepType.INPUT_TEXT -> InputStepExecutor()
            StepType.WAIT -> WaitStepExecutor()
            StepType.SWIPE, StepType.SWIPE_UP, StepType.SWIPE_DOWN, 
            StepType.SWIPE_LEFT, StepType.SWIPE_RIGHT -> SwipeStepExecutor()
            StepType.SCROLL -> ScrollStepExecutor()
            StepType.BACK, StepType.HOME -> SystemStepExecutor()
        }
    }
    
    /**
     * 驗證步驟配置
     */
    fun validateStep(step: AutomationStep): Boolean {
        return try {
            val executor = getExecutor(step)
            executor.validate(step)
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 獲取步驟描述
     */
    fun getStepDescription(step: AutomationStep): String {
        return try {
            val executor = getExecutor(step)
            executor.getDescription(step)
        } catch (e: Exception) {
            "未知步驟"
        }
    }
    
    /**
     * 獲取所有支持的步驟類型
     */
    fun getSupportedStepTypes(): List<StepType> {
        return listOf(
            StepType.CLICK,
            StepType.LONG_CLICK,
            StepType.INPUT_TEXT,
            StepType.WAIT,
            StepType.SWIPE,
            StepType.SWIPE_UP,
            StepType.SWIPE_DOWN,
            StepType.SWIPE_LEFT,
            StepType.SWIPE_RIGHT,
            StepType.SCROLL,
            StepType.BACK,
            StepType.HOME
        )
    }
    
    /**
     * 檢查步驟類型是否需要目標元素
     */
    fun requiresTarget(stepType: StepType): Boolean {
        return when (stepType) {
            StepType.CLICK, StepType.LONG_CLICK, StepType.INPUT_TEXT -> true
            StepType.WAIT, StepType.BACK, StepType.HOME -> false
            StepType.SWIPE -> true  // 自定義滑動需要座標
            StepType.SWIPE_UP, StepType.SWIPE_DOWN, 
            StepType.SWIPE_LEFT, StepType.SWIPE_RIGHT -> false  // 預定義滑動不需要
            StepType.SCROLL -> false  // 滾動通常在整個頁面上進行
        }
    }
    
    /**
     * 檢查步驟類型是否需要輸入文字
     */
    fun requiresInputText(stepType: StepType): Boolean {
        return stepType == StepType.INPUT_TEXT
    }
    
    /**
     * 檢查步驟類型是否需要等待時間
     */
    fun requiresWaitDuration(stepType: StepType): Boolean {
        return stepType == StepType.WAIT
    }
    
    /**
     * 檢查步驟類型是否需要滑動參數
     */
    fun requiresSwipeParameters(stepType: StepType): Boolean {
        return stepType == StepType.SWIPE
    }
    
    /**
     * 檢查步驟類型是否需要滾動參數
     */
    fun requiresScrollParameters(stepType: StepType): Boolean {
        return stepType == StepType.SCROLL
    }
    
    /**
     * 獲取步驟類型的默認配置
     */
    fun getDefaultStepConfig(stepType: StepType): Map<String, Any> {
        return when (stepType) {
            StepType.CLICK, StepType.LONG_CLICK -> mapOf(
                "retryCount" to 3,
                "timeoutMs" to 10000L,
                "isOptional" to false
            )
            StepType.INPUT_TEXT -> mapOf(
                "retryCount" to 3,
                "timeoutMs" to 10000L,
                "isOptional" to false,
                "inputText" to ""
            )
            StepType.WAIT -> mapOf(
                "retryCount" to 1,
                "timeoutMs" to 1000L,
                "isOptional" to false,
                "waitDuration" to 1000L
            )
            StepType.SWIPE -> mapOf(
                "retryCount" to 3,
                "timeoutMs" to 5000L,
                "isOptional" to false,
                "swipeDuration" to 500L
            )
            StepType.SWIPE_UP, StepType.SWIPE_DOWN, 
            StepType.SWIPE_LEFT, StepType.SWIPE_RIGHT -> mapOf(
                "retryCount" to 3,
                "timeoutMs" to 5000L,
                "isOptional" to false
            )
            StepType.SCROLL -> mapOf(
                "retryCount" to 3,
                "timeoutMs" to 5000L,
                "isOptional" to false,
                "scrollDirection" to 2  // 默認向下滾動
            )
            StepType.BACK, StepType.HOME -> mapOf(
                "retryCount" to 1,
                "timeoutMs" to 3000L,
                "isOptional" to false
            )
        }
    }
}
