package com.example.autolaunch.automation.steps

import android.accessibilityservice.GestureDescription
import android.graphics.Path
import com.example.autolaunch.model.AutomationStep
import com.example.autolaunch.model.StepType
import com.example.autolaunch.service.AutoLaunchAccessibilityService
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import kotlinx.coroutines.Dispatchers

/**
 * 滑動步驟執行器
 * 處理各種滑動操作
 */
class SwipeStepExecutor : BaseStepExecutor() {
    
    override suspend fun execute(
        step: AutomationStep,
        accessibilityService: AutoLaunchAccessibilityService
    ): Boolean {
        if (!isStepEnabled(step)) {
            logExecution(step, "Step is disabled, skipping")
            return true
        }
        
        logExecutionStart(step)
        
        return safeExecute(step) {
            val success = when (step.getStepType()) {
                StepType.SWIPE -> performCustomSwipe(step, accessibilityService)
                StepType.SWIPE_UP -> performPredefinedSwipe(step, accessibilityService, SwipeDirection.UP)
                StepType.SWIPE_DOWN -> performPredefinedSwipe(step, accessibilityService, SwipeDirection.DOWN)
                StepType.SWIPE_LEFT -> performPredefinedSwipe(step, accessibilityService, SwipeDirection.LEFT)
                StepType.SWIPE_RIGHT -> performPredefinedSwipe(step, accessibilityService, SwipeDirection.RIGHT)
                else -> {
                    logExecutionFailure(step, "Unsupported swipe type: ${step.getStepType()}")
                    false
                }
            }
            
            if (success) {
                logExecutionSuccess(step)
                // 滑動後短暫延遲，等待UI響應
                delay(300)
            } else {
                logExecutionFailure(step, "Swipe operation failed")
            }
            
            success
        } ?: false
    }
    
    override fun validate(step: AutomationStep): Boolean {
        return when (step.getStepType()) {
            StepType.SWIPE -> {
                // 自定義滑動需要起始和結束座標
                step.swipeStartX != null && step.swipeStartY != null &&
                step.swipeEndX != null && step.swipeEndY != null
            }
            StepType.SWIPE_UP, StepType.SWIPE_DOWN, 
            StepType.SWIPE_LEFT, StepType.SWIPE_RIGHT -> {
                // 預定義滑動不需要額外參數
                true
            }
            else -> false
        }
    }
    
    override fun getDescription(step: AutomationStep): String {
        return when (step.getStepType()) {
            StepType.SWIPE -> {
                "滑動 (${step.swipeStartX},${step.swipeStartY}) → (${step.swipeEndX},${step.swipeEndY})"
            }
            StepType.SWIPE_UP -> "向上滑動"
            StepType.SWIPE_DOWN -> "向下滑動"
            StepType.SWIPE_LEFT -> "向左滑動"
            StepType.SWIPE_RIGHT -> "向右滑動"
            else -> "滑動操作"
        }
    }
    
    /**
     * 執行自定義滑動
     */
    private suspend fun performCustomSwipe(
        step: AutomationStep,
        accessibilityService: AutoLaunchAccessibilityService
    ): Boolean {
        val startX = step.swipeStartX ?: return false
        val startY = step.swipeStartY ?: return false
        val endX = step.swipeEndX ?: return false
        val endY = step.swipeEndY ?: return false
        val duration = step.swipeDuration ?: 500L
        
        logExecution(step, "Performing custom swipe from ($startX,$startY) to ($endX,$endY)")
        
        return performGestureSwipe(
            accessibilityService,
            startX, startY, endX, endY, duration
        )
    }
    
    /**
     * 執行預定義滑動
     */
    private suspend fun performPredefinedSwipe(
        step: AutomationStep,
        accessibilityService: AutoLaunchAccessibilityService,
        direction: SwipeDirection
    ): Boolean {
        val displayMetrics = accessibilityService.resources.displayMetrics
        val centerX = displayMetrics.widthPixels / 2
        val centerY = displayMetrics.heightPixels / 2
        val swipeDistance = minOf(displayMetrics.widthPixels, displayMetrics.heightPixels) / 3
        
        val (startX, startY, endX, endY) = when (direction) {
            SwipeDirection.UP -> {
                val startY = centerY + swipeDistance / 2
                val endY = centerY - swipeDistance / 2
                Quadruple(centerX, startY, centerX, endY)
            }
            SwipeDirection.DOWN -> {
                val startY = centerY - swipeDistance / 2
                val endY = centerY + swipeDistance / 2
                Quadruple(centerX, startY, centerX, endY)
            }
            SwipeDirection.LEFT -> {
                val startX = centerX + swipeDistance / 2
                val endX = centerX - swipeDistance / 2
                Quadruple(startX, centerY, endX, centerY)
            }
            SwipeDirection.RIGHT -> {
                val startX = centerX - swipeDistance / 2
                val endX = centerX + swipeDistance / 2
                Quadruple(startX, centerY, endX, centerY)
            }
        }
        
        logExecution(step, "Performing ${direction.name.lowercase()} swipe from ($startX,$startY) to ($endX,$endY)")
        
        return performGestureSwipe(
            accessibilityService,
            startX, startY, endX, endY, 500L
        )
    }
    
    /**
     * 執行手勢滑動
     */
    private suspend fun performGestureSwipe(
        accessibilityService: AutoLaunchAccessibilityService,
        startX: Int, startY: Int,
        endX: Int, endY: Int,
        duration: Long
    ): Boolean {
        return withContext(Dispatchers.Main) {
            try {
                val path = Path().apply {
                    moveTo(startX.toFloat(), startY.toFloat())
                    lineTo(endX.toFloat(), endY.toFloat())
                }
                
                val gesture = GestureDescription.Builder()
                    .addStroke(GestureDescription.StrokeDescription(path, 0, duration))
                    .build()
                
                val result = CompletableDeferred<Boolean>()
                
                accessibilityService.dispatchGesture(
                    gesture,
                    object : AutoLaunchAccessibilityService.GestureResultCallback() {
                        override fun onCompleted(gestureDescription: GestureDescription?) {
                            result.complete(true)
                        }
                        
                        override fun onCancelled(gestureDescription: GestureDescription?) {
                            result.complete(false)
                        }
                    },
                    null
                )
                
                result.await()
            } catch (e: Exception) {
                false
            }
        }
    }
    
    /**
     * 滑動方向枚舉
     */
    private enum class SwipeDirection {
        UP, DOWN, LEFT, RIGHT
    }
    
    /**
     * 四元組數據類
     */
    private data class Quadruple<T>(val first: T, val second: T, val third: T, val fourth: T)
}
