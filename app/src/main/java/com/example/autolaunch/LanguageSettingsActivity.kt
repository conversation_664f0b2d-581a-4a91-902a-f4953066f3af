package com.example.autolaunch

import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AlertDialog
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.autolaunch.adapters.LanguageAdapter
import com.example.autolaunch.base.BaseActivity
import com.example.autolaunch.databinding.ActivityLanguageSettingsBinding
import com.example.autolaunch.utils.LanguageManager

/**
 * 語言設定頁面
 */
class LanguageSettingsActivity : BaseActivity() {

    private lateinit var binding: ActivityLanguageSettingsBinding
    private lateinit var languageAdapter: LanguageAdapter
    private var hasLanguageChanged = false
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        binding = ActivityLanguageSettingsBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupUI()
        setupRecyclerView()
        setupWindowInsets()
    }
    
    private fun setupUI() {
        // 設定工具列
        binding.toolbar.setNavigationIcon(androidx.appcompat.R.drawable.abc_ic_ab_back_material)
        binding.toolbar.setNavigationOnClickListener {
            finish()
        }
    }
    
    private fun setupRecyclerView() {
        val currentLanguage = LanguageManager.getCurrentLanguage(this)
        
        // 準備語言列表，使用當前語言環境的顯示名稱
        val languages = LanguageManager.supportedLanguages.map { language ->
            LanguageManager.Language(
                code = language.code,
                displayName = LanguageManager.getLanguageDisplayName(this, language.code),
                nativeName = language.nativeName
            )
        }
        
        languageAdapter = LanguageAdapter(
            languages = languages,
            currentLanguage = currentLanguage,
            onLanguageSelected = { languageCode ->
                onLanguageSelected(languageCode)
            }
        )
        
        binding.recyclerViewLanguages.apply {
            layoutManager = LinearLayoutManager(this@LanguageSettingsActivity)
            adapter = languageAdapter
        }
    }
    
    private fun onLanguageSelected(languageCode: String) {
        val currentLanguage = LanguageManager.getCurrentLanguage(this)

        if (LanguageManager.isLanguageChanged(this, languageCode)) {
            // 保存語言設定
            LanguageManager.setLanguage(this, languageCode)
            hasLanguageChanged = true

            // 顯示重新啟動提示
            binding.cardRestartHint.visibility = View.VISIBLE

            // 主動詢問用戶是否要重啟
            showRestartDialog()
        }
    }
    
    private fun showRestartDialog() {
        AlertDialog.Builder(this)
            .setTitle(getString(R.string.restart_app_title))
            .setMessage(getString(R.string.restart_app_message))
            .setPositiveButton(getString(R.string.restart_now)) { _, _ ->
                restartApp()
            }
            .setNegativeButton(getString(R.string.restart_later)) { dialog, _ ->
                dialog.dismiss()
            }
            .setCancelable(false)
            .show()
    }

    private fun setupWindowInsets() {
        ViewCompat.setOnApplyWindowInsetsListener(binding.root) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }
    }
    
    override fun onBackPressed() {
        if (hasLanguageChanged) {
            // 如果語言已變更，重新啟動應用程式
            restartApp()
        } else {
            super.onBackPressed()
        }
    }
    
    private fun restartApp() {
        // 重新啟動應用程式
        val intent = packageManager.getLaunchIntentForPackage(packageName)
        intent?.addFlags(android.content.Intent.FLAG_ACTIVITY_CLEAR_TOP)
        intent?.addFlags(android.content.Intent.FLAG_ACTIVITY_NEW_TASK)
        startActivity(intent)
        finishAffinity()
    }
}
