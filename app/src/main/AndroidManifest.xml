<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- 查詢所有已安裝的應用程式 -->
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES"
        tools:ignore="QueryAllPackagesPermission" />
    
    <!-- 接收開機完成廣播，用於重新註冊排程 -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    
    <!-- 設定精確鬧鐘，用於精準的時間排程 -->
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
    
    <!-- 使用鬧鐘服務 -->
    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
    
    <!-- 前台服務權限 -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />

    <!-- 前台服務數據同步權限 -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />

    <!-- 喚醒鎖定權限，確保定時任務能正常執行 -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    
    <!-- 通知權限 -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    
    <!-- 請求忽略電池優化權限 -->
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />

    <!-- 系統警報窗口權限 (Android 10+) -->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />

    <!-- 自動啟動權限 (已在上方定義，此處移除重複) -->

    <!-- 設備管理員權限 -->
    <uses-permission android:name="android.permission.BIND_DEVICE_ADMIN" />

    <!-- 網路權限（可能需要用於某些功能） -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <!-- 文件存取權限（用於備份功能） -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />

    <!-- Android 13+ 文件權限 -->
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />

    <!-- Google Drive API 權限 -->
    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
    <uses-permission android:name="android.permission.USE_CREDENTIALS" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.AutoLaunch"
        android:hardwareAccelerated="true"
        android:largeHeap="true"
        tools:targetApi="31">
        
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:theme="@style/Theme.AutoLaunch">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        
        <activity
            android:name=".AddEditScheduleActivity"
            android:exported="false"
            android:parentActivityName=".MainActivity"
            android:theme="@style/Theme.AutoLaunch.NoActionBar"
            android:label="" />
        
        <activity
            android:name=".AppSelectorActivity"
            android:exported="false"
            android:parentActivityName=".AddEditScheduleActivity"
            android:theme="@style/Theme.AutoLaunch" />
        
        <activity
            android:name=".TutorialActivity"
            android:exported="false"
            android:parentActivityName=".MainActivity"
            android:theme="@style/Theme.AutoLaunch.NoActionBar" />

        <activity
            android:name=".UpdateHistoryActivity"
            android:exported="false"
            android:parentActivityName=".MainActivity"
            android:theme="@style/Theme.AutoLaunch.NoActionBar" />

        <activity
            android:name=".AboutActivity"
            android:exported="false"
            android:parentActivityName=".MainActivity"
            android:theme="@style/Theme.AutoLaunch.NoActionBar" />

        <activity
            android:name=".SystemLogActivity"
            android:exported="false"
            android:parentActivityName=".MainActivity"
            android:theme="@style/Theme.AutoLaunch.NoActionBar" />

        <activity
            android:name=".QAActivity"
            android:exported="false"
            android:parentActivityName=".MainActivity"
            android:theme="@style/Theme.AutoLaunch.NoActionBar" />

        <activity
            android:name=".BackupRestoreActivity"
            android:exported="false"
            android:parentActivityName=".MainActivity"
            android:theme="@style/Theme.AutoLaunch.NoActionBar" />

        <activity
            android:name=".LanguageSettingsActivity"
            android:exported="false"
            android:parentActivityName=".MainActivity"
            android:theme="@style/Theme.AutoLaunch.NoActionBar" />

        <activity
            android:name=".ThemeSettingsActivity"
            android:exported="false"
            android:parentActivityName=".MainActivity"
            android:theme="@style/Theme.AutoLaunch.NoActionBar" />

        <activity
            android:name=".SettingsActivity"
            android:exported="false"
            android:parentActivityName=".MainActivity"
            android:theme="@style/Theme.AutoLaunch.NoActionBar" />

        <activity
            android:name=".HelpActivity"
            android:exported="false"
            android:parentActivityName=".MainActivity"
            android:theme="@style/Theme.AutoLaunch.NoActionBar" />

        <activity
            android:name=".WelcomeActivity"
            android:exported="false"
            android:theme="@style/Theme.AutoLaunch.NoActionBar"
            android:screenOrientation="portrait" />

        <!-- 開機廣播接收器 -->
        <receiver
            android:name=".receiver.BootReceiver"
            android:enabled="true"
            android:exported="true"
            android:directBootAware="true">
            <intent-filter android:priority="1000">
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.LOCKED_BOOT_COMPLETED" />
                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
                <action android:name="android.intent.action.PACKAGE_REPLACED" />
                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
                <data android:scheme="package" />
            </intent-filter>
        </receiver>

        <!-- APP 啟動廣播接收器 -->
        <receiver
            android:name=".receiver.LaunchReceiver"
            android:enabled="true"
            android:exported="false"
            android:directBootAware="true">
            <intent-filter android:priority="1000">
                <action android:name="com.example.autolaunch.LAUNCH_APP" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </receiver>

        <!-- 排程監控前台服務 -->
        <service
            android:name=".service.ScheduleService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="dataSync" />

        <!-- 無障礙服務 -->
        <service
            android:name=".service.AutoLaunchAccessibilityService"
            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE"
            android:exported="true">
            <intent-filter>
                <action android:name="android.accessibilityservice.AccessibilityService" />
            </intent-filter>
            <meta-data
                android:name="android.accessibilityservice"
                android:resource="@xml/accessibility_service_config" />
        </service>

        <!-- 應用程式卸載廣播接收器 -->
        <receiver
            android:name=".receiver.PackageRemovedReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter android:priority="1000">
                <action android:name="android.intent.action.PACKAGE_REMOVED" />
                <data android:scheme="package" />
            </intent-filter>
        </receiver>

    </application>

</manifest> 