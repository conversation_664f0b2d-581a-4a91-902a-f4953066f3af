<?xml version="1.0" encoding="utf-8"?>
<accessibility-service xmlns:android="http://schemas.android.com/apk/res/android"
    android:accessibilityEventTypes="typeAllMask"
    android:accessibilityFlags="flagDefault|flagRetrieveInteractiveWindows|flagReportViewIds"
    android:accessibilityFeedbackType="feedbackGeneric"
    android:notificationTimeout="100"
    android:canRetrieveWindowContent="true"
    android:canPerformGestures="true"
    android:canRequestTouchExplorationMode="false"
    android:canRequestEnhancedWebAccessibility="false"
    android:canRequestFilterKeyEvents="false"
    android:settingsActivity="com.example.autolaunch.MainActivity"
    android:description="@string/accessibility_service_description" />
