<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">AutoLaunch</string>
    <string name="hello_world">Welcome to AutoLaunch!</string>
    <string name="description">Automatically schedule app launches</string>
    <string name="add_schedule">Add Schedule</string>
    <string name="schedule_list">Schedule List</string>
    <string name="no_schedules">No schedules currently</string>

    <!-- Strings for AddEditScheduleActivity -->
    <string name="title_add_schedule">Add Schedule</string>
    <string name="title_edit_schedule">Edit Schedule</string>
    <string name="error_schedule_not_found">Schedule not found</string>
    <string name="error_load_schedule_failed">Failed to load schedule: %s</string>
    <string name="title_select_execution_time">Select execution time</string>
    <string name="title_select_execution_date">Select execution date</string>
    <string name="date_format_yyyy_mm_dd">%1$d/%2$d/%3$d</string>
    <string name="hint_select_date">Tap to select date</string>
    <string name="hint_select_app">Select App</string>
    <string name="status_no_app_selected">Tap to select</string>
    <string name="success_schedule_added">Schedule added successfully</string>
    <string name="success_schedule_updated">Schedule updated successfully</string>
    <string name="error_save_schedule_failed">Save failed: %s</string>
    <string name="error_select_app_to_launch">Please select an app to launch</string>
    <string name="error_select_date_for_once_mode">Please select execution date for once mode</string>
    <string name="error_select_day_for_weekly_mode">Please select at least one day for weekly mode</string>
    <string name="label_app_name">App name</string>
    <string name="label_package_name">Package name</string>
    <string name="label_execution_time">Execution time</string>
    <string name="label_repeat_mode">Repeat mode</string>
    <string name="label_once">Once</string>
    <string name="label_daily">Daily</string>
    <string name="label_weekly">Weekly</string>
    <string name="label_execution_date">Execution date</string>
    <string name="label_days_of_week">Days of week</string>
    <string name="label_monday">Mon</string>
    <string name="label_tuesday">Tue</string>
    <string name="label_wednesday">Wed</string>
    <string name="label_thursday">Thu</string>
    <string name="label_friday">Fri</string>
    <string name="label_saturday">Sat</string>
    <string name="label_sunday">Sun</string>
    <string name="button_save">Save</string>
    <string name="button_cancel">Cancel</string>
    <string name="delete">Delete</string>
    <string name="cancel">Cancel</string>
    <string name="delete_schedule_title">Delete Schedule</string>
    <string name="delete_schedule_message">Are you sure you want to delete schedule "%s"? This action cannot be undone.</string>
    <string name="time_picker_title">Select time</string>
    <string name="date_picker_title">Select date</string>

    <!-- Strings for ScheduleAdapter -->
    <string name="repeat_mode_once">Once</string>
    <string name="repeat_mode_daily">Daily</string>
    <string name="repeat_mode_weekly">Weekly</string>
    <string name="repeat_mode_monthly">Monthly</string>
    <string name="label_last_execution">Last: %s</string>
    <string name="label_next_execution">Next: %s</string>
    <string name="status_completed">Completed</string>
    <string name="status_disabled">Disabled</string>

    <!-- Strings for PackageRemovedWorker -->
    <string name="unknown_app">Unknown app</string>
    <string name="notification_channel_name_package_removed">App uninstall notification</string>
    <string name="notification_channel_description_package_removed">Notification when scheduled app is uninstalled</string>
    <string name="notification_title_schedule_cleaned">Schedule auto-cleaned</string>
    <string name="notification_text_package_removed">"%1$s" has been uninstalled, %2$d related schedules removed</string>
    <string name="notification_big_text_package_removed">App "%1$s" has been uninstalled.\n\nAutomatically cleaned %2$d related schedules and alarm settings.</string>

    <!-- Strings for ScheduleViewModel -->
    <string name="error_load_schedule_failed_generic">Failed to load schedule: %s</string>
    <string name="success_schedule_toggled">Schedule "%1$s" %2$s</string>
    <string name="status_enabled_true">enabled</string>
    <string name="status_enabled_false">disabled</string>
    <string name="error_update_schedule_status_failed">Failed to update schedule status: %s</string>
    <string name="success_schedule_restored">Restored schedule "%s"</string>
    <string name="error_insert_schedule_failed">Failed to insert schedule: %s</string>
    <string name="success_schedule_deleted">Deleted schedule "%s"</string>
    <string name="error_delete_schedule_failed">Failed to delete schedule: %s</string>
    <string name="error_init_test_data_failed">Failed to initialize test data: %s</string>
    <string name="success_schedule_reordered">Schedule order updated</string>
    <string name="error_reorder_schedule_failed">Failed to update schedule order: %s</string>
    <string name="error_update_sort_order_failed">Failed to update sort order: %s</string>
    <string name="sort_description">Sort</string>
    <string name="sort_mode_enabled">Sort mode enabled, long press items to drag and reorder</string>
    <string name="sort_mode_disabled">Sort mode disabled</string>

    <!-- Strings for AppSelectorAdapter -->
    <string name="label_system_app">System app</string>

    <!-- Q&A Page -->
    <string name="qa_title">FAQ</string>
    <string name="qa_question_1">Schedule not executing automatically?</string>
    <string name="qa_answer_1">Please check the following settings:\n\n1. Ensure "Exact alarm" permission is granted\n2. Add AutoLaunch to battery optimization whitelist\n3. Verify schedule time is set correctly\n4. Check if schedule is enabled\n5. Restart the app\n\nIf the problem persists, check system logs for details.</string>

    <!-- Backup and Import -->
    <string name="backup_restore_title">Backup &amp; Import</string>
    <string name="backup_create_success">Backup created successfully</string>
    <string name="backup_create_failed">Failed to create backup</string>
    <string name="backup_restore_success">Restore successful</string>
    <string name="backup_restore_failed">Restore failed</string>
    <string name="backup_no_schedules">No schedule data to backup</string>
    <string name="backup_file_invalid">Invalid backup file format or corrupted data</string>
    <string name="backup_google_not_signed_in">Please sign in to Google account first</string>
    <string name="backup_google_drive_error">Cannot connect to Google Drive</string>
    <string name="backup_local_files_empty">No local backup files found</string>
    <string name="backup_cloud_files_empty">No cloud backup files found</string>
    <string name="backup_delete_confirm_title">Delete backup file</string>
    <string name="backup_delete_confirm_message">Are you sure you want to delete the backup file? This action cannot be undone.</string>
    <string name="backup_delete_success">Backup file deleted</string>
    <string name="backup_delete_failed">Failed to delete backup file</string>
    <string name="backup_sync_success">Backup sync completed</string>
    <string name="backup_sync_partial">Partial backup successful</string>
    <string name="backup_sync_failed">Backup sync failed</string>
    <string name="backup_import_summary">Imported: %1$d schedules\nSkipped: %2$d\nFailed: %3$d</string>

    <!-- File Picker -->
    <string name="file_picker_title">Select backup file</string>
    <string name="file_picker_error">File selection error</string>
    <string name="file_save_title">Save backup file</string>
    <string name="file_save_error">File save error</string>

    <!-- Google Drive -->
    <string name="google_drive_sign_in">Sign in to Google</string>
    <string name="google_drive_sign_out">Sign out</string>
    <string name="google_drive_signed_in">Signed in: %1$s</string>
    <string name="google_drive_not_signed_in">Not signed in to Google</string>
    <string name="google_drive_sign_in_success">Google sign in successful</string>
    <string name="google_drive_sign_in_failed">Google sign in failed</string>
    <string name="google_drive_sign_out_success">Signed out from Google</string>
    <string name="google_login_prompt_for_backup">Please sign in to your Google account to use the cloud backup feature</string>
    <string name="google_sign_out_confirm_title">Confirm Sign Out</string>
    <string name="google_sign_out_confirm_message">Are you sure you want to sign out of your Google account?</string>
    <string name="restore">Restore</string>

    <!-- Permissions -->
    <string name="permission_storage_title">Storage permission</string>
    <string name="permission_storage_message">Storage permission needed to save and read backup files</string>
    <string name="permission_denied">Permission denied</string>
    <string name="permission_required">Permission required to continue</string>
    <string name="permission_system_alert_window">Display over other apps permission</string>
    <string name="permission_battery_optimization">Battery optimization ignore setting</string>
    <string name="permission_exact_alarm">Exact alarm permission</string>
    <string name="permission_granted">Granted</string>
    <string name="permission_not_granted">Not granted</string>
    <string name="permission_check_message">To ensure AutoLaunch runs properly in the background, please check the following permission settings:</string>
    <string name="permission_recommendation">It is recommended to grant all permissions for the best background execution experience.</string>

    <!-- Language Settings -->
    <string name="language_settings_title">Language Settings</string>
    <string name="language_follow_system">Follow system</string>
    <string name="language_chinese">中文</string>
    <string name="language_english">English</string>
    <string name="language_japanese">日本語</string>
    <string name="language_korean">한국어</string>
    <string name="language_changed_restart">Language changed. Please restart the app to apply changes.</string>
    <string name="language_settings">Language Settings</string>
    <string name="menu_language_settings">Language Settings</string>

    <!-- Settings Activity -->
    <string name="settings_title">Settings</string>
    <string name="settings_notification_section">Notification Settings</string>
    <string name="settings_persistent_notification_title">Persistent Notification</string>
    <string name="settings_persistent_notification_description">Show AutoLaunch icon in notification bar for quick access</string>
    <string name="settings_welcome_card_title">Show Welcome Prompt</string>
    <string name="settings_welcome_card_description">Display permission status and welcome prompt card on homepage</string>

    <!-- Persistent Notification -->
    <string name="notification_channel_persistent_name">Persistent Notification</string>
    <string name="notification_channel_persistent_description">AutoLaunch persistent notification for quick app access</string>
    <string name="persistent_notification_text">Tap to quickly launch AutoLaunch</string>

    <!-- Restart Dialog -->
    <string name="restart_app_title">Restart Application</string>
    <string name="restart_app_message">Language settings have been changed. Would you like to restart the app now to apply the new language settings?</string>
    <string name="restart_now">Restart Now</string>
    <string name="restart_later">Restart Later</string>

    <!-- Add Schedule Page -->
    <string name="schedule_settings">Schedule Settings</string>
    <string name="schedule_type_app">Launch App</string>
    <string name="schedule_type_url">Open URL</string>
    <string name="hint_select_app_description">Please select an app</string>
    <string name="preview_url">Preview URL</string>
    <string name="url_format_valid">URL format is valid</string>
    <string name="time_settings">Time Settings</string>
    <string name="repeat_settings">Repeat Settings</string>
    <string name="select_execution_date">Select Execution Date</string>
    <string name="select_days_of_week">Select Days of Week</string>

    <!-- Days of Week (Full Version) -->
    <string name="day_monday">Mon</string>
    <string name="day_tuesday">Tue</string>
    <string name="day_wednesday">Wed</string>
    <string name="day_thursday">Thu</string>
    <string name="day_friday">Fri</string>
    <string name="day_saturday">Sat</string>
    <string name="day_sunday">Sun</string>

    <!-- Main Page -->
    <string name="menu_description">Menu</string>
    <string name="empty_schedule_description">Empty schedule list</string>
    <string name="no_schedules_yet">No schedules yet</string>
    <string name="welcome_message_permission_needed">Tap to configure</string>
    <string name="welcome_message_ready">Your schedules will run automatically</string>
    <string name="permission_hint_text">Tap to check status</string>
    <string name="app_version_format">Version %s</string>
    <string name="app_version_default">Version 1.0.0</string>
    <string name="error_open_add_schedule">Cannot open add schedule page: %s</string>
    <string name="status_icon_ready">Permissions configured</string>
    <string name="status_icon_warning">Permissions needed</string>
    <string name="empty_schedule_description_detail">You haven\'t created any schedules yet\nTap the button below to create your first schedule!</string>
    <string name="create_first_schedule">Create First Schedule</string>
    <string name="permission_hint_text_detail">Tap to check execution status</string>

    <!-- Schedule Categories -->
    <string name="schedule_category_apps">Applications</string>
    <string name="schedule_category_web">Web Pages</string>

    <!-- Sidebar Menu -->
    <string name="app_running_status">AutoLaunch is running</string>
    <string name="menu_tutorial">Tutorial</string>
    <string name="menu_invite_friends">Invite Friends</string>
    <string name="menu_update_history">Update History</string>
    <string name="menu_qa">Q&amp;A (FAQ)</string>
    <string name="menu_backup_restore">Backup &amp; Import</string>
    <string name="menu_system_log">System Log</string>
    <string name="menu_about">About This App</string>

    <!-- Content Descriptions -->
    <string name="tutorial_description">Tutorial</string>
    <string name="invite_friends_description">Invite Friends</string>
    <string name="update_history_description">Update History</string>
    <string name="qa_description">Q&amp;A (FAQ)</string>
    <string name="backup_restore_description">Backup &amp; Import</string>
    <string name="system_log_description">System Log</string>
    <string name="about_description">About This App</string>
    <string name="theme_settings_description">Theme Settings</string>
    <string name="menu_theme_settings">Theme Settings</string>
    <string name="autolaunch_running">AutoLaunch is running</string>

    <!-- AboutActivity -->
    <string name="app_version_text">Version %s</string>
    <string name="app_version_default_text">Version 1.0.0</string>
    <string name="invite_friends_subject">Recommend a Great Auto-Launch App</string>
    <string name="invite_friends_content">I\'m using AutoLaunch, an app that can automatically launch applications at specified times. It\'s very convenient!\n\nFeatures:\n• Automatically launch any app at scheduled times\n• Support for one-time, daily, and weekly repeats\n• Custom task names\n• Beautiful Material Design interface\n\nI recommend you try it too!</string>
    <string name="invite_friends_chooser_title">Invite Friends to Use AutoLaunch</string>
    <string name="share_function_unavailable">Share function temporarily unavailable</string>

    <!-- BatteryOptimizationDialog -->
    <string name="battery_optimization_title">Battery Optimization Settings</string>
    <string name="battery_optimization_special_reminder">📱 Special Reminder:\n</string>
    <string name="battery_optimization_go_to_settings">Go to Settings</string>
    <string name="battery_optimization_later">Later</string>
    <string name="battery_optimization_completed">Already Configured</string>
    <string name="battery_optimization_success">✅ Configuration complete! Thank you for your cooperation</string>
    <string name="battery_optimization_not_detected">No configuration changes detected. Please check settings if there are issues</string>
    <string name="battery_optimization_cannot_open">Cannot open settings page. Please manually go to battery optimization settings</string>
    <string name="battery_optimization_error">Cannot open settings page: %s</string>

    <!-- MainActivity Permission Messages -->
    <string name="exact_alarm_permission_title">Exact Alarm Permission Required</string>
    <string name="exact_alarm_permission_message">To ensure schedules execute on time, exact alarm permission is required. Click \"Go to Settings\" to grant permission.</string>
    <string name="permission_status_all_good">✅ Excellent! Your AutoLaunch is fully configured\n\n✓ All necessary permissions granted\n✓ Background service is running\n✓ Schedules will execute automatically on time\n\nYou can use all features with confidence!</string>
    <string name="permission_status_service_not_running">⚠️ Permissions configured, but background service not running\n\n✓ All necessary permissions granted\n❌ Background service not started\n\nRecommend restarting the app to ensure service runs normally.</string>
    <string name="permission_status_missing_permissions">❌ Need to grant the following permissions to ensure schedules execute normally:\n\n%s\n\nClick \"Go to Settings\" to grant these permissions one by one.</string>
    <string name="permission_check_details">View Detailed Status</string>
    <string name="permission_go_to_settings">Go to Settings</string>

    <!-- BackupFileListDialog -->
    <string name="backup_file_schedule_count">%d schedules</string>
    <string name="backup_file_cannot_read">Cannot read file information</string>
    <string name="backup_file_unknown_time">Unknown</string>

    <!-- LocalBackupFragment -->
    <string name="load_backup_file_failed">Failed to load backup file: %s</string>
    <string name="create_local_backup_failed">Failed to create local backup: %s</string>
    <string name="restore_from_local_failed">Failed to restore from local file: %s</string>
    <string name="backup_file_deleted">Backup file deleted</string>
    <string name="delete_backup_file_failed">Failed to delete backup file</string>
    <string name="delete_backup_file_error">Failed to delete backup file: %s</string>
    <string name="backup_path_copied">Backup path copied to clipboard</string>
    <string name="copy_path_failed">Failed to copy path</string>

    <!-- CloudBackupFragment -->
    <string name="google_login_cancelled">Google login has been cancelled</string>
    <string name="google_signin_start_failed">Failed to start Google login: %s</string>
    <string name="google_account_signin_success">Google account login successful</string>
    <string name="google_login_failed">Google login failed</string>
    <string name="google_account_signout_success">Signed out from Google account</string>
    <string name="google_signout_failed">Google logout failed</string>
    <string name="google_signout_error">Google logout failed: %s</string>
    <string name="create_cloud_backup_failed">Failed to create cloud backup: %s</string>
    <string name="sync_backup_failed">Backup sync failed: %s</string>
    <string name="no_cloud_backup_files_found">No cloud backup files found</string>
    <string name="load_cloud_backup_failed">Failed to load cloud backup files: %s</string>
    <string name="file_name_info">File: %s</string>
    <string name="cloud_backup_file_deleted">Cloud backup file deleted</string>
    <string name="delete_cloud_backup_failed">Failed to delete cloud backup file: %s</string>
    <string name="last_backup_info_placeholder">Last backup: %s (%d schedules)</string>
    <string name="cloud_backup_description">Manage your backups in Google Drive</string>
    <string name="no_cloud_backups_found">No backups found in your Google Drive</string>
    <string name="google_signed_in_as">Signed in as: %s</string>
    <string name="google_not_signed_in">Not signed in to Google</string>
    <string name="google_sign_out">Sign Out</string>
    <string name="google_sign_in">Sign in with Google</string>

    <!-- DaysOfWeek related strings -->
    <string name="day_name_monday">Monday</string>
    <string name="day_name_tuesday">Tuesday</string>
    <string name="day_name_wednesday">Wednesday</string>
    <string name="day_name_thursday">Thursday</string>
    <string name="day_name_friday">Friday</string>
    <string name="day_name_saturday">Saturday</string>
    <string name="day_name_sunday">Sunday</string>
    <string name="day_short_monday">Mon</string>
    <string name="day_short_tuesday">Tue</string>
    <string name="day_short_wednesday">Wed</string>
    <string name="day_short_thursday">Thu</string>
    <string name="day_short_friday">Fri</string>
    <string name="day_short_saturday">Sat</string>
    <string name="day_short_sunday">Sun</string>
    <string name="days_none">None</string>
    <string name="days_daily">Daily</string>
    <string name="days_weekdays">Weekdays</string>
    <string name="days_weekend">Weekend</string>
    <string name="days_of_week_short_sun">Sun</string>
    <string name="days_of_week_short_mon">Mon</string>

    <!-- Time formatting strings -->
    <string name="time_just_now">Just now</string>
    <string name="time_minutes_ago">%d minutes ago</string>
    <string name="time_hour_ago">1 hour ago</string>
    <string name="time_hours_ago">%d hours ago</string>
    <string name="time_yesterday">Yesterday %s</string>
    <string name="time_day_ago">1 day ago</string>
    <string name="time_days_ago">%d days ago</string>
    <string name="time_week_ago">1 week ago</string>
    <string name="time_weeks_ago">%d weeks ago</string>
    <string name="time_expired">Expired</string>
    <string name="time_minute_later">In 1 minute</string>
    <string name="time_minutes_later">In %d minutes</string>
    <string name="time_hour_later">In 1 hour</string>
    <string name="time_hours_later">In %d hours</string>
    <string name="time_day_later">In 1 day</string>
    <string name="time_days_later">In %d days</string>
    <string name="time_week_later">In 1 week</string>
    <string name="time_weeks_later">In %d weeks</string>

    <!-- Schedule related -->
    <string name="schedule_type_web">Web</string>
    <string name="schedule_unknown_app">Unknown App</string>
    <string name="repeat_mode_once_short">Once</string>
    <string name="repeat_mode_daily_short">Daily</string>
    <string name="repeat_mode_weekly_short">Weekly</string>
    <string name="repeat_mode_monthly_short">Monthly</string>

    <!-- Welcome pages -->
    <string name="welcome_page_1_title">Welcome to AutoLaunch</string>
    <string name="welcome_page_1_description">Smart scheduling, automatic launching\nMake your phone work smarter</string>
    <string name="welcome_page_2_title">Schedule Applications</string>
    <string name="welcome_page_2_description">Set times to automatically launch your favorite apps\nSupports once, daily, weekly repeat modes</string>
    <string name="welcome_page_3_title">Open Web Links</string>
    <string name="welcome_page_3_description">Not just apps, but also timed web page opening\nNews, social media, work platforms at one click</string>
    <string name="welcome_page_4_title">Permission Setup</string>
    <string name="welcome_page_4_description">To ensure schedules work properly\nSome necessary system permissions need to be set</string>
    <string name="welcome_page_5_title">Get Started</string>
    <string name="welcome_page_5_description">Everything is ready!\nNow start creating your first schedule</string>

    <!-- Menu group titles -->
    <string name="menu_group_settings">Settings</string>
    <string name="menu_group_tools">Tools</string>
    <string name="menu_group_help">Help</string>

    <!-- Menu items -->
    <string name="menu_theme_settings_title">Theme Settings</string>
    <string name="menu_language_settings_title">Language Settings</string>
    <string name="menu_notification_settings">Notification Settings</string>
    <string name="menu_notification_settings_description">Notification Settings</string>

    <!-- System log actions -->
    <string name="action_search">Search</string>
    <string name="action_filter">Filter</string>
    <string name="action_export_logs">Export Logs</string>
    <string name="action_clear_logs">Clear Logs</string>
    <string name="action_refresh">Refresh</string>
    <string name="action_clear">Clear</string>

    <!-- Homepage Tab titles -->
    <string name="tab_app">APP</string>
    <string name="tab_web">Web</string>

    <!-- Backup page Tab titles -->
    <string name="tab_local_backup">Local Backup</string>
    <string name="tab_cloud_backup">Cloud Backup</string>

    <!-- Help page Tab titles -->
    <string name="tab_tutorial">Tutorial</string>
    <string name="tab_qa">Q&amp;A</string>

    <!-- Theme settings Tab titles -->
    <string name="tab_light_mode">Light Mode</string>
    <string name="tab_dark_mode">Dark Mode</string>

    <!-- Welcome Activity Strings -->
    <string name="skip">Skip</string>
    <string name="previous">Previous</string>
    <string name="next">Next</string>
    <string name="get_started">Get Started</string>
    <string name="welcome_title">Welcome to AutoLaunch</string>
    <string name="welcome_description">Smart scheduling, automatic launching\nMake your phone work smarter</string>
    <string name="welcome_icon">Welcome page icon</string>
    <string name="settings_show_welcome_guide_title">Show Welcome Guide Again</string>
    <string name="settings_show_welcome_guide_description">Re-enable app onboarding guide</string>
    <string name="show_welcome_guide">Show Welcome Guide</string>

    <!-- SystemLogManager strings -->
    <string name="log_schedule_created">Schedule created: %s</string>
    <string name="log_schedule_updated">Schedule updated: %s</string>
    <string name="log_schedule_deleted">Schedule deleted: %s</string>
    <string name="log_schedule_executed">Schedule executed: %s</string>
    <string name="log_schedule_execution_failed">Schedule execution failed: %s</string>
    <string name="log_system_error">System error: %s</string>
    <string name="log_export_header">AutoLaunch System Log Export</string>
    <string name="log_export_time">Export time: %s</string>
    <string name="log_export_count">Log count: %d</string>
    <string name="language_settings_description">Choose your preferred display language</string>

    <!-- Unsaved changes dialog -->
    <string name="unsaved_changes_title">Unsaved Changes</string>
    <string name="unsaved_changes_message">You have unsaved changes. Are you sure you want to leave?</string>
    <string name="leave">Leave</string>

    <!-- Backup page -->
    <string name="backup_location_title">Auto Backup Location</string>
    <string name="create_local_backup">Create Local Backup</string>
    <string name="no_local_backup_files">No local backup files yet</string>
    <string name="tap_to_create_first_backup">Tap the button above to create your first backup</string>
    <string name="copy_path">Copy Path</string>
    <string name="create_cloud_backup">Create Cloud Backup</string>

    <!-- Additional strings for Add/Edit Schedule page -->
    <string name="hint_enter_schedule_name">Enter schedule name</string>
    <string name="hint_enter_url">Enter URL</string>
    <string name="helper_text_url_example">Example: https://www.google.com</string>
    <string name="content_description_back">Back</string>
    <string name="theme_settings_title">Theme Settings</string>

    <!-- Tutorial page -->
    <string name="tutorial_title">Welcome to AutoLaunch!</string>
    <string name="tutorial_welcome_title">Welcome to AutoLaunch</string>
    <string name="tutorial_welcome_subtitle">Automatically launch apps at your specified time</string>
    <string name="tutorial_step_add_schedule">Add Schedule</string>
    <string name="tutorial_step_add_schedule_desc">Tap the \"+\" button in the bottom right corner of the main page to add your first scheduled task.</string>
    <string name="tutorial_step_select_app">Select App</string>
    <string name="tutorial_step_select_app_desc">Choose the app you want to auto-launch from the installed apps list, and set a custom task name.</string>
    <string name="tutorial_management_functions">📋 Management Features</string>
    <string name="tutorial_management_desc">• Edit Schedule: Tap schedule item to enter edit page\n• Delete Schedule: Swipe left on schedule item to quickly delete\n• Reorder: Long press schedule item to drag and adjust display order\n• Enable/Disable: Use switch to control whether schedule executes</string>
    <string name="tutorial_tips_title">💡 Usage Tips</string>
    <string name="tutorial_learn_more_desc">Learn how to use AutoLaunch</string>
    <string name="tutorial_qa_desc">Frequently Asked Questions</string>
    <string name="tutorial_app_icon_desc">AutoLaunch Icon</string>

    <!-- URL validation related -->
    <string name="url_format_correct">URL format is correct</string>
    <string name="url_format_invalid">URL format is invalid</string>
    <string name="error_enter_valid_url">Please enter a valid URL</string>
    <string name="error_invalid_url_format">URL format is invalid</string>
    <string name="error_cannot_open_url">Cannot open URL: %s</string>
    <string name="error_enter_valid_url_preview">Please enter a valid URL</string>

    <!-- System logs related -->
    <string name="clear_logs_title">Clear Logs</string>
    <string name="clear_logs_message">Are you sure you want to clear all system logs? This action cannot be undone.</string>
    <string name="confirm">Confirm</string>
    <string name="search_logs_title">Search Logs</string>
    <string name="search_keyword_hint">Enter search keyword</string>
    <string name="search">Search</string>
    <string name="clear_search">Clear Search</string>
    <string name="no_matching_logs">No matching logs found</string>
    <string name="found_matching_logs">Found %d matching logs</string>

    <!-- Debug Menu -->
    <string name="debug_menu_title">Debug Menu</string>
    <string name="debug_basic_data_created">Basic test data created</string>
    <string name="debug_edge_cases_created">Edge case test data created</string>
    <string name="debug_stress_data_created">Stress test data created</string>
    <string name="debug_all_data_cleared">All data cleared</string>
    <string name="debug_health_check_complete">System health check completed, please check Logcat</string>

    <!-- Emulator Related -->
    <string name="emulator_detected">Emulator environment detected, automatically adapted</string>

    <!-- Backup preview related -->
    <string name="preview_header_apps">App Schedules (%d)</string>
    <string name="preview_header_urls">URL Schedules (%d)</string>
    <string name="preview_total_count">Total: %d</string>
</resources>
