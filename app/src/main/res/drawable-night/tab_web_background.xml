<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 選中狀態 -->
    <item android:state_selected="true">
        <layer-list>
            <!-- 陰影效果 -->
            <item android:top="1dp" android:left="1dp" android:right="1dp" android:bottom="3dp">
                <shape android:shape="rectangle">
                    <solid android:color="#60000000" />
                    <corners android:radius="14dp" />
                </shape>
            </item>
            <!-- 主要背景 -->
            <item>
                <shape android:shape="rectangle">
                    <gradient
                        android:startColor="#1B5E20"
                        android:endColor="#2E7D32"
                        android:angle="90" />
                    <corners android:radius="12dp" />
                    <stroke
                        android:width="2dp"
                        android:color="#4CAF50" />
                </shape>
            </item>
        </layer-list>
    </item>
    
    <!-- 未選中狀態 -->
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:startColor="?attr/colorSurfaceVariant"
                android:endColor="?attr/colorSurface"
                android:angle="90" />
            <corners android:radius="12dp" />
            <stroke
                android:width="1dp"
                android:color="?attr/colorOutlineVariant" />
        </shape>
    </item>
</selector>
