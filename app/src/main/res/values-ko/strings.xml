<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">AutoLaunch</string>
    <string name="hello_world">AutoLaunch에 오신 것을 환영합니다!</string>
    <string name="description">앱 자동 실행 예약</string>
    <string name="add_schedule">일정 추가</string>
    <string name="schedule_list">일정 목록</string>
    <string name="no_schedules">현재 일정이 없습니다</string>

    <!-- Strings for AddEditScheduleActivity -->
    <string name="title_add_schedule">일정 추가</string>
    <string name="title_edit_schedule">일정 편집</string>
    <string name="error_schedule_not_found">일정을 찾을 수 없습니다</string>
    <string name="error_load_schedule_failed">일정 로드 실패: %s</string>
    <string name="title_select_execution_time">실행 시간 선택</string>
    <string name="title_select_execution_date">실행 날짜 선택</string>
    <string name="date_format_yyyy_mm_dd">%1$d년 %2$d월 %3$d일</string>
    <string name="hint_select_date">탭하여 날짜 선택</string>
    <string name="hint_select_app">앱 선택</string>
    <string name="status_no_app_selected">탭하여 선택</string>
    <string name="success_schedule_added">일정이 추가되었습니다</string>
    <string name="success_schedule_updated">일정이 업데이트되었습니다</string>
    <string name="error_save_schedule_failed">저장 실패: %s</string>
    <string name="error_select_app_to_launch">실행할 앱을 선택해주세요</string>
    <string name="error_select_date_for_once_mode">한 번 실행 모드에서는 실행 날짜를 선택해주세요</string>
    <string name="error_select_day_for_weekly_mode">주간 모드에서는 최소 하나의 요일을 선택해주세요</string>
    <string name="label_app_name">앱 이름</string>
    <string name="label_package_name">패키지 이름</string>
    <string name="label_execution_time">실행 시간</string>
    <string name="label_repeat_mode">반복 모드</string>
    <string name="label_once">한 번</string>
    <string name="label_daily">매일</string>
    <string name="label_weekly">매주</string>
    <string name="label_execution_date">실행 날짜</string>
    <string name="label_days_of_week">요일</string>
    <string name="label_monday">월</string>
    <string name="label_tuesday">화</string>
    <string name="label_wednesday">수</string>
    <string name="label_thursday">목</string>
    <string name="label_friday">금</string>
    <string name="label_saturday">토</string>
    <string name="label_sunday">일</string>
    <string name="button_save">저장</string>
    <string name="button_cancel">취소</string>
    <string name="delete">삭제</string>
    <string name="cancel">취소</string>
    <string name="delete_schedule_title">일정 삭제</string>
    <string name="delete_schedule_message">일정 "%s"을(를) 삭제하시겠습니까? 이 작업은 되돌릴 수 없습니다.</string>
    <string name="time_picker_title">시간 선택</string>
    <string name="date_picker_title">날짜 선택</string>

    <!-- Strings for ScheduleAdapter -->
    <string name="repeat_mode_once">한 번 실행</string>
    <string name="repeat_mode_daily">매일 반복</string>
    <string name="repeat_mode_weekly">매주 반복</string>
    <string name="repeat_mode_monthly">매월 반복</string>
    <string name="label_last_execution">이전: %s</string>
    <string name="label_next_execution">다음: %s</string>
    <string name="status_completed">완료</string>
    <string name="status_disabled">비활성화</string>

    <!-- Strings for PackageRemovedWorker -->
    <string name="unknown_app">알 수 없는 앱</string>
    <string name="notification_channel_name_package_removed">앱 제거 알림</string>
    <string name="notification_channel_description_package_removed">예약된 앱이 제거될 때 알림</string>
    <string name="notification_title_schedule_cleaned">일정이 자동 정리되었습니다</string>
    <string name="notification_text_package_removed">"%1$s"가 제거되어 %2$d개의 관련 일정이 삭제되었습니다</string>
    <string name="notification_big_text_package_removed">앱 "%1$s"가 제거되었습니다.\n\n%2$d개의 관련 일정과 알람 설정을 자동으로 정리했습니다.</string>

    <!-- Strings for ScheduleViewModel -->
    <string name="error_load_schedule_failed_generic">일정 로드 실패: %s</string>
    <string name="success_schedule_toggled">일정 "%1$s"을(를) %2$s</string>
    <string name="status_enabled_true">활성화했습니다</string>
    <string name="status_enabled_false">비활성화했습니다</string>
    <string name="error_update_schedule_status_failed">일정 상태 업데이트 실패: %s</string>
    <string name="success_schedule_restored">일정 "%s"을(를) 복원했습니다</string>
    <string name="error_insert_schedule_failed">일정 삽입 실패: %s</string>
    <string name="success_schedule_deleted">일정 "%s"을(를) 삭제했습니다</string>
    <string name="error_delete_schedule_failed">일정 삭제 실패: %s</string>
    <string name="error_init_test_data_failed">테스트 데이터 초기화 실패: %s</string>
    <string name="success_schedule_reordered">일정 순서가 업데이트되었습니다</string>
    <string name="error_reorder_schedule_failed">일정 순서 업데이트 실패: %s</string>
    <string name="error_update_sort_order_failed">정렬 순서 업데이트 실패: %s</string>
    <string name="sort_description">정렬</string>
    <string name="sort_mode_enabled">정렬 모드가 활성화되었습니다. 항목을 길게 눌러 드래그하여 순서를 변경하세요</string>
    <string name="sort_mode_disabled">정렬 모드가 비활성화되었습니다</string>

    <!-- Strings for AppSelectorAdapter -->
    <string name="label_system_app">시스템 앱</string>

    <!-- Q&A Page -->
    <string name="qa_title">자주 묻는 질문</string>
    <string name="qa_question_1">일정이 자동으로 실행되지 않나요?</string>
    <string name="qa_answer_1">다음 설정을 확인해주세요:\n\n1. "정확한 알람" 권한이 허용되었는지 확인\n2. AutoLaunch를 배터리 최적화 화이트리스트에 추가\n3. 일정 시간이 올바르게 설정되었는지 확인\n4. 일정이 활성화되어 있는지 확인\n5. 앱 재시작\n\n문제가 지속되면 시스템 로그에서 자세한 내용을 확인하세요.</string>

    <!-- Backup and Import -->
    <string name="backup_restore_title">백업 &amp; 가져오기</string>
    <string name="backup_create_success">백업이 생성되었습니다</string>
    <string name="backup_create_failed">백업 생성 실패</string>
    <string name="backup_restore_success">복원 성공</string>
    <string name="backup_restore_failed">복원 실패</string>
    <string name="backup_no_schedules">백업할 일정 데이터가 없습니다</string>
    <string name="backup_file_invalid">잘못된 백업 파일 형식이거나 데이터가 손상됨</string>
    <string name="backup_google_not_signed_in">먼저 Google 계정에 로그인해주세요</string>
    <string name="backup_google_drive_error">Google Drive에 연결할 수 없습니다</string>
    <string name="backup_local_files_empty">로컬 백업 파일을 찾을 수 없습니다</string>
    <string name="backup_cloud_files_empty">클라우드 백업 파일을 찾을 수 없습니다</string>
    <string name="backup_delete_confirm_title">백업 파일 삭제</string>
    <string name="backup_delete_confirm_message">백업 파일을 삭제하시겠습니까? 이 작업은 되돌릴 수 없습니다.</string>
    <string name="backup_delete_success">백업 파일이 삭제되었습니다</string>
    <string name="backup_delete_failed">백업 파일 삭제 실패</string>
    <string name="backup_sync_success">백업 동기화 완료</string>
    <string name="backup_sync_partial">부분 백업 성공</string>
    <string name="backup_sync_failed">백업 동기화 실패</string>
    <string name="backup_import_summary">가져옴: %1$d개 일정\n건너뜀: %2$d개\n실패: %3$d개</string>

    <!-- File Picker -->
    <string name="file_picker_title">백업 파일 선택</string>
    <string name="file_picker_error">파일 선택 오류</string>
    <string name="file_save_title">백업 파일 저장</string>
    <string name="file_save_error">파일 저장 오류</string>

    <!-- Google Drive -->
    <string name="google_drive_sign_in">Google에 로그인</string>
    <string name="google_drive_sign_out">로그아웃</string>
    <string name="google_drive_signed_in">로그인됨: %1$s</string>
    <string name="google_drive_not_signed_in">Google에 로그인하지 않음</string>
    <string name="google_drive_sign_in_success">Google 로그인 성공</string>
    <string name="google_drive_sign_in_failed">Google 로그인 실패</string>
    <string name="google_drive_sign_out_success">Google에서 로그아웃했습니다</string>

    <!-- Permissions -->
    <string name="permission_storage_title">저장소 권한</string>
    <string name="permission_storage_message">백업 파일 저장 및 읽기를 위해 저장소 권한이 필요합니다</string>
    <string name="permission_denied">권한이 거부되었습니다</string>
    <string name="permission_required">계속하려면 권한이 필요합니다</string>
    <string name="permission_system_alert_window">다른 앱 위에 표시 권한</string>
    <string name="permission_battery_optimization">배터리 최적화 무시 설정</string>
    <string name="permission_exact_alarm">정확한 알람 권한</string>
    <string name="permission_granted">허용됨</string>
    <string name="permission_not_granted">허용되지 않음</string>
    <string name="permission_check_message">AutoLaunch가 백그라운드에서 정상적으로 작동하도록 다음 권한 설정을 확인하세요:</string>
    <string name="permission_recommendation">최적의 백그라운드 실행 경험을 위해 모든 권한을 허용하는 것을 권장합니다.</string>

    <!-- Language Settings -->
    <string name="language_settings_title">언어 설정</string>
    <string name="language_follow_system">시스템 따르기</string>
    <string name="language_chinese">中文</string>
    <string name="language_english">English</string>
    <string name="language_japanese">日本語</string>
    <string name="language_korean">한국어</string>
    <string name="language_changed_restart">언어가 변경되었습니다. 변경사항을 적용하려면 앱을 재시작해주세요.</string>
    <string name="language_settings">언어 설정</string>
    <string name="menu_language_settings">언어 설정</string>

    <!-- 재시작 대화상자 -->
    <string name="restart_app_title">애플리케이션 재시작</string>
    <string name="restart_app_message">언어 설정이 변경되었습니다. 새로운 언어 설정을 적용하기 위해 지금 앱을 재시작하시겠습니까?</string>
    <string name="restart_now">지금 재시작</string>
    <string name="restart_later">나중에 재시작</string>

    <!-- 일정 추가 페이지 -->
    <string name="schedule_settings">일정 설정</string>
    <string name="schedule_type_app">앱 실행</string>
    <string name="schedule_type_url">URL 열기</string>
    <string name="hint_select_app_description">앱을 선택해주세요</string>
    <string name="preview_url">URL 미리보기</string>
    <string name="url_format_valid">URL 형식이 올바름</string>
    <string name="time_settings">시간 설정</string>
    <string name="repeat_settings">반복 설정</string>
    <string name="select_execution_date">실행 날짜 선택</string>
    <string name="select_days_of_week">요일 선택</string>

    <!-- 요일 (전체 버전) -->
    <string name="day_monday">월</string>
    <string name="day_tuesday">화</string>
    <string name="day_wednesday">수</string>
    <string name="day_thursday">목</string>
    <string name="day_friday">금</string>
    <string name="day_saturday">토</string>
    <string name="day_sunday">일</string>

    <!-- 메인 페이지 -->
    <string name="menu_description">메뉴</string>
    <string name="empty_schedule_description">빈 일정 목록</string>
    <string name="no_schedules_yet">아직 일정이 없습니다</string>
    <string name="welcome_message_permission_needed">탭하여 권한 설정</string>
    <string name="welcome_message_ready">일정이 자동으로 실행됩니다</string>
    <string name="permission_hint_text">탭하여 상태 확인</string>
    <string name="app_version_format">버전 %s</string>
    <string name="app_version_default">버전 1.0.0</string>
    <string name="error_open_add_schedule">일정 추가 페이지를 열 수 없습니다: %s</string>
    <string name="empty_schedule_description_detail">아직 일정을 만들지 않았습니다\n아래 버튼을 탭하여 첫 번째 일정을 만들어보세요!</string>
    <string name="create_first_schedule">첫 번째 일정 만들기</string>
    <string name="permission_hint_text_detail">탭하여 실행 상태 확인</string>

    <!-- 일정 카테고리 -->
    <string name="schedule_category_apps">애플리케이션</string>
    <string name="schedule_category_web">웹 페이지</string>

    <!-- 사이드바 메뉴 -->
    <string name="app_running_status">AutoLaunch 실행 중</string>
    <string name="menu_tutorial">튜토리얼</string>
    <string name="menu_invite_friends">친구 초대</string>
    <string name="menu_update_history">업데이트 기록</string>
    <string name="menu_qa">Q&amp;A (자주 묻는 질문)</string>
    <string name="menu_backup_restore">백업 &amp; 가져오기</string>
    <string name="menu_system_log">시스템 로그</string>
    <string name="menu_about">이 앱에 대해</string>

    <!-- 콘텐츠 설명 -->
    <string name="tutorial_description">튜토리얼</string>
    <string name="invite_friends_description">친구 초대</string>
    <string name="update_history_description">업데이트 기록</string>
    <string name="qa_description">Q&amp;A (자주 묻는 질문)</string>
    <string name="backup_restore_description">백업 &amp; 가져오기</string>
    <string name="system_log_description">시스템 로그</string>
    <string name="about_description">이 앱에 대해</string>
    <string name="theme_settings_description">테마 설정</string>
    <string name="menu_theme_settings">테마 설정</string>
    <string name="autolaunch_running">AutoLaunch 실행 중</string>

    <!-- Settings Activity -->
    <string name="settings_title">설정</string>
    <string name="settings_notification_section">알림 설정</string>
    <string name="settings_persistent_notification_title">상주 알림</string>
    <string name="settings_persistent_notification_description">빠른 접근을 위해 알림 표시줄에 AutoLaunch 아이콘 표시</string>
    <string name="settings_welcome_card_title">환영 프롬프트 표시</string>
    <string name="settings_welcome_card_description">홈페이지에 권한 상태와 환영 프롬프트 카드 표시</string>

    <!-- Persistent Notification -->
    <string name="notification_channel_persistent_name">상주 알림</string>
    <string name="notification_channel_persistent_description">빠른 앱 접근을 위한 AutoLaunch 상주 알림</string>
    <string name="persistent_notification_text">탭하여 AutoLaunch 빠르게 실행</string>

    <!-- AboutActivity -->
    <string name="app_version_text">버전 %s</string>
    <string name="app_version_default_text">버전 1.0.0</string>
    <string name="invite_friends_subject">유용한 자동 실행 앱 추천</string>
    <string name="invite_friends_content">AutoLaunch라는 앱을 사용하고 있습니다. 지정된 시간에 앱을 자동으로 실행할 수 있어서 매우 편리합니다!\n\n기능:\n• 원하는 앱을 시간 지정하여 자동 실행\n• 한 번, 매일, 매주 반복 지원\n• 사용자 정의 작업 이름\n• 아름다운 Material Design 인터페이스\n\n당신도 꼭 사용해보세요!</string>
    <string name="invite_friends_chooser_title">친구에게 AutoLaunch 추천하기</string>
    <string name="share_function_unavailable">공유 기능을 일시적으로 사용할 수 없습니다</string>

    <!-- BatteryOptimizationDialog -->
    <string name="battery_optimization_title">배터리 최적화 설정</string>
    <string name="battery_optimization_special_reminder">📱 특별 알림:\n</string>
    <string name="battery_optimization_go_to_settings">설정으로 이동</string>
    <string name="battery_optimization_later">나중에</string>
    <string name="battery_optimization_completed">설정 완료</string>
    <string name="battery_optimization_success">✅ 설정 완료! 협조해 주셔서 감사합니다</string>
    <string name="battery_optimization_not_detected">설정 변경이 감지되지 않았습니다. 문제가 있으면 설정을 확인해주세요</string>
    <string name="battery_optimization_cannot_open">설정 페이지를 열 수 없습니다. 수동으로 배터리 최적화 설정으로 이동해주세요</string>
    <string name="battery_optimization_error">설정 페이지를 열 수 없습니다: %s</string>

    <!-- MainActivity Permission Messages -->
    <string name="exact_alarm_permission_title">정확한 알람 권한 필요</string>
    <string name="exact_alarm_permission_message">일정이 정시에 실행되도록 하려면 정확한 알람 권한이 필요합니다. "설정으로 이동"을 클릭하여 권한을 허용해주세요.</string>
    <string name="permission_status_all_good">✅ 훌륭합니다! AutoLaunch가 완전히 설정되었습니다\n\n✓ 모든 필요한 권한이 허용됨\n✓ 백그라운드 서비스가 실행 중\n✓ 일정이 정시에 자동 실행됩니다\n\n모든 기능을 안심하고 사용하실 수 있습니다!</string>
    <string name="permission_status_service_not_running">⚠️ 권한 설정은 완료되었지만 백그라운드 서비스가 실행되지 않습니다\n\n✓ 모든 필요한 권한이 허용됨\n❌ 백그라운드 서비스가 시작되지 않음\n\n서비스가 정상적으로 실행되도록 앱을 재시작하는 것을 권장합니다.</string>
    <string name="permission_status_missing_permissions">❌ 일정이 정상적으로 실행되도록 다음 권한을 허용해야 합니다:\n\n%s\n\n"설정으로 이동"을 클릭하여 이러한 권한을 하나씩 허용해주세요.</string>
    <string name="permission_check_details">자세한 상태 보기</string>
    <string name="permission_go_to_settings">설정으로 이동</string>

    <!-- BackupFileListDialog -->
    <string name="backup_file_schedule_count">%d개 일정</string>
    <string name="backup_file_cannot_read">파일 정보를 읽을 수 없습니다</string>
    <string name="backup_file_unknown_time">알 수 없음</string>

    <!-- LocalBackupFragment -->
    <string name="load_backup_file_failed">백업 파일 로드 실패: %s</string>
    <string name="restore_from_local_failed">로컬 파일에서 복원 실패: %s</string>
    <string name="backup_file_deleted">백업 파일이 삭제되었습니다</string>
    <string name="delete_backup_file_failed">백업 파일 삭제 실패</string>
    <string name="delete_backup_file_error">백업 파일 삭제 실패: %s</string>
    <string name="create_local_backup_failed">로컬 백업 생성 실패: %s</string>
    <string name="backup_path_copied">백업 경로가 클립보드에 복사되었습니다</string>
    <string name="copy_path_failed">경로 복사 실패</string>

    <!-- CloudBackupFragment -->
    <string name="google_login_failed">Google 로그인 실패</string>
    <string name="google_login_cancelled">Google 로그인이 취소되었습니다</string>
    <string name="google_signin_start_failed">Google 로그인 시작 실패: %s</string>
    <string name="google_account_signin_success">Google 계정 로그인 성공</string>
    <string name="google_signin_failed">Google 로그인 실패: %s</string>
    <string name="google_account_signout_success">Google 계정에서 로그아웃했습니다</string>
    <string name="google_signout_failed">Google 로그아웃 실패</string>
    <string name="google_signout_error">Google 로그아웃 실패: %s</string>
    <string name="create_cloud_backup_failed">클라우드 백업 생성 실패: %s</string>
    <string name="sync_backup_failed">백업 동기화 실패: %s</string>
    <string name="no_cloud_backup_files_found">클라우드 백업 파일을 찾을 수 없습니다</string>
    <string name="load_cloud_backup_failed">클라우드 백업 파일 로드 실패: %s</string>
    <string name="file_name_info">파일: %s</string>
    <string name="cloud_backup_file_deleted">클라우드 백업 파일이 삭제되었습니다</string>
    <string name="delete_cloud_backup_failed">클라우드 백업 파일 삭제 실패: %s</string>
    <string name="last_backup_info_placeholder">마지막 백업: %s (%d개 일정)</string>
    <string name="cloud_backup_description">Google Drive에서 백업 관리</string>
    <string name="no_cloud_backups_found">Google Drive에서 백업을 찾을 수 없습니다</string>
    <string name="google_signed_in_as">로그인됨: %s</string>
    <string name="google_not_signed_in">Google에 로그인되지 않음</string>
    <string name="google_sign_out">로그아웃</string>
    <string name="google_sign_in">Google로 로그인</string>
    <string name="google_login_prompt_for_backup">클라우드 백업 기능을 사용하려면 Google 계정에 로그인하세요</string>

    <!-- DaysOfWeek related strings -->
    <string name="day_name_monday">월요일</string>
    <string name="day_name_tuesday">화요일</string>
    <string name="day_name_wednesday">수요일</string>
    <string name="day_name_thursday">목요일</string>
    <string name="day_name_friday">금요일</string>
    <string name="day_name_saturday">토요일</string>
    <string name="day_name_sunday">일요일</string>
    <string name="day_short_monday">월</string>
    <string name="day_short_tuesday">화</string>
    <string name="day_short_wednesday">수</string>
    <string name="day_short_thursday">목</string>
    <string name="day_short_friday">금</string>
    <string name="day_short_saturday">토</string>
    <string name="day_short_sunday">일</string>
    <string name="days_none">없음</string>
    <string name="days_daily">매일</string>
    <string name="days_weekdays">평일</string>
    <string name="days_weekend">주말</string>
    <string name="days_of_week_short_sun">일</string>
    <string name="days_of_week_short_mon">월</string>

    <!-- Time formatting strings -->
    <string name="time_just_now">방금</string>
    <string name="time_minutes_ago">%d분 전</string>
    <string name="time_hour_ago">1시간 전</string>
    <string name="time_hours_ago">%d시간 전</string>
    <string name="time_yesterday">어제 %s</string>
    <string name="time_day_ago">1일 전</string>
    <string name="time_days_ago">%d일 전</string>
    <string name="time_week_ago">1주 전</string>
    <string name="time_weeks_ago">%d주 전</string>
    <string name="time_expired">만료됨</string>
    <string name="time_minute_later">1분 후</string>
    <string name="time_minutes_later">%d분 후</string>
    <string name="time_hour_later">1시간 후</string>
    <string name="time_hours_later">%d시간 후</string>
    <string name="time_day_later">1일 후</string>
    <string name="time_days_later">%d일 후</string>
    <string name="time_week_later">1주 후</string>
    <string name="time_weeks_later">%d주 후</string>

    <!-- Schedule related -->
    <string name="schedule_type_web">웹</string>
    <string name="schedule_unknown_app">알 수 없는 앱</string>
    <string name="repeat_mode_once_short">한 번</string>
    <string name="repeat_mode_daily_short">매일</string>
    <string name="repeat_mode_weekly_short">매주</string>
    <string name="repeat_mode_monthly_short">매월</string>

    <!-- Welcome pages -->
    <string name="welcome_page_1_title">AutoLaunch에 오신 것을 환영합니다</string>
    <string name="welcome_page_1_description">스마트 스케줄링, 자동 실행\n휴대폰을 더 똑똑하게 작동시키세요</string>
    <string name="welcome_page_2_title">애플리케이션 스케줄</string>
    <string name="welcome_page_2_description">좋아하는 앱을 자동으로 실행할 시간을 설정하세요\n한 번, 매일, 매주 반복 모드를 지원합니다</string>
    <string name="welcome_page_3_title">웹 링크 열기</string>
    <string name="welcome_page_3_description">앱뿐만 아니라 시간 지정 웹 페이지 열기도 가능\n뉴스, 소셜 미디어, 작업 플랫폼에 원클릭 액세스</string>
    <string name="welcome_page_4_title">권한 설정</string>
    <string name="welcome_page_4_description">스케줄이 정상적으로 작동하도록\n몇 가지 필요한 시스템 권한을 설정해야 합니다</string>
    <string name="welcome_page_5_title">시작하기</string>
    <string name="welcome_page_5_description">모든 준비가 완료되었습니다!\n이제 첫 번째 스케줄을 만들어 보세요</string>

    <!-- Menu group titles -->
    <string name="menu_group_settings">설정</string>
    <string name="menu_group_tools">도구</string>
    <string name="menu_group_help">도움말</string>

    <!-- Menu items -->
    <string name="menu_theme_settings_title">테마 설정</string>
    <string name="menu_language_settings_title">언어 설정</string>
    <string name="menu_notification_settings">알림 설정</string>
    <string name="menu_notification_settings_description">알림 설정</string>

    <!-- System log actions -->
    <string name="action_search">검색</string>
    <string name="action_filter">필터</string>
    <string name="action_export_logs">로그 내보내기</string>
    <string name="action_clear_logs">로그 지우기</string>

    <!-- Homepage Tab titles -->
    <string name="tab_app">APP</string>
    <string name="tab_web">웹</string>

    <!-- Backup page Tab titles -->
    <string name="tab_local_backup">로컬 백업</string>
    <string name="tab_cloud_backup">클라우드 백업</string>

    <!-- Help page Tab titles -->
    <string name="tab_tutorial">사용법</string>
    <string name="tab_qa">Q&amp;A</string>

    <!-- Theme settings Tab titles -->
    <string name="tab_light_mode">라이트 모드</string>
    <string name="tab_dark_mode">다크 모드</string>

    <!-- Welcome Activity Strings -->
    <string name="skip">건너뛰기</string>
    <string name="previous">이전</string>
    <string name="next">다음</string>
    <string name="get_started">시작하기</string>
    <string name="welcome_title">AutoLaunch에 오신 것을 환영합니다</string>
    <string name="welcome_description">스마트 스케줄링, 자동 실행\n휴대폰을 더 스마트하게 만드세요</string>
    <string name="welcome_icon">환영 페이지 아이콘</string>
    <string name="settings_show_welcome_guide_title">환영 가이드 다시 표시</string>
    <string name="settings_show_welcome_guide_description">앱 온보딩 가이드 다시 활성화</string>
    <string name="show_welcome_guide">환영 가이드 표시</string>

    <!-- SystemLogManager 문자열 -->
    <string name="log_schedule_created">일정 생성: %s</string>
    <string name="log_schedule_updated">일정 수정: %s</string>
    <string name="log_schedule_deleted">일정 삭제: %s</string>
    <string name="log_schedule_executed">일정 실행: %s</string>
    <string name="log_schedule_execution_failed">일정 실행 실패: %s</string>
    <string name="log_system_error">시스템 오류: %s</string>
    <string name="log_export_header">AutoLaunch 시스템 로그 내보내기</string>
    <string name="log_export_time">내보내기 시간: %s</string>
    <string name="log_export_count">로그 수: %d</string>
    <string name="language_settings_description">원하는 표시 언어를 선택하세요</string>

    <!-- 저장되지 않은 변경사항 대화상자 -->
    <string name="unsaved_changes_title">저장되지 않은 변경사항</string>
    <string name="unsaved_changes_message">저장되지 않은 변경사항이 있습니다. 정말 나가시겠습니까?</string>
    <string name="leave">나가기</string>

    <!-- 백업 페이지 -->
    <string name="backup_location_title">자동 백업 위치</string>
    <string name="create_local_backup">로컬 백업 생성</string>
    <string name="no_local_backup_files">아직 로컬 백업 파일이 없습니다</string>
    <string name="tap_to_create_first_backup">위의 버튼을 탭하여 첫 번째 백업을 만드세요</string>
    <string name="copy_path">경로 복사</string>

    <!-- 일정 추가/편집 페이지 추가 문자열 -->
    <string name="hint_enter_schedule_name">일정 이름 입력</string>
    <string name="hint_enter_url">URL 입력</string>
    <string name="helper_text_url_example">예: https://www.google.com</string>
    <string name="content_description_back">뒤로</string>
    <string name="theme_settings_title">테마 설정</string>

    <!-- 튜토리얼 페이지 -->
    <string name="tutorial_title">튜토리얼</string>
    <string name="tutorial_welcome_title">AutoLaunch에 오신 것을 환영합니다</string>
    <string name="tutorial_welcome_subtitle">지정된 시간에 앱을 자동으로 실행하세요</string>
    <string name="tutorial_step_add_schedule">일정 추가</string>
    <string name="tutorial_step_add_schedule_desc">메인 페이지 오른쪽 하단의 "+" 버튼을 눌러 첫 번째 일정 작업을 추가하세요.</string>
    <string name="tutorial_step_select_app">앱 선택</string>
    <string name="tutorial_step_select_app_desc">설치된 앱 목록에서 자동 실행할 앱을 선택하고 사용자 정의 작업 이름을 설정할 수 있습니다.</string>
    <string name="tutorial_management_functions">📋 관리 기능</string>
    <string name="tutorial_management_desc">• 일정 편집: 일정 항목을 눌러 편집 페이지로 이동\n• 일정 삭제: 일정 항목을 왼쪽으로 스와이프하여 빠르게 삭제\n• 순서 조정: 일정 항목을 길게 눌러 표시 순서 조정\n• 활성화/비활성화: 스위치로 일정 실행 제어</string>
    <string name="tutorial_tips_title">💡 사용 팁</string>
    <string name="tutorial_learn_more_desc">AutoLaunch 사용법 알아보기</string>
    <string name="tutorial_qa_desc">자주 묻는 질문</string>
    <string name="tutorial_app_icon_desc">AutoLaunch 아이콘</string>

    <!-- URL 검증 관련 -->
    <string name="url_format_correct">URL 형식이 올바릅니다</string>
    <string name="url_format_invalid">URL 형식이 잘못되었습니다</string>
    <string name="error_enter_valid_url">유효한 URL을 입력하세요</string>
    <string name="error_invalid_url_format">URL 형식이 잘못되었습니다</string>
    <string name="error_cannot_open_url">URL을 열 수 없습니다: %s</string>
    <string name="error_enter_valid_url_preview">유효한 URL을 입력하세요</string>

    <!-- 시스템 로그 관련 -->
    <string name="clear_logs_title">로그 지우기</string>
    <string name="clear_logs_message">모든 시스템 로그를 지우시겠습니까? 이 작업은 되돌릴 수 없습니다.</string>
    <string name="confirm">확인</string>
    <string name="search_logs_title">로그 검색</string>
    <string name="search_keyword_hint">검색 키워드 입력</string>
    <string name="search">검색</string>
    <string name="clear_search">검색 지우기</string>
    <string name="no_matching_logs">일치하는 로그를 찾을 수 없습니다</string>
    <string name="found_matching_logs">%d개의 일치하는 로그를 찾았습니다</string>

    <!-- 디버그 메뉴 -->
    <string name="debug_menu_title">디버그 메뉴</string>
    <string name="debug_basic_data_created">기본 테스트 데이터가 생성되었습니다</string>
    <string name="debug_edge_cases_created">엣지 케이스 테스트 데이터가 생성되었습니다</string>
    <string name="debug_stress_data_created">스트레스 테스트 데이터가 생성되었습니다</string>
    <string name="debug_all_data_cleared">모든 데이터가 지워졌습니다</string>
    <string name="debug_health_check_complete">시스템 상태 검사 완료, Logcat을 확인하세요</string>

    <!-- 에뮬레이터 관련 -->
    <string name="emulator_detected">에뮬레이터 환경 감지, 자동으로 적응했습니다</string>

    <!-- New strings for the code block -->
    <string name="create_cloud_backup">클라우드 백업 생성</string>
    <string name="google_sign_out_confirm_message">Google 계정에서 로그아웃하시겠습니까?</string>
    <string name="restore">복원</string>
    <string name="preview_header_apps">앱 일정 (%d)</string>
    <string name="preview_header_urls">URL 일정 (%d)</string>
    <string name="preview_total_count">총 %d개</string>
</resources>
