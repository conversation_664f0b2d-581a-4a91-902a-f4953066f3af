<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 陰影效果 -->
    <item android:top="2dp" android:left="2dp" android:right="2dp" android:bottom="2dp">
        <shape android:shape="rectangle">
            <solid android:color="#20000000" />
            <corners android:radius="18dp" />
        </shape>
    </item>

    <!-- 主要背景 -->
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:startColor="?attr/colorSurfaceVariant"
                android:endColor="?attr/colorSurface"
                android:angle="90" />
            <corners android:radius="16dp" />
            <stroke
                android:width="1dp"
                android:color="?attr/colorOutlineVariant" />
        </shape>
    </item>
</layer-list>
