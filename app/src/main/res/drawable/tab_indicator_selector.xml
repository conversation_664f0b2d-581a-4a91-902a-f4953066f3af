<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 選中狀態 -->
    <item android:state_selected="true">
        <shape android:shape="oval">
            <solid android:color="?attr/colorPrimary" />
            <size android:width="10dp" android:height="10dp" />
        </shape>
    </item>

    <!-- 未選中狀態 -->
    <item>
        <shape android:shape="oval">
            <solid android:color="?attr/colorOnSurfaceVariant" />
            <size android:width="10dp" android:height="10dp" />
        </shape>
    </item>
</selector>
