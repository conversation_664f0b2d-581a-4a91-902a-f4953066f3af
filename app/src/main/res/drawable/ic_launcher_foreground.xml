<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="108dp"
    android:height="108dp"
    android:viewportWidth="108"
    android:viewportHeight="108">
    
    <group android:scaleX="0.8"
           android:scaleY="0.8"
           android:pivotX="54"
           android:pivotY="54">
        
        <!-- 時鐘外圓環 -->
        <path
            android:fillColor="@android:color/transparent"
            android:strokeColor="#FFFFFF"
            android:strokeWidth="4"
            android:pathData="M54,20C35.22,20 20,35.22 20,54s15.22,34 34,34 34,-15.22 34,-34S72.78,20 54,20z" />
        
        <!-- 時鐘刻度 -->
        <path
            android:fillColor="#FFFFFF"
            android:pathData="M54,24 L54,30" />
        <path
            android:fillColor="#FFFFFF"
            android:pathData="M54,78 L54,84" />
        <path
            android:fillColor="#FFFFFF"
            android:pathData="M84,54 L78,54" />
        <path
            android:fillColor="#FFFFFF"
            android:pathData="M30,54 L24,54" />
        
        <!-- 時針 (指向10點) -->
        <path
            android:fillColor="#FFFFFF"
            android:strokeColor="#FFFFFF"
            android:strokeWidth="3"
            android:strokeLineCap="round"
            android:pathData="M54,54 L45,40" />
        
        <!-- 分針 (指向2點) -->
        <path
            android:fillColor="#FFFFFF"
            android:strokeColor="#FFFFFF"
            android:strokeWidth="2"
            android:strokeLineCap="round"
            android:pathData="M54,54 L68,42" />
        
        <!-- 中心點 -->
        <path
            android:fillColor="#FFFFFF"
            android:pathData="M54,54m-3,0a3,3 0,1 1,6 0a3,3 0,1 1,-6 0" />
        
        <!-- 啟動箭頭 (右上角) -->
        <path
            android:fillColor="#4CAF50"
            android:pathData="M75,25 L85,30 L80,35 L75,30 Z" />
        <path
            android:fillColor="#4CAF50"
            android:pathData="M75,30 L82,30 L82,32 L75,32 Z" />
            
    </group>
    
</vector> 