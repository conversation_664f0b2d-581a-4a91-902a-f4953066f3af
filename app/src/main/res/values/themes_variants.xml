<?xml version="1.0" encoding="utf-8"?>
<resources>
    
    <!-- Light Classic Theme - 經典淺色主題 (重新命名現有主題) -->
    <style name="Theme_AutoLaunch_Light_Classic" parent="Theme.Material3.DayNight">
        <item name="colorPrimary">@color/md_theme_light_primary</item>
        <item name="colorOnPrimary">@color/md_theme_light_onPrimary</item>
        <item name="colorSecondary">@color/md_theme_light_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_light_onSecondary</item>
        <item name="colorSurface">@color/md_theme_light_surface</item>
        <item name="colorOnSurface">@color/md_theme_light_onSurface</item>
        <item name="colorSurfaceVariant">@color/md_theme_light_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_light_onSurfaceVariant</item>
        <item name="colorPrimaryContainer">@color/md_theme_light_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_light_onPrimaryContainer</item>
        <item name="android:colorBackground">@color/md_theme_light_background</item>
        <item name="colorOnBackground">@color/md_theme_light_onBackground</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="actionBarSize">48dp</item>
    </style>
    
    <!-- Light Warm Theme - 溫暖淺色主題 -->
    <style name="Theme_AutoLaunch_Light_Warm" parent="Theme.Material3.DayNight">
        <item name="colorPrimary">@color/md_theme_light_warm_primary</item>
        <item name="colorOnPrimary">@color/md_theme_light_warm_onPrimary</item>
        <item name="colorSecondary">@color/md_theme_light_warm_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_light_warm_onSecondary</item>
        <item name="colorSurface">@color/md_theme_light_warm_surface</item>
        <item name="colorOnSurface">@color/md_theme_light_warm_onSurface</item>
        <item name="colorSurfaceVariant">@color/md_theme_light_warm_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_light_warm_onSurfaceVariant</item>
        <item name="colorPrimaryContainer">@color/md_theme_light_warm_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_light_warm_onPrimaryContainer</item>
        <item name="android:colorBackground">@color/md_theme_light_warm_background</item>
        <item name="colorOnBackground">@color/md_theme_light_warm_onBackground</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="actionBarSize">48dp</item>
    </style>
    
    <!-- Light Cool Theme - 清涼淺色主題 -->
    <style name="Theme_AutoLaunch_Light_Cool" parent="Theme.Material3.DayNight">
        <item name="colorPrimary">@color/md_theme_light_cool_primary</item>
        <item name="colorOnPrimary">@color/md_theme_light_cool_onPrimary</item>
        <item name="colorSecondary">@color/md_theme_light_cool_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_light_cool_onSecondary</item>
        <item name="colorSurface">@color/md_theme_light_cool_surface</item>
        <item name="colorOnSurface">@color/md_theme_light_cool_onSurface</item>
        <item name="colorSurfaceVariant">@color/md_theme_light_cool_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_light_cool_onSurfaceVariant</item>
        <item name="colorPrimaryContainer">@color/md_theme_light_cool_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_light_cool_onPrimaryContainer</item>
        <item name="android:colorBackground">@color/md_theme_light_cool_background</item>
        <item name="colorOnBackground">@color/md_theme_light_cool_onBackground</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="actionBarSize">48dp</item>
    </style>

    <!-- Light Pink Theme - 粉紅淺色主題 -->
    <style name="Theme_AutoLaunch_Light_Pink" parent="Theme.Material3.DayNight">
        <item name="colorPrimary">@color/md_theme_light_pink_primary</item>
        <item name="colorOnPrimary">@color/md_theme_light_pink_onPrimary</item>
        <item name="colorSecondary">@color/md_theme_light_pink_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_light_pink_onSecondary</item>
        <item name="colorSurface">@color/md_theme_light_pink_surface</item>
        <item name="colorOnSurface">@color/md_theme_light_pink_onSurface</item>
        <item name="colorSurfaceVariant">@color/md_theme_light_pink_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_light_pink_onSurfaceVariant</item>
        <item name="colorPrimaryContainer">@color/md_theme_light_pink_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_light_pink_onPrimaryContainer</item>
        <item name="android:colorBackground">@color/md_theme_light_pink_background</item>
        <item name="colorOnBackground">@color/md_theme_light_pink_onBackground</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="actionBarSize">48dp</item>
    </style>

    <!-- Light Yellow Theme - 陽光淺色主題 -->
    <style name="Theme_AutoLaunch_Light_Yellow" parent="Theme.Material3.DayNight">
        <item name="colorPrimary">@color/md_theme_light_yellow_primary</item>
        <item name="colorOnPrimary">@color/md_theme_light_yellow_onPrimary</item>
        <item name="colorSecondary">@color/md_theme_light_yellow_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_light_yellow_onSecondary</item>
        <item name="colorSurface">@color/md_theme_light_yellow_surface</item>
        <item name="colorOnSurface">@color/md_theme_light_yellow_onSurface</item>
        <item name="colorSurfaceVariant">@color/md_theme_light_yellow_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_light_yellow_onSurfaceVariant</item>
        <item name="colorPrimaryContainer">@color/md_theme_light_yellow_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_light_yellow_onPrimaryContainer</item>
        <item name="android:colorBackground">@color/md_theme_light_yellow_background</item>
        <item name="colorOnBackground">@color/md_theme_light_yellow_onBackground</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="actionBarSize">48dp</item>
    </style>

    <!-- Light Mint Theme - 薄荷淺色主題 -->
    <style name="Theme_AutoLaunch_Light_Mint" parent="Theme.Material3.DayNight">
        <item name="colorPrimary">@color/md_theme_light_mint_primary</item>
        <item name="colorOnPrimary">@color/md_theme_light_mint_onPrimary</item>
        <item name="colorSecondary">@color/md_theme_light_mint_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_light_mint_onSecondary</item>
        <item name="colorSurface">@color/md_theme_light_mint_surface</item>
        <item name="colorOnSurface">@color/md_theme_light_mint_onSurface</item>
        <item name="colorSurfaceVariant">@color/md_theme_light_mint_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_light_mint_onSurfaceVariant</item>
        <item name="colorPrimaryContainer">@color/md_theme_light_mint_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_light_mint_onPrimaryContainer</item>
        <item name="android:colorBackground">@color/md_theme_light_mint_background</item>
        <item name="colorOnBackground">@color/md_theme_light_mint_onBackground</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="actionBarSize">48dp</item>
    </style>

    <!-- Light Lavender Theme - 薰衣草淺色主題 -->
    <style name="Theme_AutoLaunch_Light_Lavender" parent="Theme.Material3.DayNight">
        <item name="colorPrimary">@color/md_theme_light_lavender_primary</item>
        <item name="colorOnPrimary">@color/md_theme_light_lavender_onPrimary</item>
        <item name="colorSecondary">@color/md_theme_light_lavender_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_light_lavender_onSecondary</item>
        <item name="colorSurface">@color/md_theme_light_lavender_surface</item>
        <item name="colorOnSurface">@color/md_theme_light_lavender_onSurface</item>
        <item name="colorSurfaceVariant">@color/md_theme_light_lavender_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_light_lavender_onSurfaceVariant</item>
        <item name="colorPrimaryContainer">@color/md_theme_light_lavender_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_light_lavender_onPrimaryContainer</item>
        <item name="android:colorBackground">@color/md_theme_light_lavender_background</item>
        <item name="colorOnBackground">@color/md_theme_light_lavender_onBackground</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="actionBarSize">48dp</item>
    </style>

    <!-- Dark Classic Theme - 經典深色主題 (重新命名現有主題) -->
    <style name="Theme_AutoLaunch_Dark_Classic" parent="Theme.Material3.DayNight">
        <item name="colorPrimary">@color/md_theme_dark_primary</item>
        <item name="colorOnPrimary">@color/md_theme_dark_onPrimary</item>
        <item name="colorSecondary">@color/md_theme_dark_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_dark_onSecondary</item>
        <item name="colorSurface">@color/md_theme_dark_surface</item>
        <item name="colorOnSurface">@color/md_theme_dark_onSurface</item>
        <item name="colorSurfaceVariant">@color/md_theme_dark_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_dark_onSurfaceVariant</item>
        <item name="colorPrimaryContainer">@color/md_theme_dark_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_dark_onPrimaryContainer</item>
        <item name="android:colorBackground">@color/md_theme_dark_background</item>
        <item name="colorOnBackground">@color/md_theme_dark_onBackground</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="actionBarSize">48dp</item>
    </style>
    
    <!-- Dark Blue Theme - 深藍主題 -->
    <style name="Theme_AutoLaunch_Dark_Blue" parent="Theme.Material3.DayNight">
        <item name="colorPrimary">@color/md_theme_dark_blue_primary</item>
        <item name="colorOnPrimary">@color/md_theme_dark_blue_onPrimary</item>
        <item name="colorSecondary">@color/md_theme_dark_blue_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_dark_blue_onSecondary</item>
        <item name="colorSurface">@color/md_theme_dark_blue_surface</item>
        <item name="colorOnSurface">@color/md_theme_dark_blue_onSurface</item>
        <item name="colorSurfaceVariant">@color/md_theme_dark_blue_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_dark_blue_onSurfaceVariant</item>
        <item name="colorPrimaryContainer">@color/md_theme_dark_blue_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_dark_blue_onPrimaryContainer</item>
        <item name="android:colorBackground">@color/md_theme_dark_blue_background</item>
        <item name="colorOnBackground">@color/md_theme_dark_blue_onBackground</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="actionBarSize">48dp</item>
    </style>
    
    <!-- Dark Green Theme - 深綠主題 -->
    <style name="Theme_AutoLaunch_Dark_Green" parent="Theme.Material3.DayNight">
        <item name="colorPrimary">@color/md_theme_dark_green_primary</item>
        <item name="colorOnPrimary">@color/md_theme_dark_green_onPrimary</item>
        <item name="colorSecondary">@color/md_theme_dark_green_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_dark_green_onSecondary</item>
        <item name="colorSurface">@color/md_theme_dark_green_surface</item>
        <item name="colorOnSurface">@color/md_theme_dark_green_onSurface</item>
        <item name="colorSurfaceVariant">@color/md_theme_dark_green_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_dark_green_onSurfaceVariant</item>
        <item name="colorPrimaryContainer">@color/md_theme_dark_green_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_dark_green_onPrimaryContainer</item>
        <item name="android:colorBackground">@color/md_theme_dark_green_background</item>
        <item name="colorOnBackground">@color/md_theme_dark_green_onBackground</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="actionBarSize">48dp</item>
    </style>

    <!-- Dark Purple Theme - 深紫主題 -->
    <style name="Theme_AutoLaunch_Dark_Purple" parent="Theme.Material3.DayNight">
        <item name="colorPrimary">@color/md_theme_dark_purple_primary</item>
        <item name="colorOnPrimary">@color/md_theme_dark_purple_onPrimary</item>
        <item name="colorSecondary">@color/md_theme_dark_purple_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_dark_purple_onSecondary</item>
        <item name="colorSurface">@color/md_theme_dark_purple_surface</item>
        <item name="colorOnSurface">@color/md_theme_dark_purple_onSurface</item>
        <item name="colorSurfaceVariant">@color/md_theme_dark_purple_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_dark_purple_onSurfaceVariant</item>
        <item name="colorPrimaryContainer">@color/md_theme_dark_purple_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_dark_purple_onPrimaryContainer</item>
        <item name="android:colorBackground">@color/md_theme_dark_purple_background</item>
        <item name="colorOnBackground">@color/md_theme_dark_purple_onBackground</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="actionBarSize">48dp</item>
    </style>

    <!-- Dark Orange Theme - 深橙主題 -->
    <style name="Theme_AutoLaunch_Dark_Orange" parent="Theme.Material3.DayNight">
        <item name="colorPrimary">@color/md_theme_dark_orange_primary</item>
        <item name="colorOnPrimary">@color/md_theme_dark_orange_onPrimary</item>
        <item name="colorSecondary">@color/md_theme_dark_orange_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_dark_orange_onSecondary</item>
        <item name="colorSurface">@color/md_theme_dark_orange_surface</item>
        <item name="colorOnSurface">@color/md_theme_dark_orange_onSurface</item>
        <item name="colorSurfaceVariant">@color/md_theme_dark_orange_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_dark_orange_onSurfaceVariant</item>
        <item name="colorPrimaryContainer">@color/md_theme_dark_orange_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_dark_orange_onPrimaryContainer</item>
        <item name="android:colorBackground">@color/md_theme_dark_orange_background</item>
        <item name="colorOnBackground">@color/md_theme_dark_orange_onBackground</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="actionBarSize">48dp</item>
    </style>

    <!-- Dark Rose Theme - 玫瑰深色主題 -->
    <style name="Theme_AutoLaunch_Dark_Rose" parent="Theme.Material3.DayNight">
        <item name="colorPrimary">@color/md_theme_dark_rose_primary</item>
        <item name="colorOnPrimary">@color/md_theme_dark_rose_onPrimary</item>
        <item name="colorSecondary">@color/md_theme_dark_rose_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_dark_rose_onSecondary</item>
        <item name="colorSurface">@color/md_theme_dark_rose_surface</item>
        <item name="colorOnSurface">@color/md_theme_dark_rose_onSurface</item>
        <item name="colorSurfaceVariant">@color/md_theme_dark_rose_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_dark_rose_onSurfaceVariant</item>
        <item name="colorPrimaryContainer">@color/md_theme_dark_rose_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_dark_rose_onPrimaryContainer</item>
        <item name="android:colorBackground">@color/md_theme_dark_rose_background</item>
        <item name="colorOnBackground">@color/md_theme_dark_rose_onBackground</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="actionBarSize">48dp</item>
    </style>

    <!-- Dark Gold Theme - 金色深色主題 -->
    <style name="Theme_AutoLaunch_Dark_Gold" parent="Theme.Material3.DayNight">
        <item name="colorPrimary">@color/md_theme_dark_gold_primary</item>
        <item name="colorOnPrimary">@color/md_theme_dark_gold_onPrimary</item>
        <item name="colorSecondary">@color/md_theme_dark_gold_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_dark_gold_onSecondary</item>
        <item name="colorSurface">@color/md_theme_dark_gold_surface</item>
        <item name="colorOnSurface">@color/md_theme_dark_gold_onSurface</item>
        <item name="colorSurfaceVariant">@color/md_theme_dark_gold_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_dark_gold_onSurfaceVariant</item>
        <item name="colorPrimaryContainer">@color/md_theme_dark_gold_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_dark_gold_onPrimaryContainer</item>
        <item name="android:colorBackground">@color/md_theme_dark_gold_background</item>
        <item name="colorOnBackground">@color/md_theme_dark_gold_onBackground</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="actionBarSize">48dp</item>
    </style>

    <!-- Dark Teal Theme - 青綠深色主題 -->
    <style name="Theme_AutoLaunch_Dark_Teal" parent="Theme.Material3.DayNight">
        <item name="colorPrimary">@color/md_theme_dark_teal_primary</item>
        <item name="colorOnPrimary">@color/md_theme_dark_teal_onPrimary</item>
        <item name="colorSecondary">@color/md_theme_dark_teal_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_dark_teal_onSecondary</item>
        <item name="colorSurface">@color/md_theme_dark_teal_surface</item>
        <item name="colorOnSurface">@color/md_theme_dark_teal_onSurface</item>
        <item name="colorSurfaceVariant">@color/md_theme_dark_teal_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_dark_teal_onSurfaceVariant</item>
        <item name="colorPrimaryContainer">@color/md_theme_dark_teal_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_dark_teal_onPrimaryContainer</item>
        <item name="android:colorBackground">@color/md_theme_dark_teal_background</item>
        <item name="colorOnBackground">@color/md_theme_dark_teal_onBackground</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="actionBarSize">48dp</item>
    </style>

    <!-- Dark Crimson Theme - 深紅主題 -->
    <style name="Theme_AutoLaunch_Dark_Crimson" parent="Theme.Material3.DayNight">
        <item name="colorPrimary">@color/md_theme_dark_crimson_primary</item>
        <item name="colorOnPrimary">@color/md_theme_dark_crimson_onPrimary</item>
        <item name="colorSecondary">@color/md_theme_dark_crimson_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_dark_crimson_onSecondary</item>
        <item name="colorSurface">@color/md_theme_dark_crimson_surface</item>
        <item name="colorOnSurface">@color/md_theme_dark_crimson_onSurface</item>
        <item name="colorSurfaceVariant">@color/md_theme_dark_crimson_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_dark_crimson_onSurfaceVariant</item>
        <item name="colorPrimaryContainer">@color/md_theme_dark_crimson_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_dark_crimson_onPrimaryContainer</item>
        <item name="android:colorBackground">@color/md_theme_dark_crimson_background</item>
        <item name="colorOnBackground">@color/md_theme_dark_crimson_onBackground</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="actionBarSize">48dp</item>
    </style>

</resources>
