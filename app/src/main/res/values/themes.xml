<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Light theme -->
    <style name="Theme.AutoLaunch" parent="Theme.Material3.DayNight">
        <item name="colorPrimary">@color/md_theme_light_primary</item>
        <item name="colorOnPrimary">@color/md_theme_light_onPrimary</item>
        <item name="colorSecondary">@color/md_theme_light_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_light_onSecondary</item>
        <item name="colorSurface">@color/md_theme_light_surface</item>
        <item name="colorOnSurface">@color/md_theme_light_onSurface</item>
        <item name="colorSurfaceVariant">@color/md_theme_light_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_light_onSurfaceVariant</item>
        <item name="colorPrimaryContainer">@color/md_theme_light_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_light_onPrimaryContainer</item>
        <item name="android:colorBackground">@color/md_theme_light_background</item>
        <item name="colorOnBackground">@color/md_theme_light_onBackground</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">true</item>
        <!-- 自定義更緊湊的 ActionBar 高度 -->
        <item name="actionBarSize">48dp</item>
    </style>

    <!-- NoActionBar 主題 - 用於特定頁面，繼承主主題的顏色 -->
    <style name="Theme.AutoLaunch.NoActionBar" parent="Theme.AutoLaunch">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <!-- 可點擊標題樣式 -->
    <style name="ClickableTitle" parent="TextAppearance.Material3.TitleLarge">
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">?attr/colorOnSurface</item>
        <item name="android:background">?attr/selectableItemBackgroundBorderless</item>
        <item name="android:clickable">true</item>
        <item name="android:focusable">true</item>
        <item name="android:padding">8dp</item>
        <item name="android:drawablePadding">4dp</item>
        <item name="android:drawableTint">?attr/colorOnSurfaceVariant</item>
    </style>
</resources>