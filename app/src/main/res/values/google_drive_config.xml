<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Google Drive API 配置 -->
    <!-- 注意：在實際部署時，請替換為您的 Google Cloud Console 項目的實際配置 -->
    
    <!-- Google OAuth 2.0 客戶端 ID -->
    <!-- 這是一個示例 ID，實際使用時需要從 Google Cloud Console 獲取 -->
    <string name="google_oauth_client_id">YOUR_GOOGLE_OAUTH_CLIENT_ID.apps.googleusercontent.com</string>
    
    <!-- Google Drive API 範圍 -->
    <string name="google_drive_scope">https://www.googleapis.com/auth/drive.file</string>
    
    <!-- 應用程式名稱（用於 Google Drive API） -->
    <string name="google_drive_app_name">AutoLaunch</string>
    
    <!-- 備份文件夾名稱 -->
    <string name="backup_folder_name">AutoLaunch Backups</string>
    
    <!-- 備份文件 MIME 類型 -->
    <string name="backup_file_mime_type">application/json</string>
    
    <!-- 最大備份文件數量 -->
    <integer name="max_backup_files">10</integer>
    
    <!-- 備份文件擴展名 -->
    <string name="backup_file_extension">.json</string>
</resources>
