<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?android:attr/colorBackground">

    <!-- 網頁排程列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewWebSchedules"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingHorizontal="16dp"
        android:paddingTop="8dp"
        android:paddingBottom="120dp"
        android:clipToPadding="false"
        tools:listitem="@layout/item_schedule"
        tools:itemCount="3" />

    <!-- 空狀態 -->
    <LinearLayout
        android:id="@+id/emptyStateLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="32dp"
        android:visibility="gone">

        <ImageView
            android:layout_width="96dp"
            android:layout_height="96dp"
            android:layout_marginBottom="16dp"
            android:alpha="0.6"
            android:src="@drawable/ic_web_24"
            app:tint="?attr/colorOnSurfaceVariant" />

        <com.google.android.material.textview.MaterialTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="暫無網頁排程"
            android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall"
            android:textColor="?attr/colorOnSurfaceVariant" />

        <com.google.android.material.textview.MaterialTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="點擊右下角的 + 按鈕\n來新增您的第一個網頁排程"
            android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
            android:textColor="?attr/colorOnSurfaceVariant" />

    </LinearLayout>

</FrameLayout>
