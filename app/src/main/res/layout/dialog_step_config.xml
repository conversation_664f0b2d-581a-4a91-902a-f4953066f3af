<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp">

    <!-- 標題 -->
    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/tvDialogTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="配置自動化步驟"
        android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall"
        android:textColor="?attr/colorOnSurface"
        android:layout_marginBottom="24dp"
        android:gravity="center" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:maxHeight="400dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- 步驟類型選擇 -->
            <com.google.android.material.textview.MaterialTextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="步驟類型"
                android:textAppearance="@style/TextAppearance.Material3.TitleSmall"
                android:textColor="?attr/colorOnSurface"
                android:layout_marginBottom="8dp" />

            <com.google.android.material.button.MaterialButtonToggleGroup
                android:id="@+id/toggleGroupStepType"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:singleSelection="true">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnTypeClick"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="點擊"
                    style="@style/Widget.Material3.Button.OutlinedButton" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnTypeInput"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="輸入"
                    style="@style/Widget.Material3.Button.OutlinedButton" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnTypeWait"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="等待"
                    style="@style/Widget.Material3.Button.OutlinedButton" />

            </com.google.android.material.button.MaterialButtonToggleGroup>

            <!-- 更多步驟類型 -->
            <com.google.android.material.button.MaterialButtonToggleGroup
                android:id="@+id/toggleGroupStepTypeMore"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:singleSelection="true">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnTypeSwipe"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="滑動"
                    style="@style/Widget.Material3.Button.OutlinedButton" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnTypeBack"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="返回"
                    style="@style/Widget.Material3.Button.OutlinedButton" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnTypeScroll"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="滾動"
                    style="@style/Widget.Material3.Button.OutlinedButton" />

            </com.google.android.material.button.MaterialButtonToggleGroup>

            <!-- 步驟名稱 -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/layoutStepName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="步驟名稱（可選）"
                style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etStepName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- 目標元素配置 -->
            <LinearLayout
                android:id="@+id/layoutTargetConfig"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginBottom="16dp">

                <com.google.android.material.textview.MaterialTextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="目標元素"
                    android:textAppearance="@style/TextAppearance.Material3.TitleSmall"
                    android:textColor="?attr/colorOnSurface"
                    android:layout_marginBottom="8dp" />

                <!-- 定位方式選擇 -->
                <com.google.android.material.button.MaterialButtonToggleGroup
                    android:id="@+id/toggleGroupTargetType"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    app:singleSelection="true">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnTargetText"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="文字"
                        style="@style/Widget.Material3.Button.OutlinedButton" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnTargetId"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="ID"
                        style="@style/Widget.Material3.Button.OutlinedButton" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnTargetCoordinate"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="座標"
                        style="@style/Widget.Material3.Button.OutlinedButton" />

                </com.google.android.material.button.MaterialButtonToggleGroup>

                <!-- 目標文字輸入 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/layoutTargetText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:hint="目標文字"
                    style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etTargetText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="text"
                        android:maxLines="1" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- 目標ID輸入 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/layoutTargetId"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:hint="元素ID"
                    android:visibility="gone"
                    style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etTargetId"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="text"
                        android:maxLines="1" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- 座標輸入 -->
                <LinearLayout
                    android:id="@+id/layoutTargetCoordinate"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:visibility="gone"
                    android:layout_marginBottom="12dp">

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        android:hint="X座標"
                        style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etTargetX"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="number"
                            android:maxLines="1" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="8dp"
                        android:hint="Y座標"
                        style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etTargetY"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="number"
                            android:maxLines="1" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

            </LinearLayout>

            <!-- 輸入文字配置 -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/layoutInputText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="輸入文字內容"
                android:visibility="gone"
                style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etInputText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:maxLines="3" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- 等待時間配置 -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/layoutWaitDuration"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="等待時間（毫秒）"
                android:visibility="gone"
                style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etWaitDuration"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="number"
                    android:maxLines="1"
                    android:text="1000" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- 高級選項 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardElevation="0dp"
                app:cardCornerRadius="8dp"
                app:strokeWidth="1dp"
                app:strokeColor="?attr/colorOutlineVariant">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="高級選項"
                        android:textAppearance="@style/TextAppearance.Material3.TitleSmall"
                        android:textColor="?attr/colorOnSurface"
                        android:layout_marginBottom="12dp" />

                    <!-- 可選步驟開關 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="12dp">

                        <com.google.android.material.textview.MaterialTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="可選步驟"
                            android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                            android:textColor="?attr/colorOnSurface" />

                        <com.google.android.material.materialswitch.MaterialSwitch
                            android:id="@+id/switchOptional"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content" />

                    </LinearLayout>

                    <!-- 重試次數 -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:hint="重試次數"
                        style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etRetryCount"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="number"
                            android:maxLines="1"
                            android:text="3" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- 超時時間 -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="超時時間（毫秒）"
                        style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etTimeoutMs"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="number"
                            android:maxLines="1"
                            android:text="10000" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- 操作按鈕 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end"
        android:layout_marginTop="24dp">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnCancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="取消"
            android:layout_marginEnd="8dp"
            style="@style/Widget.Material3.Button.TextButton" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnSave"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="儲存"
            style="@style/Widget.Material3.Button" />

    </LinearLayout>

</LinearLayout>
