<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="16dp"
    android:layout_marginTop="6dp"
    android:layout_marginEnd="16dp"
    android:layout_marginBottom="6dp"
    app:cardElevation="3dp"
    app:cardCornerRadius="12dp"
    app:cardBackgroundColor="?attr/colorSurface">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <!-- App 圖標 - 適中尺寸 -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/cardAppIcon"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:layout_marginEnd="48dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="1dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/ivAppIcon"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/ic_launcher_foreground"
                android:scaleType="centerCrop"
                tools:src="@mipmap/ic_launcher" />

        </com.google.android.material.card.MaterialCardView>

        <!-- 時間顯示區域 - 緊湊設計 -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/cardTimeDisplay"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="1dp"
            app:cardBackgroundColor="?attr/colorPrimaryContainer"
            app:layout_constraintEnd_toStartOf="@+id/switchEnabled"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:padding="8dp"
                android:minWidth="70dp">

                <!-- 設定時間 - 緊湊顯示 -->
                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/tvScheduleTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="08:00"
                    android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                    android:textColor="?attr/colorOnPrimaryContainer"
                    android:textStyle="bold"
                    tools:text="08:00" />

                <!-- 重複模式標籤 -->
                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/tvRepeatMode"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:text="每日"
                    android:textAppearance="@style/TextAppearance.Material3.LabelSmall"
                    android:textColor="?attr/colorOnPrimaryContainer"
                    tools:text="每日" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- App 資訊區域 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginStart="24dp"
            android:layout_marginEnd="16dp"
            app:layout_constraintStart_toEndOf="@+id/cardAppIcon"
            app:layout_constraintEnd_toStartOf="@+id/cardTimeDisplay"
            app:layout_constraintTop_toTopOf="@+id/cardAppIcon">

            <!-- 任務名稱 -->
            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/tvAppName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="任務名稱"
                android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                android:textColor="?attr/colorOnSurface"
                android:textStyle="bold"
                android:maxLines="1"
                android:ellipsize="end"
                tools:text="早晨瀏覽" />

            <!-- App名稱 (副標題) -->
            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/tvAppNameSubtitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="1dp"
                android:text="Chrome"
                android:textAppearance="@style/TextAppearance.Material3.LabelMedium"
                android:textColor="?attr/colorOnSurfaceVariant"
                android:maxLines="1"
                android:ellipsize="end"
                tools:text="Chrome" />

            <!-- 重複模式詳細說明 -->
            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/tvRepeatModeDetail"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:text="每日重複"
                android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                android:textColor="?attr/colorOnSurfaceVariant"
                tools:text="週一到週五" />

            <!-- 執行狀態信息 - 緊湊顯示 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginTop="4dp">

                <!-- 下次執行時間 -->
                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/tvNextExecution"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="下次: 明天 08:00"
                    android:textAppearance="@style/TextAppearance.Material3.LabelSmall"
                    android:textColor="?attr/colorPrimary"
                    android:textStyle="bold"
                    android:visibility="gone"
                    tools:text="下次: 1小時後"
                    tools:visibility="visible" />

                <!-- 上次執行時間 -->
                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/tvLastExecutionTime"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:text="上次: 2小時前"
                    android:textAppearance="@style/TextAppearance.Material3.LabelSmall"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    android:visibility="gone"
                    android:alpha="0.8"
                    tools:text="上次: 2小時前"
                    tools:visibility="visible" />

            </LinearLayout>

        </LinearLayout>

        <!-- 隱藏的布局容器 - 保持兼容性 -->
        <LinearLayout
            android:id="@+id/layoutNextExecution"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:id="@+id/layoutLastExecution"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 上次執行時間 - 隱藏的TextView保持兼容性 -->
        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/tvLastExecution"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 啟用/禁用開關 -->
        <com.google.android.material.materialswitch.MaterialSwitch
            android:id="@+id/switchEnabled"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:checked="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="@+id/cardAppIcon" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView> 