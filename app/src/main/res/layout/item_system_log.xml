<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="16dp"
    android:layout_marginVertical="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="1dp"
    app:strokeWidth="0dp"
    android:background="?attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- 時間 -->
        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/tvTime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:minWidth="60dp"
            android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
            android:textColor="?attr/colorOnSurfaceVariant"
            android:textStyle="bold"
            tools:text="09:06" />

        <!-- 日志類型圖標 -->
        <ImageView
            android:id="@+id/ivLogType"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_marginEnd="12dp"
            android:layout_marginTop="2dp"
            tools:src="@drawable/ic_check_circle_24"
            tools:tint="@color/success_color" />

        <!-- 內容區域 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- 主要消息 -->
            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/tvMessage"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                android:textColor="?attr/colorOnSurface"
                android:maxLines="2"
                android:ellipsize="end"
                tools:text="執行排程：啟動 TW 事群" />

            <!-- 操作類型和排程名稱 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:orientation="horizontal">

                <!-- 操作類型 -->
                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/tvActionType"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                    android:textStyle="bold"
                    tools:text="排程執行"
                    tools:textColor="@color/success_color" />

                <!-- 排程名稱 -->
                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/tvScheduleName"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:visibility="gone"
                    tools:text="啟動 TW 事群"
                    tools:visibility="visible" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
