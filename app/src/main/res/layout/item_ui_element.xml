<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    android:checkable="true"
    app:cardElevation="1dp"
    app:cardCornerRadius="8dp"
    app:strokeWidth="1dp"
    app:strokeColor="?attr/colorOutlineVariant">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical">

        <!-- 元素圖標 -->
        <ImageView
            android:id="@+id/ivElementIcon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginEnd="12dp"
            android:src="@drawable/ic_touch_app_24"
            app:tint="?attr/colorPrimary"
            tools:src="@drawable/ic_touch_app_24" />

        <!-- 元素信息 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- 元素名稱 -->
            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/tvElementName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                android:textColor="?attr/colorOnSurface"
                android:maxLines="2"
                android:ellipsize="end"
                tools:text="登入按鈕" />

            <!-- 元素類型 -->
            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/tvElementType"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                android:textColor="?attr/colorOnSurfaceVariant"
                android:maxLines="1"
                android:ellipsize="end"
                tools:text="可點擊 • Button" />

            <!-- 元素位置 -->
            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/tvElementPosition"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                android:textColor="?attr/colorOnSurfaceVariant"
                android:maxLines="1"
                android:ellipsize="end"
                tools:text="位置: (540, 960)" />

        </LinearLayout>

        <!-- 選擇指示器 -->
        <com.google.android.material.checkbox.MaterialCheckBox
            android:id="@+id/checkboxSelect"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:clickable="false"
            android:focusable="false" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
