<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?android:attr/colorBackground"
    tools:context=".SystemLogActivity">

    <!-- App Bar -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/colorPrimary">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:title="系統日誌"
            app:titleTextColor="?attr/colorOnPrimary"
            app:navigationIconTint="?attr/colorOnPrimary" />

        <!-- 統計信息卡片 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp"
            app:strokeWidth="0dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="16dp">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/tvTotalCount"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="總計：0"
                        android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                        android:textColor="?attr/colorOnSurface"
                        android:textStyle="bold" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/tvSuccessCount"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="成功：0"
                        android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                        android:textColor="@color/success_color"
                        android:textStyle="bold" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/tvWarningCount"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="警告：0"
                        android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                        android:textColor="@color/warning_color"
                        android:textStyle="bold" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/tvErrorCount"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="錯誤：0"
                        android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                        android:textColor="@color/error_color"
                        android:textStyle="bold" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </com.google.android.material.appbar.AppBarLayout>

    <!-- 主要內容 -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipeRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <!-- 日誌清單 -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerViewLogs"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="false"
                android:paddingBottom="16dp"
                tools:listitem="@layout/item_system_log" />

            <!-- 載入狀態 -->
            <LinearLayout
                android:id="@+id/loadingStateLayout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="32dp"
                android:visibility="gone">

                <com.google.android.material.progressindicator.CircularProgressIndicator
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:indeterminate="true" />

                <com.google.android.material.textview.MaterialTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="載入日誌中..."
                    android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                    android:textColor="?attr/colorOnSurfaceVariant" />

            </LinearLayout>

            <!-- 空狀態 -->
            <LinearLayout
                android:id="@+id/emptyStateLayout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="32dp"
                android:visibility="gone">

                <ImageView
                    android:layout_width="96dp"
                    android:layout_height="96dp"
                    android:layout_marginBottom="16dp"
                    android:alpha="0.6"
                    android:src="@drawable/ic_history_24"
                    app:tint="?attr/colorOnSurfaceVariant" />

                <com.google.android.material.textview.MaterialTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="暫無日誌記錄"
                    android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall"
                    android:textColor="?attr/colorOnSurfaceVariant" />

                <com.google.android.material.textview.MaterialTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="當您使用應用程式時，\n相關操作記錄將會顯示在這裡"
                    android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                    android:textColor="?attr/colorOnSurfaceVariant" />

            </LinearLayout>

            <!-- 錯誤狀態 -->
            <LinearLayout
                android:id="@+id/errorStateLayout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="32dp"
                android:visibility="gone">

                <ImageView
                    android:layout_width="96dp"
                    android:layout_height="96dp"
                    android:layout_marginBottom="16dp"
                    android:alpha="0.6"
                    android:src="@drawable/ic_error_24"
                    app:tint="@color/error_color" />

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/tvErrorTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="載入失敗"
                    android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall"
                    android:textColor="@color/error_color" />

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/tvErrorMessage"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:gravity="center"
                    android:text="載入日誌時發生錯誤"
                    android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                    android:textColor="?attr/colorOnSurfaceVariant" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnRetry"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="重試"
                    style="@style/Widget.Material3.Button.OutlinedButton" />

            </LinearLayout>

        </FrameLayout>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
