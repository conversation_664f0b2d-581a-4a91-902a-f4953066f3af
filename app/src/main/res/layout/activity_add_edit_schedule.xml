<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/colorSurface"
    tools:context=".AddEditScheduleActivity">

    <!-- 自定義頂部區域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="?attr/colorSurface"
        android:elevation="4dp"
        android:paddingStart="4dp"
        android:paddingEnd="12dp"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        android:gravity="center_vertical"
        android:minHeight="64dp">

        <!-- 返回按鈕 -->
        <ImageButton
            android:id="@+id/btnBack"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_arrow_back_24"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/content_description_back"
            app:tint="?attr/colorOnSurface" />

        <!-- 標題區域 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:layout_marginStart="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etToolbarTitle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/title_add_schedule"
                    android:textAppearance="@style/TextAppearance.Material3.TitleLarge"
                    android:textColor="?attr/colorOnSurface"
                    android:textStyle="bold"
                    android:background="@android:color/transparent"
                    android:padding="8dp"
                    android:hint="@string/hint_enter_schedule_name"
                    android:inputType="text"
                    android:maxLines="1"
                    android:imeOptions="actionDone" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnSaveSchedule"
                    android:layout_width="wrap_content"
                    android:layout_height="48dp"
                    android:layout_marginStart="12dp"
                    android:text="@string/button_save"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:minWidth="96dp"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp"
                    android:paddingTop="8dp"
                    android:paddingBottom="8dp"
                    app:icon="@drawable/ic_save_24"
                    app:iconSize="18dp"
                    app:iconPadding="6dp"
                    app:cornerRadius="24dp"
                    style="@style/Widget.Material3.Button" />

            </LinearLayout>

            <!-- 底線 -->
            <View
                android:layout_width="match_parent"
                android:layout_height="2dp"
                android:background="?attr/colorPrimary"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp" />

        </LinearLayout>

        <!-- 隱藏的編輯圖標（保持兼容性） -->
        <ImageView
            android:id="@+id/ivEditTitle"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone" />

        <!-- 隱藏的Toolbar（保持兼容性） -->
        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone" />

    </LinearLayout>

    <!-- 內容區域 -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="64dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- 排程類型選擇 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardElevation="2dp"
                app:cardCornerRadius="12dp"
                app:strokeColor="?attr/colorOutline"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <!-- 標題 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="16dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_schedule_24"
                            android:layout_marginEnd="12dp"
                            app:tint="?attr/colorPrimary" />

                        <com.google.android.material.textview.MaterialTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/schedule_settings"
                            android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                            android:textColor="?attr/colorOnSurface" />

                    </LinearLayout>

                    <!-- 類型選擇按鈕組 -->
                    <com.google.android.material.button.MaterialButtonToggleGroup
                        android:id="@+id/toggleGroupScheduleType"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:singleSelection="true"
                        app:selectionRequired="true">

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnTypeApp"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/schedule_type_app"
                            app:icon="@drawable/ic_android"
                            style="@style/Widget.Material3.Button.OutlinedButton" />

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnTypeUrl"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/schedule_type_url"
                            app:icon="@drawable/ic_link_24"
                            style="@style/Widget.Material3.Button.OutlinedButton" />

                    </com.google.android.material.button.MaterialButtonToggleGroup>

                    <!-- 應用程式選擇區域 -->
                    <LinearLayout
                        android:id="@+id/cardAppSelectionContainer"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginTop="16dp">

                        <!-- 分隔線 -->
                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:background="?attr/colorOutlineVariant"
                            android:layout_marginBottom="16dp" />

                        <!-- App 選擇卡片 -->
                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/cardAppSelection"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:clickable="true"
                            android:focusable="true"
                            android:foreground="?attr/selectableItemBackground"
                            app:cardElevation="0dp"
                            app:cardCornerRadius="8dp"
                            app:strokeWidth="1dp"
                            app:strokeColor="?attr/colorOutline">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:padding="16dp"
                                android:gravity="center_vertical">

                                <ImageView
                                    android:id="@+id/ivSelectedAppIcon"
                                    android:layout_width="48dp"
                                    android:layout_height="48dp"
                                    android:layout_marginEnd="16dp"
                                    android:src="@mipmap/ic_launcher"
                                    android:contentDescription="應用程式圖標" />

                                <LinearLayout
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:orientation="vertical">

                                    <com.google.android.material.textview.MaterialTextView
                                        android:id="@+id/tvSelectedAppName"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:text="@string/hint_select_app"
                                        android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                                        android:textColor="?attr/colorOnSurface" />

                                    <com.google.android.material.textview.MaterialTextView
                                        android:id="@+id/tvSelectedAppPackage"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:text="@string/hint_select_app_description"
                                        android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                                        android:textColor="?attr/colorOnSurfaceVariant"
                                        android:layout_marginTop="4dp" />

                                </LinearLayout>

                                <ImageView
                                    android:layout_width="24dp"
                                    android:layout_height="24dp"
                                    android:src="@drawable/ic_arrow_forward_24"
                                    android:contentDescription="選擇"
                                    app:tint="?attr/colorOnSurfaceVariant" />

                            </LinearLayout>

                        </com.google.android.material.card.MaterialCardView>

                    </LinearLayout>

                    <!-- URL 輸入區域 -->
                    <LinearLayout
                        android:id="@+id/layoutUrlInputContainer"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginTop="16dp"
                        android:visibility="gone">

                        <!-- 分隔線 -->
                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:background="?attr/colorOutlineVariant"
                            android:layout_marginBottom="16dp" />

                        <!-- URL 輸入框 -->
                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/tilUrl"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp"
                            android:hint="@string/hint_enter_url"
                            app:helperText="@string/helper_text_url_example"
                            app:helperTextEnabled="true"
                            app:startIconDrawable="@drawable/ic_link_24"
                            app:endIconMode="clear_text"
                            style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/etUrl"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="textUri"
                                android:maxLines="3"
                                android:scrollHorizontally="false" />

                        </com.google.android.material.textfield.TextInputLayout>



                        <!-- 預覽按鈕 -->
                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnPreviewUrl"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="end"
                            android:text="@string/preview_url"
                            app:icon="@drawable/ic_preview_24"
                            style="@style/Widget.Material3.Button.TextButton" />

                        <!-- URL 驗證狀態 -->
                        <LinearLayout
                            android:id="@+id/layoutUrlStatus"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:padding="12dp"
                            android:layout_marginTop="8dp"
                            android:background="?attr/colorSurfaceVariant"
                            android:visibility="gone">

                            <ImageView
                                android:id="@+id/ivUrlStatus"
                                android:layout_width="20dp"
                                android:layout_height="20dp"
                                android:layout_marginEnd="8dp"
                                android:src="@drawable/ic_check_circle_24"
                                app:tint="?attr/colorPrimary" />

                            <com.google.android.material.textview.MaterialTextView
                                android:id="@+id/tvUrlStatus"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="@string/url_format_valid"
                                android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                                android:textColor="?attr/colorOnSurfaceVariant" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>





            <!-- 時間設定 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardElevation="2dp"
                app:cardCornerRadius="12dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/time_settings"
                        android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                        android:textColor="?attr/colorOnSurface"
                        android:layout_marginBottom="12dp" />

                    <!-- 時間選擇卡片 -->
                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/cardTimeSelection"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:clickable="true"
                        android:focusable="true"
                        android:foreground="?attr/selectableItemBackground"
                        app:cardElevation="0dp"
                        app:cardCornerRadius="8dp"
                        app:strokeWidth="1dp"
                        app:strokeColor="?attr/colorOutline">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:padding="16dp"
                            android:gravity="center_vertical">

                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:layout_marginEnd="16dp"
                                android:src="@drawable/ic_schedule_24"
                                android:contentDescription="時間圖標"
                                app:tint="?attr/colorPrimary" />

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical">

                                <com.google.android.material.textview.MaterialTextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="@string/label_execution_time"
                                    android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                                    android:textColor="?attr/colorOnSurfaceVariant" />

                                <com.google.android.material.textview.MaterialTextView
                                    android:id="@+id/tvSelectedTime"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="08:00"
                                    android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall"
                                    android:textColor="?attr/colorOnSurface"
                                    android:layout_marginTop="4dp" />

                            </LinearLayout>

                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:src="@drawable/ic_arrow_forward_24"
                                android:contentDescription="選擇"
                                app:tint="?attr/colorOnSurfaceVariant" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 重複設定 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardElevation="2dp"
                app:cardCornerRadius="12dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/repeat_settings"
                        android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                        android:textColor="?attr/colorOnSurface"
                        android:layout_marginBottom="12dp" />

                    <!-- 重複模式選擇 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <!-- 重複模式選項 -->
                        <com.google.android.material.button.MaterialButtonToggleGroup
                            android:id="@+id/toggleGroupRepeatMode"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            app:singleSelection="true"
                            app:selectionRequired="true">

                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/btnRepeatOnce"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="@string/label_once"
                                style="@style/Widget.Material3.Button.OutlinedButton" />

                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/btnRepeatDaily"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="@string/label_daily"
                                style="@style/Widget.Material3.Button.OutlinedButton" />

                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/btnRepeatWeekly"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="@string/label_weekly"
                                style="@style/Widget.Material3.Button.OutlinedButton" />

                        </com.google.android.material.button.MaterialButtonToggleGroup>

                        <!-- 日期選擇 (僅在單次模式下顯示) -->
                        <LinearLayout
                            android:id="@+id/layoutDateSelection"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:layout_marginTop="16dp"
                            android:visibility="gone">

                            <com.google.android.material.textview.MaterialTextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="@string/select_execution_date"
                                android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                                android:textColor="?attr/colorOnSurface"
                                android:layout_marginBottom="8dp" />

                            <com.google.android.material.card.MaterialCardView
                                android:id="@+id/cardDateSelection"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:clickable="true"
                                android:focusable="true"
                                android:foreground="?attr/selectableItemBackground"
                                app:cardElevation="0dp"
                                app:cardCornerRadius="8dp"
                                app:strokeWidth="1dp"
                                app:strokeColor="?attr/colorOutline">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="horizontal"
                                    android:padding="16dp"
                                    android:gravity="center_vertical">

                                    <ImageView
                                        android:layout_width="24dp"
                                        android:layout_height="24dp"
                                        android:layout_marginEnd="16dp"
                                        android:src="@drawable/ic_calendar_24"
                                        android:contentDescription="日期圖標"
                                        app:tint="?attr/colorPrimary" />

                                    <LinearLayout
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:orientation="vertical">

                                        <com.google.android.material.textview.MaterialTextView
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content"
                                            android:text="@string/label_execution_date"
                                            android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                                            android:textColor="?attr/colorOnSurfaceVariant" />

                                        <com.google.android.material.textview.MaterialTextView
                                            android:id="@+id/tvSelectedDate"
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content"
                                            android:text="@string/hint_select_date"
                                            android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                                            android:textColor="?attr/colorOnSurface"
                                            android:layout_marginTop="4dp" />

                                    </LinearLayout>

                                    <ImageView
                                        android:layout_width="24dp"
                                        android:layout_height="24dp"
                                        android:src="@drawable/ic_arrow_forward_24"
                                        android:contentDescription="選擇"
                                        app:tint="?attr/colorOnSurfaceVariant" />

                                </LinearLayout>

                            </com.google.android.material.card.MaterialCardView>

                        </LinearLayout>

                        <!-- 星期幾選擇 (僅在每週模式下顯示) -->
                        <LinearLayout
                            android:id="@+id/layoutDaysOfWeek"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:layout_marginTop="16dp"
                            android:visibility="gone">

                            <com.google.android.material.textview.MaterialTextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="@string/select_days_of_week"
                                android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                                android:textColor="?attr/colorOnSurface"
                                android:layout_marginBottom="8dp" />

                            <com.google.android.material.chip.ChipGroup
                                android:id="@+id/chipGroupDaysOfWeek"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                app:chipSpacingHorizontal="8dp"
                                app:chipSpacingVertical="4dp">

                                <com.google.android.material.chip.Chip
                                    android:id="@+id/chipMonday"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/day_monday"
                                    android:checkable="true"
                                    style="@style/Widget.Material3.Chip.Filter" />

                                <com.google.android.material.chip.Chip
                                    android:id="@+id/chipTuesday"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/day_tuesday"
                                    android:checkable="true"
                                    style="@style/Widget.Material3.Chip.Filter" />

                                <com.google.android.material.chip.Chip
                                    android:id="@+id/chipWednesday"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/day_wednesday"
                                    android:checkable="true"
                                    style="@style/Widget.Material3.Chip.Filter" />

                                <com.google.android.material.chip.Chip
                                    android:id="@+id/chipThursday"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/day_thursday"
                                    android:checkable="true"
                                    style="@style/Widget.Material3.Chip.Filter" />

                                <com.google.android.material.chip.Chip
                                    android:id="@+id/chipFriday"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/day_friday"
                                    android:checkable="true"
                                    style="@style/Widget.Material3.Chip.Filter" />

                                <com.google.android.material.chip.Chip
                                    android:id="@+id/chipSaturday"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/day_saturday"
                                    android:checkable="true"
                                    style="@style/Widget.Material3.Chip.Filter" />

                                <com.google.android.material.chip.Chip
                                    android:id="@+id/chipSunday"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/day_sunday"
                                    android:checkable="true"
                                    style="@style/Widget.Material3.Chip.Filter" />

                            </com.google.android.material.chip.ChipGroup>

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 自動化步驟配置區域 -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/cardAutomationSteps"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:visibility="gone"
                app:cardElevation="0dp"
                app:cardCornerRadius="8dp"
                app:strokeWidth="1dp"
                app:strokeColor="?attr/colorOutline">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <!-- 標題 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="16dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_automation_24"
                            android:layout_marginEnd="12dp"
                            app:tint="?attr/colorPrimary" />

                        <com.google.android.material.textview.MaterialTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="自動化步驟"
                            android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                            android:textColor="?attr/colorOnSurface" />

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnToggleAutomation"
                            android:layout_width="wrap_content"
                            android:layout_height="36dp"
                            android:text="啟用"
                            android:textSize="12sp"
                            android:minWidth="64dp"
                            style="@style/Widget.Material3.Button.OutlinedButton" />

                    </LinearLayout>

                    <!-- 步驟列表 -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recyclerViewSteps"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:nestedScrollingEnabled="false" />

                    <!-- 空狀態提示 -->
                    <LinearLayout
                        android:id="@+id/layoutEmptySteps"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="24dp"
                        android:visibility="visible">

                        <ImageView
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:src="@drawable/ic_automation_24"
                            android:layout_marginBottom="12dp"
                            app:tint="?attr/colorOutlineVariant" />

                        <com.google.android.material.textview.MaterialTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="尚未配置自動化步驟"
                            android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                            android:textColor="?attr/colorOnSurfaceVariant"
                            android:layout_marginBottom="4dp" />

                        <com.google.android.material.textview.MaterialTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="點擊下方按鈕新增第一個步驟"
                            android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                            android:textColor="?attr/colorOnSurfaceVariant" />

                    </LinearLayout>

                    <!-- 操作按鈕 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center">

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnAddStep"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="新增步驟"
                            android:layout_marginEnd="8dp"
                            app:icon="@drawable/ic_add_24"
                            style="@style/Widget.Material3.Button.OutlinedButton" />

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnPreviewSteps"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="預覽執行"
                            app:icon="@drawable/ic_preview_24"
                            style="@style/Widget.Material3.Button.OutlinedButton" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout> 