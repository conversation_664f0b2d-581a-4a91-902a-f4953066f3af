<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_create_cloud_backup"
        style="@style/Widget.Material3.Button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="16dp"
        android:text="@string/create_cloud_backup"
        app:icon="@drawable/ic_cloud_upload_24" />

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/google_account_card"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="16dp"
        app:cardElevation="2dp">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="12dp">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:layout_toStartOf="@+id/btn_google_sign_in"
                android:layout_marginEnd="8dp"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Google 帳戶"
                    android:textAppearance="?attr/textAppearanceTitleMedium"
                    android:textColor="?attr/colorOnSurface"
                    app:drawableStartCompat="@drawable/ic_account_circle_24"
                    android:drawablePadding="8dp" />

                <TextView
                    android:id="@+id/tv_google_account_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="未登錄 Google 帳戶"
                    android:textAppearance="?attr/textAppearanceBodyMedium"
                    android:textColor="?attr/colorOnSurfaceVariant" />

            </LinearLayout>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_google_sign_in"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:text="登錄 Google 帳戶"
                app:icon="@drawable/ic_login_24" />

        </RelativeLayout>
    </com.google.android.material.card.MaterialCardView>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="8dp"
        android:layout_weight="1">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:paddingBottom="16dp"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            tools:listitem="@layout/item_backup_file" />

        <TextView
            android:id="@+id/tv_empty_or_loading"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:text="@string/google_login_prompt_for_backup"
            android:textAppearance="?attr/textAppearanceBodyLarge"
            android:textColor="?attr/colorOnSurfaceVariant"
            android:visibility="gone" />
    </FrameLayout>

</LinearLayout>
