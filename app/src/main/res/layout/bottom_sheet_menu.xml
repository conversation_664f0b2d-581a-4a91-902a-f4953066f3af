<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="?attr/colorSurface"
    android:paddingTop="16dp"
    android:paddingBottom="32dp">

    <!-- 拖拽指示器 -->
    <View
        android:layout_width="32dp"
        android:layout_height="4dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="24dp"
        android:background="?attr/colorOnSurfaceVariant"
        android:alpha="0.4" />

    <!-- App 資訊標題 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:paddingHorizontal="24dp"
        android:paddingBottom="16dp">

        <ImageView
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginEnd="16dp"
            android:src="@mipmap/ic_launcher"
            android:contentDescription="App圖標" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <com.google.android.material.textview.MaterialTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/app_name"
                android:textAppearance="@style/TextAppearance.Material3.TitleLarge"
                android:textColor="?attr/colorOnSurface" />

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/tvAppVersion"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/app_version_default"
                android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                android:textColor="?attr/colorOnSurfaceVariant" />

            <!-- 運行狀態指示 -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginTop="4dp">

                <View
                    android:id="@+id/statusIndicator"
                    android:layout_width="8dp"
                    android:layout_height="8dp"
                    android:layout_marginEnd="6dp"
                    android:background="@drawable/circle_status_indicator"
                    android:alpha="0.8" />

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/tvRunningStatus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/app_running_status"
                    android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                    android:textColor="?attr/colorPrimary" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

    <!-- 分隔線 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginHorizontal="24dp"
        android:layout_marginBottom="8dp"
        android:background="?attr/colorOutlineVariant"
        android:alpha="0.5" />

    <!-- 選單項目 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 設定類分組標題 -->
        <com.google.android.material.textview.MaterialTextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="設定"
            android:textAppearance="@style/TextAppearance.Material3.LabelMedium"
            android:textColor="?attr/colorPrimary"
            android:paddingHorizontal="24dp"
            android:paddingVertical="8dp"
            android:layout_marginTop="8dp" />

        <!-- 主題設定 -->
        <LinearLayout
            android:id="@+id/menuThemeSettings"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingHorizontal="24dp"
            android:background="?attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="16dp"
                android:src="@drawable/ic_palette_24"
                app:tint="?attr/colorOnSurfaceVariant"
                android:contentDescription="主題設定" />

            <com.google.android.material.textview.MaterialTextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="主題設定"
                android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                android:textColor="?attr/colorOnSurface" />

        </LinearLayout>

        <!-- 語言設定 -->
        <LinearLayout
            android:id="@+id/menuLanguageSettings"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingHorizontal="24dp"
            android:background="?attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="16dp"
                android:src="@drawable/ic_language_24"
                app:tint="?attr/colorOnSurfaceVariant"
                android:contentDescription="語言設定" />

            <com.google.android.material.textview.MaterialTextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/language_settings"
                android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                android:textColor="?attr/colorOnSurface" />

        </LinearLayout>

        <!-- 分隔線 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginVertical="8dp"
            android:layout_marginHorizontal="24dp"
            android:background="?attr/colorOutlineVariant"
            android:alpha="0.5" />

        <!-- 資料管理分組標題 -->
        <com.google.android.material.textview.MaterialTextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="資料管理"
            android:textAppearance="@style/TextAppearance.Material3.LabelMedium"
            android:textColor="?attr/colorPrimary"
            android:paddingHorizontal="24dp"
            android:paddingVertical="8dp" />

        <!-- 備份與匯入 -->
        <LinearLayout
            android:id="@+id/menuBackupRestore"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingHorizontal="24dp"
            android:background="?attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="16dp"
                android:src="@drawable/ic_cloud_24"
                app:tint="?attr/colorOnSurfaceVariant"
                android:contentDescription="@string/backup_restore_description" />

            <com.google.android.material.textview.MaterialTextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/menu_backup_restore"
                android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                android:textColor="?attr/colorOnSurface" />

        </LinearLayout>

        <!-- 系統紀錄 -->
        <LinearLayout
            android:id="@+id/menuSystemLog"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingHorizontal="24dp"
            android:background="?attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="16dp"
                android:src="@drawable/ic_history_24"
                app:tint="?attr/colorOnSurfaceVariant"
                android:contentDescription="@string/system_log_description" />

            <com.google.android.material.textview.MaterialTextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/menu_system_log"
                android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                android:textColor="?attr/colorOnSurface" />

        </LinearLayout>

        <!-- 分隔線 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginVertical="8dp"
            android:layout_marginHorizontal="24dp"
            android:background="?attr/colorOutlineVariant"
            android:alpha="0.5" />

        <!-- 幫助支援分組標題 -->
        <com.google.android.material.textview.MaterialTextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="幫助支援"
            android:textAppearance="@style/TextAppearance.Material3.LabelMedium"
            android:textColor="?attr/colorPrimary"
            android:paddingHorizontal="24dp"
            android:paddingVertical="8dp" />

        <!-- 教學 -->
        <LinearLayout
            android:id="@+id/menuTutorial"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingHorizontal="24dp"
            android:background="?attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="16dp"
                android:src="@drawable/ic_help_24"
                app:tint="?attr/colorOnSurfaceVariant"
                android:contentDescription="@string/tutorial_description" />

            <com.google.android.material.textview.MaterialTextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/menu_tutorial"
                android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                android:textColor="?attr/colorOnSurface" />

        </LinearLayout>

        <!-- Q&A(常見問題) -->
        <LinearLayout
            android:id="@+id/menuQA"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingHorizontal="24dp"
            android:background="?attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="16dp"
                android:src="@drawable/ic_help_24"
                app:tint="?attr/colorOnSurfaceVariant"
                android:contentDescription="@string/qa_description" />

            <com.google.android.material.textview.MaterialTextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/menu_qa"
                android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                android:textColor="?attr/colorOnSurface" />

        </LinearLayout>

        <!-- 分隔線 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginVertical="8dp"
            android:layout_marginHorizontal="24dp"
            android:background="?attr/colorOutlineVariant"
            android:alpha="0.5" />

        <!-- 其他分組標題 -->
        <com.google.android.material.textview.MaterialTextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="其他"
            android:textAppearance="@style/TextAppearance.Material3.LabelMedium"
            android:textColor="?attr/colorPrimary"
            android:paddingHorizontal="24dp"
            android:paddingVertical="8dp" />

        <!-- 邀請朋友 -->
        <LinearLayout
            android:id="@+id/menuInviteFriends"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingHorizontal="24dp"
            android:background="?attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="16dp"
                android:src="@drawable/ic_share_24"
                app:tint="?attr/colorOnSurfaceVariant"
                android:contentDescription="@string/invite_friends_description" />

            <com.google.android.material.textview.MaterialTextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/menu_invite_friends"
                android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                android:textColor="?attr/colorOnSurface" />

        </LinearLayout>

        <!-- 更新紀錄 -->
        <LinearLayout
            android:id="@+id/menuUpdateHistory"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingHorizontal="24dp"
            android:background="?attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="16dp"
                android:src="@drawable/ic_history_24"
                app:tint="?attr/colorOnSurfaceVariant"
                android:contentDescription="@string/update_history_description" />

            <com.google.android.material.textview.MaterialTextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/menu_update_history"
                android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                android:textColor="?attr/colorOnSurface" />

        </LinearLayout>

        <!-- 關於此APP -->
        <LinearLayout
            android:id="@+id/menuAbout"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingHorizontal="24dp"
            android:background="?attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="16dp"
                android:src="@drawable/ic_info_24"
                app:tint="?attr/colorOnSurfaceVariant"
                android:contentDescription="@string/about_description" />

            <com.google.android.material.textview.MaterialTextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/menu_about"
                android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                android:textColor="?attr/colorOnSurface" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout> 