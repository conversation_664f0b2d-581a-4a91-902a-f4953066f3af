<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:paddingHorizontal="16dp"
    android:paddingVertical="12dp"
    android:layout_marginTop="8dp">

    <!-- 分類圖標 -->
    <ImageView
        android:id="@+id/ivSectionIcon"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:src="@drawable/ic_apps_24"
        android:layout_marginEnd="12dp"
        app:tint="?attr/colorOnSurfaceVariant" />

    <!-- 分類標題 -->
    <TextView
        android:id="@+id/tvSectionTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="應用程式"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="?attr/colorOnSurface" />

    <!-- 數量標籤 -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:cardCornerRadius="12dp"
        app:cardElevation="0dp"
        app:cardBackgroundColor="?attr/colorSecondaryContainer">

        <TextView
            android:id="@+id/tvSectionCount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="1"
            android:textSize="12sp"
            android:textStyle="bold"
            android:textColor="?attr/colorOnSecondaryContainer"
            android:paddingHorizontal="8dp"
            android:paddingVertical="4dp"
            android:minWidth="24dp"
            android:gravity="center" />

    </com.google.android.material.card.MaterialCardView>

</LinearLayout>
