<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?android:attr/colorBackground"
    tools:context=".QAActivity">

    <!-- App Bar -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/colorPrimary">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:title="Q&amp;A(常見問題)"
            app:titleTextColor="?attr/colorOnPrimary"
            app:navigationIcon="@drawable/ic_arrow_back_24"
            app:navigationIconTint="?attr/colorOnPrimary" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- 內容區域 -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- Q1: 排程沒有自動執行嗎？ -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardElevation="2dp"
                app:cardCornerRadius="12dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <!-- 問題標題 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="12dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="12dp"
                            android:src="@drawable/ic_help_24"
                            app:tint="?attr/colorPrimary"
                            android:contentDescription="問題" />

                        <com.google.android.material.textview.MaterialTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Q1"
                            android:textAppearance="@style/TextAppearance.Material3.LabelLarge"
                            android:textColor="?attr/colorPrimary" />

                    </LinearLayout>

                    <!-- 問題內容 -->
                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="排程沒有自動執行嗎？"
                        android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                        android:textColor="?attr/colorOnSurface"
                        android:layout_marginBottom="16dp" />

                    <!-- 答案標題 -->
                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="💡 解答"
                        android:textAppearance="@style/TextAppearance.Material3.TitleSmall"
                        android:textColor="?attr/colorSecondary"
                        android:layout_marginBottom="8dp" />

                    <!-- 答案內容 -->
                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="AutoLaunch 在 Android 系統上需要特殊權限才能在背景執行排程。如果您的排程沒有自動執行，請檢查以下設定：\n\n📱 必要權限設定：\n• 自動啟動權限：允許 AutoLaunch 在背景啟動\n• 電池優化白名單：防止系統休眠時關閉 App\n• 通知權限：接收排程執行通知\n\n⚠️ 重要提醒：\n由於 Android 系統限制，排程只有在 AutoLaunch 處於前台運行時才能正常執行。建議在需要執行排程的時間前，先開啟 AutoLaunch 應用程式。\n\n🔧 故障排除步驟：\n1. 點擊主頁面的歡迎卡片檢查權限狀態\n2. 確保所有必要權限都已授予\n3. 在排程執行時間前開啟 AutoLaunch\n4. 查看「系統紀錄」了解執行狀況"
                        android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                        android:textColor="?attr/colorOnSurface"
                        android:lineSpacingExtra="4dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
