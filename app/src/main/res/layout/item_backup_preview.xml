<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:paddingStart="16dp"
    android:paddingEnd="16dp"
    android:paddingTop="8dp"
    android:paddingBottom="8dp">

    <TextView
        android:id="@+id/tv_schedule_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textAppearance="?attr/textAppearanceListItem"
        android:textColor="?attr/colorOnSurface"
        tools:text="遊戲時間" />

    <TextView
        android:id="@+id/tv_app_name_to_launch"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:textAppearance="?attr/textAppearanceListItemSecondary"
        android:textColor="?attr/colorOnSurfaceVariant"
        tools:text="啟動: Mobile Legends" />

</LinearLayout> 