<?xml version="1.0" encoding="utf-8"?>
<LinearLayout 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:paddingHorizontal="16dp"
    android:paddingVertical="12dp"
    android:background="?attr/colorSurfaceVariant">

    <!-- 展開/收起圖標 -->
    <ImageView
        android:id="@+id/ivExpandCollapse"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_marginEnd="8dp"
        android:src="@drawable/ic_arrow_forward_24"
        android:rotation="90"
        android:alpha="0.6"
        android:visibility="gone" />

    <!-- 日期 -->
    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/tvDate"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
        android:textColor="?attr/colorOnSurfaceVariant"
        android:textStyle="bold"
        tools:text="星期四 22 六月 2025" />

    <!-- 星期幾 -->
    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/tvDayOfWeek"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
        android:textColor="?attr/colorOnSurfaceVariant"
        tools:text="星期四" />

</LinearLayout>
