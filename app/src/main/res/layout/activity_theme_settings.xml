<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="?android:attr/colorBackground">

    <!-- Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorSurface"
        android:elevation="4dp"
        app:title="@string/theme_settings_title"
        app:titleTextColor="?attr/colorOnSurface"
        app:navigationIcon="@drawable/ic_arrow_back_24"
        app:navigationIconTint="?attr/colorOnSurface" />

    <!-- 內容區域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <!-- Tab Layout -->
        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tabLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="?attr/colorSurface"
            app:tabMode="fixed"
            app:tabGravity="fill"
            app:tabIndicatorColor="?attr/colorPrimary"
            app:tabSelectedTextColor="?attr/colorPrimary"
            app:tabTextColor="?attr/colorOnSurface" />

        <!-- ViewPager2 -->
        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/viewPager"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </LinearLayout>

</LinearLayout>
