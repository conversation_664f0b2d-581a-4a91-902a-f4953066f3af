<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:context=".AppSelectorActivity">

    <!-- App Bar Layout -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fitsSystemWindows="true">

        <!-- Toolbar -->
        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:title="選擇應用程式"
            app:titleTextColor="?attr/colorOnPrimary"
            app:navigationIcon="@drawable/ic_arrow_back" />

        <!-- Search View Card -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp"
            app:strokeWidth="0dp">

            <androidx.appcompat.widget.SearchView
                android:id="@+id/searchView"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:background="@android:color/transparent"
                app:iconifiedByDefault="false"
                app:queryHint="搜尋應用程式..."
                app:searchIcon="@drawable/ic_search"
                app:closeIcon="@drawable/ic_close" />

        </com.google.android.material.card.MaterialCardView>

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Progress Bar -->
            <com.google.android.material.progressindicator.LinearProgressIndicator
                android:id="@+id/progressBar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:indeterminate="true"
                android:visibility="gone"
                app:indicatorColor="?attr/colorPrimary" />

            <!-- Apps RecyclerView -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerViewApps"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clipToPadding="false"
                android:paddingTop="8dp"
                android:paddingBottom="16dp"
                tools:listitem="@layout/item_app_selector" />

            <!-- Empty State Layout -->
            <LinearLayout
                android:id="@+id/emptyStateLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="64dp"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="32dp"
                android:visibility="gone">

                <ImageView
                    android:layout_width="96dp"
                    android:layout_height="96dp"
                    android:layout_marginBottom="16dp"
                    android:alpha="0.6"
                    android:src="@drawable/ic_search"
                    app:tint="?attr/colorOnSurfaceVariant" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="找不到應用程式"
                    android:textAppearance="?attr/textAppearanceHeadlineSmall"
                    android:textColor="?attr/colorOnSurfaceVariant" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="請嘗試使用不同的搜尋關鍵字"
                    android:textAppearance="?attr/textAppearanceBodyMedium"
                    android:textColor="?attr/colorOnSurfaceVariant" />

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout> 