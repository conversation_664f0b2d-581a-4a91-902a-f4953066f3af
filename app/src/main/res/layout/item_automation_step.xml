<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardElevation="2dp"
    app:cardCornerRadius="8dp"
    app:strokeWidth="1dp"
    app:strokeColor="?attr/colorOutlineVariant">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical">

        <!-- 拖拽手柄 -->
        <ImageView
            android:id="@+id/ivDragHandle"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginEnd="8dp"
            android:src="@drawable/ic_drag_handle_24"
            android:contentDescription="拖拽排序"
            android:alpha="0.6"
            app:tint="?attr/colorOnSurfaceVariant" />

        <!-- 步驟順序 -->
        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/tvStepOrder"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginEnd="12dp"
            android:background="@drawable/circle_background"
            android:gravity="center"
            android:text="1"
            android:textAppearance="@style/TextAppearance.Material3.LabelMedium"
            android:textColor="?attr/colorOnPrimary"
            android:textStyle="bold"
            tools:text="1" />

        <!-- 步驟內容 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- 步驟名稱 -->
            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/tvStepName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                android:textColor="?attr/colorOnSurface"
                android:maxLines="2"
                android:ellipsize="end"
                tools:text="點擊「登入」按鈕" />

            <!-- 步驟詳情 -->
            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/tvStepDetails"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                android:textColor="?attr/colorOnSurfaceVariant"
                android:maxLines="2"
                android:ellipsize="end"
                android:visibility="visible"
                tools:text="點擊 • 目標：「登入」• 重試：3次" />

        </LinearLayout>

        <!-- 步驟狀態和操作 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginStart="12dp">

            <!-- 可選標記 -->
            <com.google.android.material.chip.Chip
                android:id="@+id/chipOptional"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="可選"
                android:textSize="10sp"
                android:visibility="gone"
                android:layout_marginEnd="8dp"
                style="@style/Widget.Material3.Chip.Assist"
                tools:visibility="visible" />

            <!-- 啟用狀態開關 -->
            <com.google.android.material.materialswitch.MaterialSwitch
                android:id="@+id/switchEnabled"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:checked="true" />

            <!-- 更多操作按鈕 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnMoreActions"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:insetTop="0dp"
                android:insetBottom="0dp"
                android:insetLeft="0dp"
                android:insetRight="0dp"
                app:icon="@drawable/ic_more_vert_24"
                app:iconSize="20dp"
                app:iconPadding="0dp"
                style="@style/Widget.Material3.Button.IconButton" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
