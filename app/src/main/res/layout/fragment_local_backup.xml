<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/colorSurface">

    <!-- 創建備份按鈕 -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnCreateBackup"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="16dp"
        android:text="@string/create_local_backup"
        app:icon="@drawable/ic_save_24"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 備份文件列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewBackups"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="16dp"
        android:clipToPadding="false"
        android:paddingBottom="80dp"
        app:layout_constraintBottom_toTopOf="@+id/cardBackupLocation"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/btnCreateBackup"
        tools:listitem="@layout/item_backup_file" />

    <!-- 空狀態提示 -->
    <LinearLayout
        android:id="@+id/layoutEmptyState"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/cardBackupLocation"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/btnCreateBackup">

        <ImageView
            android:layout_width="96dp"
            android:layout_height="96dp"
            android:layout_marginBottom="16dp"
            android:alpha="0.6"
            android:contentDescription="沒有備份文件"
            android:src="@drawable/ic_storage_24"
            app:tint="?attr/colorOnSurfaceVariant" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/no_local_backup_files"
            android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
            android:textColor="?attr/colorOnSurfaceVariant" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="@string/tap_to_create_first_backup"
            android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
            android:textColor="?attr/colorOnSurfaceVariant" />

    </LinearLayout>

    <!-- 自動備份位置卡片 -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/cardBackupLocation"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="16dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="2dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/backup_location_title"
                android:textAppearance="@style/TextAppearance.Material3.TitleSmall"
                android:textColor="?attr/colorOnSurface" />

            <TextView
                android:id="@+id/tvBackupLocation"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                android:textColor="?attr/colorOnSurfaceVariant"
                android:textIsSelectable="true"
                tools:text="/storage/emulated/0/Android/data/com.example.autolaunch/files/AutoBackup/" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnCopyPath"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:layout_marginTop="8dp"
                android:text="@string/copy_path"
                app:icon="@drawable/ic_copy_24" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

</androidx.constraintlayout.widget.ConstraintLayout>
