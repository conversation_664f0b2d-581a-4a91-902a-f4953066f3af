<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/colorPrimary"
    android:padding="32dp">

    <!-- 圖示 -->
    <ImageView
        android:id="@+id/ivIcon"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:layout_marginBottom="32dp"
        android:contentDescription="@string/welcome_icon"
        android:scaleType="centerInside"
        android:src="@drawable/ic_rocket_launch_24"
        android:tint="?attr/colorOnPrimary"
        app:layout_constraintBottom_toTopOf="@+id/tvTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <!-- 標題 -->
    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:gravity="center"
        android:text="@string/welcome_title"
        android:textColor="?attr/colorOnPrimary"
        android:textSize="28sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@+id/tvDescription"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ivIcon"
        tools:text="歡迎使用 AutoLaunch" />

    <!-- 描述 -->
    <TextView
        android:id="@+id/tvDescription"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:lineSpacingExtra="4dp"
        android:text="@string/welcome_description"
        android:textColor="?attr/colorOnPrimary"
        android:textSize="16sp"
        android:alpha="0.9"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle"
        tools:text="智能排程，自動啟動\n讓您的手機更聰明地工作" />

</androidx.constraintlayout.widget.ConstraintLayout>
