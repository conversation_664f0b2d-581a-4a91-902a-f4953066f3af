<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cardBackupFile"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:id="@+id/tvFileName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="備份文件名稱"
            android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
            android:textColor="?attr/colorOnSurface"
            android:maxLines="2"
            android:ellipsize="end" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="8dp">

            <TextView
                android:id="@+id/tvFileInfo"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="文件信息"
                android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                android:textColor="?attr/colorOnSurfaceVariant" />

            <TextView
                android:id="@+id/tvFileSize"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="文件大小"
                android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                android:textColor="?attr/colorOnSurfaceVariant" />

        </LinearLayout>

        <TextView
            android:id="@+id/tvCreatedTime"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="創建時間"
            android:textAppearance="@style/TextAppearance.Material3.BodySmall"
            android:textColor="?attr/colorOnSurfaceVariant" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="12dp">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnRestore"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:text="恢復"
                app:icon="@drawable/ic_cloud_download_24" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnDelete"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="刪除"
                app:icon="@drawable/ic_delete_24"
                app:iconTint="?attr/colorError" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
