<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:id="@+id/tvDialogTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="備份文件列表"
        android:textAppearance="@style/TextAppearance.Material3.TitleLarge"
        android:textColor="?attr/colorOnSurface"
        android:layout_marginBottom="16dp" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewFiles"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:maxHeight="400dp"
        android:scrollbars="vertical" />

    <TextView
        android:id="@+id/tvEmptyMessage"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="沒有找到備份文件"
        android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
        android:textColor="?attr/colorOnSurfaceVariant"
        android:gravity="center"
        android:padding="32dp"
        android:visibility="gone" />

</LinearLayout>
