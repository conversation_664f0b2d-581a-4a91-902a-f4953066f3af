<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingTop="16dp"
    android:paddingBottom="8dp">

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/tvSectionTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="區段標題"
        android:textAppearance="@style/TextAppearance.Material3.TitleSmall"
        android:textColor="?attr/colorPrimary"
        android:textStyle="bold" />

    <!-- 分隔線 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="8dp"
        android:background="?attr/colorOutlineVariant"
        android:alpha="0.5" />

</LinearLayout>
