<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <!-- URL 輸入區域 -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="2dp"
        app:strokeColor="?attr/colorOutline"
        app:strokeWidth="1dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp">

            <!-- 標題 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="16dp">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_link_24"
                    android:layout_marginEnd="12dp"
                    app:tint="?attr/colorPrimary" />

                <com.google.android.material.textview.MaterialTextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="網址設定"
                    android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                    android:textColor="?attr/colorOnSurface" />

            </LinearLayout>

            <!-- URL 輸入框 -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/tilUrl"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="輸入網址"
                app:helperText="例如：https://www.google.com"
                app:helperTextEnabled="true"
                app:startIconDrawable="@drawable/ic_link_24"
                app:endIconMode="clear_text"
                style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etUrl"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textUri"
                    android:maxLines="3"
                    android:scrollHorizontally="false" />

            </com.google.android.material.textfield.TextInputLayout>



            <!-- 預覽按鈕 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnPreviewUrl"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:text="預覽網址"
                app:icon="@drawable/ic_preview_24"
                style="@style/Widget.Material3.Button.TextButton" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- URL 驗證狀態 -->
    <LinearLayout
        android:id="@+id/layoutUrlStatus"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="12dp"
        android:background="?attr/colorSurfaceVariant"
        android:visibility="gone">

        <ImageView
            android:id="@+id/ivUrlStatus"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_marginEnd="8dp"
            android:src="@drawable/ic_check_circle_24"
            app:tint="?attr/colorPrimary" />

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/tvUrlStatus"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="網址格式正確"
            android:textAppearance="@style/TextAppearance.Material3.BodySmall"
            android:textColor="?attr/colorOnSurfaceVariant" />

    </LinearLayout>

</LinearLayout>
