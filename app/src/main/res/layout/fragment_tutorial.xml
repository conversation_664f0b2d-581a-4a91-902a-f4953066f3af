<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="24dp">

        <!-- 歡迎卡片 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardElevation="2dp"
            app:cardCornerRadius="16dp"
            app:cardBackgroundColor="?attr/colorPrimaryContainer">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="24dp"
                android:gravity="center">

                <ImageView
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:layout_marginBottom="16dp"
                    android:src="@mipmap/ic_launcher"
                    android:contentDescription="@string/tutorial_app_icon_desc" />

                <com.google.android.material.textview.MaterialTextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/tutorial_welcome_title"
                    android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall"
                    android:textColor="?attr/colorOnPrimaryContainer"
                    android:gravity="center"
                    android:layout_marginBottom="8dp" />

                <com.google.android.material.textview.MaterialTextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/tutorial_welcome_subtitle"
                    android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                    android:textColor="?attr/colorOnPrimaryContainer"
                    android:gravity="center" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 步驟1: 新增排程 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardElevation="2dp"
            app:cardCornerRadius="12dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="20dp">

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_marginEnd="16dp"
                    app:cardCornerRadius="20dp"
                    app:cardBackgroundColor="?attr/colorPrimary">

                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:text="1"
                        android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                        android:textColor="?attr/colorOnPrimary"
                        android:textStyle="bold"
                        android:gravity="center" />

                </com.google.android.material.card.MaterialCardView>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/tutorial_step_add_schedule"
                        android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                        android:textColor="?attr/colorOnSurface"
                        android:layout_marginBottom="8dp" />

                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/tutorial_step_add_schedule_desc"
                        android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                        android:textColor="?attr/colorOnSurfaceVariant" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 步驟2: 選擇應用程式 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardElevation="2dp"
            app:cardCornerRadius="12dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="20dp">

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_marginEnd="16dp"
                    app:cardCornerRadius="20dp"
                    app:cardBackgroundColor="?attr/colorPrimary">

                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:text="2"
                        android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                        android:textColor="?attr/colorOnPrimary"
                        android:textStyle="bold"
                        android:gravity="center" />

                </com.google.android.material.card.MaterialCardView>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/tutorial_step_select_app"
                        android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                        android:textColor="?attr/colorOnSurface"
                        android:layout_marginBottom="8dp" />

                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/tutorial_step_select_app_desc"
                        android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                        android:textColor="?attr/colorOnSurfaceVariant" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 步驟3: 設定時間 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardElevation="2dp"
            app:cardCornerRadius="12dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="20dp">

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_marginEnd="16dp"
                    app:cardCornerRadius="20dp"
                    app:cardBackgroundColor="?attr/colorPrimary">

                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:text="3"
                        android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                        android:textColor="?attr/colorOnPrimary"
                        android:textStyle="bold"
                        android:gravity="center" />

                </com.google.android.material.card.MaterialCardView>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="設定時間"
                        android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                        android:textColor="?attr/colorOnSurface"
                        android:layout_marginBottom="8dp" />

                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="選擇您希望應用程式自動啟動的時間，支援精確到分鐘的時間設定。"
                        android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                        android:textColor="?attr/colorOnSurfaceVariant" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 步驟4: 選擇重複模式 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardElevation="2dp"
            app:cardCornerRadius="12dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="20dp">

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_marginEnd="16dp"
                    app:cardCornerRadius="20dp"
                    app:cardBackgroundColor="?attr/colorPrimary">

                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:text="4"
                        android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                        android:textColor="?attr/colorOnPrimary"
                        android:textStyle="bold"
                        android:gravity="center" />

                </com.google.android.material.card.MaterialCardView>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="選擇重複模式"
                        android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                        android:textColor="?attr/colorOnSurface"
                        android:layout_marginBottom="8dp" />

                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="根據需求選擇單次執行、每日重複或自定義週期重複模式。"
                        android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                        android:textColor="?attr/colorOnSurfaceVariant" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 管理功能 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardElevation="2dp"
            app:cardCornerRadius="12dp"
            app:cardBackgroundColor="?attr/colorTertiaryContainer">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <com.google.android.material.textview.MaterialTextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/tutorial_management_functions"
                    android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                    android:textColor="?attr/colorOnTertiaryContainer"
                    android:layout_marginBottom="12dp" />

                <com.google.android.material.textview.MaterialTextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/tutorial_management_desc"
                    android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                    android:textColor="?attr/colorOnTertiaryContainer"
                    android:lineSpacingExtra="4dp" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 小提示 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardElevation="2dp"
            app:cardCornerRadius="12dp"
            app:cardBackgroundColor="?attr/colorSecondaryContainer">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="20dp">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginEnd="16dp"
                    android:src="@drawable/ic_info_24"
                    app:tint="?attr/colorOnSecondaryContainer"
                    android:contentDescription="提示" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/tutorial_tips_title"
                        android:textAppearance="@style/TextAppearance.Material3.TitleSmall"
                        android:textColor="?attr/colorOnSecondaryContainer"
                        android:layout_marginBottom="8dp" />

                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="• 請確保手機電池優化設定允許AutoLaunch在背景運行\n• 建議將AutoLaunch加入白名單以確保排程正常執行\n• 向左滑動排程項目可快速刪除排程\n• 長按排程項目可拖拽調整顯示順序\n• 您可以隨時在主頁面編輯或管理排程"
                        android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                        android:textColor="?attr/colorOnSecondaryContainer" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

</androidx.core.widget.NestedScrollView>
