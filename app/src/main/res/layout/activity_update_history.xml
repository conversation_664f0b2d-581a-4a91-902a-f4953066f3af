<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?android:attr/colorBackground"
    tools:context=".UpdateHistoryActivity">

    <!-- App Bar -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/colorPrimary">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:title="更新紀錄"
            app:titleTextColor="?attr/colorOnPrimary"
            app:navigationIcon="@drawable/ic_arrow_back_24"
            app:navigationIconTint="?attr/colorOnPrimary" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- 內容區域 -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- 版本 1.0.0 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardElevation="2dp"
                app:cardCornerRadius="12dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <!-- 版本標題 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="12dp">

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="12dp"
                            app:cardCornerRadius="6dp"
                            app:cardBackgroundColor="?attr/colorPrimary">

                            <com.google.android.material.textview.MaterialTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="v1.0.0"
                                android:textAppearance="@style/TextAppearance.Material3.LabelMedium"
                                android:textColor="?attr/colorOnPrimary"
                                android:textStyle="bold"
                                android:paddingHorizontal="8dp"
                                android:paddingVertical="4dp" />

                        </com.google.android.material.card.MaterialCardView>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <com.google.android.material.textview.MaterialTextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="首次發布"
                                android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                                android:textColor="?attr/colorOnSurface" />

                            <com.google.android.material.textview.MaterialTextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="2025年6月3日"
                                android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                                android:textColor="?attr/colorOnSurfaceVariant" />

                        </LinearLayout>

                    </LinearLayout>

                    <!-- 更新內容 -->
                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="🎉 新功能"
                        android:textAppearance="@style/TextAppearance.Material3.TitleSmall"
                        android:textColor="?attr/colorPrimary"
                        android:layout_marginBottom="8dp" />

                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="• 自動啟動應用程式功能\n• 支援單次、每日、每週重複模式\n• 自定義任務名稱\n• 美觀的Material Design 3界面\n• 執行時間顯示和追蹤\n• 排程管理和編輯功能"
                        android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                        android:textColor="?attr/colorOnSurface"
                        android:layout_marginBottom="16dp" />

                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="✨ 界面優化"
                        android:textAppearance="@style/TextAppearance.Material3.TitleSmall"
                        android:textColor="?attr/colorSecondary"
                        android:layout_marginBottom="8dp" />

                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="• 美化歡迎卡片，採用漸層背景設計\n• 添加脈動動畫效果\n• 優化排程項目布局和間距\n• 重新設計App圖標\n• 新增漢堡選單導航"
                        android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                        android:textColor="?attr/colorOnSurface" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 即將推出 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardElevation="2dp"
                app:cardCornerRadius="12dp"
                app:cardBackgroundColor="?attr/colorTertiaryContainer">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="12dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="12dp"
                            android:src="@drawable/ic_schedule_24"
                            app:tint="?attr/colorOnTertiaryContainer"
                            android:contentDescription="即將推出" />

                        <com.google.android.material.textview.MaterialTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="即將推出"
                            android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                            android:textColor="?attr/colorOnTertiaryContainer" />

                    </LinearLayout>

                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="🔮 計劃中的功能"
                        android:textAppearance="@style/TextAppearance.Material3.TitleSmall"
                        android:textColor="?attr/colorOnTertiaryContainer"
                        android:layout_marginBottom="8dp" />

                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="• 月重複模式支援\n• 排程執行統計\n• 深色模式主題\n• 排程匯入/匯出功能\n• 更多自定義選項\n• 小工具支援"
                        android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                        android:textColor="?attr/colorOnTertiaryContainer" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout> 