<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/drawerLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?android:attr/colorBackground"
    android:fitsSystemWindows="true"
    tools:context=".MainActivity">

    <!-- 主內容區域 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="?android:attr/colorBackground">

        <!-- 頂部工具列 -->
        <LinearLayout
            android:id="@+id/topToolbar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingTop="8dp"
            android:paddingBottom="4dp"
            android:paddingStart="12dp"
            android:paddingEnd="12dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <!-- 漢堡選單按鈕 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnMenu"
                style="@style/Widget.Material3.Button.IconButton"
                android:layout_width="40dp"
                android:layout_height="40dp"
                app:icon="@drawable/ic_menu_24"
                app:iconTint="?attr/colorOnSurface"
                android:contentDescription="@string/menu_description" />

            <!-- AutoLaunch 標題 -->
            <com.google.android.material.textview.MaterialTextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="AutoLaunch"
                android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall"
                android:textColor="?attr/colorOnSurface"
                android:textStyle="bold"
                android:gravity="center" />

            <!-- 右側佔位符，確保標題完全置中 -->
            <View
                android:layout_width="40dp"
                android:layout_height="40dp" />

        </LinearLayout>


        <!-- Tab Layout -->
        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tabLayout"
            android:layout_width="0dp"
            android:layout_height="56dp"
            android:layout_marginTop="8dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:background="@android:color/transparent"
            android:visibility="gone"
            app:tabTextColor="?attr/colorOnSurfaceVariant"
            app:tabSelectedTextColor="?attr/colorPrimary"
            app:tabIndicatorColor="?attr/colorPrimary"
            app:tabIndicatorHeight="3dp"
            app:tabMode="fixed"
            app:tabGravity="fill"
            app:tabTextAppearance="@style/CustomTabTextStyle"
            app:tabPaddingTop="8dp"
            app:tabPaddingBottom="8dp"
            app:layout_constraintTop_toBottomOf="@+id/welcomeCard"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <com.google.android.material.tabs.TabItem
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/tab_app" />

            <com.google.android.material.tabs.TabItem
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/tab_web" />

        </com.google.android.material.tabs.TabLayout>

        <!-- 簡單的分隔線 -->
        <View
            android:id="@+id/tabDivider"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:background="?attr/colorOutlineVariant"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@+id/tabLayout"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- ViewPager2 for tab content -->
        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/viewPager"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="16dp"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@+id/tabDivider"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- 統一排程列表 (保留作為備用) -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerViewSchedules"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="16dp"
            android:paddingHorizontal="16dp"
            android:paddingBottom="120dp"
            android:clipToPadding="false"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintTop_toBottomOf="@+id/welcomeCard"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            tools:listitem="@layout/item_schedule"
            tools:itemCount="5" />

        <!-- 空狀態卡片 (沒有排程時顯示) -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/emptyStateCard"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="24dp"
            android:layout_marginTop="32dp"
            android:layout_marginEnd="24dp"
            app:cardElevation="2dp"
            app:cardCornerRadius="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/topToolbar">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:padding="32dp">

                <ImageView
                    android:layout_width="96dp"
                    android:layout_height="96dp"
                    android:layout_marginBottom="24dp"
                    android:contentDescription="@string/empty_schedule_description"
                    android:src="@drawable/ic_schedule_24"
                    android:alpha="0.6"
                    app:tint="?attr/colorPrimary" />

                <com.google.android.material.textview.MaterialTextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/no_schedules_yet"
                    android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall"
                    android:textColor="?attr/colorOnSurface"
                    android:gravity="center"
                    android:layout_marginBottom="12dp" />

                <com.google.android.material.textview.MaterialTextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/empty_schedule_description_detail"
                    android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    android:gravity="center"
                    android:layout_marginBottom="24dp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnCreateFirstSchedule"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/create_first_schedule"
                    app:icon="@drawable/ic_add_24" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>
        <!-- 美化的歡迎提示卡片 (有排程時顯示) -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/welcomeCard"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="16dp"
            android:visibility="gone"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/topToolbar">

            <!-- 漸層背景容器 -->
            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/gradient_welcome_card">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:padding="12dp">

                    <!-- 動畫狀態圖標 -->
                    <FrameLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="12dp">

                        <ImageView
                            android:id="@+id/statusIcon"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_status_warning"
                            android:contentDescription="@string/status_icon_warning" />

                    </FrameLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <com.google.android.material.textview.MaterialTextView
                            android:id="@+id/tvWelcomeMessage"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/welcome_message_permission_needed"
                            android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                            android:textColor="#FFFFFF"
                            android:shadowColor="#40000000"
                            android:shadowDx="1"
                            android:shadowDy="1"
                            android:shadowRadius="1" />

                        <com.google.android.material.textview.MaterialTextView
                            android:id="@+id/tvPermissionHint"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="1dp"
                            android:text="@string/permission_hint_text_detail"
                            android:textAppearance="@style/TextAppearance.Material3.LabelSmall"
                            android:textColor="#E0FFFFFF"
                            android:shadowColor="#40000000"
                            android:shadowDx="1"
                            android:shadowDy="1"
                            android:shadowRadius="1" />

                    </LinearLayout>

                    <!-- 點擊指示器 -->
                    <ImageView
                        android:id="@+id/ivPermissionArrow"
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:layout_marginStart="6dp"
                        android:src="@drawable/ic_arrow_forward_24"
                        android:contentDescription="點擊查看"
                        android:alpha="0.8" />

                    <!-- 關閉按鈕 -->
                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnCloseWelcome"
                        style="@style/Widget.Material3.Button.IconButton"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:layout_marginStart="8dp"
                        android:layout_marginEnd="4dp"
                        app:icon="@drawable/ic_close_24"
                        app:iconTint="#FFFFFF"
                        app:iconSize="18dp"
                        android:contentDescription="關閉提示"
                        android:alpha="0.8" />

                </LinearLayout>

            </FrameLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 新增排程按鈕 -->
        <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
            android:id="@+id/fabAddSchedule"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="24dp"
            android:layout_marginBottom="80dp"
            android:text="@string/add_schedule"
            app:icon="@drawable/ic_add_24"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- 模糊覆蓋層 (選單顯示時使用) -->
        <View
            android:id="@+id/blurOverlay"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/blur_overlay_background"
            android:visibility="gone"
            android:clickable="false"
            android:focusable="false"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 左側導航抽屜 -->
    <include layout="@layout/navigation_drawer_menu" />

</androidx.drawerlayout.widget.DrawerLayout>