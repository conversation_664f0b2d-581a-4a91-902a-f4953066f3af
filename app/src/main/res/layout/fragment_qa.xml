<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="24dp">

        <!-- Q1: 排程沒有自動執行嗎？ -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardElevation="2dp"
            app:cardCornerRadius="12dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <!-- 問題標題 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="12dp"
                        android:src="@drawable/ic_help_24"
                        app:tint="?attr/colorPrimary"
                        android:contentDescription="問題" />

                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Q1"
                        android:textAppearance="@style/TextAppearance.Material3.LabelLarge"
                        android:textColor="?attr/colorPrimary" />

                </LinearLayout>

                <!-- 問題內容 -->
                <com.google.android.material.textview.MaterialTextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="排程沒有自動執行嗎？"
                    android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                    android:textColor="?attr/colorOnSurface"
                    android:layout_marginBottom="16dp" />

                <!-- 答案標題 -->
                <com.google.android.material.textview.MaterialTextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="💡 解答"
                    android:textAppearance="@style/TextAppearance.Material3.TitleSmall"
                    android:textColor="?attr/colorSecondary"
                    android:layout_marginBottom="8dp" />

                <!-- 答案內容 -->
                <com.google.android.material.textview.MaterialTextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="AutoLaunch 在 Android 系統上需要特殊權限才能在背景執行排程。如果您的排程沒有自動執行，請檢查以下設定：\n\n📱 必要權限設定：\n• 自動啟動權限：允許 AutoLaunch 在背景啟動\n• 電池優化白名單：防止系統休眠時關閉 App\n• 通知權限：接收排程執行通知\n\n⚠️ 重要提醒：\n由於 Android 系統限制，排程只有在 AutoLaunch 處於前台運行時才能正常執行。建議在需要執行排程的時間前，先開啟 AutoLaunch 應用程式。\n\n🔧 故障排除步驟：\n1. 點擊主頁面的歡迎卡片檢查權限狀態\n2. 確保所有必要權限都已授予\n3. 在排程執行時間前開啟 AutoLaunch\n4. 查看「系統紀錄」了解執行狀況"
                    android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                    android:textColor="?attr/colorOnSurface"
                    android:lineSpacingExtra="4dp" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Q2: 如何設定電池優化白名單？ -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardElevation="2dp"
            app:cardCornerRadius="12dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <!-- 問題標題 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="12dp"
                        android:src="@drawable/ic_help_24"
                        app:tint="?attr/colorPrimary"
                        android:contentDescription="問題" />

                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Q2"
                        android:textAppearance="@style/TextAppearance.Material3.LabelLarge"
                        android:textColor="?attr/colorPrimary" />

                </LinearLayout>

                <!-- 問題內容 -->
                <com.google.android.material.textview.MaterialTextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="如何設定電池優化白名單？"
                    android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                    android:textColor="?attr/colorOnSurface"
                    android:layout_marginBottom="16dp" />

                <!-- 答案標題 -->
                <com.google.android.material.textview.MaterialTextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="💡 解答"
                    android:textAppearance="@style/TextAppearance.Material3.TitleSmall"
                    android:textColor="?attr/colorSecondary"
                    android:layout_marginBottom="8dp" />

                <!-- 答案內容 -->
                <com.google.android.material.textview.MaterialTextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="電池優化白名單設定步驟：\n\n📋 一般步驟：\n1. 開啟手機「設定」\n2. 找到「電池」或「電源管理」\n3. 選擇「電池優化」或「應用程式電源管理」\n4. 找到「AutoLaunch」\n5. 選擇「不優化」或「允許背景活動」\n\n📱 不同品牌設定路徑：\n• Samsung：設定 → 電池與裝置維護 → 電池 → 背景應用程式限制\n• Huawei：設定 → 電池 → 應用程式啟動管理\n• Xiaomi：設定 → 電池與效能 → 應用程式電池用量\n• OPPO/OnePlus：設定 → 電池 → 電池優化\n\n💡 快速方法：\n點擊 AutoLaunch 主頁面的歡迎卡片，系統會自動引導您到正確的設定頁面。"
                    android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                    android:textColor="?attr/colorOnSurface"
                    android:lineSpacingExtra="4dp" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Q3: 支援哪些重複模式？ -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardElevation="2dp"
            app:cardCornerRadius="12dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <!-- 問題標題 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="12dp"
                        android:src="@drawable/ic_help_24"
                        app:tint="?attr/colorPrimary"
                        android:contentDescription="問題" />

                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Q3"
                        android:textAppearance="@style/TextAppearance.Material3.LabelLarge"
                        android:textColor="?attr/colorPrimary" />

                </LinearLayout>

                <!-- 問題內容 -->
                <com.google.android.material.textview.MaterialTextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="支援哪些重複模式？"
                    android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                    android:textColor="?attr/colorOnSurface"
                    android:layout_marginBottom="16dp" />

                <!-- 答案標題 -->
                <com.google.android.material.textview.MaterialTextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="💡 解答"
                    android:textAppearance="@style/TextAppearance.Material3.TitleSmall"
                    android:textColor="?attr/colorSecondary"
                    android:layout_marginBottom="8dp" />

                <!-- 答案內容 -->
                <com.google.android.material.textview.MaterialTextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="AutoLaunch 支援多種重複模式：\n\n🔄 重複選項：\n• 單次執行：只執行一次，執行後自動刪除\n• 每日重複：每天在指定時間執行\n• 自定義週期：選擇特定的星期幾執行\n\n📅 自定義週期範例：\n• 工作日：週一到週五\n• 週末：週六、週日\n• 特定日期：例如週一、週三、週五\n\n⏰ 時間設定：\n• 支援24小時制\n• 精確到分鐘\n• 可設定多個不同時間的排程"
                    android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                    android:textColor="?attr/colorOnSurface"
                    android:lineSpacingExtra="4dp" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Q4: 如何查看排程執行記錄？ -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardElevation="2dp"
            app:cardCornerRadius="12dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <!-- 問題標題 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="12dp"
                        android:src="@drawable/ic_help_24"
                        app:tint="?attr/colorPrimary"
                        android:contentDescription="問題" />

                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Q4"
                        android:textAppearance="@style/TextAppearance.Material3.LabelLarge"
                        android:textColor="?attr/colorPrimary" />

                </LinearLayout>

                <!-- 問題內容 -->
                <com.google.android.material.textview.MaterialTextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="如何查看排程執行記錄？"
                    android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                    android:textColor="?attr/colorOnSurface"
                    android:layout_marginBottom="16dp" />

                <!-- 答案標題 -->
                <com.google.android.material.textview.MaterialTextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="💡 解答"
                    android:textAppearance="@style/TextAppearance.Material3.TitleSmall"
                    android:textColor="?attr/colorSecondary"
                    android:layout_marginBottom="8dp" />

                <!-- 答案內容 -->
                <com.google.android.material.textview.MaterialTextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="查看排程執行記錄的方法：\n\n📋 系統紀錄功能：\n1. 開啟左側選單\n2. 點擊「系統紀錄」\n3. 查看詳細的執行記錄\n\n📊 記錄內容包括：\n• 排程執行時間\n• 執行結果（成功/失敗）\n• 錯誤訊息（如有）\n• 應用程式啟動狀態\n\n🔍 搜尋和篩選：\n• 使用搜尋功能快速找到特定記錄\n• 按日期篩選記錄\n• 按執行狀態篩選\n\n🗂️ 記錄管理：\n• 系統自動保留7天的記錄\n• 可匯出記錄進行分析\n• 支援清除舊記錄"
                    android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                    android:textColor="?attr/colorOnSurface"
                    android:lineSpacingExtra="4dp" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

</androidx.core.widget.NestedScrollView>
