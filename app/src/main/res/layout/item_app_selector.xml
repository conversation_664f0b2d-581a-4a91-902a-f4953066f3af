<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="16dp"
    android:layout_marginVertical="4dp"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp"
    app:rippleColor="?attr/colorPrimary"
    app:strokeWidth="0dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- App Icon -->
        <ImageView
            android:id="@+id/imageAppIcon"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="16dp"
            android:contentDescription="應用程式圖示"
            android:scaleType="centerCrop"
            tools:src="@drawable/ic_android" />

        <!-- App Info Container -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- App Name and System App Badge Container -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <!-- App Name -->
                <TextView
                    android:id="@+id/textAppName"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textAppearance="?attr/textAppearanceBodyLarge"
                    android:textColor="?attr/colorOnSurface"
                    android:textStyle="bold"
                    tools:text="Chrome" />

                <!-- System App Badge -->
                <com.google.android.material.chip.Chip
                    android:id="@+id/textSystemApp"
                    style="@style/Widget.Material3.Chip.Assist"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:text="系統應用"
                    android:textSize="10sp"
                    android:visibility="gone"
                    app:chipBackgroundColor="?attr/colorSecondaryContainer"
                    app:chipMinHeight="24dp"
                    app:chipStrokeWidth="0dp"
                    app:textEndPadding="8dp"
                    app:textStartPadding="8dp"
                    tools:visibility="visible" />

            </LinearLayout>

            <!-- Package Name -->
            <TextView
                android:id="@+id/textPackageName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:textAppearance="?attr/textAppearanceBodyMedium"
                android:textColor="?attr/colorOnSurfaceVariant"
                tools:text="com.android.chrome" />

            <!-- Version Info -->
            <TextView
                android:id="@+id/textVersionInfo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:textAppearance="?attr/textAppearanceBodySmall"
                android:textColor="?attr/colorOnSurfaceVariant"
                android:visibility="gone"
                tools:text="v108.0.5359.128"
                tools:visibility="visible" />

        </LinearLayout>

        <!-- Selection Indicator -->
        <ImageView
            android:id="@+id/imageSelectionIndicator"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="8dp"
            android:contentDescription="選擇指示器"
            android:src="@drawable/ic_arrow_forward"
            android:visibility="visible"
            app:tint="?attr/colorOnSurfaceVariant" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView> 