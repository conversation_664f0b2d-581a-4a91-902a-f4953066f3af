<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/colorSurface"
    tools:context=".WelcomeActivity">

    <!-- 跳過按鈕 -->
    <TextView
        android:id="@+id/btnSkip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="16dp"
        android:background="?android:attr/selectableItemBackgroundBorderless"
        android:clickable="true"
        android:focusable="true"
        android:padding="12dp"
        android:text="@string/skip"
        android:textColor="?attr/colorOnSurfaceVariant"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- ViewPager2 用於滑動頁面 -->
    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewPager"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"
        app:layout_constraintBottom_toTopOf="@+id/tabLayout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/btnSkip" />

    <!-- TabLayout 作為頁面指示器 -->
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tabLayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="32dp"
        app:layout_constraintBottom_toTopOf="@+id/navigationContainer"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:tabBackground="@drawable/tab_indicator_selector"
        app:tabGravity="center"
        app:tabIndicatorHeight="0dp"
        app:tabMaxWidth="16dp"
        app:tabMinWidth="16dp"
        app:tabRippleColor="@android:color/transparent" />

    <!-- 導航按鈕容器 -->
    <LinearLayout
        android:id="@+id/navigationContainer"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginEnd="24dp"
        android:layout_marginBottom="32dp"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <!-- 上一頁按鈕 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnPrevious"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minWidth="80dp"
            android:text="@string/previous"
            android:textColor="?attr/colorOnSurfaceVariant"
            android:textSize="16sp" />

        <!-- 空白區域 -->
        <View
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_weight="1" />

        <!-- 下一頁按鈕 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnNext"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minWidth="80dp"
            android:text="@string/next"
            android:textColor="?attr/colorPrimary"
            android:textSize="16sp" />

        <!-- 開始使用按鈕 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnGetStarted"
            style="@style/Widget.Material3.Button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="2"
            android:backgroundTint="?attr/colorPrimary"
            android:text="@string/get_started"
            android:textColor="?attr/colorOnPrimary"
            android:textSize="16sp"
            android:visibility="invisible" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
