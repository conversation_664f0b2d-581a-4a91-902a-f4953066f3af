<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?android:attr/colorBackground"
    tools:context=".AboutActivity">

    <!-- App Bar -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/colorPrimary">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:title="關於此APP"
            app:titleTextColor="?attr/colorOnPrimary"
            app:navigationIcon="@drawable/ic_arrow_back_24"
            app:navigationIconTint="?attr/colorOnPrimary" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- 內容區域 -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- App 資訊卡片 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardElevation="2dp"
                app:cardCornerRadius="16dp"
                app:cardBackgroundColor="?attr/colorPrimaryContainer">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="32dp"
                    android:gravity="center">

                    <ImageView
                        android:layout_width="96dp"
                        android:layout_height="96dp"
                        android:layout_marginBottom="20dp"
                        android:src="@mipmap/ic_launcher"
                        android:contentDescription="AutoLaunch圖標" />

                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/app_name"
                        android:textAppearance="@style/TextAppearance.Material3.HeadlineMedium"
                        android:textColor="?attr/colorOnPrimaryContainer"
                        android:gravity="center"
                        android:layout_marginBottom="8dp" />

                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/tvAppVersion"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="版本 1.0.0"
                        android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                        android:textColor="?attr/colorOnPrimaryContainer"
                        android:gravity="center"
                        android:layout_marginBottom="16dp" />

                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="讓您的手機在指定時間自動啟動應用程式"
                        android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                        android:textColor="?attr/colorOnPrimaryContainer"
                        android:gravity="center" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 功能特色 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardElevation="2dp"
                app:cardCornerRadius="12dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="✨ 功能特色"
                        android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                        android:textColor="?attr/colorOnSurface"
                        android:layout_marginBottom="16dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="12dp">

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_marginEnd="12dp"
                            android:layout_marginTop="2dp"
                            android:src="@drawable/ic_schedule_24"
                            app:tint="?attr/colorPrimary"
                            android:contentDescription="功能" />

                        <com.google.android.material.textview.MaterialTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="定時自動啟動任何已安裝的應用程式"
                            android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                            android:textColor="?attr/colorOnSurface" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="12dp">

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_marginEnd="12dp"
                            android:layout_marginTop="2dp"
                            android:src="@drawable/ic_history_24"
                            app:tint="?attr/colorPrimary"
                            android:contentDescription="功能" />

                        <com.google.android.material.textview.MaterialTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="支援單次、每日、每週重複模式"
                            android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                            android:textColor="?attr/colorOnSurface" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="12dp">

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_marginEnd="12dp"
                            android:layout_marginTop="2dp"
                            android:src="@drawable/ic_info_24"
                            app:tint="?attr/colorPrimary"
                            android:contentDescription="功能" />

                        <com.google.android.material.textview.MaterialTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="自定義任務名稱，更好地管理排程"
                            android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                            android:textColor="?attr/colorOnSurface" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_marginEnd="12dp"
                            android:layout_marginTop="2dp"
                            android:src="@drawable/ic_help_24"
                            app:tint="?attr/colorPrimary"
                            android:contentDescription="功能" />

                        <com.google.android.material.textview.MaterialTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="美觀的Material Design 3界面設計"
                            android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                            android:textColor="?attr/colorOnSurface" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 開發者資訊 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardElevation="2dp"
                app:cardCornerRadius="12dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="👨‍💻 開發者資訊"
                        android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                        android:textColor="?attr/colorOnSurface"
                        android:layout_marginBottom="16dp" />

                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="開發者"
                        android:textAppearance="@style/TextAppearance.Material3.LabelMedium"
                        android:textColor="?attr/colorOnSurfaceVariant"
                        android:layout_marginBottom="4dp" />

                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="AutoLaunch Team"
                        android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                        android:textColor="?attr/colorOnSurface"
                        android:layout_marginBottom="12dp" />

                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="聯絡方式"
                        android:textAppearance="@style/TextAppearance.Material3.LabelMedium"
                        android:textColor="?attr/colorOnSurfaceVariant"
                        android:layout_marginBottom="4dp" />

                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="<EMAIL>"
                        android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                        android:textColor="?attr/colorPrimary" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 技術資訊 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardElevation="2dp"
                app:cardCornerRadius="12dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="🔧 技術資訊"
                        android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                        android:textColor="?attr/colorOnSurface"
                        android:layout_marginBottom="16dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="8dp">

                        <com.google.android.material.textview.MaterialTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="開發語言"
                            android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                            android:textColor="?attr/colorOnSurfaceVariant" />

                        <com.google.android.material.textview.MaterialTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Kotlin"
                            android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                            android:textColor="?attr/colorOnSurface" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="8dp">

                        <com.google.android.material.textview.MaterialTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="最低Android版本"
                            android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                            android:textColor="?attr/colorOnSurfaceVariant" />

                        <com.google.android.material.textview.MaterialTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Android 7.0 (API 24)"
                            android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                            android:textColor="?attr/colorOnSurface" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <com.google.android.material.textview.MaterialTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="設計規範"
                            android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                            android:textColor="?attr/colorOnSurfaceVariant" />

                        <com.google.android.material.textview.MaterialTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Material Design 3"
                            android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                            android:textColor="?attr/colorOnSurface" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 更多功能 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardElevation="2dp"
                app:cardCornerRadius="12dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="📱 更多功能"
                        android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                        android:textColor="?attr/colorOnSurface"
                        android:layout_marginBottom="8dp" />

                    <!-- 更新紀錄 -->
                    <LinearLayout
                        android:id="@+id/menuUpdateHistory"
                        android:layout_width="match_parent"
                        android:layout_height="56dp"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingHorizontal="8dp"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="16dp"
                            android:src="@drawable/ic_history_24"
                            app:tint="?attr/colorOnSurfaceVariant"
                            android:contentDescription="@string/update_history_description" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <com.google.android.material.textview.MaterialTextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="@string/menu_update_history"
                                android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                                android:textColor="?attr/colorOnSurface" />

                            <com.google.android.material.textview.MaterialTextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="查看版本更新內容"
                                android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                                android:textColor="?attr/colorOnSurfaceVariant" />

                        </LinearLayout>

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:src="@drawable/ic_chevron_right_24"
                            app:tint="?attr/colorOnSurfaceVariant"
                            android:contentDescription="前往" />

                    </LinearLayout>

                    <!-- 邀請朋友 -->
                    <LinearLayout
                        android:id="@+id/menuInviteFriends"
                        android:layout_width="match_parent"
                        android:layout_height="56dp"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingHorizontal="8dp"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="16dp"
                            android:src="@drawable/ic_share_24"
                            app:tint="?attr/colorOnSurfaceVariant"
                            android:contentDescription="@string/invite_friends_description" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <com.google.android.material.textview.MaterialTextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="@string/menu_invite_friends"
                                android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                                android:textColor="?attr/colorOnSurface" />

                            <com.google.android.material.textview.MaterialTextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="分享給朋友使用"
                                android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                                android:textColor="?attr/colorOnSurfaceVariant" />

                        </LinearLayout>

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:src="@drawable/ic_chevron_right_24"
                            app:tint="?attr/colorOnSurfaceVariant"
                            android:contentDescription="前往" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 版權資訊 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardElevation="2dp"
                app:cardCornerRadius="12dp"
                app:cardBackgroundColor="?attr/colorSecondaryContainer">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp"
                    android:gravity="center">

                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="© 2025 AutoLaunch Team"
                        android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                        android:textColor="?attr/colorOnSecondaryContainer"
                        android:gravity="center"
                        android:layout_marginBottom="8dp" />

                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="感謝您使用 AutoLaunch！\n如有任何問題或建議，歡迎聯絡我們。"
                        android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                        android:textColor="?attr/colorOnSecondaryContainer"
                        android:gravity="center" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout> 