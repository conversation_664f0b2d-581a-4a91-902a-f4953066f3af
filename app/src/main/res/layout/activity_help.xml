<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?android:attr/colorBackground"
    tools:context=".HelpActivity">

    <!-- 工具列 -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="?attr/colorSurface"
        android:elevation="4dp"
        app:title="幫助"
        app:titleTextColor="?attr/colorOnSurface"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- 幫助內容 -->
    <ScrollView
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:fillViewport="true"
        app:layout_constraintTop_toBottomOf="@+id/toolbar"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 幫助選項區塊 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <!-- 區塊標題 -->
                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="幫助與支援"
                        android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                        android:textColor="?attr/colorOnSurface"
                        android:layout_marginBottom="8dp" />

                    <!-- 教學 -->
                    <LinearLayout
                        android:id="@+id/menuTutorial"
                        android:layout_width="match_parent"
                        android:layout_height="56dp"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingHorizontal="8dp"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="16dp"
                            android:src="@drawable/ic_help_24"
                            app:tint="?attr/colorOnSurfaceVariant"
                            android:contentDescription="@string/tutorial_description" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <com.google.android.material.textview.MaterialTextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="@string/menu_tutorial"
                                android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                                android:textColor="?attr/colorOnSurface" />

                            <com.google.android.material.textview.MaterialTextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="@string/tutorial_learn_more_desc"
                                android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                                android:textColor="?attr/colorOnSurfaceVariant" />

                        </LinearLayout>

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:src="@drawable/ic_chevron_right_24"
                            app:tint="?attr/colorOnSurfaceVariant"
                            android:contentDescription="前往" />

                    </LinearLayout>

                    <!-- Q&A(常見問題) -->
                    <LinearLayout
                        android:id="@+id/menuQA"
                        android:layout_width="match_parent"
                        android:layout_height="56dp"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingHorizontal="8dp"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="16dp"
                            android:src="@drawable/ic_help_24"
                            app:tint="?attr/colorOnSurfaceVariant"
                            android:contentDescription="@string/qa_description" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <com.google.android.material.textview.MaterialTextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="@string/menu_qa"
                                android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                                android:textColor="?attr/colorOnSurface" />

                            <com.google.android.material.textview.MaterialTextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="@string/tutorial_qa_desc"
                                android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                                android:textColor="?attr/colorOnSurfaceVariant" />

                        </LinearLayout>

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:src="@drawable/ic_chevron_right_24"
                            app:tint="?attr/colorOnSurfaceVariant"
                            android:contentDescription="前往" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>
