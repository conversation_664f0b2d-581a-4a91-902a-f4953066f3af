<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.navigation.NavigationView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/navigationView"
    android:layout_width="280dp"
    android:layout_height="match_parent"
    android:layout_gravity="start"
    android:background="?attr/colorSurface"
    android:fitsSystemWindows="true"
    android:layout_marginTop="24dp">

    <!-- 自定義標題區域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingTop="24dp"
        android:paddingBottom="16dp"
        android:paddingHorizontal="24dp">

        <!-- App 資訊標題 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingBottom="16dp">

            <ImageView
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginEnd="16dp"
                android:src="@mipmap/ic_launcher"
                android:contentDescription="App圖標" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <com.google.android.material.textview.MaterialTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="AutoLaunch"
                    android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall"
                    android:textColor="?attr/colorOnSurface"
                    android:textStyle="bold" />

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/tvAppVersion"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/app_version_default"
                    android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                    android:textColor="?attr/colorOnSurfaceVariant" />

            </LinearLayout>

        </LinearLayout>

        <!-- 運行狀態指示器 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingVertical="8dp"
            android:paddingHorizontal="16dp"
            android:background="@drawable/status_indicator_background">

            <ImageView
                android:id="@+id/statusIndicator"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_marginEnd="8dp"
                android:src="@drawable/ic_status_running"
                android:contentDescription="運行狀態指示器" />

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/tvRunningStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/autolaunch_running"
                android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                android:textColor="?attr/colorOnSurfaceVariant" />

        </LinearLayout>

        <!-- 分隔線 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginBottom="8dp"
            android:background="?attr/colorOutlineVariant"
            android:alpha="0.5" />

        <!-- 選單項目 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- 設定類分組標題 -->
            <com.google.android.material.textview.MaterialTextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/menu_group_settings"
                android:textAppearance="@style/TextAppearance.Material3.LabelMedium"
                android:textColor="?attr/colorPrimary"
                android:paddingHorizontal="16dp"
                android:paddingVertical="8dp"
                android:layout_marginTop="8dp" />

            <!-- 主題設定 -->
            <LinearLayout
                android:id="@+id/menuThemeSettings"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingHorizontal="16dp"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginEnd="16dp"
                    android:src="@drawable/ic_palette_24"
                    app:tint="?attr/colorOnSurfaceVariant"
                    android:contentDescription="@string/theme_settings_description" />

                <com.google.android.material.textview.MaterialTextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/menu_theme_settings_title"
                    android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                    android:textColor="?attr/colorOnSurface" />

            </LinearLayout>

            <!-- 語言設定 -->
            <LinearLayout
                android:id="@+id/menuLanguageSettings"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingHorizontal="16dp"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginEnd="16dp"
                    android:src="@drawable/ic_language_24"
                    app:tint="?attr/colorOnSurfaceVariant"
                    android:contentDescription="@string/language_settings_description" />

                <com.google.android.material.textview.MaterialTextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/menu_language_settings_title"
                    android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                    android:textColor="?attr/colorOnSurface" />

            </LinearLayout>

            <!-- 通知設定 -->
            <LinearLayout
                android:id="@+id/menuSettings"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingHorizontal="16dp"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginEnd="16dp"
                    android:src="@drawable/ic_notifications_24"
                    app:tint="?attr/colorOnSurfaceVariant"
                    android:contentDescription="@string/menu_notification_settings_description" />

                <com.google.android.material.textview.MaterialTextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/menu_notification_settings"
                    android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                    android:textColor="?attr/colorOnSurface" />

            </LinearLayout>

            <!-- 工具類分組標題 -->
            <com.google.android.material.textview.MaterialTextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/menu_group_tools"
                android:textAppearance="@style/TextAppearance.Material3.LabelMedium"
                android:textColor="?attr/colorPrimary"
                android:paddingHorizontal="16dp"
                android:paddingVertical="8dp"
                android:layout_marginTop="16dp" />

            <!-- 備份與還原 -->
            <LinearLayout
                android:id="@+id/menuBackupRestore"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingHorizontal="16dp"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginEnd="16dp"
                    android:src="@drawable/ic_backup_24"
                    app:tint="?attr/colorOnSurfaceVariant"
                    android:contentDescription="@string/backup_restore_description" />

                <com.google.android.material.textview.MaterialTextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/menu_backup_restore"
                    android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                    android:textColor="?attr/colorOnSurface" />

            </LinearLayout>

            <!-- 系統紀錄 -->
            <LinearLayout
                android:id="@+id/menuSystemLog"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingHorizontal="16dp"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginEnd="16dp"
                    android:src="@drawable/ic_history_24"
                    app:tint="?attr/colorOnSurfaceVariant"
                    android:contentDescription="@string/system_log_description" />

                <com.google.android.material.textview.MaterialTextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/menu_system_log"
                    android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                    android:textColor="?attr/colorOnSurface" />

            </LinearLayout>

            <!-- 分隔線 -->
            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginVertical="8dp"
                android:layout_marginHorizontal="16dp"
                android:background="?attr/colorOutlineVariant"
                android:alpha="0.5" />

            <!-- 幫助類分組標題 -->
            <com.google.android.material.textview.MaterialTextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/menu_group_help"
                android:textAppearance="@style/TextAppearance.Material3.LabelMedium"
                android:textColor="?attr/colorPrimary"
                android:paddingHorizontal="16dp"
                android:paddingVertical="8dp"
                android:layout_marginTop="8dp" />

            <!-- 教學 -->
            <LinearLayout
                android:id="@+id/menuTutorial"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingHorizontal="16dp"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginEnd="16dp"
                    android:src="@drawable/ic_help_24"
                    app:tint="?attr/colorOnSurfaceVariant"
                    android:contentDescription="@string/tutorial_description" />

                <com.google.android.material.textview.MaterialTextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/menu_tutorial"
                    android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                    android:textColor="?attr/colorOnSurface" />

            </LinearLayout>





            <!-- 邀請朋友 -->
            <LinearLayout
                android:id="@+id/menuInviteFriends"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingHorizontal="16dp"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginEnd="16dp"
                    android:src="@drawable/ic_share_24"
                    app:tint="?attr/colorOnSurfaceVariant"
                    android:contentDescription="@string/invite_friends_description" />

                <com.google.android.material.textview.MaterialTextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/menu_invite_friends"
                    android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                    android:textColor="?attr/colorOnSurface" />

            </LinearLayout>

            <!-- 關於此APP -->
            <LinearLayout
                android:id="@+id/menuAbout"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingHorizontal="16dp"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginEnd="16dp"
                    android:src="@drawable/ic_info_24"
                    app:tint="?attr/colorOnSurfaceVariant"
                    android:contentDescription="@string/about_description" />

                <com.google.android.material.textview.MaterialTextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/menu_about"
                    android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                    android:textColor="?attr/colorOnSurface" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.navigation.NavigationView>
