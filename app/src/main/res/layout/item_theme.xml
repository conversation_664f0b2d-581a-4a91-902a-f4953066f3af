<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cardTheme"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="12dp"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp"
    app:strokeColor="?attr/colorPrimary"
    app:strokeWidth="0dp"
    android:foreground="?attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="16dp">

        <!-- 主題預覽 -->
        <View
            android:id="@+id/viewThemePreview"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginEnd="16dp"
            android:background="@drawable/circle_background" />

        <!-- 主題資訊 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/tvThemeName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="主題名稱"
                android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                android:textColor="?attr/colorOnSurface"
                android:textStyle="bold" />

            <!-- 系統主題標籤 -->
            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/tvSystemThemeLabel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:text="系統主題"
                android:textAppearance="@style/TextAppearance.Material3.LabelSmall"
                android:textColor="?attr/colorPrimary"
                android:textStyle="bold"
                android:visibility="gone" />

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/tvThemeDescription"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="主題描述"
                android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                android:textColor="?attr/colorOnSurfaceVariant" />

        </LinearLayout>

        <!-- 選擇按鈕 -->
        <com.google.android.material.radiobutton.MaterialRadioButton
            android:id="@+id/radioButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:clickable="true"
            android:focusable="true" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
