<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp">

    <!-- 標題 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="16dp">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_target_24"
            android:layout_marginEnd="12dp"
            app:tint="?attr/colorPrimary" />

        <com.google.android.material.textview.MaterialTextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="選擇目標元素"
            android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall"
            android:textColor="?attr/colorOnSurface" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnRefresh"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:insetTop="0dp"
            android:insetBottom="0dp"
            android:insetLeft="0dp"
            android:insetRight="0dp"
            app:icon="@drawable/ic_refresh_24"
            app:iconSize="20dp"
            app:iconPadding="0dp"
            style="@style/Widget.Material3.Button.IconButton" />

    </LinearLayout>

    <!-- 搜索框 -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:hint="搜索元素"
        app:startIconDrawable="@drawable/ic_search"
        style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/etSearch"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text"
            android:maxLines="1" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- 篩選選項 -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:cardElevation="0dp"
        app:cardCornerRadius="8dp"
        app:strokeWidth="1dp"
        app:strokeColor="?attr/colorOutlineVariant">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="12dp">

            <com.google.android.material.textview.MaterialTextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="篩選條件"
                android:textAppearance="@style/TextAppearance.Material3.LabelLarge"
                android:textColor="?attr/colorOnSurface"
                android:layout_marginBottom="8dp" />

            <com.google.android.material.chip.ChipGroup
                android:id="@+id/chipGroupFilters"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:singleSelection="false">

                <com.google.android.material.chip.Chip
                    android:id="@+id/chipClickable"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="可點擊"
                    android:checked="true"
                    style="@style/Widget.Material3.Chip.Filter" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chipEditable"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="可編輯"
                    style="@style/Widget.Material3.Chip.Filter" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chipScrollable"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="可滾動"
                    style="@style/Widget.Material3.Chip.Filter" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chipCheckable"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="可勾選"
                    style="@style/Widget.Material3.Chip.Filter" />

            </com.google.android.material.chip.ChipGroup>

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- 元素列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewElements"
        android:layout_width="match_parent"
        android:layout_height="300dp"
        android:layout_marginBottom="16dp"
        tools:listitem="@layout/item_ui_element" />

    <!-- 空狀態 -->
    <LinearLayout
        android:id="@+id/layoutEmptyState"
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:orientation="vertical"
        android:gravity="center"
        android:visibility="gone">

        <ImageView
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_search_off_24"
            android:layout_marginBottom="12dp"
            app:tint="?attr/colorOutlineVariant" />

        <com.google.android.material.textview.MaterialTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="未找到符合條件的元素"
            android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
            android:textColor="?attr/colorOnSurfaceVariant"
            android:layout_marginBottom="4dp" />

        <com.google.android.material.textview.MaterialTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="請調整篩選條件或刷新頁面"
            android:textAppearance="@style/TextAppearance.Material3.BodySmall"
            android:textColor="?attr/colorOnSurfaceVariant" />

    </LinearLayout>

    <!-- 操作按鈕 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnCancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="取消"
            android:layout_marginEnd="8dp"
            style="@style/Widget.Material3.Button.TextButton" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnSelect"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="選擇"
            android:enabled="false"
            style="@style/Widget.Material3.Button" />

    </LinearLayout>

</LinearLayout>
