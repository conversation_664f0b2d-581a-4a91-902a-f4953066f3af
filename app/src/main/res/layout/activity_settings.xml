<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?android:attr/colorBackground"
    tools:context=".SettingsActivity">

    <!-- 工具列 -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="?attr/colorSurface"
        android:elevation="4dp"
        app:title="通知設定"
        app:titleTextColor="?attr/colorOnSurface"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- 設置內容 -->
    <ScrollView
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:fillViewport="true"
        app:layout_constraintTop_toBottomOf="@+id/toolbar"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 通知設置區塊 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <!-- 區塊標題 -->
                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/settings_notification_section"
                        android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                        android:textColor="?attr/colorOnSurface"
                        android:layout_marginBottom="16dp" />

                    <!-- 常駐通知設置 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingVertical="8dp">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <com.google.android.material.textview.MaterialTextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="@string/settings_persistent_notification_title"
                                android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                                android:textColor="?attr/colorOnSurface" />

                            <com.google.android.material.textview.MaterialTextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:text="@string/settings_persistent_notification_description"
                                android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                                android:textColor="?attr/colorOnSurfaceVariant" />

                        </LinearLayout>

                        <com.google.android.material.materialswitch.MaterialSwitch
                            android:id="@+id/switchPersistentNotification"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp" />

                    </LinearLayout>

                    <!-- 歡迎卡片顯示設置 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingVertical="8dp"
                        android:layout_marginTop="8dp">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <com.google.android.material.textview.MaterialTextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="@string/settings_welcome_card_title"
                                android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                                android:textColor="?attr/colorOnSurface" />

                            <com.google.android.material.textview.MaterialTextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:text="@string/settings_welcome_card_description"
                                android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                                android:textColor="?attr/colorOnSurfaceVariant" />

                        </LinearLayout>

                        <com.google.android.material.materialswitch.MaterialSwitch
                            android:id="@+id/switchWelcomeCard"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp" />

                    </LinearLayout>

                    <!-- 重新顯示歡迎導覽按鈕 -->
                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnShowWelcomeGuide"
                        style="@style/Widget.Material3.Button.TextButton"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:text="@string/show_welcome_guide"
                        android:textColor="?attr/colorPrimary"
                        android:gravity="start|center_vertical"
                        android:paddingStart="0dp"
                        android:paddingEnd="16dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>





        </LinearLayout>

    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>
