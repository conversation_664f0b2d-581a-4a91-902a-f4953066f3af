#!/bin/bash

# 主題修復驗證腳本
# 測試主題切換功能是否正常工作

echo "🎨 AutoLaunch 主題修復驗證"
echo "=========================="

# 檢查編譯是否成功
echo ""
echo "📦 檢查應用程式編譯..."
./gradlew app:assembleDebug --quiet
if [ $? -eq 0 ]; then
    echo "✅ 應用程式編譯成功"
else
    echo "❌ 應用程式編譯失敗"
    exit 1
fi

# 運行主題相關測試
echo ""
echo "🧪 運行主題系統測試..."
./gradlew app:testDebugUnitTest --tests "com.example.autolaunch.ThemeApplicationTest" --quiet
if [ $? -eq 0 ]; then
    echo "✅ 主題系統測試通過"
else
    echo "❌ 主題系統測試失敗"
    exit 1
fi

# 運行所有主題相關測試
echo ""
echo "🧪 運行所有主題測試..."
./gradlew app:testDebugUnitTest --tests "*Theme*" --quiet
if [ $? -eq 0 ]; then
    echo "✅ 所有主題測試通過"
else
    echo "❌ 部分主題測試失敗"
fi

# 檢查關鍵文件是否存在
echo ""
echo "📁 檢查關鍵文件..."

files=(
    "app/src/main/java/com/example/autolaunch/base/BaseActivity.kt"
    "app/src/main/java/com/example/autolaunch/utils/ThemeManager.kt"
    "app/src/main/java/com/example/autolaunch/utils/ThemeType.kt"
    "app/src/main/java/com/example/autolaunch/ThemeSettingsActivity.kt"
)

for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file"
    else
        echo "❌ $file (缺失)"
    fi
done

# 檢查是否移除了舊的 BaseActivity
if [ ! -f "app/src/main/java/com/example/autolaunch/BaseActivity.kt" ]; then
    echo "✅ 舊的 BaseActivity 已移除"
else
    echo "⚠️  舊的 BaseActivity 仍然存在"
fi

echo ""
echo "🎯 修復驗證完成！"
echo ""
echo "修復內容："
echo "- ✅ 統一 BaseActivity 類別"
echo "- ✅ 修復所有 Activity 繼承關係"
echo "- ✅ 改進主題管理器"
echo "- ✅ 移除重複代碼"
echo ""
echo "現在主題切換應該立即生效，無需重啟應用程式！"
