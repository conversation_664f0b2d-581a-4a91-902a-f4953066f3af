#!/bin/bash

# Android AutoLaunch 自動截圖腳本
# 完全自動化截圖，支援多國語系

set -e

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
PACKAGE_NAME="com.example.autolaunch"
SCREENSHOTS_DIR="screenshots"
CURRENT_LANG="zh_tw"
OUTPUT_DIR="$SCREENSHOTS_DIR/$CURRENT_LANG"
AUTO_MODE=true
DELAY_BETWEEN_SCREENSHOTS=3
PAGE_LOAD_DELAY=2

# 支援的語言
declare -a SUPPORTED_LANGUAGES=("zh_tw" "en" "ja" "ko")

# 截圖清單（格式：檔案名稱|描述|ADB操作）
SCREENSHOT_LIST=(
    "01_main_homepage|主頁面|launch_main_activity"
    "02_welcome_guide|歡迎導覽|clear_app_data_and_launch"
    "03_permission_check|權限檢查|navigate_to_permission_check"
    "04_add_schedule|新增排程|navigate_to_add_schedule"
    "05_app_selector|應用程式選擇器|navigate_to_app_selector"
    "06_time_picker|時間選擇器|navigate_to_time_picker"
    "07_date_picker|日期選擇器|navigate_to_date_picker"
    "08_repeat_settings|重複設定|navigate_to_repeat_settings"
    "09_backup_restore|備份與恢復|navigate_to_backup"
    "10_cloud_backup|雲端備份|navigate_to_cloud_backup"
    "11_language_settings|語言設定|navigate_to_language_settings"
    "12_theme_settings|主題設定|navigate_to_theme_settings"
    "13_system_log|系統日誌|navigate_to_system_log"
    "14_about_page|關於頁面|navigate_to_about"
    "15_schedule_status|排程狀態|show_schedule_status"
    "16_battery_optimization|電池優化|navigate_to_battery_optimization"
    "17_navigation_drawer|側邊選單|show_navigation_drawer"
    "18_edit_schedule|編輯排程|navigate_to_edit_schedule"
)

# 語言對應的設定值（使用函數方式替代關聯陣列）
get_language_setting() {
    case "$1" in
        "zh_tw") echo "zh-TW" ;;
        "en") echo "en" ;;
        "ja") echo "ja" ;;
        "ko") echo "ko" ;;
        *) echo "$1" ;;
    esac
}

# 函數：顯示標題
show_title() {
    echo -e "${BLUE}"
    echo "=============================================="
    echo "  Android AutoLaunch 自動截圖助手"
    echo "=============================================="
    echo -e "${NC}"
}

# 函數：記錄日誌
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 函數：檢查依賴
check_dependencies() {
    log_info "檢查依賴項目..."
    
    if ! command -v adb &> /dev/null; then
        log_error "找不到 adb 命令"
        echo "請安裝 Android SDK 或 Android Studio"
        exit 1
    fi
    
    log_info "依賴項目檢查完成"
}

# 函數：檢查設備連接
check_device() {
    log_info "檢查設備連接..."
    
    DEVICE_COUNT=$(adb devices | grep -c "device$" || true)
    
    if [ "$DEVICE_COUNT" -eq 0 ]; then
        log_error "未找到連接的 Android 設備"
        echo "請確保："
        echo "1. 設備已連接並開啟 USB 調試"
        echo "2. 或啟動 Android 模擬器"
        echo "3. 執行 'adb devices' 確認設備狀態"
        exit 1
    elif [ "$DEVICE_COUNT" -gt 1 ]; then
        log_warning "檢測到多個設備，將使用第一個設備"
    fi
    
    log_info "設備連接正常"
}

# 函數：建立目錄結構
create_directories() {
    log_info "建立目錄結構..."
    
    for lang in "${SUPPORTED_LANGUAGES[@]}"; do
        mkdir -p "$SCREENSHOTS_DIR/$lang"
    done
    
    mkdir -p "$SCREENSHOTS_DIR/tutorial/annotated"
    mkdir -p "$SCREENSHOTS_DIR/tutorial/cropped"
    mkdir -p "$SCREENSHOTS_DIR/tutorial/composite"
    
    log_info "目錄結構建立完成"
}

# 函數：檢查應用程式是否安裝
check_app_installed() {
    log_info "檢查應用程式安裝狀態..."
    
    if adb shell pm list packages | grep -q "$PACKAGE_NAME"; then
        log_info "應用程式已安裝"
    else
        log_error "未找到應用程式 $PACKAGE_NAME"
        echo "請確保應用程式已安裝到設備上"
        exit 1
    fi
}

# 函數：等待元素出現
wait_for_element() {
    local element_id="$1"
    local timeout="${2:-10}"
    local count=0
    
    log_info "等待元素出現: $element_id"
    
    while [ $count -lt $timeout ]; do
        if adb shell uiautomator dump | grep -q "$element_id"; then
            log_info "元素已找到: $element_id"
            return 0
        fi
        sleep 1
        count=$((count + 1))
    done
    
    log_warning "元素未找到: $element_id"
    return 1
}

# 函數：點擊元素
click_element_by_text() {
    local text="$1"
    log_info "點擊元素: $text"
    
    # 使用 UI Automator 點擊包含特定文字的元素
    adb shell uiautomator dump
    sleep 1
    
    # 嘗試點擊元素（使用座標）
    adb shell input tap 500 1000  # 預設點擊位置，可能需要調整
    sleep $PAGE_LOAD_DELAY
}

# 函數：點擊座標
click_coordinates() {
    local x="$1"
    local y="$2"
    log_info "點擊座標: ($x, $y)"
    adb shell input tap "$x" "$y"
    sleep $PAGE_LOAD_DELAY
}

# 函數：按返回鍵
press_back() {
    log_info "按返回鍵"
    adb shell input keyevent KEYCODE_BACK
    sleep $PAGE_LOAD_DELAY
}

# 函數：按主頁鍵
press_home() {
    log_info "按主頁鍵"
    adb shell input keyevent KEYCODE_HOME
    sleep 1
}

# 函數：啟動應用程式
launch_app() {
    log_info "啟動應用程式..."
    
    # 強制停止應用程式
    adb shell am force-stop "$PACKAGE_NAME"
    sleep 1
    
    # 啟動主 Activity
    adb shell am start -n "$PACKAGE_NAME/.MainActivity"
    sleep 3
    
    log_info "應用程式啟動完成"
}

# 函數：清除應用程式資料
clear_app_data() {
    log_info "清除應用程式資料..."
    adb shell pm clear "$PACKAGE_NAME"
    sleep 2
}

# 函數：設定語言
set_app_language() {
    local lang_code="$1"
    log_info "設定應用程式語言為: $lang_code"
    
    # 這裡需要根據應用程式的實際語言設定方式來實現
    # 假設通過 SharedPreferences 設定
    adb shell "echo 'INSERT OR REPLACE INTO preferences (key, value) VALUES (\"language\", \"$lang_code\");' | sqlite3 /data/data/$PACKAGE_NAME/databases/preferences.db" 2>/dev/null || true
    
    # 重新啟動應用程式以套用語言設定
    launch_app
}

# 導航函數 - 啟動主 Activity
launch_main_activity() {
    launch_app
}

# 導航函數 - 清除資料並啟動（用於歡迎導覽）
clear_app_data_and_launch() {
    clear_app_data
    launch_app
}

# 導航函數 - 導航到權限檢查
navigate_to_permission_check() {
    launch_app
    # 點擊歡迎卡片或權限提示
    click_coordinates 540 800  # 需要根據實際UI調整
}

# 導航函數 - 導航到新增排程
navigate_to_add_schedule() {
    launch_app
    # 點擊新增排程按鈕 (FAB)
    click_coordinates 950 1750  # FAB 通常在右下角
}

# 導航函數 - 導航到應用程式選擇器
navigate_to_app_selector() {
    navigate_to_add_schedule
    # 選擇應用程式類型
    click_coordinates 540 600  # 應用程式選項
    sleep 1
    # 點擊選擇應用程式按鈕
    click_coordinates 540 800
}

# 導航函數 - 導航到時間選擇器
navigate_to_time_picker() {
    navigate_to_add_schedule
    # 點擊時間設定
    click_coordinates 540 1000
}

# 導航函數 - 導航到日期選擇器
navigate_to_date_picker() {
    navigate_to_add_schedule
    # 點擊日期設定
    click_coordinates 540 1200
}

# 導航函數 - 導航到重複設定
navigate_to_repeat_settings() {
    navigate_to_add_schedule
    # 點擊重複設定
    click_coordinates 540 1400
}

# 導航函數 - 導航到備份頁面
navigate_to_backup() {
    launch_app
    # 開啟側邊選單
    click_coordinates 50 100
    sleep 1
    # 點擊備份選項
    click_coordinates 300 600
}

# 導航函數 - 導航到雲端備份
navigate_to_cloud_backup() {
    navigate_to_backup
    # 點擊雲端備份標籤
    click_coordinates 750 300
}

# 導航函數 - 導航到語言設定
navigate_to_language_settings() {
    launch_app
    # 開啟側邊選單
    click_coordinates 50 100
    sleep 1
    # 點擊語言設定
    click_coordinates 300 800
}

# 導航函數 - 導航到主題設定
navigate_to_theme_settings() {
    launch_app
    # 開啟側邊選單
    click_coordinates 50 100
    sleep 1
    # 點擊主題設定
    click_coordinates 300 700
}

# 導航函數 - 導航到系統日誌
navigate_to_system_log() {
    launch_app
    # 開啟側邊選單
    click_coordinates 50 100
    sleep 1
    # 點擊系統日誌
    click_coordinates 300 900
}

# 導航函數 - 導航到關於頁面
navigate_to_about() {
    launch_app
    # 開啟側邊選單
    click_coordinates 50 100
    sleep 1
    # 點擊關於選項
    click_coordinates 300 1000
}

# 導航函數 - 顯示排程狀態
show_schedule_status() {
    launch_app
    # 假設主頁面就有排程狀態顯示
}

# 導航函數 - 導航到電池優化
navigate_to_battery_optimization() {
    navigate_to_permission_check
    # 點擊電池優化選項
    click_coordinates 540 1200
}

# 導航函數 - 顯示側邊選單
show_navigation_drawer() {
    launch_app
    # 開啟側邊選單
    click_coordinates 50 100
}

# 導航函數 - 導航到編輯排程
navigate_to_edit_schedule() {
    launch_app
    # 假設點擊現有排程進入編輯
    click_coordinates 540 600
}

# 函數：拍攝截圖
take_screenshot() {
    local filename="$1"
    local description="$2"
    local action="$3"
    
    log_info "準備拍攝截圖：$description ($filename)"
    
    # 執行導航動作
    if [ -n "$action" ] && [ "$action" != "none" ]; then
        log_info "執行導航動作: $action"
        eval "$action"
    fi
    
    # 等待頁面載入
    sleep $PAGE_LOAD_DELAY
    
    # 拍攝截圖
    log_info "拍攝截圖..."
    
    # 生成時間戳
    TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
    TEMP_NAME="/sdcard/screenshot_$TIMESTAMP.png"
    
    # 在設備上拍攝截圖
    if adb shell screencap -p "$TEMP_NAME"; then
        # 拉取截圖到本地
        if adb pull "$TEMP_NAME" "$OUTPUT_DIR/$filename.png" >/dev/null 2>&1; then
            # 刪除設備上的臨時檔案
            adb shell rm "$TEMP_NAME"
            log_info "截圖完成：$OUTPUT_DIR/$filename.png"
            return 0
        else
            log_error "無法拉取截圖檔案"
            return 1
        fi
    else
        log_error "無法拍攝截圖"
        return 1
    fi
}

# 函數：批量截圖
batch_screenshot() {
    log_info "開始批量截圖..."
    echo ""
    
    local count=0
    local total=${#SCREENSHOT_LIST[@]}
    local success=0
    local failed=0
    
    for item in "${SCREENSHOT_LIST[@]}"; do
        count=$((count + 1))
        echo -e "${BLUE}進度：$count/$total${NC}"
        
        # 解析檔案名稱、描述和動作
        local filename=$(echo "$item" | cut -d'|' -f1)
        local description=$(echo "$item" | cut -d'|' -f2)
        local action=$(echo "$item" | cut -d'|' -f3)
        
        if take_screenshot "$filename" "$description" "$action"; then
            success=$((success + 1))
        else
            failed=$((failed + 1))
        fi
        
        # 在截圖之間等待
        if [ $count -lt $total ]; then
            sleep $DELAY_BETWEEN_SCREENSHOTS
        fi
    done
    
    echo ""
    log_info "批量截圖完成！"
    log_info "成功：$success 張，失敗：$failed 張"
}

# 函數：多語言截圖
multi_language_screenshot() {
    local languages=("$@")
    
    if [ ${#languages[@]} -eq 0 ]; then
        languages=("${SUPPORTED_LANGUAGES[@]}")
    fi
    
    log_info "開始多語言截圖，語言：${languages[*]}"
    
    for lang in "${languages[@]}"; do
        echo ""
        log_info "===== 開始截圖語言版本：$lang ====="
        
        # 設定當前語言
        CURRENT_LANG="$lang"
        OUTPUT_DIR="$SCREENSHOTS_DIR/$CURRENT_LANG"
        
        # 建立目錄
        mkdir -p "$OUTPUT_DIR"
        
        # 設定應用程式語言
        local lang_setting=$(get_language_setting "$lang")
        if [ -n "$lang_setting" ]; then
            set_app_language "$lang_setting"
        fi
        
        # 進行截圖
        batch_screenshot
        
        # 生成報告
        generate_report
        
        log_info "===== 完成語言版本：$lang ====="
    done
    
    log_info "所有語言版本截圖完成！"
}

# 函數：生成報告
generate_report() {
    log_info "生成截圖報告..."
    
    local report_file="$OUTPUT_DIR/screenshot_report.txt"
    
    {
        echo "Android AutoLaunch 截圖報告"
        echo "生成時間：$(date)"
        echo "語言版本：$CURRENT_LANG"
        echo "輸出目錄：$OUTPUT_DIR"
        echo ""
        echo "截圖清單："
        
        local success_count=0
        local total_count=0
        
        for item in "${SCREENSHOT_LIST[@]}"; do
            local filename=$(echo "$item" | cut -d'|' -f1)
            local description=$(echo "$item" | cut -d'|' -f2)
            total_count=$((total_count + 1))
            
            if [ -f "$OUTPUT_DIR/$filename.png" ]; then
                echo "✓ $filename.png - $description"
                success_count=$((success_count + 1))
            else
                echo "✗ $filename.png - $description (缺失)"
            fi
        done
        
        echo ""
        echo "統計：成功 $success_count/$total_count 張截圖"
    } > "$report_file"
    
    log_info "報告已生成：$report_file"
}

# 函數：顯示使用說明
show_usage() {
    echo "使用方法："
    echo "  $0 [選項]"
    echo ""
    echo "選項："
    echo "  -h, --help              顯示此幫助資訊"
    echo "  -l, --lang LANG         設定單一語言 (zh_tw, en, ja, ko)"
    echo "  -m, --multi LANGS       多語言截圖，用逗號分隔 (例：zh_tw,en,ja)"
    echo "  -a, --all               所有支援的語言"
    echo "  -s, --single NAME       只拍攝單個截圖"
    echo "  -d, --delay SECONDS     設定截圖間隔時間 (預設: 3 秒)"
    echo "  --list                  列出所有可用的截圖"
    echo "  --dry-run               測試模式，不實際拍攝截圖"
    echo ""
    echo "範例："
    echo "  $0 -a                           # 所有語言截圖"
    echo "  $0 -m zh_tw,en                  # 繁中和英文截圖"
    echo "  $0 -l ja                        # 只拍攝日文版本"
    echo "  $0 -s 01_main_homepage          # 只拍攝主頁面"
    echo "  $0 -l zh_tw -d 5                # 繁中版本，5秒間隔"
}

# 函數：列出所有截圖
list_screenshots() {
    echo -e "${BLUE}可用的截圖：${NC}"
    echo ""
    
    for item in "${SCREENSHOT_LIST[@]}"; do
        local filename=$(echo "$item" | cut -d'|' -f1)
        local description=$(echo "$item" | cut -d'|' -f2)
        echo -e "${YELLOW}$filename${NC}: $description"
    done
}

# 函數：測試模式
dry_run() {
    log_info "測試模式 - 模擬截圖流程"
    
    for item in "${SCREENSHOT_LIST[@]}"; do
        local filename=$(echo "$item" | cut -d'|' -f1)
        local description=$(echo "$item" | cut -d'|' -f2)
        local action=$(echo "$item" | cut -d'|' -f3)
        
        echo "模擬：$filename - $description (動作: $action)"
        sleep 0.5
    done
    
    log_info "測試模式完成"
}

# 主函數
main() {
    local languages=()
    local single_screenshot=""
    local dry_run_mode=false
    
    # 解析命令行參數
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_usage
                exit 0
                ;;
            -l|--lang)
                CURRENT_LANG="$2"
                OUTPUT_DIR="$SCREENSHOTS_DIR/$CURRENT_LANG"
                languages=("$2")
                shift 2
                ;;
            -m|--multi)
                IFS=',' read -ra languages <<< "$2"
                shift 2
                ;;
            -a|--all)
                languages=("${SUPPORTED_LANGUAGES[@]}")
                shift
                ;;
            -s|--single)
                single_screenshot="$2"
                shift 2
                ;;
            -d|--delay)
                DELAY_BETWEEN_SCREENSHOTS="$2"
                shift 2
                ;;
            --list)
                list_screenshots
                exit 0
                ;;
            --dry-run)
                dry_run_mode=true
                shift
                ;;
            *)
                log_error "未知選項：$1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    # 顯示標題
    show_title
    
    # 測試模式
    if [ "$dry_run_mode" = true ]; then
        dry_run
        exit 0
    fi
    
    # 檢查環境
    check_dependencies
    check_device
    create_directories
    check_app_installed
    
    # 執行截圖
    if [ -n "$single_screenshot" ]; then
        # 單個截圖
        log_info "單個截圖模式：$single_screenshot"
        
        # 檢查是否指定了多語言
        if [ ${#languages[@]} -gt 0 ]; then
            # 多語言單個截圖
            for lang in "${languages[@]}"; do
                log_info "語言版本：$lang"
                CURRENT_LANG="$lang"
                OUTPUT_DIR="$SCREENSHOTS_DIR/$CURRENT_LANG"
                mkdir -p "$OUTPUT_DIR"
                
                # 設定應用程式語言
                local lang_setting=$(get_language_setting "$lang")
                if [ -n "$lang_setting" ]; then
                    set_app_language "$lang_setting"
                fi
                
                # 找到並執行截圖
                for item in "${SCREENSHOT_LIST[@]}"; do
                    local filename=$(echo "$item" | cut -d'|' -f1)
                    local description=$(echo "$item" | cut -d'|' -f2)
                    local action=$(echo "$item" | cut -d'|' -f3)
                    
                    if [ "$filename" = "$single_screenshot" ]; then
                        take_screenshot "$filename" "$description" "$action"
                        generate_report
                        break
                    fi
                done
            done
        else
            # 單語言單個截圖
            for item in "${SCREENSHOT_LIST[@]}"; do
                local filename=$(echo "$item" | cut -d'|' -f1)
                local description=$(echo "$item" | cut -d'|' -f2)
                local action=$(echo "$item" | cut -d'|' -f3)
                
                if [ "$filename" = "$single_screenshot" ]; then
                    mkdir -p "$OUTPUT_DIR"
                    take_screenshot "$filename" "$description" "$action"
                    generate_report
                    exit 0
                fi
            done
            
            log_error "未找到截圖：$single_screenshot"
            exit 1
        fi
        
    elif [ ${#languages[@]} -gt 0 ]; then
        # 多語言截圖
        multi_language_screenshot "${languages[@]}"
    else
        # 預設單語言截圖
        mkdir -p "$OUTPUT_DIR"
        batch_screenshot
        generate_report
    fi
    
    log_info "截圖任務完成！"
    echo "截圖儲存在：$SCREENSHOTS_DIR"
}

# 執行主函數
main "$@" 