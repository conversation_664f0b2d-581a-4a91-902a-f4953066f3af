#!/bin/bash

# 全局主題切換功能測試腳本
# 驗證主題切換後返回首頁是否也應用了新主題

echo "🌍 AutoLaunch 全局主題切換測試"
echo "==============================="

# 檢查編譯是否成功
echo ""
echo "📦 檢查應用程式編譯..."
./gradlew app:assembleDebug --quiet
if [ $? -eq 0 ]; then
    echo "✅ 應用程式編譯成功"
else
    echo "❌ 應用程式編譯失敗"
    exit 1
fi

# 檢查關鍵修復
echo ""
echo "🔍 檢查全局主題切換修復..."

# 檢查 BaseActivity 是否有主題變更檢測
if grep -q "onResume" app/src/main/java/com/example/autolaunch/base/BaseActivity.kt; then
    echo "✅ BaseActivity 包含 onResume 主題檢測"
else
    echo "❌ BaseActivity 缺少 onResume 主題檢測"
fi

if grep -q "currentThemeId" app/src/main/java/com/example/autolaunch/base/BaseActivity.kt; then
    echo "✅ BaseActivity 追蹤當前主題ID"
else
    echo "❌ BaseActivity 未追蹤當前主題ID"
fi

if grep -q "recreate()" app/src/main/java/com/example/autolaunch/base/BaseActivity.kt; then
    echo "✅ BaseActivity 包含主題變更時重新創建邏輯"
else
    echo "❌ BaseActivity 缺少重新創建邏輯"
fi

# 檢查 ThemeManager 的強制全局切換
if grep -q "Force triggering global theme change" app/src/main/java/com/example/autolaunch/utils/ThemeManager.kt; then
    echo "✅ ThemeManager 包含強制全局主題切換"
else
    echo "❌ ThemeManager 缺少強制全局主題切換"
fi

if grep -q "tempMode" app/src/main/java/com/example/autolaunch/utils/ThemeManager.kt; then
    echo "✅ ThemeManager 使用臨時模式觸發切換"
else
    echo "❌ ThemeManager 未使用臨時模式觸發切換"
fi

# 檢查 MainActivity 是否繼承 BaseActivity
if grep -q "class MainActivity : BaseActivity" app/src/main/java/com/example/autolaunch/MainActivity.kt; then
    echo "✅ MainActivity 正確繼承 BaseActivity"
else
    echo "❌ MainActivity 未正確繼承 BaseActivity"
fi

# 運行主題相關測試
echo ""
echo "🧪 運行主題系統測試..."
./gradlew app:testDebugUnitTest --tests "*Theme*" --quiet
if [ $? -eq 0 ]; then
    echo "✅ 所有主題測試通過"
else
    echo "❌ 部分主題測試失敗"
fi

echo ""
echo "📋 修復總結："
echo "============"
echo ""
echo "🔧 已修復的問題："
echo "- ✅ 改進了 applyThemeImmediately() 方法"
echo "- ✅ 使用臨時模式強制觸發全局主題切換"
echo "- ✅ BaseActivity 添加了主題變更檢測"
echo "- ✅ 在 onResume 時檢查並應用主題變更"
echo "- ✅ 確保所有 Activity 都能響應主題變更"
echo ""
echo "🎯 預期效果："
echo "- 在主題設定頁面點擊 radio button 後主題立即生效"
echo "- 返回首頁時首頁也會應用新主題"
echo "- 所有頁面都會同步更新到新主題"
echo "- 無需手動重啟應用程式"
echo ""
echo "📱 測試步驟："
echo "1. 打開應用程式首頁，記住當前主題"
echo "2. 進入設定 → 主題設定"
echo "3. 點擊不同的 radio button 選擇新主題"
echo "4. 觀察主題設定頁面是否立即應用新主題"
echo "5. 返回首頁"
echo "6. 檢查首頁是否也應用了新主題"
echo ""
echo "🔍 調試日誌："
echo "查看 logcat 中的以下日誌："
echo "- 'Applying theme immediately: [主題名稱]'"
echo "- 'Force triggering global theme change'"
echo "- 'onCreate: Applied theme [主題ID]'"
echo "- 'Theme changed from [舊主題] to [新主題], recreating activity'"
echo ""
echo "⚠️  注意事項："
echo "- 主題切換會觸發 Activity 重新創建"
echo "- 這是正常行為，確保主題完全生效"
echo "- 如果看到頁面閃爍，這是重新創建的視覺效果"
echo ""
echo "🎉 全局主題切換功能修復完成！"
