### TASK_016: 排程啟用/禁用功能

**描述:**
在主介面的排程列表項目中添加一個開關 (Switch)，允許用戶方便地啟用或禁用單個排程。當排程狀態改變時，需要更新資料庫並相應地設定或取消 `AlarmManager` 鬧鐘。

**接受標準:**
1.  主介面排程列表的每個項目中包含一個可切換的開關 (例如 `Switch` 或 `ToggleButton`)。
2.  開關的狀態能正確反映排程的 `isEnabled` 屬性。
3.  用戶切換開關時：
    *   更新對應排程在資料庫中的 `isEnabled` 狀態。
    *   如果從禁用切換到啟用，呼叫 `AlarmManager` 設定鬧鐘。
    *   如果從啟用切換到禁用，呼叫 `AlarmManager` 取消鬧鐘。
4.  操作後，列表介面能即時更新顯示排程的新狀態。

**依賴:**
*   `TASK_002_排程數據模型定義`
*   `TASK_003_Room 資料庫整合與 DAO`
*   `TASK_005_排程列表數據展示與更新`
*   `TASK_013_AlarmManager 核心調度服務`

**預估工時:**
*   3 小時

**負責人:**
*   [待定]

**備註:**
*   資料庫更新和 `AlarmManager` 操作應在後台線程執行。
*   提供視覺回饋 (例如，當禁用時排程項目呈現灰色)。