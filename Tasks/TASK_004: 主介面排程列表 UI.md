### TASK_004: 主介面排程列表 UI

**描述:**
設計並實現應用程式的主介面使用者介面。該介面應以列表形式顯示所有已建立的排程，並提供一個新增排程的入口。

**接受標準:**
1.  創建 `activity_main.xml` 或使用 Jetpack Compose 實現主介面佈局。
2.  介面包含一個 `RecyclerView` (或 Compose 的 `LazyColumn`) 用於顯示排程列表。
3.  介面包含一個明顯的“新增排程”按鈕（例如，右下角的 `FloatingActionButton`）。
4.  每個排程列表項目 (List Item) 的佈局初步設計完成，能夠顯示以下基本資訊（可以使用佔位符文字）：
    *   App 圖標 (Placeholder)
    *   App 名稱 (Placeholder)
    *   設定的時間 (Placeholder)
    *   重複模式 (Placeholder)
    *   啟用/禁用開關 (Placeholder)

**依賴:**
*   `TASK_001_專案基礎設定`

**預估工時:**
*   3 小時

**負責人:**
*   [待定]

**備註:**
*   考慮使用 Material Design 元件，例如 `FloatingActionButton` 和 `CardView` (或 Compose 的 `Card`) 來美化列表項目。
*   為列表項目創建一個獨立的佈局檔案（例如 `item_schedule.xml`）。