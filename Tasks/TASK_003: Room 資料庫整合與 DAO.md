### TASK_003: Room 資料庫整合與 DAO

**描述:**
將 Room Persistence Library 整合到專案中，定義資料庫類別 (`AppDatabase`) 和資料存取物件 (`DAO`) 介面，用於對排程數據進行增、刪、改、查操作。

**接受標準:**
1.  在 `build.gradle (Module: app)` 中添加 Room 相關依賴。
2.  創建一個繼承自 `RoomDatabase` 的抽象類別 `AppDatabase`。
3.  在 `AppDatabase` 中定義 `Schedule` 實體。
4.  創建一個 `@Dao` 介面 `ScheduleDao`，並包含以下方法：
    *   `insert(schedule: Schedule)`：插入一個新的排程。
    *   `update(schedule: Schedule)`：更新現有排程。
    *   `delete(schedule: Schedule)`：刪除一個排程。
    *   `getAllSchedules(): Flow<List<Schedule>>` 或 `LiveData<List<Schedule>>`：獲取所有排程，並支援數據變動時自動更新。
    *   `getScheduleById(id: Long): Flow<Schedule>` 或 `LiveData<Schedule>`：根據 ID 獲取特定排程。
5.  實現資料庫的單例模式 (Singleton Pattern) 或使用依賴注入 (DI) 框架來提供資料庫實例。

**依賴:**
*   `TASK_001_專案基礎設定`
*   `TASK_002_排程數據模型定義`

**預估工時:**
*   4 小時

**負責人:**
*   [待定]

**備註:**
*   為了數據更新的反應性，建議使用 Kotlin Flow 或 LiveData 來獲取排程列表。
*   考慮資料庫版本管理和遷移 (Migration)，儘管初期可能不需要。