### TASK_015: 設備重啟後重新註冊排程

**描述:**
實現一個 `BroadcastReceiver`，用於監聽設備啟動完成的廣播 (`BOOT_COMPLETED`)。當設備重啟後，該接收器將負責重新載入所有已啟用的排程，並將它們重新註冊到 `AlarmManager`，以確保排程的持久性。

**接受標準:**
1.  創建一個新的 `BroadcastReceiver` (例如 `BootCompletedReceiver`)。
2.  在 `AndroidManifest.xml` 中宣告此 `BroadcastReceiver`，並註冊 `android.intent.action.BOOT_COMPLETED` 動作。
3.  在 `AndroidManifest.xml` 中宣告 `RECEIVE_BOOT_COMPLETED` 權限。
4.  `onReceive()` 方法能夠監聽到設備重啟廣播。
5.  在接收到廣播後，從資料庫中查詢所有**已啟用**的排程。
6.  對於每個已啟用的排程，呼叫 `AlarmManager` 服務 (由 `TASK_013` 提供的方法) 重新設定其鬧鐘。
7.  確保此過程在後台執行，不會阻塞主線程。

**依賴:**
*   `TASK_001_專案基礎設定` (權限宣告)
*   `TASK_003_Room 資料庫整合與 DAO`
*   `TASK_013_AlarmManager 核心調度服務`

**預估工時:**
*   4 小時

**負責人:**
*   [待定]

**備註:**
*   在 `onReceive()` 內部不要執行耗時操作，應將數據查詢和鬧鐘重新設定的邏輯交給一個 `WorkManager` 或 `Service` 來處理。
*   Android O (API 26) 及以上版本對隱式廣播有限制，但 `BOOT_COMPLETED` 仍是被允許的。