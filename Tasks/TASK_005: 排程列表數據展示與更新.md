### TASK_005: 排程列表數據展示與更新

**描述:**
將從 Room 資料庫獲取的排程數據填充到主介面的 `RecyclerView` 中，並確保當資料庫數據發生變化時，介面能自動更新。

**接受標準:**
1.  創建一個 `RecyclerView.Adapter` (或 Compose 的 List Item Composable) 用於顯示 `Schedule` 物件。
2.  在 `MainActivity` 或相應的 `Fragment` 中，實例化並設定 `ViewModel`。
3.  `ViewModel` 應透過 `ScheduleDao` 獲取所有排程數據 (使用 `Flow` 或 `LiveData`)。
4.  介面能夠觀察 `ViewModel` 中的排程列表數據，並在數據更新時自動重新繪製 `RecyclerView`。
5.  每個排程列表項目能正確顯示從 `Schedule` 模型中提取的以下資訊：
    *   App 名稱 (`appName`)
    *   設定的時間 (`hour`, `minute`)
    *   啟用/禁用狀態 (`isEnabled`)
    *   重複模式 (`repeatMode`, `daysOfWeek`) 的簡要文字描述。

**依賴:**
*   `TASK_003_Room 資料庫整合與 DAO`
*   `TASK_004_主介面排程列表 UI`

**預估工時:**
*   4 小時

**負責人:**
*   [待定]

**備註:**
*   使用 `ListAdapter` 及其內部的 `DiffUtil` 可以更有效地更新列表。
*   在 `ViewModel` 中管理資料庫操作，保持 Activity/Fragment 的簡潔。