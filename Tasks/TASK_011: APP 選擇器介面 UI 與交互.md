### TASK_011: APP 選擇器介面 UI 與交互

**描述:**
設計並實現用於選擇應用程式的介面。該介面將以列表形式顯示所有可選的 App，並支援用戶點擊選擇一個 App。

**接受標準:**
1.  創建一個新的 Activity 或 Fragment (例如 `AppSelectorActivity` 或 `AppSelectorFragment`) 用於顯示 App 列表。
2.  介面包含一個 `RecyclerView` (或 `LazyColumn`) 來顯示 App 列表。
3.  每個列表項目能夠清晰顯示 App 的圖標和名稱。
4.  用戶可以透過點擊列表中的 App 來選擇它。
5.  選擇 App 後，選擇的 App 的資訊（包名和名稱）能返回到新增/編輯排程介面，並顯示在相應的 UI 元件上。
6.  （可選）提供搜尋功能或快速滾動條以便用戶快速找到 App。

**依賴:**
*   `TASK_007_新增_編輯排程介面 UI`
*   `TASK_010_APP 選擇器權限與應用列表獲取`

**預估工時:**
*   5 小時

**負責人:**
*   [待定]

**備註:**
*   App 列表可能很長，考慮使用 `RecyclerView` 的高效刷新機制 (例如 `ListAdapter`)。
*   在返回新增/編輯介面時，使用 `setResult()` 或 Navigation Component 的 Safe Args 傳遞選定的 App 資訊。