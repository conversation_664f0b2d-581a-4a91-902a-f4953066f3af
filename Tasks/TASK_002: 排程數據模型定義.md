### TASK_002: 排程數據模型定義

**描述:**
定義應用程式排程的核心數據模型（Data Model/Entity）。這個模型將包含排程的所有必要資訊，例如：要啟動的 App、設定的時間、重複模式、啟用狀態、上次執行時間和下次執行時間。

**接受標準:**
1.  創建一個 Kotlin `data class` 或 Room `@Entity`，命名為 `Schedule` 或類似名稱。
2.  該模型包含以下屬性（考慮 Room 持久化時的註解）：
    *   `id` (主鍵，自動生成)
    *   `appName` (App 顯示名稱)
    *   `packageName` (App 包名，用於啟動)
    *   `appIcon` (App 圖標的 URI 或其他可序列化的形式，或動態載入)
    *   `hour` (排程小時，24 小時制)
    *   `minute` (排程分鐘)
    *   `repeatMode` (重複模式：單次、每日、每週等，使用枚舉或整數表示)
    *   `daysOfWeek` (適用於每週模式，例如位元遮罩或字串表示選中的星期幾)
    *   `singleExecuteDate` (適用於單次模式，具體日期，例如 Unix timestamp)
    *   `isEnabled` (布林值，表示排程是否啟用)
    *   `lastExecutedTime` (上次執行時間的 Unix timestamp，可為 null)
    *   `nextExecutedTime` (下次執行時間的 Unix timestamp，用於顯示，可為 null)
3.  所有屬性具有適當的數據類型。
4.  為 Room ORM 做好準備（例如，`@PrimaryKey`, `@ColumnInfo`）。

**依賴:**
*   `TASK_001_專案基礎設定`

**預估工時:**
*   3 小時

**負責人:**
*   [待定]

**備註:**
*   `appIcon` 可以考慮在啟動時從 `PackageManager` 動態載入，而不必儲存在資料庫中，以減少資料庫負擔。如果儲存，考慮將其轉換為 Base64 字串或檔案路徑。
*   `daysOfWeek` 可以用一個 `Int` 來儲存，每個位元代表一個星期幾（例如，1=週一，2=週二，4=週三，以此類推）。
*   時間戳建議使用 `Long` 類型。