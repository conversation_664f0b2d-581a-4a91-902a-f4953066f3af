### TASK_014: APP 啟動廣播接收器與邏輯

**描述:**
創建一個 `BroadcastReceiver`，當 `AlarmManager` 觸發時接收到廣播。這個接收器將負責根據排程資訊啟動目標應用程式，並更新排程的“上次執行時間”。

**接受標準:**
1.  創建一個繼承自 `BroadcastReceiver` 的類別 (例如 `AppLaunchReceiver`)。
2.  在 `AndroidManifest.xml` 中註冊此 `BroadcastReceiver`。
3.  `onReceive()` 方法能夠正確接收由 `AlarmManager` 發送的 Intent。
4.  從 Intent 中提取排程的 ID 和目標 App 的包名。
5.  使用 `PackageManager` 和 `Intent` 來啟動目標 App。
    *   確保啟動 App 時設置 `Intent.FLAG_ACTIVITY_NEW_TASK` 旗標，因為是從非 Activity 上下文啟動。
6.  啟動 App 後，更新資料庫中對應排程的 `lastExecutedTime` 為當前時間。
7.  處理 App 啟動失敗的可能性（例如 App 已被卸載，或 `PackageManager` 無法找到啟動 Intent）。

**依賴:**
*   `TASK_002_排程數據模型定義`
*   `TASK_003_Room 資料庫整合與 DAO`
*   `TASK_013_AlarmManager 核心調度服務` (此任務接收其發送的廣播)
*   `TASK_018_上次執行時間更新與顯示` (此任務實現其核心邏輯)

**預估工時:**
*   5 小時

**負責人:**
*   [待定]

**備註:**
*   `onReceive()` 方法應儘快執行，如果需要執行耗時操作（例如資料庫更新），應將其移到後台線程或使用 `GoAsync()`。
*   考慮使用 `WakeLock` 確保在啟動 App 過程中 CPU 不會休眠。