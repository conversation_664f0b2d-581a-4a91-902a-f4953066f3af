### TASK_007: 新增_編輯排程介面 UI

**描述:**
設計並實現用於新增和編輯排程的專用使用者介面。此介面將包含所有排程設定所需的輸入控制項。

**接受標準:**
1.  創建 `activity_add_edit_schedule.xml` 或使用 Jetpack Compose 實現新增/編輯排程介面佈局。
2.  介面包含以下輸入控制項的佈局：
    *   時間選擇器（例如，一個顯示時間的 `TextView` 或 `Button`，點擊後觸發時間選擇對話框）。
    *   日期/週期設定區域（例如，`RadioGroup` 或 `Spinner` 用於選擇重複模式，`CheckBox` 或 `ToggleButton` 用於選擇星期幾）。
    *   App 選擇器（例如，一個顯示當前選定 App 圖標和名稱的區域，點擊後導航到 App 選擇列表）。
    *   一個保存排程的按鈕。
3.  佈局清晰、直觀，易於用戶理解和操作。
4.  介面在新增模式下所有輸入為空白，在編輯模式下預填充現有排程數據。

**依賴:**
*   `TASK_006_新增排程入口與基本導航`

**預估工時:**
*   4 小時

**負責人:**
*   [待定]

**備註:**
*   考慮使用 Material Design 的 `TextInputLayout` 來優化輸入欄位。
*   App 選擇器部分在初始階段可以先用一個 `TextView` 佔位，待 `TASK_011` 完成後再替換為實際的 App 資訊。