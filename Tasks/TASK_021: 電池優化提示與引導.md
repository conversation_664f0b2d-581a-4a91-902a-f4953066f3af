### TASK_021: 電池優化提示與引導

**描述:**
為了確保 `AlarmManager` 能夠在設備休眠或低功耗模式下穩定運行，需要提示用戶將本應用程式添加到電池優化白名單中。實現一個 UI 元素來引導用戶完成此操作。

**接受標準:**
1.  在應用程式首次啟動或在用戶可能需要時（例如，排程無法按時觸發時），顯示一個提示或對話框。
2.  提示內容應清晰解釋電池優化可能影響應用程式排程功能，並建議用戶將應用程式加入白名單。
3.  提供一個按鈕或連結，點擊後能直接跳轉到系統的電池優化設定介面，或至少引導用戶手動操作。
    *   可以使用 `PowerManager.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS` Intent 跳轉（針對 API 23 及以上）。
4.  使用 `PowerManager.isIgnoringBatteryOptimizations()` 檢查應用程式是否已被加入白名單，避免重複提示。

**依賴:**
*   `TASK_001_專案基礎設定`

**預估工時:**
*   3 小時

**負責人:**
*   [待定]

**備註:**
*   務必告知用戶這樣做的原因和重要性，避免混淆。
*   某些手機廠商可能會有自定義的電池優化設置，通用跳轉可能不完全適用，需要提供文字說明作為補充。