### TASK_013: AlarmManager 核心調度服務

**描述:**
實現應用程式的核心排程機制，使用 Android 的 `AlarmManager` 服務來設定、取消和更新排程的鬧鐘。確保鬧鐘能夠在指定時間準確觸發，即使設備處於休眠狀態。

**接受標準:**
1.  創建一個幫助類別或服務來封裝 `AlarmManager` 的操作。
2.  提供設定鬧鐘的方法：`setAlarm(schedule: Schedule)`。
    *   根據 `Schedule` 中的時間和重複模式，計算下次觸發時間。
    *   使用 `AlarmManager.setExactAndAllowWhileIdle()` 或 `setAndAllowWhileIdle()` 來設定鬧鐘，以確保在休眠模式下也能觸發。
    *   為每個排程產生唯一的 `PendingIntent` 請求碼。
    *   `PendingIntent` 應指向後續的 `BroadcastReceiver` (`TASK_014`)。
3.  提供取消鬧鐘的方法：`cancelAlarm(scheduleId: Long)`。
    *   使用相同的 `PendingIntent` 請求碼來取消對應的鬧鐘。
4.  提供更新鬧鐘的方法：`updateAlarm(schedule: Schedule)`。
    *   先取消舊鬧鐘，再設定新鬧鐘。
5.  正確處理 `AlarmManager` 相關的權限問題。
6.  鬧鐘設定邏輯能在保存/更新排程時被 `TASK_012` 呼叫。

**依賴:**
*   `TASK_002_排程數據模型定義`
*   `TASK_012_排程數據保存/更新邏輯` (此任務被其呼叫)
*   `TASK_014_APP 啟動廣播接收器與邏輯` (此任務設定的鬧鐘會觸發該接收器)

**預估工時:**
*   8 小時

**負責人:**
*   [待定]

**備註:**
*   `AlarmManager` 的使用需要仔細考慮設備的 Doze 模式和應用程式待機模式，確保穩定性。
*   為每個排程生成唯一的 `PendingIntent` 請求碼至關重要，通常基於排程的 ID。
*   需要處理 Android 12 (API 31) 及以上版本對精確鬧鐘權限 (`SCHEDULE_EXACT_ALARM`) 的要求。