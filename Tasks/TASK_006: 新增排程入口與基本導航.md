### TASK_006: 新增排程入口與基本導航

**描述:**
實現從主介面點擊“新增排程”按鈕後，導航到新增/編輯排程介面的功能。同時確保基本的介面間導航（如返回）運作正常。

**接受標準:**
1.  點擊主介面上的“新增排程”按鈕 (例如 `FloatingActionButton`) 能正確跳轉到一個新的 Activity 或 Fragment (用於新增/編輯排程)。
2.  從新增/編輯排程介面，點擊返回按鈕（系統返回鍵或 AppBar 上的返回箭頭）能正確返回主介面。
3.  導航過程流暢，沒有明顯的延遲或卡頓。

**依賴:**
*   `TASK_004_主介面排程列表 UI`
*   `TASK_007_新增_編輯排程介面 UI` (可同時進行或作為緊密關聯的後續任務)

**預估工時:**
*   2 小時

**負責人:**
*   [待定]

**備註:**
*   推薦使用 Android Jetpack Navigation Component 進行導航管理。
*   新增排程介面初始狀態為空白，等待用戶輸入。