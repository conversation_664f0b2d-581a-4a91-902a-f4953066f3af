### TASK_018: 上次執行時間更新與顯示

**描述:**
當一個排程成功觸發並啟動目標 App 後，更新該排程的 `lastExecutedTime` 欄位為當前時間，並在主介面排程列表中顯示此信息。

**接受標準:**
1.  在 `TASK_014_APP 啟動廣播接收器與邏輯` 中，成功啟動 App 後，獲取當前時間戳。
2.  使用 `ScheduleDao.update()` 方法，將對應排程的 `lastExecutedTime` 屬性更新為該時間戳。
3.  主介面排程列表的每個項目應顯示其“上次執行時間”。
4.  顯示格式應為用戶友好型，例如“5 分鐘前”、“1 小時前”、“昨天 10:30”等相對時間。

**依賴:**
*   `TASK_002_排程數據模型定義`
*   `TASK_003_Room 資料庫整合與 DAO`
*   `TASK_005_排程列表數據展示與更新`
*   `TASK_014_APP 啟動廣播接收器與邏輯`

**預估工時:**
*   3 小時

**負責人:**
*   [待定]

**備註:**
*   時間格式化可以使用 `DateUtils.getRelativeTimeSpanString()` 或自訂格式化工具。
*   需要確保資料庫更新不會阻塞 UI 線程。