### TASK_022: UX/UI 細節優化與空狀態處理

**描述:**
對應用程式的整體使用者介面和使用者體驗進行最終的優化和打磨。這包括處理排程列表為空時的狀態、提供清晰的用戶回饋、統一介面風格以及改善導航流暢度。

**接受標準:**
1.  **空狀態處理：** 當主介面的排程列表沒有任何排程時，顯示一個友好的提示資訊（例如：“您還沒有建立任何排程。點擊右下角的 '+' 按鈕來新增一個！”），並配上一個相關的圖示。
2.  **成功/錯誤回饋：**
    *   保存排程成功後，顯示一個簡短的 `Toast` 訊息或 `Snackbar`。
    *   刪除排程成功後，提供視覺回饋並可選的“撤銷”選項。
    *   應用程式啟動失敗或 App 被卸載時，清晰的通知。
3.  **導航流暢度：** 確保所有 Activity/Fragment 之間的過渡動畫自然流暢。
4.  **一致性：** 統一應用程式的顏色、字體、圖標和元件風格，遵循 Material Design 規範。
5.  **響應式設計：** 介面在不同螢幕尺寸和方向下都能良好顯示。
6.  **易用性：** 檢查所有交互元素是否清晰可點擊，文字說明是否簡潔明瞭。
7.  （可選）為 App 圖標和啟動畫面 (Splash Screen) 進行設計。

**依賴:**
*   所有涉及到 UI/UX 的任務 (例如 `TASK_004`, `TASK_005`, `TASK_007`, `TASK_011`, `TASK_017`, `TASK_020`)

**預估工時:**
*   8 小時

**負責人:**
*   [待定]

**備註:**
*   收集用戶回饋並據此進行迭代優化是此階段的關鍵。
*   利用 Android Studio 的 Layout Inspector 進行佈局調試。