### TASK_001: 專案基礎設定

**描述:**
建立新的 Android Studio 專案，配置專案結構、Gradle 依賴，並在 `AndroidManifest.xml` 中添加必要的 Android 權限宣告。這是所有後續開發的基礎。

**接受標準:**
1.  成功建立一個 Android Studio 專案，並使用 Kotlin 語言。
2.  `build.gradle (Module: app)` 檔案已配置好 `minSdk`、`targetSdk`、`compileSdk`。
3.  已在 `AndroidManifest.xml` 中宣告 `QUERY_ALL_PACKAGES` 和 `RECEIVE_BOOT_COMPLETED` 權限（雖然 `QUERY_ALL_PACKAGES` 在後續任務才會用到，但提前宣告）。
4.  專案能夠成功編譯並透過模擬器或真機運行一個基本的“Hello World”介面。
5.  移除不必要的預設生成程式碼和資源。

**依賴:**
*   無前置依賴。

**預估工時:**
*   2 小時

**負責人:**
*   [待定]

**備註:**
*   選擇最新的穩定版 Android Studio 和 Kotlin 版本。
*   建議使用 `Empty Activity` 模板開始。