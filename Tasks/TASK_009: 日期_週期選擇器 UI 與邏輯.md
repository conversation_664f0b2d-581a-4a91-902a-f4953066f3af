### TASK_009: 日期/週期選擇器 UI 與邏輯

**描述:**
實現排程的日期和重複週期設定功能。這包括選擇“單次執行”、“每日”、“每週”等模式，並在“每週”模式下支援多選星期幾。

**接受標準:**
1.  在新增/編輯排程介面中，提供 UI 讓用戶選擇以下重複模式：
    *   **單次執行 (Once):** 彈出日期選擇器 (`DatePicker`) 供用戶選擇具體執行日期。
    *   **每日 (Daily):** 簡單的選項，表示每天執行。
    *   **每週 (Weekly):** 提供可多選的星期幾選項（例如，多個 `CheckBox` 或 `ToggleButton`，標識週一至週日）。
2.  用戶選定的重複模式和相關日期/星期幾資訊能正確更新到排程數據模型中。
3.  不同模式間的 UI 應能正確切換，例如，選中“單次執行”時顯示日期選擇器，選中“每週”時顯示星期幾選擇器。

**依賴:**
*   `TASK_007_新增_編輯排程介面 UI`
*   `TASK_002_排程數據模型定義`
*   `TASK_008_時間選擇器功能實現`

**預估工時:**
*   5 小時

**負責人:**
*   [待定]

**備註:**
*   對於“單次執行”模式，可以使用 `MaterialDatePicker` 或 `DatePickerDialog`。
*   星期幾的選擇結果可以儲存為一個整數位元遮罩 (bitmask) 到 `Schedule` 模型的 `daysOfWeek` 欄位。