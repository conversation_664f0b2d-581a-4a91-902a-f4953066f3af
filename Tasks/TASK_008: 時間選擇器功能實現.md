### TASK_008: 時間選擇器功能實現

**描述:**
為新增/編輯排程介面中的時間設定部分實現功能。當用戶點擊時間輸入框時，應彈出一個小時和分鐘選擇器，並將選定的時間更新到介面和排程數據模型中。

**接受標準:**
1.  在新增/編輯排程介面中，點擊時間顯示元件（例如 `TextView` 或 `Button`）時，能正確彈出 Android 系統或 Material Design 的 `TimePicker` 對話框。
2.  用戶在 `TimePicker` 中選擇時間後，該時間（小時和分鐘）能正確顯示在介面的時間輸入元件上。
3.  選定的時間（小時和分鐘）能被正確儲存在排程數據模型中，等待保存。
4.  支援 24 小時制顯示。

**依賴:**
*   `TASK_007_新增_編輯排程介面 UI`
*   `TASK_002_排程數據模型定義`

**預估工時:**
*   2 小時

**負責人:**
*   [待定]

**備註:**
*   推薦使用 `MaterialTimePicker` 來提供更好的使用者體驗和遵循 Material Design 規範。
*   需要處理用戶取消選擇的情況。