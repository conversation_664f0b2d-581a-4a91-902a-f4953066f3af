### TASK_023: 測試與調試

**描述:**
全面測試應用程式的各項功能，確保其穩定性、可靠性和正確性。這包括功能測試、壓力測試、邊緣案例測試、跨設備兼容性測試，並進行必要的調試來修復發現的問題。

**接受標準:**
1.  **功能測試：**
    *   新增、編輯、刪除排程功能正常。
    *   時間和日期/週期設定準確。
    *   App 選擇器能正確顯示和選擇 App。
    *   排程啟用/禁用功能正常。
    *   排程列表數據顯示正確，更新及時。
    *   上次執行時間和下次執行時間計算和顯示正確。
2.  **核心功能測試：**
    *   排程的 App 能夠在指定時間準確自動啟動。
    *   設備重啟後，排程能自動重新啟用並正常觸發。
    *   在設備休眠 (Doze Mode) 狀態下，排程也能按時觸發。
3.  **錯誤處理測試：**
    *   目標 App 被卸載後，排程能被正確處理（禁用/提示）。
    *   App 啟動失敗時的提示和處理。
4.  **邊緣案例測試：**
    *   排程設定在午夜或換日時間。
    *   在排程時間前後進行設備重啟、關機。
    *   設置極端時間（例如，未來很遠的單次排程）。
    *   長時間不使用應用程式後，排程是否依然有效。
5.  **兼容性測試：**
    *   在不同 Android 版本（例如，Min SDK 到 Target SDK）和不同品牌設備上測試。
6.  **性能測試：**
    *   App 啟動和運行流暢，無明顯卡頓。
    *   後台服務對電池消耗影響最小。
7.  所有發現的 bug 都已記錄並修復。

**依賴:**
*   所有已完成的開發任務。

**預估工時:**
*   16 小時 (這是持續性的工作，會貫穿整個開發後期)

**負責人:**
*   [待定]

**備註:**
*   利用 Android Studio 的 Profiler 工具進行性能分析。
*   可以考慮編寫單元測試 (Unit Test) 和儀器測試 (Instrumented Test) 來提高程式碼品質和測試效率。
*   多裝置測試是確保應用廣泛兼容性的關鍵。