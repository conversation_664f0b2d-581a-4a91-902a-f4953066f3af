### TASK_020: 目標 APP 卸載或啟動失敗處理

**描述:**
實現在目標 App 被卸載或因其他原因無法啟動時，應用程式的健壯性處理。這包括檢測這些情況並通知用戶，同時自動禁用或刪除無效的排程。

**接受標準:**
1.  在 `TASK_014_APP 啟動廣播接收器與邏輯` 中，當嘗試啟動 App 時，如果 `PackageManager` 無法找到目標 App 或啟動失敗：
    *   將對應的排程在資料庫中標記為 `isEnabled = false` (禁用)。
    *   向用戶發送一條通知，說明哪個排程的哪個 App 無法啟動，並建議用戶檢查或刪除該排程。
2.  考慮實現一個 `BroadcastReceiver` 來監聽 `ACTION_PACKAGE_REMOVED` 廣播。當監聽到一個 App 被卸載時：
    *   從資料庫中查找所有指向該被卸載 App 的排程。
    *   自動禁用或刪除這些排程，並通知用戶。
3.  所有這些操作都應在後台執行，不阻塞 UI。

**依賴:**
*   `TASK_002_排程數據模型定義`
*   `TASK_003_Room 資料庫整合與 DAO`
*   `TASK_014_APP 啟動廣播接收器與邏輯`
*   `TASK_016_排程啟用/禁用功能` (會使用其底層邏輯)
*   `TASK_017_排程刪除功能` (會使用其底層邏輯)

**預估工時:**
*   5 小時

**負責人:**
*   [待定]

**備註:**
*   發送通知時，確保通知通道 (Notification Channel) 的正確配置。
*   `ACTION_PACKAGE_REMOVED` 廣播在 Android 8.0 (API 26) 及更高版本中，如果接收器未在 `AndroidManifest.xml` 中聲明為靜態，或應用程式本身沒有在前台運行，可能不會被接收。