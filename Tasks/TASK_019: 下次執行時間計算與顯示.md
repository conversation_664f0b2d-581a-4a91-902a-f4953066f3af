### TASK_019: 下次執行時間計算與顯示

**描述:**
為主介面排程列表中的每個排程計算並顯示其“下次執行時間”。這個計算應根據排程設定的時間、重複模式和當前時間動態完成。

**接受標準:**
1.  為 `Schedule` 模型添加或計算一個 `nextExecutedTime` 屬性。
2.  在主介面排程列表的 Adapter 或 ViewModel 中，實現邏輯來根據排程的 `hour`、`minute`、`repeatMode`、`daysOfWeek` 和 `singleExecuteDate`，計算出距離當前時間最近的未來觸發時間。
3.  對於已禁用的排程，`nextExecutedTime` 應顯示為“已禁用”或空白。
4.  對於“單次執行”且已執行過的排程，`nextExecutedTime` 應顯示為“已完成”或空白。
5.  在主介面排程列表中正確顯示計算出的下次執行時間。
6.  顯示格式應為用戶友好型，例如“今天 08:00”、“明天 10:30”、“週三 09:00”或“2 小時後”。

**依賴:**
*   `TASK_002_排程數據模型定義`
*   `TASK_005_排程列表數據展示與更新`
*   `TASK_009_日期/週期選擇器 UI 與邏輯`

**預估工時:**
*   6 小時

**負責人:**
*   [待定]

**備註:**
*   這個計算邏輯可能比較複雜，特別是對於跨週或跨年的“每週”模式。
*   使用 `java.time` (在 API 26+) 或 `Calendar` 類來處理日期和時間計算。
*   考慮數據量大時的性能優化，避免在主線程進行大量計算。