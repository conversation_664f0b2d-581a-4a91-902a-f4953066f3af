### TASK_012: 排程數據保存/更新邏輯

**描述:**
實現新增/編輯排程介面中的“保存”按鈕功能。這包括將用戶輸入的數據組合成 `Schedule` 物件，並根據是新增還是編輯模式，將數據插入或更新到 Room 資料庫中。同時，在保存後更新排程的 `AlarmManager` 設置。

**接受標準:**
1.  點擊保存按鈕後，從介面上的所有輸入控制項中收集數據（時間、日期/週期、選定的 App ）。
2.  將收集到的數據正確地組合成一個 `Schedule` 實例。
3.  判斷當前是新增排程還是編輯現有排程。
    *   如果是新增，則呼叫 `ScheduleDao.insert()` 將新排程插入資料庫。
    *   如果是編輯，則呼叫 `ScheduleDao.update()` 更新現有排程。
4.  成功保存或更新後，關閉新增/編輯介面，並返回主介面，主介面的排程列表應自動更新。
5.  呼叫 `AlarmManager` 相關功能（由 `TASK_013` 提供）來設定或更新該排程的鬧鐘。
6.  處理基本的輸入驗證（例如，是否所有必填項都已填寫）。

**依賴:**
*   `TASK_002_排程數據模型定義`
*   `TASK_003_Room 資料庫整合與 DAO`
*   `TASK_008_時間選擇器功能實現`
*   `TASK_009_日期/週期選擇器 UI 與邏輯`
*   `TASK_011_APP 選擇器介面 UI 與交互`
*   `TASK_013_AlarmManager 核心調度服務` (此任務會呼叫其功能)

**預估工時:**
*   6 小時

**負責人:**
*   [待定]

**備註:**
*   資料庫操作應在後台執行（例如使用協程）。
*   保存成功後，可以顯示一個簡短的 `Toast` 訊息給用戶。