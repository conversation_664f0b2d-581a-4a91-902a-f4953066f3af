### TASK_017: 排程刪除功能

**描述:**
為主介面的排程列表實現刪除功能，允許用戶方便地移除不需要的排程。這可能透過滑動刪除或長按彈出上下文選單來實現。

**接受標準:**
1.  實現一種刪除交互方式，例如：
    *   **滑動刪除 (Swipe-to-Dismiss):** 用戶可以向左或向右滑動列表項目來觸發刪除。
    *   **長按選單：** 長按列表項目後彈出一個上下文選單，提供“刪除”選項。
2.  在執行刪除操作前，彈出一個確認對話框，防止誤刪。
3.  確認刪除後：
    *   從資料庫中刪除對應的排程數據。
    *   呼叫 `AlarmManager` 服務取消與該排程相關的所有鬧鐘。
    *   更新主介面排程列表，移除被刪除的項目。
4.  （可選）提供一個“撤銷 (Undo)”選項，在短時間內允許用戶恢復剛刪除的排程。

**依賴:**
*   `TASK_003_Room 資料庫整合與 DAO`
*   `TASK_005_排程列表數據展示與更新`
*   `TASK_013_AlarmManager 核心調度服務`

**預估工時:**
*   4 小時

**負責人:**
*   [待定]

**備註:**
*   對於滑動刪除，`ItemTouchHelper` 是 `RecyclerView` 的標準實現方式。
*   資料庫和 `AlarmManager` 操作應在後台線程執行。