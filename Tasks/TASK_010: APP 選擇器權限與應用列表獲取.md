### TASK_010: APP 選擇器權限與應用列表獲取

**描述:**
為應用程式選擇功能獲取必要的權限 (`QUERY_ALL_PACKAGES`)，並從設備上掃描並獲取所有可啟動的已安裝應用程式列表。

**接受標準:**
1.  如果 Android 版本是 API 30 (Android 11) 或更高，應用程式已在 `AndroidManifest.xml` 中宣告 `QUERY_ALL_PACKAGES` 權限。
2.  應用程式能成功獲取並處理 `QUERY_ALL_PACKAGES` 權限（如果需要動態請求，則需要處理請求結果）。
3.  使用 `PackageManager` API，能夠成功獲取設備上所有已安裝應用程式的列表。
4.  過濾列表，僅包含具有 `ACTION_MAIN` 和 `CATEGORY_LAUNCHER` 的應用程式（即，可在啟動器中顯示的應用程式）。
5.  對於每個應用程式，能夠獲取其名稱 (Label)、包名 (Package Name) 和應用程式圖標 (Icon)。

**依賴:**
*   `TASK_001_專案基礎設定`

**預估工時:**
*   3 小時

**負責人:**
*   [待定]

**備註:**
*   `QUERY_ALL_PACKAGES` 權限對隱私有影響，應僅在必要時使用。
*   獲取 App 列表可能需要一些時間，建議在後台執行 (例如使用協程 `Dispatchers.IO`)。