#!/bin/bash

# AutoLaunch 應用程序測試運行腳本
# 運行所有測試並生成報告

echo "🚀 開始運行 AutoLaunch 應用程序測試套件..."
echo "=================================================="

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函數：打印帶顏色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 函數：檢查命令是否成功
check_result() {
    if [ $? -eq 0 ]; then
        print_message $GREEN "✅ $1 成功"
    else
        print_message $RED "❌ $1 失敗"
        exit 1
    fi
}

# 清理之前的測試結果
print_message $BLUE "🧹 清理之前的測試結果..."
./gradlew clean
check_result "清理"

# 運行單元測試
print_message $BLUE "🔬 運行單元測試..."
./gradlew test
check_result "單元測試"

# 運行 Android 儀器測試
print_message $BLUE "📱 運行 Android 儀器測試..."
./gradlew connectedAndroidTest
check_result "Android 儀器測試"

# 生成測試報告
print_message $BLUE "📊 生成測試報告..."
./gradlew jacocoTestReport
check_result "測試報告生成"

# 運行 lint 檢查
print_message $BLUE "🔍 運行 Lint 檢查..."
./gradlew lint
check_result "Lint 檢查"

# 顯示測試結果摘要
print_message $GREEN "🎉 所有測試完成！"
echo "=================================================="
print_message $YELLOW "📋 測試結果摘要："
echo ""

# 檢查測試報告文件是否存在
if [ -f "app/build/reports/tests/testDebugUnitTest/index.html" ]; then
    print_message $GREEN "📄 單元測試報告: app/build/reports/tests/testDebugUnitTest/index.html"
fi

if [ -f "app/build/reports/androidTests/connected/index.html" ]; then
    print_message $GREEN "📄 儀器測試報告: app/build/reports/androidTests/connected/index.html"
fi

if [ -f "app/build/reports/lint-results.html" ]; then
    print_message $GREEN "📄 Lint 報告: app/build/reports/lint-results.html"
fi

if [ -f "app/build/reports/jacoco/jacocoTestReport/html/index.html" ]; then
    print_message $GREEN "📄 代碼覆蓋率報告: app/build/reports/jacoco/jacocoTestReport/html/index.html"
fi

echo ""
print_message $BLUE "💡 提示："
echo "  - 在瀏覽器中打開 HTML 報告文件查看詳細結果"
echo "  - 確保所有測試都通過並且代碼覆蓋率達到預期"
echo "  - 修復任何 Lint 警告以提高代碼質量"
echo ""
print_message $GREEN "✨ 測試完成！應用程序質量得到保障。"
