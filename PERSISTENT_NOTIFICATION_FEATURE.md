# 常駐通知欄功能

## 功能概述

AutoLaunch 應用現在支援在通知欄常駐顯示，方便用戶隨時快速點擊啟動應用。此功能可以在設置頁面中開啟或關閉。

## 功能特點

### 1. 常駐通知顯示
- 在通知欄顯示 AutoLaunch 圖標
- 點擊通知可快速啟動應用
- 低優先級通知，不會干擾用戶
- 不顯示時間戳，保持簡潔

### 2. 設置控制
- 在側邊欄菜單中新增「設置」選項
- 設置頁面提供「常駐通知欄」開關
- 預設為啟用狀態
- 即時生效，無需重啟應用

### 3. 系統整合
- 開機自動恢復（如果啟用）
- 應用更新後自動恢復
- 支援 Android 13+ 通知權限檢查
- 與現有前台服務通知分離（不同 ID）

## 技術實現

### 核心組件

1. **SettingsManager** - 設置管理器
   - 管理常駐通知開關狀態
   - 使用 SharedPreferences 持久化設置

2. **PersistentNotificationManager** - 常駐通知管理器
   - 創建和管理常駐通知
   - 處理通知權限檢查
   - 提供顯示/隱藏/更新方法

3. **SettingsActivity** - 設置頁面
   - 提供用戶界面控制常駐通知
   - 即時更新通知狀態

### 通知 ID 分配
- ScheduleService 前台服務：ID 1000
- 常駐通知：ID 1001
- 避免 ID 衝突

### 權限處理
- Android 13+ 檢查 POST_NOTIFICATIONS 權限
- 無權限時優雅降級，記錄日誌

## 多語言支持

支援以下語言：
- 繁體中文（預設）
- 英文
- 日文
- 韓文

## 使用方式

1. 開啟 AutoLaunch 應用
2. 點擊左上角選單按鈕
3. 選擇「設置」
4. 切換「常駐通知欄」開關
5. 通知會立即顯示或隱藏

## 測試

### 單元測試
- `SettingsManagerTest` - 測試設置管理器功能

### UI 測試
- `SettingsActivityTest` - 測試設置頁面 UI 和交互

## 注意事項

1. 常駐通知會在應用關閉後繼續顯示
2. 用戶可以通過系統設置禁用應用通知
3. 在某些設備上可能需要手動允許通知權限
4. 通知優先級設為 LOW，減少對用戶的干擾
