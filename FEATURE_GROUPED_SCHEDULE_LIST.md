# 首頁排程列表分組顯示功能

## 功能概述

實現了首頁排程列表按類型分組顯示的功能，將排程分為「應用程式」和「網頁」兩個類別，提供更清晰的視覺組織和更好的用戶體驗。

## 實現的功能

### 1. 分組顯示
- **應用程式排程區塊**：顯示所有 APP 類型的排程
- **網頁排程區塊**：顯示所有 URL 類型的排程
- **動態顯示**：只有當該類型有排程時才顯示對應區塊
- **計數顯示**：每個區塊標題旁顯示該類型的排程數量

### 2. UI 設計
- **圖標區分**：應用程式使用 apps 圖標，網頁使用 web 圖標
- **清晰標題**：每個區塊都有明確的標題和計數徽章
- **滾動支持**：使用 ScrollView 包裝，支持長列表滾動
- **Material Design**：遵循 Material Design 3 設計規範

### 3. 功能保持
- **編輯功能**：點擊排程項目仍可進入編輯頁面
- **開關切換**：啟用/禁用開關功能正常工作
- **滑動刪除**：兩個區塊都支持滑動刪除功能
- **撤銷操作**：刪除後可以撤銷恢復

## 修改的文件

### 1. 佈局文件
- `app/src/main/res/layout/activity_main.xml`
  - 替換單一 RecyclerView 為分組佈局
  - 添加應用程式和網頁兩個區塊
  - 每個區塊包含標題、圖標、計數和 RecyclerView

### 2. 圖標資源
- `app/src/main/res/drawable/ic_apps_24.xml` - 應用程式圖標
- `app/src/main/res/drawable/ic_web_24.xml` - 網頁圖標
- `app/src/main/res/drawable/bg_count_badge.xml` - 計數徽章背景

### 3. MainActivity
- 修改適配器設置，使用兩個獨立的 ScheduleAdapter
- 更新顯示邏輯，支援分組顯示
- 修改滑動刪除功能，支援兩個 RecyclerView
- 更新刪除確認對話框，支援兩種類型

### 4. 測試數據
- `app/src/main/java/com/example/autolaunch/model/ScheduleRepository.kt`
  - 添加 URL 類型的測試數據
  - 確保兩種類型都有示例數據用於測試

### 5. ViewModel
- `app/src/main/java/com/example/autolaunch/ScheduleViewModel.kt`
  - 啟用測試數據初始化以展示分組功能

## 技術實現細節

### 分組邏輯
```kotlin
// 按類型分組排程
val appSchedules = schedules.filter { it.scheduleType == ScheduleType.APP.value }
val urlSchedules = schedules.filter { it.scheduleType == ScheduleType.URL.value }
```

### 動態顯示
```kotlin
// 只有當該類型有排程時才顯示區塊
if (appSchedules.isNotEmpty()) {
    binding.appScheduleSection.visibility = View.VISIBLE
    binding.appScheduleCount.text = appSchedules.size.toString()
    appScheduleAdapter.submitList(appSchedules)
} else {
    binding.appScheduleSection.visibility = View.GONE
}
```

### 雙適配器設計
- 為每個類型創建獨立的 ScheduleAdapter 實例
- 支援獨立的點擊和切換事件處理
- 各自管理滑動刪除功能

## 用戶體驗改進

1. **清晰分類**：用戶可以快速區分應用程式排程和網頁排程
2. **視覺層次**：通過圖標和標題提供清晰的視覺層次
3. **計數信息**：即時顯示每個類型的排程數量
4. **空間優化**：只顯示有內容的區塊，節省屏幕空間
5. **一致體驗**：保持原有的所有交互功能

## 測試驗證

- ✅ 編譯成功
- ✅ 安裝成功
- ✅ 應用程式啟動正常
- ✅ 無運行時錯誤
- ✅ 分組顯示功能正常
- ✅ 測試數據正確加載

## 後續可能的改進

1. **排序選項**：為每個分組添加排序選項（按時間、名稱等）
2. **摺疊功能**：允許用戶摺疊/展開各個分組
3. **搜索功能**：在分組內搜索特定排程
4. **統計信息**：顯示更詳細的統計信息（啟用數量、今日執行等）
5. **自定義分組**：允許用戶創建自定義分組標籤
