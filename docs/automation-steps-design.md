# 自動化步驟功能設計文檔

## 1. 功能概述

### 1.1 需求描述
擴充現有的排程功能，讓使用者在新增排程時，除了選擇要啟動的 App，還能設定一連串的自動化步驟。當排程的 APP 啟動後，系統會模擬用戶點擊的行為，自動執行預設的操作序列。

### 1.2 核心功能
- **步驟編輯器**：提供直觀的UI界面讓用戶配置自動化步驟
- **步驟執行引擎**：解析和執行自動化步驟序列
- **無障礙服務**：利用Android AccessibilityService實現UI自動化
- **錯誤處理**：提供穩定的執行環境和錯誤恢復機制

## 2. 技術架構

### 2.1 整體架構
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   排程觸發      │───▶│   APP啟動       │───▶│  自動化步驟執行  │
│ (AlarmManager)  │    │ (LaunchReceiver)│    │(AutomationEngine)│
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
                                                       ▼
                                              ┌─────────────────┐
                                              │   無障礙服務     │
                                              │(AccessibilityService)│
                                              └─────────────────┘
```

### 2.2 核心組件

#### 2.2.1 數據層
- **AutomationStep**: 自動化步驟實體類
- **AutomationStepDao**: 數據庫訪問對象
- **StepType**: 步驟類型枚舉

#### 2.2.2 服務層
- **AutoLaunchAccessibilityService**: 無障礙服務實現
- **AutomationEngine**: 步驟執行引擎
- **UIElementFinder**: UI元素定位器

#### 2.2.3 UI層
- **AutomationStepEditor**: 步驟編輯器組件
- **StepConfigDialog**: 步驟配置對話框
- **StepListAdapter**: 步驟列表適配器

## 3. 數據模型設計

### 3.1 AutomationStep 實體類
```kotlin
@Entity(tableName = "automation_steps")
data class AutomationStep(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    
    @ColumnInfo(name = "schedule_id")
    val scheduleId: Long, // 關聯的排程ID
    
    @ColumnInfo(name = "step_order")
    val stepOrder: Int, // 執行順序
    
    @ColumnInfo(name = "step_type")
    val stepType: Int, // 步驟類型
    
    @ColumnInfo(name = "target_text")
    val targetText: String? = null, // 目標文字
    
    @ColumnInfo(name = "target_id")
    val targetId: String? = null, // 目標元素ID
    
    @ColumnInfo(name = "target_x")
    val targetX: Int? = null, // 目標X座標
    
    @ColumnInfo(name = "target_y")
    val targetY: Int? = null, // 目標Y座標
    
    @ColumnInfo(name = "input_text")
    val inputText: String? = null, // 輸入文字內容
    
    @ColumnInfo(name = "wait_duration")
    val waitDuration: Long? = null, // 等待時間(毫秒)
    
    @ColumnInfo(name = "is_enabled")
    val isEnabled: Boolean = true,
    
    @ColumnInfo(name = "created_time")
    val createdTime: Long = System.currentTimeMillis()
)
```

### 3.2 StepType 枚舉
```kotlin
enum class StepType(val value: Int, val displayName: String) {
    CLICK(1, "點擊"),
    LONG_CLICK(2, "長按"),
    INPUT_TEXT(3, "輸入文字"),
    SWIPE(4, "滑動"),
    WAIT(5, "等待"),
    SCROLL(6, "滾動"),
    BACK(7, "返回"),
    HOME(8, "回到主頁")
}
```

## 4. UI設計

### 4.1 步驟編輯器界面
在 `AddEditScheduleActivity` 中新增自動化步驟配置區域：

```xml
<!-- 自動化步驟配置區域 -->
<com.google.android.material.card.MaterialCardView
    android:id="@+id/cardAutomationSteps"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:visibility="gone">
    
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">
        
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="自動化步驟"
            android:textAppearance="@style/TextAppearance.Material3.TitleMedium" />
        
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerViewSteps"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp" />
        
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnAddStep"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="新增步驟"
            android:layout_marginTop="12dp"
            style="@style/Widget.Material3.Button.OutlinedButton" />
        
    </LinearLayout>
</com.google.android.material.card.MaterialCardView>
```

### 4.2 步驟配置對話框
提供各種步驟類型的配置界面：
- **點擊步驟**：選擇目標元素（文字/ID/座標）
- **輸入步驟**：選擇輸入框 + 輸入內容
- **等待步驟**：設定等待時間
- **滑動步驟**：設定起始和結束座標

## 5. 無障礙服務實現

### 5.1 服務配置
```xml
<!-- res/xml/accessibility_service_config.xml -->
<accessibility-service
    android:accessibilityEventTypes="typeAllMask"
    android:accessibilityFlags="flagDefault"
    android:accessibilityFeedbackType="feedbackGeneric"
    android:notificationTimeout="100"
    android:canRetrieveWindowContent="true"
    android:canPerformGestures="true" />
```

### 5.2 核心功能
- **UI元素識別**：通過文字、ID、座標定位元素
- **手勢模擬**：點擊、長按、滑動等操作
- **狀態監控**：監控APP狀態變化和執行結果

## 6. 執行流程

### 6.1 整體流程
1. **排程觸發** → AlarmManager觸發排程
2. **APP啟動** → LaunchReceiver啟動目標APP
3. **等待APP載入** → 檢測APP完全啟動
4. **執行自動化步驟** → 按順序執行配置的步驟
5. **完成記錄** → 記錄執行結果和日誌

### 6.2 步驟執行邏輯
```kotlin
class AutomationEngine {
    suspend fun executeSteps(scheduleId: Long) {
        val steps = getAutomationSteps(scheduleId)
        
        for (step in steps.sortedBy { it.stepOrder }) {
            if (!step.isEnabled) continue
            
            try {
                when (step.getStepType()) {
                    StepType.CLICK -> performClick(step)
                    StepType.INPUT_TEXT -> performInput(step)
                    StepType.WAIT -> performWait(step)
                    // ... 其他步驟類型
                }
                
                // 記錄成功執行
                logStepExecution(step, true)
                
            } catch (e: Exception) {
                // 錯誤處理和重試
                handleStepError(step, e)
            }
        }
    }
}
```

## 7. 權限和安全

### 7.1 必要權限
- **無障礙服務權限**：`android.permission.BIND_ACCESSIBILITY_SERVICE`
- **系統彈窗權限**：`android.permission.SYSTEM_ALERT_WINDOW`
- **前台服務權限**：`android.permission.FOREGROUND_SERVICE`

### 7.2 安全考量
- **用戶授權**：明確告知用戶自動化功能的作用和風險
- **權限檢查**：在執行前檢查所有必要權限
- **執行限制**：僅在用戶明確配置的APP中執行自動化
- **隱私保護**：不記錄敏感輸入內容

## 8. 錯誤處理

### 8.1 常見錯誤類型
- **元素未找到**：目標UI元素不存在或已變更
- **權限不足**：無障礙服務未啟用或權限不足
- **APP狀態異常**：目標APP崩潰或未正常啟動
- **執行超時**：步驟執行時間過長

### 8.2 處理策略
- **重試機制**：失敗步驟自動重試（最多3次）
- **跳過策略**：非關鍵步驟失敗時繼續執行
- **用戶通知**：執行失敗時通知用戶具體原因
- **日誌記錄**：詳細記錄執行過程和錯誤信息

## 9. 性能優化

### 9.1 執行效率
- **異步執行**：使用協程避免阻塞主線程
- **智能等待**：動態調整等待時間
- **資源管理**：及時釋放無障礙服務資源

### 9.2 用戶體驗
- **進度提示**：顯示當前執行步驟
- **中斷機制**：允許用戶中斷執行
- **預覽功能**：執行前預覽步驟序列

## 10. 測試策略

### 10.1 單元測試
- 數據模型驗證
- 步驟執行邏輯測試
- 錯誤處理機制測試

### 10.2 整合測試
- 完整自動化流程測試
- 多種APP兼容性測試
- 權限和安全性測試

### 10.3 用戶測試
- 易用性測試
- 穩定性測試
- 性能測試
