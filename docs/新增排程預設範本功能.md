# 新增排程預設範本功能

## 功能概述

在新增排程時，系統現在會自動提供預設範本，讓用戶能夠更快速地創建排程：

- **APP 排程**：預設顯示 AutoLaunch 應用本身
- **Web 排程**：預設顯示 Google (https://www.google.com)

## 功能特點

### 1. 自動預設範本
- 當用戶進入新增排程頁面時，系統會根據選擇的排程類型自動設定預設範本
- APP 排程會自動選擇 AutoLaunch 應用本身作為預設應用
- Web 排程會自動填入 Google 網址和標題

### 2. 動態切換範本
- 當用戶在新增排程時切換排程類型（APP ↔ Web），系統會自動應用對應的預設範本
- 只在新增模式下生效，編輯現有排程時不會覆蓋原有設定

### 3. 智能預設設定
- APP 排程：自動獲取 AutoLaunch 應用的名稱和包名
- Web 排程：預設設定為 "Google" 標題和 "https://www.google.com" 網址

## 使用方法

### 新增 APP 排程
1. 點擊「新增排程」按鈕
2. 選擇「應用程式」排程類型（預設已選擇）
3. 系統會自動選擇 AutoLaunch 應用作為預設應用
4. 用戶可以點擊應用選擇區域來更換其他應用
5. 設定時間和重複模式後儲存

### 新增 Web 排程
1. 點擊「新增排程」按鈕
2. 選擇「網頁」排程類型
3. 系統會自動填入 Google 網址和標題
4. 用戶可以修改網址和標題為其他網站
5. 設定時間和重複模式後儲存

### 切換排程類型
1. 在新增排程頁面中，點擊不同的排程類型按鈕
2. 系統會自動應用對應類型的預設範本
3. 用戶可以在此基礎上進行修改

## 技術實現

### 核心方法
- `setupDefaultTemplates()`: 根據當前排程類型設定預設範本
- `setupDefaultAppTemplate()`: 設定 APP 排程預設範本
- `setupDefaultWebTemplate()`: 設定 Web 排程預設範本

### 觸發時機
- 新增排程頁面初始化時
- 用戶切換排程類型時（僅限新增模式）

### 安全機制
- 只在新增模式（scheduleId == -1L）下生效
- 編輯現有排程時不會覆蓋原有設定
- 獲取應用資訊失敗時會優雅降級，保持原有狀態

## 優勢

1. **提升用戶體驗**：減少用戶手動輸入的步驟
2. **快速上手**：新用戶可以立即看到範例設定
3. **常用場景**：預設範本涵蓋最常見的使用場景
4. **靈活性**：用戶仍可自由修改預設設定

## 注意事項

- 預設範本只在新增排程時生效
- 編輯現有排程時不會改變原有設定
- 如果無法獲取 AutoLaunch 應用資訊，APP 排程會保持空白狀態
- Web 排程的預設網址可以正常訪問和預覽
