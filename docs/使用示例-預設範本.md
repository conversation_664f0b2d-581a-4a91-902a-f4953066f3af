# 預設範本功能使用示例

## 功能演示

### 1. 新增 APP 排程（預設範本）

當用戶點擊「新增排程」時：

```
步驟 1: 進入新增排程頁面
- 排程類型：預設選擇「應用程式」
- 應用選擇：自動顯示「AutoLaunch」應用
- 時間設定：預設為當前時間 + 1分鐘
- 重複模式：預設為「每日」

步驟 2: 用戶可以直接使用預設設定
- 調整時間（如需要）
- 調整重複模式（如需要）
- 點擊「儲存」完成

步驟 3: 或者修改應用選擇
- 點擊應用選擇區域
- 選擇其他應用程式
- 完成設定並儲存
```

### 2. 新增 Web 排程（預設範本）

當用戶切換到「網頁」排程類型時：

```
步驟 1: 選擇「網頁」排程類型
- 網址欄位：自動填入 "https://www.google.com"
- 標題欄位：自動填入 "Google"
- 時間設定：保持當前設定
- 重複模式：保持當前設定

步驟 2: 用戶可以直接使用預設設定
- 調整時間（如需要）
- 調整重複模式（如需要）
- 點擊「儲存」完成

步驟 3: 或者修改網址設定
- 修改網址為其他網站
- 修改標題為自定義名稱
- 完成設定並儲存
```

### 3. 動態切換範本

在新增排程過程中切換類型：

```
場景 A: 從 APP 切換到 Web
- 初始狀態：APP 類型，顯示 AutoLaunch 應用
- 切換到 Web 類型
- 自動清空應用選擇，填入 Google 網址和標題

場景 B: 從 Web 切換到 APP
- 初始狀態：Web 類型，顯示 Google 網址
- 切換到 APP 類型
- 自動清空網址設定，選擇 AutoLaunch 應用
```

## 實際使用場景

### 場景 1: 快速創建自啟動排程
```
用戶需求：設定每天早上 8:00 自動啟動 AutoLaunch
操作步驟：
1. 點擊「新增排程」
2. 保持預設的 APP 類型和 AutoLaunch 應用
3. 修改時間為 08:00
4. 保持「每日」重複模式
5. 點擊「儲存」
```

### 場景 2: 快速創建網頁排程
```
用戶需求：設定每天中午 12:00 打開 Google 搜尋
操作步驟：
1. 點擊「新增排程」
2. 切換到「網頁」類型
3. 保持預設的 Google 網址和標題
4. 修改時間為 12:00
5. 保持「每日」重複模式
6. 點擊「儲存」
```

### 場景 3: 基於範本創建自定義排程
```
用戶需求：設定每週一到五晚上 9:00 打開 YouTube
操作步驟：
1. 點擊「新增排程」
2. 切換到「網頁」類型
3. 修改網址為 "https://www.youtube.com"
4. 修改標題為 "YouTube"
5. 修改時間為 21:00
6. 修改重複模式為「每週」
7. 選擇週一到週五
8. 點擊「儲存」
```

## 優勢展示

### 1. 減少操作步驟
- **傳統方式**：選擇類型 → 手動選擇應用/輸入網址 → 設定時間 → 設定重複 → 儲存（5步）
- **預設範本**：調整時間 → 儲存（2步，其他可選）

### 2. 降低學習成本
- 新用戶立即看到完整的範例設定
- 了解每個欄位的預期格式和內容
- 可以直接基於範本進行修改

### 3. 提高設定效率
- 常用場景（自啟動、Google搜尋）一鍵設定
- 減少輸入錯誤的可能性
- 提供標準化的設定範本

## 注意事項

1. **僅限新增模式**：編輯現有排程時不會覆蓋原有設定
2. **智能降級**：如果無法獲取 AutoLaunch 應用資訊，會保持空白狀態
3. **用戶控制**：所有預設值都可以被用戶修改
4. **類型切換**：只在新增模式下切換類型時應用新範本
