# 首頁 Tab 改版功能

## 功能概述

實現了首頁使用 tabs 來分別顯示 APP 和網頁排程的功能，參考備份頁面的 tab 設計，提供更直覺的用戶體驗。

## 實現的功能

### 1. 簡約 Tab 設計
- **APP Tab**：顯示所有應用程式類型的排程
- **網頁 Tab**：顯示所有網頁 URL 類型的排程
- **左右滑動**：支持手勢滑動切換 tab
- **數量顯示**：tab 標題顯示對應類型的排程數量（如 "APP (3)"）
- **清晰指示器**：使用標準的底部指示器，3dp 高度
- **清晰分隔**：tab 下方有細線分隔，增強視覺層次
- **純文字設計**：專注於內容，不使用圖標
- **無背景干擾**：透明背景，讓內容成為焦點
- **大字體設計**：16sp 字體大小，提升可讀性

### 2. Fragment 架構
- **AppScheduleFragment**：管理 APP 排程列表
- **WebScheduleFragment**：管理網頁排程列表
- **MainPagerAdapter**：管理 Fragment 切換邏輯

### 3. 空狀態處理
- **APP 空狀態**：顯示專門的 APP 排程空狀態提示
- **網頁空狀態**：顯示專門的網頁排程空狀態提示
- **整體空狀態**：當沒有任何排程時顯示原有的空狀態卡片

## 技術實現

### 1. 新增文件

#### Fragment 文件
- `app/src/main/java/com/example/autolaunch/fragment/AppScheduleFragment.kt`
- `app/src/main/java/com/example/autolaunch/fragment/WebScheduleFragment.kt`
- `app/src/main/res/layout/fragment_app_schedule.xml`
- `app/src/main/res/layout/fragment_web_schedule.xml`

#### Adapter 文件
- `app/src/main/java/com/example/autolaunch/adapter/MainPagerAdapter.kt`

#### 資源文件
- 使用系統預設的 TabLayout 樣式
- 無需額外的自定義 drawable 資源
- 簡潔的分隔線使用系統顏色

### 2. 修改文件

#### MainActivity 修改
- 添加 TabLayout 和 ViewPager2 支持
- 新增 `setupTabs()` 方法
- 修改 `observeViewModel()` 使用新的 `showTabScheduleList()` 方法
- 更新空狀態處理邏輯

#### Layout 修改
- `app/src/main/res/layout/activity_main.xml`：添加 TabLayout 和 ViewPager2

#### 字符串資源
- 添加刪除確認對話框相關字符串

### 3. 核心邏輯

```kotlin
// Tab 設置
private fun setupTabs() {
    mainPagerAdapter = MainPagerAdapter(this)
    binding.viewPager.adapter = mainPagerAdapter

    TabLayoutMediator(binding.tabLayout, binding.viewPager) { tab, position ->
        tab.text = mainPagerAdapter.getTabTitle(position)
    }.attach()
}

// 按鈕位置優化
android:layout_marginBottom="80dp"  // 新增排程按鈕距離底部 80dp
android:paddingBottom="120dp"       // RecyclerView 底部 padding 120dp

// Tab 字體優化
app:tabTextAppearance="@style/CustomTabTextStyle"  // 自定義文字樣式
android:layout_height="56dp"                       // 增加 tab 高度
app:tabIndicatorHeight="3dp"                       // 增加指示器高度

// 顯示 Tab 列表
private fun showTabScheduleList(schedules: List<Schedule>) {
    binding.apply {
        emptyStateCard.visibility = View.GONE
        welcomeCard.visibility = if (settingsManager.isWelcomeCardVisible()) View.VISIBLE else View.GONE
        recyclerViewSchedules.visibility = View.GONE
        tabLayout.visibility = View.VISIBLE
        viewPager.visibility = View.VISIBLE
    }

    // 更新 tab 標題以顯示排程數量
    updateTabTitlesWithCount(schedules)
}

// 更新 Tab 標題顯示數量
private fun updateTabTitlesWithCount(schedules: List<Schedule>) {
    val appCount = schedules.count { it.scheduleType == ScheduleType.APP.value }
    val webCount = schedules.count { it.scheduleType == ScheduleType.URL.value }

    binding.tabLayout.getTabAt(0)?.text = mainPagerAdapter.getTabTitleWithCount(0, appCount)
    binding.tabLayout.getTabAt(1)?.text = mainPagerAdapter.getTabTitleWithCount(1, webCount)
}
```

## 用戶體驗改進

### 1. 更直覺的分類
- 用戶可以清楚地看到 APP 和網頁排程的分類
- 不再需要在長列表中尋找特定類型的排程

### 2. 更好的視覺組織
- 每個 tab 專注於單一類型的排程
- 減少視覺混亂，提高可讀性

### 3. 手勢支持
- 支持左右滑動切換 tab
- 符合現代移動應用的交互習慣

### 4. 保持功能完整性
- 保留所有原有功能（拖拽排序、滑動刪除、點擊編輯等）
- 每個 Fragment 都有完整的排程管理功能

## 設計特點

### 1. 參考備份頁面設計
- 使用相同的 TabLayout + ViewPager2 架構
- 保持一致的視覺風格和交互模式

### 2. 簡約設計原則
- **最小化視覺干擾**：透明背景，專注於內容
- **標準 Material Design**：使用系統預設的 TabLayout 樣式
- **清晰的狀態指示**：3dp 高度的底部指示器
- **一致的主題色**：使用 colorPrimary 作為指示器和選中文字顏色
- **優化字體大小**：16sp 字體，加粗顯示，提升可讀性

### 3. 視覺層次設計
- **文字顏色**：未選中使用 colorOnSurfaceVariant，選中使用 colorPrimary
- **分隔線**：使用 colorOutlineVariant 的細線分隔
- **無背景干擾**：完全透明的背景讓內容更突出

### 4. 響應式設計
- 自動適應不同屏幕尺寸
- 支持深色/淺色主題切換
- 使用系統主題色，確保一致性

## 測試狀態

- ✅ 編譯成功
- ✅ 無語法錯誤
- ✅ 字符串資源完整
- ✅ Fragment 架構正確
- ✅ Adapter 邏輯完整
- ✅ Tab 數量顯示功能完整

## 已實現的優化功能

1. **數量顯示**：✅ Tab 標題顯示對應類型的排程數量
2. **動態更新**：✅ 排程數量變化時自動更新 tab 標題
3. **按鈕位置優化**：✅ 新增排程按鈕距離底部更遠，避免被系統選單遮擋
4. **簡約設計**：✅ 透明背景，專注於內容展示
5. **清晰指示器**：✅ 3dp 高度的底部指示器，清楚標示選中狀態
6. **視覺分隔**：✅ 細線分隔增強視覺層次
7. **主題一致性**：✅ 使用系統主題色，與應用整體風格統一
8. **字體優化**：✅ 16sp 大字體，加粗顯示，大幅提升可讀性
9. **適當高度**：✅ 56dp tab 高度，為大字體提供充足空間

## 後續優化建議

1. **性能優化**：考慮使用 Fragment 懶加載
2. **狀態保存**：記住用戶最後選擇的 tab
3. **視覺優化**：考慮使用徽章樣式顯示數量
4. **無障礙支持**：添加更好的無障礙標籤和描述
